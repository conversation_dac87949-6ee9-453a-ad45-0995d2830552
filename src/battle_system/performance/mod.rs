/// 第五阶段：战斗引擎性能优化
/// 
/// 这个模块包含了战斗系统的性能优化组件：
/// - 对象池化系统
/// - 并发安全管理
/// - 内存优化
/// - 计算缓存
/// - AI决策优化

pub mod battle_engine;
pub mod object_pools;
pub mod concurrent_battle;
pub mod memory_optimization;
pub mod calculation_cache;
pub mod ai_optimization;

// 导出核心组件
pub use object_pools::{
    SkillInstancePool, EffectInstancePool, BattleEventPool,
    PooledSkillInstance, PooledEffectInstance,
};

pub use concurrent_battle::{
    ConcurrentBattleManager, BattleLock, SkillConflictResolver,
    BattleStateSync, NetworkCompensation,
};

pub use memory_optimization::{
    MemoryPool, CompactBattleState, MemoryProfiler,
    CacheOptimizedData, MemoryBudget,
};

pub use calculation_cache::{
    DamageCalculationCache, SkillEffectCache, PathfindingCache,
    CacheManager, CacheStrategy,
};

/*
pub use ai_optimization::{
    IntelligentSkillSelector, TacticalCombinationEngine,
    AIDecisionCache, BehaviorPredictor,
};

pub use battle_engine::{
    OptimizedBattleEngine, PerformanceMetrics, BattleEngineConfig,
    EngineOptimization, BenchmarkSuite,
};
*/
/// 性能优化配置
#[derive(Debug, Clone)]
pub struct PerformanceConfig {
    /// 是否启用对象池化
    pub enable_object_pooling: bool,
    /// 对象池大小
    pub pool_size: usize,
    /// 是否启用并发处理
    pub enable_concurrency: bool,
    /// 工作线程数
    pub worker_threads: usize,
    /// 是否启用计算缓存
    pub enable_caching: bool,
    /// 缓存大小限制
    pub cache_size_mb: usize,
    /// 是否启用AI优化
    pub enable_ai_optimization: bool,
    /// 内存预算限制
    pub memory_budget_mb: usize,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            enable_object_pooling: true,
            pool_size: 1000,
            enable_concurrency: true,
            worker_threads: 4,
            enable_caching: true,
            cache_size_mb: 64,
            enable_ai_optimization: true,
            memory_budget_mb: 256,
        }
    }
}

/// 性能指标
#[derive(Debug, Clone, Default)]
pub struct PerformanceStats {
    /// 技能执行次数
    pub skills_executed: u64,
    /// 平均技能执行时间（微秒）
    pub avg_skill_execution_time_us: f64,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 内存使用量（字节）
    pub memory_usage_bytes: usize,
    /// 并发冲突次数
    pub conflict_count: u64,
    /// AI决策时间（微秒）
    pub avg_ai_decision_time_us: f64,
}

/// 优化级别
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OptimizationLevel {
    /// 基础优化（最小开销）
    Basic,
    /// 标准优化（平衡性能和开销）
    Standard,
    /// 高性能优化（最大性能）
    Custom,
}

/// 性能基准测试结果
#[derive(Debug, Clone)]
pub struct BenchmarkResult {
    /// 测试名称
    pub test_name: String,
    /// 执行时间（纳秒）
    pub execution_time_ns: u64,
    /// 内存分配次数
    pub allocations: usize,
    /// 内存使用峰值（字节）
    pub peak_memory_bytes: usize,
    /// 吞吐量（操作/秒）
    pub throughput_ops_per_sec: f64,
}

/// 全局性能管理器
pub struct PerformanceManager {
    config: PerformanceConfig,
    stats: PerformanceStats,
    optimization_level: OptimizationLevel,
}

impl PerformanceManager {
    pub fn new(config: PerformanceConfig) -> Self {
        Self {
            config,
            stats: PerformanceStats::default(),
            optimization_level: OptimizationLevel::Standard,
        }
    }
    
    pub fn set_optimization_level(&mut self, level: OptimizationLevel) {
        self.optimization_level = level;
    }
    
    pub fn get_stats(&self) -> &PerformanceStats {
        &self.stats
    }
    
    pub fn reset_stats(&mut self) {
        self.stats = PerformanceStats::default();
    }
    
    /// 记录技能执行
    pub fn record_skill_execution(&mut self, execution_time_us: f64) {
        self.stats.skills_executed += 1;
        let total_time = self.stats.avg_skill_execution_time_us * (self.stats.skills_executed - 1) as f64;
        self.stats.avg_skill_execution_time_us = (total_time + execution_time_us) / self.stats.skills_executed as f64;
    }
    
    /// 记录缓存命中
    pub fn record_cache_hit(&mut self, hit: bool) {
        // 简化的命中率计算
        let current_rate = self.stats.cache_hit_rate;
        self.stats.cache_hit_rate = current_rate * 0.99 + if hit { 0.01 } else { 0.0 };
    }
    
    /// 更新内存使用
    pub fn update_memory_usage(&mut self, bytes: usize) {
        self.stats.memory_usage_bytes = bytes;
    }
    
    /// 记录并发冲突
    pub fn record_conflict(&mut self) {
        self.stats.conflict_count += 1;
    }
    
    /// 记录AI决策时间
    pub fn record_ai_decision(&mut self, decision_time_us: f64) {
        let total_time = self.stats.avg_ai_decision_time_us * self.stats.skills_executed as f64;
        self.stats.avg_ai_decision_time_us = (total_time + decision_time_us) / (self.stats.skills_executed + 1) as f64;
    }
}
