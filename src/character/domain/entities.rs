/// Character聚合内的实体
/// 
/// 这些实体有自己的身份标识，但生命周期依赖于Character聚合根

use crate::shared::*;
use crate::skill::buff::{Buff, BuffType};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use crate::attribute::attribute::{AttributeSet, CoreAttribute, AttributeType};
use std::str::FromStr;

// ============================================================================
// 技能冷却管理器实体
// ============================================================================

/// 技能冷却管理器 - 实体，管理角色的技能冷却状态
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SkillCooldownManager {
    /// 技能冷却映射：技能ID -> 剩余冷却时间
    cooldowns: HashMap<SkillId, CooldownTime>,
    /// 冷却减免百分比 (0.0-1.0)
    cooldown_reduction: f32,
}

impl SkillCooldownManager {
    /// 创建新的技能冷却管理器
    pub fn new() -> Self {
        Self {
            cooldowns: HashMap::new(),
            cooldown_reduction: 0.0,
        }
    }
    
    /// 获取技能剩余冷却时间
    pub fn get_cooldown(&self, skill_id: &SkillId) -> CooldownTime {
        self.cooldowns.get(skill_id).copied().unwrap_or(0.0)
    }
    
    /// 检查技能是否可用
    pub fn is_skill_ready(&self, skill_id: &SkillId) -> bool {
        self.get_cooldown(skill_id) <= 0.0
    }
    
    /// 设置技能冷却时间
    pub fn set_cooldown(&mut self, skill_id: SkillId, cooldown: CooldownTime) -> GameResult<()> {
        if cooldown < 0.0 {
            return Err(GameError::validation_error("cooldown", "冷却时间不能为负"));
        }
        
        // 应用冷却减免
        let actual_cooldown = cooldown * (1.0 - self.cooldown_reduction as f64);
        
        if actual_cooldown > 0.0 {
            self.cooldowns.insert(skill_id, actual_cooldown);
        } else {
            self.cooldowns.remove(&skill_id);
        }
        
        Ok(())
    }
    
    /// 更新冷却时间（通常在游戏循环中调用）
    pub fn update_cooldowns(&mut self, delta_time: f32) {
        let mut to_remove = Vec::new();
        
        for (skill_id, cooldown) in self.cooldowns.iter_mut() {
            *cooldown -= delta_time as f64;
            if *cooldown <= 0.0 {
                to_remove.push(*skill_id);
            }
        }
        
        for skill_id in to_remove {
            self.cooldowns.remove(&skill_id);
        }
    }
    
    /// 设置冷却减免
    pub fn set_cooldown_reduction(&mut self, reduction: f32) -> GameResult<()> {
        if reduction < 0.0 || reduction > 1.0 {
            return Err(GameError::validation_error("reduction", "冷却减免必须在0-1之间"));
        }
        
        self.cooldown_reduction = reduction;
        Ok(())
    }
    
    /// 获取冷却减免
    pub fn cooldown_reduction(&self) -> f32 {
        self.cooldown_reduction
    }
    
    /// 清除所有冷却
    pub fn clear_all_cooldowns(&mut self) {
        self.cooldowns.clear();
    }
    
    /// 获取所有在冷却的技能
    pub fn get_cooling_skills(&self) -> Vec<(SkillId, CooldownTime)> {
        self.cooldowns.iter().map(|(&id, &time)| (id, time)).collect()
    }

    //
    // 兼容旧trait的临时访问器
    //
    pub fn cooldowns_mut(&mut self) -> &mut HashMap<SkillId, CooldownTime> {
        &mut self.cooldowns
    }

    pub fn cooldowns(&self) -> &HashMap<SkillId, CooldownTime> {
        &self.cooldowns
    }
}

impl Default for SkillCooldownManager {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// Buff管理器实体
// ============================================================================

/// Buff管理器 - 实体，管理角色身上的所有buff
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BuffManager {
    /// 当前生效的buff列表
    buffs: Vec<Buff>,
    /// Buff上限
    max_buffs: usize,
}

impl BuffManager {
    /// 创建新的Buff管理器
    pub fn new(max_buffs: usize) -> Self {
        Self {
            buffs: Vec::new(),
            max_buffs,
        }
    }
    
    /// 添加Buff
    pub fn add_buff(&mut self, buff: Buff) -> GameResult<()> {
        // 检查是否已达到上限
        if self.buffs.len() >= self.max_buffs {
            return Err(GameError::validation_error("buff", "Buff数量已达上限"));
        }
        
        // 检查是否有同类型buff（某些buff不能叠加）
        if let Some(existing_index) = self.find_buff_by_type(&buff) {
            // 替换现有buff或叠加效果（根据buff类型决定）
            if buff.can_stack() {
                self.buffs.push(buff);
            } else {
                self.buffs[existing_index] = buff;
            }
        } else {
            self.buffs.push(buff);
        }
        
        Ok(())
    }
    
    /// 移除Buff
    pub fn remove_buff(&mut self, buff_id: &str) -> bool {
        if let Ok(id_u32) = buff_id.parse::<u32>() {
            if let Some(index) = self.buffs.iter().position(|b| b.id == id_u32) {
                self.buffs.remove(index);
                return true;
            }
        }
        false
    }
    
    /// 更新所有Buff（处理持续时间和效果）
    pub fn update_buffs(&mut self, delta_time: f32) {
        // 只更新持续时间，不移除
        for buff in &mut self.buffs {
            if !buff.is_permanent {
                buff.duration -= delta_time;
            }
        }
    }
    
    pub fn remove_expired_buffs(&mut self) {
        self.buffs.retain(|buff| !buff.is_expired());
    }

    /// 获取所有buff的引用
    pub fn get_buffs(&self) -> &[Buff] {
        &self.buffs
    }
    
    /// 获取特定类型的buff
    pub fn get_buffs_by_type(&self, buff_type: &str) -> Vec<&Buff> {
        if let Ok(target_type) = BuffType::from_str(buff_type) {
            self.buffs.iter().filter(|buff| buff.buff_type == target_type).collect()
        } else {
            Vec::new()
        }
    }
    
    /// 检查是否有特定buff
    pub fn has_buff(&self, buff_id: &str) -> bool {
        if let Ok(id_u32) = buff_id.parse::<u32>() {
            self.buffs.iter().any(|buff| buff.id == id_u32)
        } else {
            false
        }
    }
    
    /// 清除所有buff
    pub fn clear_all_buffs(&mut self) {
        self.buffs.clear();
    }
    
    /// 清除负面buff
    pub fn clear_debuffs(&mut self) {
        self.buffs.retain(|buff| !buff.is_debuff());
    }
    
    /// 清除正面buff
    pub fn clear_positive_buffs(&mut self) {
        self.buffs.retain(|buff| buff.is_debuff());
    }
    
    /// 获取buff数量
    pub fn buff_count(&self) -> usize {
        self.buffs.len()
    }
    
    /// 计算总的属性修正
    pub fn calculate_attribute_modifiers(&self) -> AttributeModifiers {
        let mut modifiers = AttributeModifiers::default();
        
        for buff in &self.buffs {
            if let Some(attr_set) = &buff.attributes {
                let buff_modifiers = self.convert_attribute_set_to_modifiers(attr_set);
                modifiers.merge(&buff_modifiers);
            }
        }
        
        modifiers
    }

    /// 将AttributeSet转换为AttributeModifiers的私有辅助函数
    fn convert_attribute_set_to_modifiers(&self, set: &AttributeSet) -> AttributeModifiers {
        let mut modifiers = AttributeModifiers::default();
        for (attr_type, attribute) in &set.attributes {
            let value = attribute.value;
            match attr_type {
                AttributeType::Base(core_attr) => match core_attr {
                    CoreAttribute::Metal => modifiers.attack_modifier += value as i32,
                    CoreAttribute::Earth => modifiers.defense_modifier += value as i32,
                    CoreAttribute::Wood => modifiers.health_modifier += value as i32,
                    CoreAttribute::Water => modifiers.mana_modifier += value as i32,
                    CoreAttribute::Fire => { /* Fire might affect speed or be a DoT, handle elsewhere */ },
                },
                AttributeType::Composite(derived_attr) => {
                    // Handle derived attributes if needed
                    // For example, Thunder could increase attack and speed
                }
            }
        }
        modifiers
    }
    
    /// 查找同类型buff的索引
    fn find_buff_by_type(&self, buff: &Buff) -> Option<usize> {
        self.buffs.iter().position(|b| b.buff_type == buff.buff_type)
    }

    //
    // 兼容旧trait的临时访问器
    //
    pub fn buffs_mut(&mut self) -> &mut Vec<Buff> {
        &mut self.buffs
    }

    pub fn buffs(&self) -> &Vec<Buff> {
        &self.buffs
    }
}

impl Default for BuffManager {
    fn default() -> Self {
        Self::new(20) // 默认最多20个buff
    }
}

// ============================================================================
// 属性修正器
// ============================================================================

/// 属性修正器 - 值对象，表示buff对属性的修正
#[derive(Debug, Clone, PartialEq, Default, Serialize, Deserialize)]
pub struct AttributeModifiers {
    /// 攻击力修正
    pub attack_modifier: i32,
    /// 防御力修正
    pub defense_modifier: i32,
    /// 速度修正
    pub speed_modifier: f32,
    /// 生命值修正
    pub health_modifier: i32,
    /// 法力值修正
    pub mana_modifier: i32,
    /// 属性百分比修正
    pub percentage_modifiers: HashMap<String, f32>,
}

impl AttributeModifiers {
    /// 创建新的属性修正器
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 合并另一个修正器
    pub fn merge(&mut self, other: &AttributeModifiers) {
        self.attack_modifier += other.attack_modifier;
        self.defense_modifier += other.defense_modifier;
        self.speed_modifier += other.speed_modifier;
        self.health_modifier += other.health_modifier;
        self.mana_modifier += other.mana_modifier;
        
        for (key, value) in &other.percentage_modifiers {
            *self.percentage_modifiers.entry(key.clone()).or_insert(0.0) += value;
        }
    }
    
    /// 应用修正到基础值
    pub fn apply_to_attack(&self, base_attack: Attack) -> Attack {
        let modified = base_attack + self.attack_modifier;
        let percentage = self.percentage_modifiers.get("attack").unwrap_or(&0.0);
        (modified as f32 * (1.0 + percentage)) as Attack
    }
    
    /// 应用修正到防御力
    pub fn apply_to_defense(&self, base_defense: Defense) -> Defense {
        let modified = base_defense + self.defense_modifier;
        let percentage = self.percentage_modifiers.get("defense").unwrap_or(&0.0);
        (modified as f32 * (1.0 + percentage)) as Defense
    }
    
    /// 应用修正到速度
    pub fn apply_to_speed(&self, base_speed: Speed) -> Speed {
        let modified = base_speed + self.speed_modifier;
        let percentage = self.percentage_modifiers.get("speed").unwrap_or(&0.0);
        modified * (1.0 + percentage)
    }
}

// ============================================================================
// 状态管理器实体
// ============================================================================

/// 状态管理器 - 实体，管理角色的各种状态
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StatusManager {
    /// 当前状态列表
    statuses: Vec<String>,
    /// 状态免疫列表
    immunities: Vec<String>,
}

impl StatusManager {
    /// 创建新的状态管理器
    pub fn new() -> Self {
        Self {
            statuses: Vec::new(),
            immunities: Vec::new(),
        }
    }
    
    /// 添加状态
    pub fn add_status(&mut self, status: String) -> GameResult<()> {
        // 检查是否免疫
        if self.immunities.contains(&status) {
            return Err(GameError::validation_error("status", "对此状态免疫"));
        }
        
        // 检查是否已存在
        if !self.statuses.contains(&status) {
            self.statuses.push(status);
        }
        
        Ok(())
    }
    
    /// 移除状态
    pub fn remove_status(&mut self, status: &str) -> bool {
        if let Some(index) = self.statuses.iter().position(|s| s == status) {
            self.statuses.remove(index);
            true
        } else {
            false
        }
    }
    
    /// 检查是否有特定状态
    pub fn has_status(&self, status: &str) -> bool {
        self.statuses.contains(&status.to_string())
    }
    
    /// 获取所有状态
    pub fn get_statuses(&self) -> &[String] {
        &self.statuses
    }
    
    /// 添加免疫
    pub fn add_immunity(&mut self, immunity: String) {
        if !self.immunities.contains(&immunity) {
            self.immunities.push(immunity);
        }
    }
    
    /// 移除免疫
    pub fn remove_immunity(&mut self, immunity: &str) -> bool {
        if let Some(index) = self.immunities.iter().position(|i| i == immunity) {
            self.immunities.remove(index);
            true
        } else {
            false
        }
    }
    
    /// 检查行动能力
    pub fn can_move(&self) -> bool {
        !self.has_status("stunned") && !self.has_status("rooted")
    }
    
    /// 检查攻击能力
    pub fn can_attack(&self) -> bool {
        !self.has_status("disarmed") && !self.has_status("stunned")
    }
    
    /// 检查施法能力
    pub fn can_cast(&self) -> bool {
        !self.has_status("silenced") && !self.has_status("stunned")
    }
    
    /// 清除所有状态
    pub fn clear_all_statuses(&mut self) {
        self.statuses.clear();
    }
    
    /// 清除负面状态
    pub fn clear_negative_statuses(&mut self) {
        let negative_statuses = [
            "stunned", "silenced", "disarmed", "rooted", "poisoned", "burned", "frozen"
        ];
        
        self.statuses.retain(|status| !negative_statuses.contains(&status.as_str()));
    }
}

impl Default for StatusManager {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 装备栏引用（简化版）
// ============================================================================

/// 装备栏引用 - 简化的装备管理器
/// 注意：这里使用简化版，完整的装备系统应该在equipment模块中
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct EquipmentReference {
    /// 装备的ID列表
    equipped_items: HashMap<String, EquipmentId>,
}

impl EquipmentReference {
    /// 创建新的装备栏引用
    pub fn new() -> Self {
        Self {
            equipped_items: HashMap::new(),
        }
    }
    
    /// 装备物品
    pub fn equip(&mut self, slot: String, equipment_id: EquipmentId) -> Option<EquipmentId> {
        self.equipped_items.insert(slot, equipment_id)
    }
    
    /// 卸下装备
    pub fn unequip(&mut self, slot: &str) -> Option<EquipmentId> {
        self.equipped_items.remove(slot)
    }
    
    /// 获取指定槽位的装备
    pub fn get_equipped(&self, slot: &str) -> Option<&EquipmentId> {
        self.equipped_items.get(slot)
    }
    
    /// 获取所有装备
    pub fn get_all_equipped(&self) -> &HashMap<String, EquipmentId> {
        &self.equipped_items
    }
    
    /// 检查槽位是否为空
    pub fn is_slot_empty(&self, slot: &str) -> bool {
        !self.equipped_items.contains_key(slot)
    }
}

impl Default for EquipmentReference {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 技能栏引用（简化版）
// ============================================================================

/// 技能栏引用 - 简化的技能管理器
/// 注意：这里使用简化版，完整的技能系统应该在skill模块中
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SkillReference {
    /// 已学会的技能ID列表
    learned_skills: Vec<SkillId>,
    /// 快捷栏技能槽位
    hotbar_slots: HashMap<u8, SkillId>,
}

impl SkillReference {
    /// 创建新的技能栏引用
    pub fn new() -> Self {
        Self {
            learned_skills: Vec::new(),
            hotbar_slots: HashMap::new(),
        }
    }
    
    /// 学习技能
    pub fn learn_skill(&mut self, skill_id: SkillId) -> GameResult<()> {
        if self.learned_skills.contains(&skill_id) {
            return Err(GameError::validation_error("skill_id", "技能已学会"));
        }
        
        self.learned_skills.push(skill_id);
        Ok(())
    }
    
    /// 忘记技能
    pub fn forget_skill(&mut self, skill_id: &SkillId) -> bool {
        if let Some(index) = self.learned_skills.iter().position(|id| id == skill_id) {
            self.learned_skills.remove(index);
            
            // 从快捷栏中移除
            self.hotbar_slots.retain(|_, id| id != skill_id);
            true
        } else {
            false
        }
    }
    
    /// 设置快捷栏
    pub fn set_hotbar_skill(&mut self, slot: u8, skill_id: SkillId) -> GameResult<()> {
        if !self.learned_skills.contains(&skill_id) {
            return Err(GameError::validation_error("skill_id", "技能未学会"));
        }
        
        self.hotbar_slots.insert(slot, skill_id);
        Ok(())
    }
    
    /// 获取快捷栏技能
    pub fn get_hotbar_skill(&self, slot: u8) -> Option<&SkillId> {
        self.hotbar_slots.get(&slot)
    }
    
    /// 检查是否学会技能
    pub fn has_learned(&self, skill_id: &SkillId) -> bool {
        self.learned_skills.contains(skill_id)
    }
    
    /// 获取所有已学技能
    pub fn get_learned_skills(&self) -> &[SkillId] {
        &self.learned_skills
    }
    
    /// 获取快捷栏
    pub fn get_hotbar(&self) -> &HashMap<u8, SkillId> {
        &self.hotbar_slots
    }
}

impl Default for SkillReference {
    fn default() -> Self {
        Self::new()
    }
}