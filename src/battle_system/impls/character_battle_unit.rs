use crate::character::domain::Character;
use crate::battle_system::battle_unit::*;
use crate::battle_system::battle_errors::BattleResult;
use std::collections::HashMap;
use crate::shared::types::*;

impl BasicAttributes for Character {
    fn get_id(&self) -> ID { self.id().0 }
    fn get_name(&self) -> &str { self.name() }
    fn get_level(&self) -> Level { self.level() }
}

impl Positionable for Character {
    fn get_position(&self) -> (f32, f32) {
        let pos = self.position();
        (pos.x, pos.y)
    }
    fn set_position(&mut self, pos: (f32, f32)) -> BattleResult<()> {
        self.move_to(Position::new(pos.0, pos.1)).map_err(|e| e.into())
    }
}

impl Vitality for Character {
    fn get_hp(&self) -> Health { self.current_health() }
    fn set_hp(&mut self, value: Health) -> BattleResult<()> {
        let current_hp = self.current_health();
        let diff = value - current_hp;
        if diff > 0 {
            self.heal(diff).map(|_| ())
        } else {
            self.take_damage(diff.abs()).map(|_| ())
        }.map_err(|e| e.into())
    }
    fn get_mana(&self) -> Mana { self.current_mana() }
    fn set_mana(&mut self, value: Mana) -> BattleResult<()> {
        let current_mana = self.current_mana();
        let diff = value - current_mana;
        if diff > 0 {
            self.restore_mana(diff)
        } else {
            self.consume_mana(diff.abs())
        }.map_err(|e| e.into())
    }
    fn get_max_hp(&self) -> Health { self.max_health() }
    fn get_max_mana(&self) -> Mana { self.max_mana() }
}

impl Experience for Character {
    fn get_exp(&self) -> Exp { self.experience() }
    fn add_exp(&mut self, amount: Exp) -> BattleResult<()> {
        self.gain_experience(amount).map(|_| ()).map_err(|e| e.into())
    }
}

impl Skillable for Character {
    fn get_skill_cooldowns_mut(&mut self) -> BattleResult<&mut HashMap<SkillId, CooldownTime>> {
        Ok(self.skill_cooldowns_mut().cooldowns_mut())
    }

    fn get_skill_cooldowns(&self) -> &HashMap<SkillId, CooldownTime> {
        self.skill_cooldowns().cooldowns()
    }

    fn get_buffs_mut(&mut self) -> BattleResult<&mut Vec<crate::skill::buff::Buff>> {
        Ok(self.buff_manager_mut().buffs_mut())
    }

    fn get_buffs(&self) -> &Vec<crate::skill::buff::Buff> {
        self.buff_manager().buffs()
    }

    fn get_skills(&self) -> Vec<ID> {
        self.skill_ref().get_learned_skills().iter().map(|id| id.0).collect()
    }
}

impl CombatAttributes for Character {
    fn get_attack(&self) -> Attack { self.calculate_attack() }
    fn get_defense(&self) -> Defense { self.calculate_defense() }
    fn get_move_speed(&self) -> Speed { self.calculate_speed() }
    fn get_attack_range(&self) -> Range { 1.0 } // 默认近战范围
    fn get_speed(&self) -> Speed { self.calculate_speed() }
}

impl StatusEffect for Character {
    fn get_status(&self) -> Vec<String> {
        self.status_manager().get_statuses().to_vec()
    }

    fn add_status(&mut self, status: String) -> BattleResult<()> {
        self.add_status(status).map_err(|e| e.into())
    }

    fn remove_status(&mut self, status: &str) -> BattleResult<()> {
        self.remove_status(status).map(|_|()).map_err(|e| e.into())
    }
}

impl BattleState for Character {
    fn is_alive(&self) -> bool {
        self.is_alive()
    }

    fn can_move(&self) -> bool {
        self.can_move()
    }

    fn can_cast(&self) -> bool {
        self.can_cast()
    }

    fn can_attack(&self) -> bool {
        self.can_attack()
    }

    fn take_damage(&mut self, damage: i32) -> BattleResult<()> {
        self.take_damage(damage).map(|_| ()).map_err(|e| e.into())
    }

    fn heal(&mut self, amount: i32) -> BattleResult<()> {
        self.heal(amount).map(|_| ()).map_err(|e| e.into())
    }
}

impl BattleUnit for Character {}
