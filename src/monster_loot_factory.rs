//! 怪物掉落池生成工厂，支持命名系统注入
use crate::monster_profile::MonsterProfile;
use crate::loot::*;
use crate::equipment::{Equipment, EquipmentType, EquipmentQuality};
use crate::attribute::attribute::AttributeSet;
use crate::naming::NamingService;
use std::collections::HashMap;
use crate::MaterialGrade;

pub fn generate_monster_loot_pool(profile: &MonsterProfile, naming: &dyn NamingService) -> HashMap<LootPoolKind, LootPool> {
    let mut pools = HashMap::new();

    // 1. 必掉池：主属性相关材料、基础货币
    let guaranteed_entries = vec![
        LootEntry {
            item: LootItem::Gold(100 + profile.level * 10),
            rules: vec![LootRule::Always],
            min_count: 1,
            max_count: 1,
        },
        LootEntry {
            item: LootItem::Material(Material {
                id: 1000 + profile.level,
                name: naming.material_name(&profile.name, &profile.main_attribute, &profile.grade),
                grade: profile.grade.clone(),
                attribute: profile.main_attribute,
                description: naming.material_desc(&profile.name, &profile.main_attribute, &profile.grade),
            }),
            rules: vec![LootRule::Always],
            min_count: 1,
            max_count: 2,
        },
    ];
    pools.insert(
        LootPoolKind::Guaranteed,
        LootPool {
            kind: LootPoolKind::Guaranteed,
            entries: guaranteed_entries,
        },
    );

    // 2. 普通池：主属性相关材料、低级装备
    let normal_entries = vec![
        LootEntry {
            item: LootItem::Material(Material {
                id: 2000 + profile.level,
                name: naming.material_name(&profile.name, &profile.main_attribute, "普通"),
                grade: "普通".to_string(),
                attribute: profile.main_attribute,
                description: naming.material_desc(&profile.name, &profile.main_attribute, "普通"),
            }),
            rules: vec![LootRule::Probability(0.5)],
            min_count: 1,
            max_count: 3,
        },
        LootEntry {
            item: LootItem::Equipment(Equipment::new(
                3000 + profile.level,
                naming.equipment_name(&profile.name, "之刃"),
                EquipmentType::Weapon,
                EquipmentQuality::Common,
                profile.level,
                AttributeSet::default(),
                MaterialGrade::Mortal,
                format!("由{}的力量锻造的武器", profile.name),
            )),
            rules: vec![LootRule::Probability(0.2)],
            min_count: 1,
            max_count: 1,
        },
    ];
    pools.insert(
        LootPoolKind::Normal,
        LootPool {
            kind: LootPoolKind::Normal,
            entries: normal_entries,
        },
    );

    // 3. 稀有池：主属性稀有材料、特殊掉落
    let rare_entries = vec![
        LootEntry {
            item: LootItem::Material(Material {
                id: 4000 + profile.level,
                name: naming.material_name(&profile.name, &profile.main_attribute, "稀有"),
                grade: "稀有".to_string(),
                attribute: profile.main_attribute,
                description: naming.material_desc(&profile.name, &profile.main_attribute, "稀有"),
            }),
            rules: vec![LootRule::Probability(0.05)],
            min_count: 1,
            max_count: 1,
        },
        LootEntry {
            item: LootItem::Special(SpecialLoot::Title(naming.material_name(&profile.name, &profile.main_attribute, "猎手"))),
            rules: vec![LootRule::Probability(0.01), LootRule::FirstKillOnly],
            min_count: 1,
            max_count: 1,
        },
    ];
    pools.insert(
        LootPoolKind::Rare,
        LootPool {
            kind: LootPoolKind::Rare,
            entries: rare_entries,
        },
    );

    // 4. 世界池：极低概率全服掉落
    let world_entries = vec![
        LootEntry {
            item: LootItem::Special(SpecialLoot::WorldEventItem(naming.material_name(&profile.name, &profile.main_attribute, "神石"))),
            rules: vec![LootRule::Probability(0.001)],
            min_count: 1,
            max_count: 1,
        },
    ];
    pools.insert(
        LootPoolKind::World,
        LootPool {
            kind: LootPoolKind::World,
            entries: world_entries,
        },
    );

    pools
}
