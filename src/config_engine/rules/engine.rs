//! # 规则引擎核心
//!
//! 基于 zen-engine 的真实业务规则引擎实现

use serde_json::Value as JsonValue;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use zen_engine::{loader::NoopLoader, model::DecisionContent, DecisionEngine};

use crate::config_engine::{
    cache::{CacheManager, CacheResult},
    config::{ConfigManager, RuleDefinition},
    error::{ConfigEngineError, RuleExecutionError},
    rules::{ExecutionContext, ExecutionResult},
    Result,
};

/// 规则执行器接口
///
/// 定义规则执行的核心接口，支持同步和异步执行
#[async_trait::async_trait]
pub trait RuleExecutor {
    /// 执行单个规则
    async fn execute_rule(
        &self,
        rule_id: &str,
        context: &ExecutionContext,
    ) -> Result<ExecutionResult>;

    /// 批量执行规则
    async fn execute_rules(
        &self,
        rule_ids: &[String],
        context: &ExecutionContext,
    ) -> Result<Vec<(String, Result<ExecutionResult>)>>;

    /// 预热规则
    async fn warmup_rule(&self, rule_id: &str) -> Result<()>;

    /// 批量预热规则
    async fn warmup_rules(&self, rule_ids: &[String]) -> Result<()>;

    /// 获取执行统计信息
    async fn get_execution_stats(&self) -> RuleEngineStats;

    /// 重置统计信息
    async fn reset_stats(&self);

    /// 健康检查
    async fn health_check(&self) -> RuleEngineHealth;

    /// 验证规则定义
    async fn validate_rule(&self, rule_id: &str) -> Result<bool>;

    /// 获取已编译规则列表
    async fn list_compiled_rules(&self) -> Vec<String>;

    /// 清除编译缓存
    async fn clear_compiled_cache(&self) -> Result<()>;

    /// 重新编译规则
    async fn recompile_rule(&self, rule_id: &str) -> Result<()>;

    /// 设置执行模式
    async fn set_execution_mode(&mut self, mode: ExecutionMode) -> Result<()>;

    /// 获取当前执行模式
    fn get_execution_mode(&self) -> ExecutionMode;
}

/// 执行模式
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ExecutionMode {
    /// 同步执行模式
    Synchronous,
    /// 异步执行模式
    Asynchronous,
    /// 并行执行模式
    Parallel,
    /// 流水线执行模式
    Pipeline,
    /// 容错执行模式
    FaultTolerant,
}

impl Default for ExecutionMode {
    fn default() -> Self {
        ExecutionMode::Asynchronous
    }
}

impl std::fmt::Display for ExecutionMode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ExecutionMode::Synchronous => write!(f, "Synchronous"),
            ExecutionMode::Asynchronous => write!(f, "Asynchronous"),
            ExecutionMode::Parallel => write!(f, "Parallel"),
            ExecutionMode::Pipeline => write!(f, "Pipeline"),
            ExecutionMode::FaultTolerant => write!(f, "FaultTolerant"),
        }
    }
}

/// 规则引擎核心
///
/// 基于 zen-engine 的真实实现
pub struct RuleEngine {
    /// zen-engine 决策引擎
    decision_engine: DecisionEngine<NoopLoader>,
    /// 配置管理器
    config_manager: Arc<ConfigManager>,
    /// 缓存管理器
    cache_manager: Arc<CacheManager>,
    /// 引擎配置
    config: RuleEngineConfig,
    /// 执行统计
    stats: Arc<RwLock<RuleEngineStats>>,
    /// 编译后的决策缓存
    compiled_decisions: Arc<RwLock<HashMap<String, zen_engine::Decision<NoopLoader>>>>,
    /// 当前执行模式
    execution_mode: Arc<RwLock<ExecutionMode>>,
}

impl RuleEngine {
    /// 创建新的规则引擎
    pub async fn new(
        config_manager: Arc<ConfigManager>,
        cache_manager: Arc<CacheManager>,
        config: RuleEngineConfig,
    ) -> Result<Self> {
        let decision_engine = DecisionEngine::default();

        let engine = Self {
            decision_engine,
            config_manager,
            cache_manager,
            config,
            stats: Arc::new(RwLock::new(RuleEngineStats::new())),
            compiled_decisions: Arc::new(RwLock::new(HashMap::new())),
            execution_mode: Arc::new(RwLock::new(ExecutionMode::default())),
        };

        // 执行规则预热
        engine.execute_initial_warmup().await?;

        Ok(engine)
    }

    /// 执行初始预热
    ///
    /// 根据配置中的预热规则列表进行规则预编译
    async fn execute_initial_warmup(&self) -> Result<()> {
        // 检查预热是否启用
        if !self.config.warmup_config.enabled {
            log::info!("规则预热已禁用，跳过预热阶段");
            return Ok(());
        }

        if self.config.warmup_rules.is_empty() {
            log::info!("没有配置预热规则，跳过预热阶段");
            return Ok(());
        }

        log::info!(
            "开始执行规则预热，规则数量: {}",
            self.config.warmup_rules.len()
        );
        let start_time = std::time::Instant::now();

        // 预热策略：支持配置规则列表或自动发现规则
        let warmup_rules = if self.config.warmup_rules.contains(&"*".to_string()) {
            // 如果包含通配符，则预热所有可用规则
            self.discover_all_rules().await?
        } else {
            // 使用配置指定的规则列表
            self.config.warmup_rules.clone()
        };

        if warmup_rules.is_empty() {
            log::info!("没有发现需要预热的规则");
            return Ok(());
        }

        // 使用配置的并发数
        let max_concurrent = self.config.warmup_config.max_concurrent_tasks;
        let mut warmup_results = WarmupResults::new();
        let semaphore = Arc::new(tokio::sync::Semaphore::new(max_concurrent));

        log::info!(
            "将预热 {} 个规则，最大并发数: {}",
            warmup_rules.len(),
            max_concurrent
        );

        let tasks: Vec<_> = warmup_rules
            .into_iter()
            .map(|rule_id| {
                let engine = self.clone();
                let sem = Arc::clone(&semaphore);
                let timeout = self.config.warmup_config.timeout_ms;

                tokio::spawn(async move {
                    let _permit = sem.acquire().await.unwrap();

                    // 对单个规则预热应用超时
                    match tokio::time::timeout(
                        std::time::Duration::from_millis(timeout),
                        engine.warmup_single_rule_with_metrics(&rule_id),
                    )
                    .await
                    {
                        Ok(result) => result,
                        Err(_) => RuleWarmupResult {
                            rule_id: rule_id.clone(),
                            success: false,
                            duration: std::time::Duration::from_millis(timeout),
                            error: Some(format!("预热超时 ({}ms)", timeout)),
                        },
                    }
                })
            })
            .collect();

        // 等待所有预热任务完成并收集结果
        for task in tasks {
            match task.await {
                Ok(result) => warmup_results.add_result(result),
                Err(e) => {
                    log::error!("预热任务执行失败: {}", e);
                    warmup_results.task_errors += 1;
                }
            }
        }

        let total_time = start_time.elapsed();
        warmup_results.log_summary(total_time);

        // 检查预热结果是否符合容忍率
        let failure_rate = 1.0 - warmup_results.success_rate();
        let tolerance = self.config.warmup_config.failure_tolerance;

        if failure_rate > tolerance || warmup_results.task_errors > 0 {
            let error_msg = format!(
                "预热失败率 {:.1}% 超过容忍率 {:.1}% (失败: {}/{}, 任务错误: {})",
                failure_rate * 100.0,
                tolerance * 100.0,
                warmup_results.failed_count,
                warmup_results.total_count(),
                warmup_results.task_errors
            );

            if self.config.warmup_config.continue_on_failure {
                log::warn!("{}，但配置为继续启动", error_msg);
            } else {
                log::error!("{}，启动失败", error_msg);
                return Err(ConfigEngineError::RuleExecution(
                    RuleExecutionError::ExecutionFailure {
                        rule_id: "warmup".to_string(),
                        reason: error_msg,
                    },
                ));
            }
        }

        log::info!(
            "规则预热完成，成功率: {:.1}%",
            warmup_results.success_rate() * 100.0
        );
        Ok(())
    }

    /// 发现所有可用规则
    async fn discover_all_rules(&self) -> Result<Vec<String>> {
        log::info!("自动发现所有可用规则...");
        let rule_ids = self.config_manager.get_all_rule_ids().await;
        log::info!("发现 {} 个规则", rule_ids.len());
        Ok(rule_ids)
    }

    /// 预热单个规则并收集指标
    async fn warmup_single_rule_with_metrics(&self, rule_id: &str) -> RuleWarmupResult {
        let start_time = std::time::Instant::now();

        match self.warmup_rule(rule_id).await {
            Ok(()) => {
                let duration = start_time.elapsed();
                log::debug!("规则 {} 预热成功，耗时: {:?}", rule_id, duration);
                RuleWarmupResult {
                    rule_id: rule_id.to_string(),
                    success: true,
                    duration,
                    error: None,
                }
            }
            Err(e) => {
                let duration = start_time.elapsed();
                log::warn!(
                    "规则 {} 预热失败，耗时: {:?}，错误: {}",
                    rule_id,
                    duration,
                    e
                );
                RuleWarmupResult {
                    rule_id: rule_id.to_string(),
                    success: false,
                    duration,
                    error: Some(e.to_string()),
                }
            }
        }
    }

    /// 执行规则
    pub async fn execute_rule(
        &self,
        rule_id: &str,
        context: &ExecutionContext,
    ) -> Result<ExecutionResult> {
        let start_time = std::time::Instant::now();

        // 更新统计
        {
            let mut stats = self.stats.write().await;
            stats.total_requests += 1;
        }

        // 生成缓存键
        let cache_key = format!("rule:{}:{:x}", rule_id, Self::hash_context(context));

        // 检查缓存
        if self.config.enable_cache {
            if let Ok(CacheResult::Hit(cached_result)) =
                self.cache_manager.get_rule_result(&cache_key).await
            {
                let execution_time = start_time.elapsed().as_millis() as u64;

                // 更新缓存命中统计
                {
                    let mut stats = self.stats.write().await;
                    stats.cache_hits += 1;
                }

                return Ok(ExecutionResult::success(cached_result, execution_time));
            }
        }

        // 获取或编译决策
        let decision = self.get_or_compile_decision(rule_id).await?;

        // 准备输入数据
        let input_data = self.prepare_input_data(context);

        // 执行规则
        let result = match tokio::time::timeout(
            std::time::Duration::from_millis(self.config.default_timeout_ms),
            decision.evaluate(&input_data),
        )
        .await
        {
            Ok(Ok(zen_result)) => {
                let execution_time = start_time.elapsed().as_millis() as u64;

                // 转换 zen-engine 结果为我们的格式
                let output = self.convert_zen_result_to_context_values(&zen_result)?;

                // 缓存结果
                if self.config.enable_cache {
                    if let Err(e) = self
                        .cache_manager
                        .cache_rule_result(&cache_key, &output)
                        .await
                    {
                        log::warn!("缓存执行结果失败: {}", e);
                    }
                }

                // 更新成功统计
                {
                    let mut stats = self.stats.write().await;
                    stats.successful_executions += 1;
                    stats.total_execution_time_ms += execution_time;
                }

                Ok(ExecutionResult::success(output, execution_time))
            }
            Ok(Err(zen_error)) => {
                // zen-engine 执行错误
                {
                    let mut stats = self.stats.write().await;
                    stats.failed_executions += 1;
                }

                Err(ConfigEngineError::RuleExecution(
                    RuleExecutionError::ExecutionFailure {
                        rule_id: rule_id.to_string(),
                        reason: format!("zen-engine 执行错误: {}", zen_error),
                    },
                ))
            }
            Err(_) => {
                // 超时
                {
                    let mut stats = self.stats.write().await;
                    stats.timeout_executions += 1;
                }

                Err(ConfigEngineError::RuleExecution(
                    RuleExecutionError::Timeout {
                        rule_id: rule_id.to_string(),
                        timeout_ms: self.config.default_timeout_ms,
                    },
                ))
            }
        };

        result
    }

    /// 获取或编译决策
    async fn get_or_compile_decision(
        &self,
        rule_id: &str,
    ) -> Result<zen_engine::Decision<NoopLoader>> {
        // 首先检查已编译的决策缓存
        {
            let compiled_decisions = self.compiled_decisions.read().await;
            if let Some(decision) = compiled_decisions.get(rule_id) {
                return Ok(decision.clone());
            }
        }

        // 获取规则定义
        let rule_def = self
            .config_manager
            .get_rule_definition(rule_id)
            .await
            .ok_or_else(|| {
                ConfigEngineError::RuleExecution(RuleExecutionError::RuleNotFound {
                    rule_id: rule_id.to_string(),
                    rule_name: rule_id.to_string(), // 没有规则定义时使用 rule_id
                })
            })?;

        // 检查规则是否启用
        if !rule_def.enabled {
            return Err(ConfigEngineError::RuleExecution(
                RuleExecutionError::ExecutionFailure {
                    rule_id: rule_id.to_string(),
                    reason: "规则已禁用".to_string(),
                },
            ));
        }

        // 解析 JDM 内容
        let decision_content: DecisionContent = serde_json::from_str(&rule_def.jdm_content)
            .map_err(|e| {
                ConfigEngineError::RuleExecution(RuleExecutionError::InvalidJdm {
                    reason: format!("JDM 解析失败: {}", e),
                })
            })?;

        // 使用 zen-engine 创建决策
        let decision = self
            .decision_engine
            .create_decision(decision_content.into());

        // 缓存编译后的决策
        {
            let mut compiled_decisions = self.compiled_decisions.write().await;
            compiled_decisions.insert(rule_id.to_string(), decision.clone());
        }

        log::info!("规则 {} 编译完成并缓存", rule_id);
        Ok(decision)
    }

    /// 准备输入数据
    fn prepare_input_data(&self, context: &ExecutionContext) -> JsonValue {
        let values = context.get_all_values();
        let mut input_map = serde_json::Map::new();

        for (key, value) in values {
            input_map.insert(key.clone(), self.context_value_to_json(&value));
        }

        JsonValue::Object(input_map)
    }

    /// 将 ContextValue 转换为 JSON Value
    fn context_value_to_json(
        &self,
        value: &crate::config_engine::rules::ContextValue,
    ) -> JsonValue {
        use crate::config_engine::rules::ContextValue;

        match value {
            ContextValue::String(s) => JsonValue::String(s.clone()),
            ContextValue::Int(i) => JsonValue::Number(serde_json::Number::from(*i)),
            ContextValue::Float(f) => serde_json::Number::from_f64(*f)
                .map(JsonValue::Number)
                .unwrap_or(JsonValue::Null),
            ContextValue::Bool(b) => JsonValue::Bool(*b),
            ContextValue::Array(arr) => {
                JsonValue::Array(arr.iter().map(|v| self.context_value_to_json(v)).collect())
            }
            ContextValue::Object(obj) => {
                let mut map = serde_json::Map::new();
                for (k, v) in obj {
                    map.insert(k.clone(), self.context_value_to_json(v));
                }
                JsonValue::Object(map)
            }
            ContextValue::Null => JsonValue::Null,
        }
    }

    /// 将 zen-engine 结果转换为 ContextValue
    fn convert_zen_result_to_context_values(
        &self,
        zen_result: &JsonValue,
    ) -> Result<HashMap<String, crate::config_engine::rules::ContextValue>> {
        let mut result = HashMap::new();

        match zen_result {
            JsonValue::Object(obj) => {
                for (key, value) in obj {
                    result.insert(key.clone(), self.json_to_context_value(value)?);
                }
            }
            _ => {
                // 如果结果不是对象，将其包装在 "result" 键中
                result.insert(
                    "result".to_string(),
                    self.json_to_context_value(zen_result)?,
                );
            }
        }

        Ok(result)
    }

    /// 将 JSON Value 转换为 ContextValue
    fn json_to_context_value(
        &self,
        value: &JsonValue,
    ) -> Result<crate::config_engine::rules::ContextValue> {
        use crate::config_engine::rules::ContextValue;

        let context_value = match value {
            JsonValue::Null => ContextValue::Null,
            JsonValue::Bool(b) => ContextValue::Bool(*b),
            JsonValue::Number(n) => {
                if let Some(i) = n.as_i64() {
                    ContextValue::Int(i)
                } else if let Some(f) = n.as_f64() {
                    ContextValue::Float(f)
                } else {
                    return Err(ConfigEngineError::RuleExecution(
                        RuleExecutionError::ExecutionFailure {
                            rule_id: "unknown".to_string(),
                            reason: format!("无法转换数字类型: {}", n),
                        },
                    ));
                }
            }
            JsonValue::String(s) => ContextValue::String(s.clone()),
            JsonValue::Array(arr) => {
                let mut context_arr = Vec::new();
                for item in arr {
                    context_arr.push(self.json_to_context_value(item)?);
                }
                ContextValue::Array(context_arr)
            }
            JsonValue::Object(obj) => {
                let mut context_obj = HashMap::new();
                for (key, val) in obj {
                    context_obj.insert(key.clone(), self.json_to_context_value(val)?);
                }
                ContextValue::Object(context_obj)
            }
        };

        Ok(context_value)
    }

    /// 预热规则
    ///
    /// 预编译指定规则，提升首次执行性能
    pub async fn warmup_rule(&self, rule_id: &str) -> Result<()> {
        let _ = self.get_or_compile_decision(rule_id).await?;
        log::info!("规则 {} 预热完成", rule_id);
        Ok(())
    }

    /// 批量预热规则
    pub async fn warmup_rules(&self, rule_ids: &[String]) -> Result<()> {
        let mut tasks = Vec::new();

        for rule_id in rule_ids {
            let rule_id = rule_id.clone();
            let engine = self.clone();

            let task = tokio::spawn(async move {
                if let Err(e) = engine.warmup_rule(&rule_id).await {
                    log::warn!("规则 {} 预热失败: {}", rule_id, e);
                }
            });

            tasks.push(task);
        }

        // 等待所有预热任务完成
        for task in tasks {
            let _ = task.await;
        }

        log::info!("批量预热完成，规则数量: {}", rule_ids.len());
        Ok(())
    }

    /// 清除编译缓存
    pub async fn clear_compiled_cache(&self) -> Result<()> {
        let mut compiled_decisions = self.compiled_decisions.write().await;
        let count = compiled_decisions.len();
        compiled_decisions.clear();

        log::info!("已清除 {} 个编译后的规则缓存", count);
        Ok(())
    }

    /// 获取已编译规则列表
    pub async fn list_compiled_rules(&self) -> Vec<String> {
        let compiled_decisions = self.compiled_decisions.read().await;
        compiled_decisions.keys().cloned().collect()
    }

    /// 重新编译规则
    pub async fn recompile_rule(&self, rule_id: &str) -> Result<()> {
        // 从缓存中移除
        {
            let mut compiled_decisions = self.compiled_decisions.write().await;
            compiled_decisions.remove(rule_id);
        }

        // 重新编译
        let _ = self.get_or_compile_decision(rule_id).await?;
        log::info!("规则 {} 重新编译完成", rule_id);
        Ok(())
    }

    /// 获取引擎统计信息
    pub async fn get_stats(&self) -> RuleEngineStats {
        self.stats.read().await.clone()
    }

    /// 重置统计信息
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.write().await;
        *stats = RuleEngineStats::new();
    }

    /// 健康检查
    pub async fn health_check(&self) -> RuleEngineHealth {
        let stats = self.get_stats().await;
        let compiled_rules = self.list_compiled_rules().await;

        // 检查配置管理器状态
        let config_healthy = self.config_manager.get_status().await.is_loaded;

        // 检查缓存管理器状态
        let cache_stats = self.cache_manager.get_stats().await;
        let cache_healthy = cache_stats.error_count == 0;

        RuleEngineHealth {
            healthy: config_healthy && cache_healthy,
            config_manager_healthy: config_healthy,
            cache_manager_healthy: cache_healthy,
            compiled_rules_count: compiled_rules.len(),
            total_requests: stats.total_requests,
            success_rate: stats.success_rate(),
            average_execution_time_ms: stats.average_execution_time_ms(),
        }
    }

    /// 生成上下文的哈希值
    fn hash_context(context: &ExecutionContext) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();

        // 按键排序以确保一致的哈希
        let values = context.get_all_values();
        let mut sorted_keys: Vec<_> = values.keys().collect();
        sorted_keys.sort();

        for key in sorted_keys {
            key.hash(&mut hasher);
            if let Some(value) = values.get(key) {
                format!("{:?}", value).hash(&mut hasher);
            }
        }

        hasher.finish()
    }
}

// 实现Clone以支持async spawn
impl Clone for RuleEngine {
    fn clone(&self) -> Self {
        Self {
            decision_engine: DecisionEngine::default(), // DecisionEngine::default() 创建新实例
            config_manager: Arc::clone(&self.config_manager),
            cache_manager: Arc::clone(&self.cache_manager),
            config: self.config.clone(),
            stats: Arc::clone(&self.stats),
            compiled_decisions: Arc::clone(&self.compiled_decisions),
            execution_mode: Arc::clone(&self.execution_mode),
        }
    }
}

/// 为 RuleEngine 实现 RuleExecutor trait
#[async_trait::async_trait]
impl RuleExecutor for RuleEngine {
    async fn execute_rule(
        &self,
        rule_id: &str,
        context: &ExecutionContext,
    ) -> Result<ExecutionResult> {
        self.execute_rule(rule_id, context).await
    }

    async fn execute_rules(
        &self,
        rule_ids: &[String],
        context: &ExecutionContext,
    ) -> Result<Vec<(String, Result<ExecutionResult>)>> {
        let execution_mode = *self.execution_mode.read().await;

        match execution_mode {
            ExecutionMode::Synchronous => {
                let mut results = Vec::new();
                for rule_id in rule_ids {
                    let result = self.execute_rule(rule_id, context).await;
                    results.push((rule_id.clone(), result));
                }
                Ok(results)
            }
            ExecutionMode::Asynchronous | ExecutionMode::Parallel => {
                let tasks: Vec<_> = rule_ids
                    .iter()
                    .map(|rule_id| {
                        let rule_id = rule_id.clone();
                        let context = context.clone();
                        let engine = self.clone();

                        tokio::spawn(async move {
                            let result = engine.execute_rule(&rule_id, &context).await;
                            (rule_id, result)
                        })
                    })
                    .collect();

                let mut results = Vec::new();
                for task in tasks {
                    match task.await {
                        Ok((rule_id, result)) => results.push((rule_id, result)),
                        Err(e) => {
                            let rule_id = "unknown".to_string();
                            let error = Err(ConfigEngineError::RuleExecution(
                                RuleExecutionError::ExecutionFailure {
                                    rule_id: rule_id.clone(),
                                    reason: format!("任务执行失败: {}", e),
                                },
                            ));
                            results.push((rule_id, error));
                        }
                    }
                }
                Ok(results)
            }
            ExecutionMode::Pipeline => {
                // 流水线模式：规则之间有依赖关系，需要按顺序执行
                // 并且前一个规则的输出可以作为后一个规则的输入
                let mut results = Vec::new();
                let mut current_context = context.clone();

                for rule_id in rule_ids {
                    let result = self.execute_rule(rule_id, &current_context).await;

                    // 如果成功，将结果合并到上下文中
                    if let Ok(ref execution_result) = result {
                        if execution_result.success {
                            for (key, value) in &execution_result.output {
                                current_context.set_value(key.clone(), value.clone());
                            }
                        }
                    }

                    results.push((rule_id.clone(), result));
                }
                Ok(results)
            }
            ExecutionMode::FaultTolerant => {
                // 容错模式：即使某些规则失败，也继续执行其他规则
                let tasks: Vec<_> = rule_ids
                    .iter()
                    .map(|rule_id| {
                        let rule_id = rule_id.clone();
                        let context = context.clone();
                        let engine = self.clone();

                        tokio::spawn(async move {
                            // 为每个规则设置超时和重试机制
                            let mut attempts = 0;
                            const MAX_ATTEMPTS: u32 = 3;

                            loop {
                                attempts += 1;

                                match tokio::time::timeout(
                                    std::time::Duration::from_millis(
                                        engine.config.default_timeout_ms * 2,
                                    ),
                                    engine.execute_rule(&rule_id, &context),
                                )
                                .await
                                {
                                    Ok(result) => {
                                        return (rule_id, result);
                                    }
                                    Err(_) if attempts < MAX_ATTEMPTS => {
                                        log::warn!(
                                            "规则 {} 执行超时，尝试重试 ({}/{})",
                                            rule_id,
                                            attempts,
                                            MAX_ATTEMPTS
                                        );
                                        tokio::time::sleep(std::time::Duration::from_millis(
                                            100 * attempts as u64,
                                        ))
                                        .await;
                                        continue;
                                    }
                                    Err(_) => {
                                        let error = Err(ConfigEngineError::RuleExecution(
                                            RuleExecutionError::Timeout {
                                                rule_id: rule_id.clone(),
                                                timeout_ms: engine.config.default_timeout_ms * 2,
                                            },
                                        ));
                                        return (rule_id, error);
                                    }
                                }
                            }
                        })
                    })
                    .collect();

                let mut results = Vec::new();
                for task in tasks {
                    match task.await {
                        Ok((rule_id, result)) => results.push((rule_id, result)),
                        Err(e) => {
                            let rule_id = "unknown".to_string();
                            let error = Err(ConfigEngineError::RuleExecution(
                                RuleExecutionError::ExecutionFailure {
                                    rule_id: rule_id.clone(),
                                    reason: format!("任务执行失败: {}", e),
                                },
                            ));
                            results.push((rule_id, error));
                        }
                    }
                }
                Ok(results)
            }
        }
    }

    async fn warmup_rule(&self, rule_id: &str) -> Result<()> {
        self.warmup_rule(rule_id).await
    }

    async fn warmup_rules(&self, rule_ids: &[String]) -> Result<()> {
        self.warmup_rules(rule_ids).await
    }

    async fn get_execution_stats(&self) -> RuleEngineStats {
        self.get_stats().await
    }

    async fn reset_stats(&self) {
        self.reset_stats().await
    }

    async fn health_check(&self) -> RuleEngineHealth {
        self.health_check().await
    }

    async fn validate_rule(&self, rule_id: &str) -> Result<bool> {
        // 验证规则定义是否有效
        match self.config_manager.get_rule_definition(rule_id).await {
            Some(rule_def) => {
                // 检查规则是否启用
                if !rule_def.enabled {
                    return Ok(false);
                }

                // 验证 JDM 内容是否有效
                match serde_json::from_str::<DecisionContent>(&rule_def.jdm_content) {
                    Ok(_) => Ok(true),
                    Err(e) => {
                        log::warn!("规则 {} JDM 内容无效: {}", rule_id, e);
                        Ok(false)
                    }
                }
            }
            None => {
                log::warn!("规则 {} 不存在", rule_id);
                Ok(false)
            }
        }
    }

    async fn list_compiled_rules(&self) -> Vec<String> {
        self.list_compiled_rules().await
    }

    async fn clear_compiled_cache(&self) -> Result<()> {
        self.clear_compiled_cache().await
    }

    async fn recompile_rule(&self, rule_id: &str) -> Result<()> {
        self.recompile_rule(rule_id).await
    }

    async fn set_execution_mode(&mut self, mode: ExecutionMode) -> Result<()> {
        let mut current_mode = self.execution_mode.write().await;
        *current_mode = mode;
        log::info!("执行模式已切换到: {}", mode);
        Ok(())
    }

    fn get_execution_mode(&self) -> ExecutionMode {
        // 使用 try_read 避免在同步方法中阻塞
        match self.execution_mode.try_read() {
            Ok(mode) => *mode,
            Err(_) => ExecutionMode::default(), // 如果无法获取锁，返回默认值
        }
    }
}

/// 规则引擎配置
#[derive(Debug, Clone)]
pub struct RuleEngineConfig {
    /// 默认超时时间（毫秒）
    pub default_timeout_ms: u64,
    /// 启用缓存
    pub enable_cache: bool,
    /// 预热规则列表（支持 "*" 通配符预热所有规则）
    pub warmup_rules: Vec<String>,
    /// 最大编译缓存大小
    pub max_compiled_cache_size: usize,
    /// 预热配置
    pub warmup_config: WarmupConfig,
}

impl Default for RuleEngineConfig {
    fn default() -> Self {
        Self {
            default_timeout_ms: 5000, // 5秒
            enable_cache: true,
            warmup_rules: Vec::new(),
            max_compiled_cache_size: 1000,
            warmup_config: WarmupConfig::default(),
        }
    }
}

/// 预热配置
#[derive(Debug, Clone)]
pub struct WarmupConfig {
    /// 是否启用预热
    pub enabled: bool,
    /// 最大并发预热任务数
    pub max_concurrent_tasks: usize,
    /// 预热超时时间（毫秒）
    pub timeout_ms: u64,
    /// 失败容忍率（0.0-1.0，超过此比例的失败将导致预热失败）
    pub failure_tolerance: f64,
    /// 是否在预热失败时继续启动
    pub continue_on_failure: bool,
}

impl Default for WarmupConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_concurrent_tasks: 10,
            timeout_ms: 30000,      // 30秒
            failure_tolerance: 0.2, // 20%失败容忍率
            continue_on_failure: false,
        }
    }
}

impl RuleEngineConfig {
    /// 创建开发环境配置（快速启动）
    pub fn development() -> Self {
        Self {
            warmup_config: WarmupConfig {
                enabled: false, // 开发环境关闭预热以加快启动
                ..Default::default()
            },
            ..Default::default()
        }
    }

    /// 创建生产环境配置（全面预热）
    pub fn production() -> Self {
        Self {
            warmup_rules: vec!["*".to_string()], // 预热所有规则
            warmup_config: WarmupConfig {
                enabled: true,
                max_concurrent_tasks: 20,
                timeout_ms: 60000,      // 1分钟
                failure_tolerance: 0.1, // 10%失败容忍率
                continue_on_failure: false,
            },
            max_compiled_cache_size: 5000,
            ..Default::default()
        }
    }

    /// 创建高性能配置（选择性预热）
    pub fn high_performance(critical_rules: Vec<String>) -> Self {
        Self {
            warmup_rules: critical_rules,
            warmup_config: WarmupConfig {
                enabled: true,
                max_concurrent_tasks: 50,
                timeout_ms: 120000,     // 2分钟
                failure_tolerance: 0.0, // 不容忍失败
                continue_on_failure: false,
            },
            max_compiled_cache_size: 10000,
            ..Default::default()
        }
    }
}

/// 规则引擎统计信息
#[derive(Debug, Clone)]
pub struct RuleEngineStats {
    /// 总请求数
    pub total_requests: u64,
    /// 成功执行数
    pub successful_executions: u64,
    /// 失败执行数
    pub failed_executions: u64,
    /// 超时执行数
    pub timeout_executions: u64,
    /// 缓存命中数
    pub cache_hits: u64,
    /// 总执行时间（毫秒）
    pub total_execution_time_ms: u64,
}

impl RuleEngineStats {
    pub fn new() -> Self {
        Self {
            total_requests: 0,
            successful_executions: 0,
            failed_executions: 0,
            timeout_executions: 0,
            cache_hits: 0,
            total_execution_time_ms: 0,
        }
    }

    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            self.successful_executions as f64 / self.total_requests as f64
        }
    }

    /// 计算缓存命中率
    pub fn cache_hit_rate(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            self.cache_hits as f64 / self.total_requests as f64
        }
    }

    /// 计算平均执行时间
    pub fn average_execution_time_ms(&self) -> f64 {
        if self.successful_executions == 0 {
            0.0
        } else {
            self.total_execution_time_ms as f64 / self.successful_executions as f64
        }
    }
}

impl Default for RuleEngineStats {
    fn default() -> Self {
        Self::new()
    }
}

/// 规则引擎健康状态
#[derive(Debug, Clone)]
pub struct RuleEngineHealth {
    /// 整体健康状态
    pub healthy: bool,
    /// 配置管理器健康状态
    pub config_manager_healthy: bool,
    /// 缓存管理器健康状态
    pub cache_manager_healthy: bool,
    /// 已编译规则数量
    pub compiled_rules_count: usize,
    /// 总请求数
    pub total_requests: u64,
    /// 成功率
    pub success_rate: f64,
    /// 平均执行时间
    pub average_execution_time_ms: f64,
}

/// 单个规则预热结果
#[derive(Debug, Clone)]
pub struct RuleWarmupResult {
    /// 规则ID
    pub rule_id: String,
    /// 是否成功
    pub success: bool,
    /// 预热耗时
    pub duration: std::time::Duration,
    /// 错误信息（如果失败）
    pub error: Option<String>,
}

/// 预热结果汇总
#[derive(Debug)]
pub struct WarmupResults {
    /// 成功预热的规则
    pub successful_rules: Vec<RuleWarmupResult>,
    /// 失败预热的规则
    pub failed_rules: Vec<RuleWarmupResult>,
    /// 成功数量
    pub success_count: usize,
    /// 失败数量
    pub failed_count: usize,
    /// 任务级别错误数量
    pub task_errors: usize,
}

impl WarmupResults {
    /// 创建新的预热结果汇总
    pub fn new() -> Self {
        Self {
            successful_rules: Vec::new(),
            failed_rules: Vec::new(),
            success_count: 0,
            failed_count: 0,
            task_errors: 0,
        }
    }

    /// 添加单个预热结果
    pub fn add_result(&mut self, result: RuleWarmupResult) {
        if result.success {
            self.success_count += 1;
            self.successful_rules.push(result);
        } else {
            self.failed_count += 1;
            self.failed_rules.push(result);
        }
    }

    /// 获取总数量
    pub fn total_count(&self) -> usize {
        self.success_count + self.failed_count
    }

    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_count() == 0 {
            0.0
        } else {
            self.success_count as f64 / self.total_count() as f64
        }
    }

    /// 判断是否应该失败（基于指定的容忍率）
    pub fn should_fail(&self, failure_tolerance: f64) -> bool {
        // 如果有任务级错误，或者失败率超过容忍率，则认为预热失败
        let failure_rate = 1.0 - self.success_rate();
        self.task_errors > 0 || failure_rate > failure_tolerance
    }

    /// 记录预热汇总日志
    pub fn log_summary(&self, total_time: std::time::Duration) {
        if self.total_count() == 0 {
            log::info!("规则预热完成，无规则需要预热");
            return;
        }

        let avg_duration = if self.success_count > 0 {
            let total_duration: std::time::Duration =
                self.successful_rules.iter().map(|r| r.duration).sum();
            total_duration / self.success_count as u32
        } else {
            std::time::Duration::from_millis(0)
        };

        log::info!(
            "规则预热完成 - 总数: {}, 成功: {}, 失败: {}, 成功率: {:.1}%, 总耗时: {:?}, 平均耗时: {:?}",
            self.total_count(),
            self.success_count,
            self.failed_count,
            self.success_rate() * 100.0,
            total_time,
            avg_duration
        );

        // 记录失败的规则详情
        if !self.failed_rules.is_empty() {
            log::warn!("预热失败的规则:");
            for failed_rule in &self.failed_rules {
                log::warn!(
                    "  - {}: {} (耗时: {:?})",
                    failed_rule.rule_id,
                    failed_rule.error.as_deref().unwrap_or("未知错误"),
                    failed_rule.duration
                );
            }
        }

        // 记录任务错误
        if self.task_errors > 0 {
            log::error!("预热过程中发生 {} 个任务级错误", self.task_errors);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config_engine::{
        cache::{CacheConfig, CacheManager},
        config::ConfigManager,
        rules::ExecutionContext,
    };
    use tokio;

    async fn create_test_engine() -> RuleEngine {
        let sources = vec![];
        let config_manager = Arc::new(ConfigManager::new(sources, false).await.unwrap());
        let cache_manager = Arc::new(CacheManager::new(CacheConfig::default()).await.unwrap());
        let engine_config = RuleEngineConfig::default();

        RuleEngine::new(config_manager, cache_manager, engine_config)
            .await
            .unwrap()
    }

    #[tokio::test]
    async fn test_engine_creation() {
        let engine = create_test_engine().await;
        let stats = engine.get_stats().await;

        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.successful_executions, 0);
    }

    #[tokio::test]
    async fn test_hash_context() {
        let context = ExecutionContext::new();
        let hash1 = RuleEngine::hash_context(&context);
        let hash2 = RuleEngine::hash_context(&context);

        assert_eq!(hash1, hash2); // 相同上下文应产生相同哈希
    }

    #[tokio::test]
    async fn test_compiled_cache_operations() {
        let engine = create_test_engine().await;

        // 初始状态应该没有编译的规则
        let rules = engine.list_compiled_rules().await;
        assert!(rules.is_empty());

        // 清除缓存应该成功
        engine.clear_compiled_cache().await.unwrap();
    }

    #[tokio::test]
    async fn test_health_check() {
        let engine = create_test_engine().await;
        let health = engine.health_check().await;

        assert_eq!(health.compiled_rules_count, 0);
        assert_eq!(health.total_requests, 0);
    }

    #[test]
    fn test_json_conversion() {
        let engine_config = RuleEngineConfig::default();
        let decision_engine = DecisionEngine::default();
        let stats = RuleEngineStats::new();

        // 测试基本类型转换
        let json_str = JsonValue::String("test".to_string());
        let json_num = JsonValue::Number(serde_json::Number::from(42));
        let json_bool = JsonValue::Bool(true);

        // 这些应该能正常创建
        assert_eq!(format!("{:?}", json_str), "String(\"test\")");
        assert_eq!(format!("{:?}", json_num), "Number(42)");
        assert_eq!(format!("{:?}", json_bool), "Bool(true)");
    }
}
