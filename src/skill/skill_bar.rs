use crate::skill::domain::value_objects::SkillEffect;
use crate::skill::Skill;
use std::collections::{HashMap, HashSet, VecDeque};

/// 技能栏（每个单位拥有自己的技能栏）
#[derive(Default, Debug, Clone)]
pub struct SkillBar {
    pub skills: Vec<Skill>,                            // 技能列表
    pub skill_cooldowns: HashMap<u32, f64>,            // 技能ID -> 剩余冷却时间
    pub skill_durations: HashMap<u32, u32>,            // 技能ID -> 效果持续时间
    pub skill_effects: HashMap<u32, Vec<SkillEffect>>, // 技能ID -> 技能效果列表
    pub quick_slots: Vec<u32>,                         // 快捷栏技能ID列表
    pub auto_cast: HashSet<u32>,                       // 自动释放的技能ID集合
    pub skill_levels: HashMap<u32, u32>,               // 技能ID -> 技能等级
    pub skill_exp: HashMap<u32, u32>,                  // 技能ID -> 技能经验值
    pub skill_mastery: HashMap<u32, f32>,              // 技能ID -> 技能熟练度(0-1)
    pub skill_combo: Vec<u32>,                         // 连击技能序列
    pub skill_chain: Vec<u32>,                         // 连锁技能序列
    pub skill_buff_timers: HashMap<u32, u32>,          // 技能buff持续时间
    pub skill_debuff_timers: HashMap<u32, u32>,        // 技能debuff持续时间
    pub skill_effect_timers: HashMap<u32, u32>,        // 技能效果持续时间
    pub skill_channeling: Option<(u32, u32)>,          // 当前引导技能(技能ID, 剩余时间)
    pub skill_cast_queue: VecDeque<u32>,               // 技能释放队列
    pub skill_interrupt_flags: HashSet<u32>,           // 可被打断的技能ID集合
    pub skill_silence_flags: HashSet<u32>,             // 被沉默的技能ID集合
    pub skill_stun_flags: HashSet<u32>,                // 被眩晕的技能ID集合
    pub skill_disabled_flags: HashSet<u32>,            // 被禁用的技能ID集合
    pub skill_enhanced_flags: HashSet<u32>,            // 被强化的技能ID集合
    pub skill_weakened_flags: HashSet<u32>,            // 被削弱的技能ID集合
    pub skill_reset_flags: HashSet<u32>,               // 可重置冷却的技能ID集合
    pub skill_refresh_flags: HashSet<u32>,             // 可刷新持续时间的技能ID集合
    pub skill_extend_flags: HashSet<u32>,              // 可延长持续时间的技能ID集合
    pub skill_reduce_flags: HashSet<u32>,              // 可减少冷却的技能ID集合
    pub skill_amplify_flags: HashSet<u32>,             // 可增幅效果的技能ID集合
    pub skill_reduce_effect_flags: HashSet<u32>,       // 可减幅效果的技能ID集合
    pub skill_chain_flags: HashSet<u32>,               // 可连锁的技能ID集合
    pub skill_combo_flags: HashSet<u32>,               // 可连击的技能ID集合
    pub skill_splash_flags: HashSet<u32>,              // 可溅射的技能ID集合
    pub skill_pierce_flags: HashSet<u32>,              // 可穿透的技能ID集合
    pub skill_bounce_flags: HashSet<u32>,              // 可弹射的技能ID集合
    pub skill_explode_flags: HashSet<u32>,             // 可爆炸的技能ID集合
    pub skill_implode_flags: HashSet<u32>,             // 可内爆的技能ID集合
    pub skill_pull_flags: HashSet<u32>,                // 可拉拽的技能ID集合
    pub skill_push_flags: HashSet<u32>,                // 可击退的技能ID集合
    pub skill_knockup_flags: HashSet<u32>,             // 可击飞的技能ID集合
    pub skill_knockback_flags: HashSet<u32>,           // 可击退的技能ID集合
    pub skill_stagger_flags: HashSet<u32>,             // 可踉跄的技能ID集合
    pub skill_charm_flags: HashSet<u32>,               // 可魅惑的技能ID集合
    pub skill_confuse_flags: HashSet<u32>,             // 可混乱的技能ID集合
    pub skill_sleep_flags: HashSet<u32>,               // 可睡眠的技能ID集合
    pub skill_transform_flags: HashSet<u32>,           // 可变形的技能ID集合
    pub skill_polymorph_flags: HashSet<u32>,           // 可变形的技能ID集合
    pub skill_curse_flags: HashSet<u32>,               // 可诅咒的技能ID集合
    pub skill_hex_flags: HashSet<u32>,                 // 可诅咒的技能ID集合
    pub skill_banish_flags: HashSet<u32>,              // 可放逐的技能ID集合
    pub skill_exile_flags: HashSet<u32>,               // 可放逐的技能ID集合
    pub skill_disarm_flags: HashSet<u32>,              // 可缴械的技能ID集合
    pub skill_disable_flags: HashSet<u32>,             // 可禁用的技能ID集合
    pub skill_seal_flags: HashSet<u32>,                // 可封印的技能ID集合
    pub skill_lock_flags: HashSet<u32>,                // 可锁定的技能ID集合
    pub skill_petrify_flags: HashSet<u32>,             // 可石化的技能ID集合
    pub skill_stasis_flags: HashSet<u32>,              // 可静止的技能ID集合
}
