
use crate::battle_system::simplified_battle_traits::*;
use crate::character::domain::character_aggregate::Character;
use crate::skill::buff::Buff;
use crate::shared::types::{Attack, BattleUnitId, Defense, Health, Level, Mana, Position, Range, SkillId, Speed};
use crate::shared::GameResult;

/// 一个包装器结构体，用于将新的 `Character` 聚合体适配到旧的 `simplified_battle_traits`。
/// 这作为一个临时的兼容层，最终将被移除。
pub struct SimplifiedCharacter {
    pub character: Character,
}

impl SimplifiedCharacter {
    pub fn new(character: Character) -> Self {
        Self { character }
    }
}

// ============================================================================
// Trait Implementations
// ============================================================================

impl BattleEntity for SimplifiedCharacter {
    fn entity_id(&self) -> BattleUnitId {
        BattleUnitId::Character(self.character.id())
    }

    fn display_name(&self) -> &str {
        self.character.name()
    }

    fn level(&self) -> Level {
        self.character.level()
    }

    fn position(&self) -> Position {
        self.character.position()
    }

    fn set_position(&mut self, position: Position) -> GameResult<()> {
        self.character.move_to(position)
    }
}

impl LifeForce for SimplifiedCharacter {
    fn current_health(&self) -> Health {
        self.character.current_health()
    }

    fn max_health(&self) -> Health {
        self.character.max_health()
    }

    fn is_alive(&self) -> bool {
        self.character.is_alive()
    }

    fn take_damage(&mut self, damage: Health) -> GameResult<DamageResult> {
        let result = self.character.take_damage(damage)?;
        Ok(DamageResult::new(
            result.actual_damage,
            result.remaining_health,
            false, // 暴击信息需要从 damage calculator 获取，暂时为 false
            false, // 未命中信息需要从 damage calculator 获取，暂时为 false
        ))
    }

    fn heal(&mut self, amount: Health) -> GameResult<HealResult> {
        let result = self.character.heal(amount)?;
        Ok(HealResult::new(
            result.actual_heal,
            false, // 暴击信息需要从 heal calculator 获取, 暂时为 false
        ))
    }

    fn current_mana(&self) -> Mana {
        self.character.current_mana()
    }

    fn max_mana(&self) -> Mana {
        self.character.max_mana()
    }

    fn consume_mana(&mut self, amount: Mana) -> GameResult<()> {
        self.character.consume_mana(amount)
    }

    fn restore_mana(&mut self, amount: Mana) -> GameResult<()> {
        self.character.restore_mana(amount)
    }
}

impl CombatCapable for SimplifiedCharacter {
    fn attack_power(&self) -> Attack {
        self.character.calculate_attack()
    }

    fn defense_power(&self) -> Defense {
        self.character.calculate_defense()
    }

    fn movement_speed(&self) -> Speed {
        self.character.calculate_speed()
    }
    
    fn attack_range(&self) -> Range {
        1.0 // 假设默认攻击范围为1.0
    }
}

impl SkillUser for SimplifiedCharacter {
    fn learned_skills(&self) -> Vec<SkillId> {
        self.character.skill_ref().get_learned_skills().to_vec()
    }

    fn skill_cooldown(&self, skill_id: &SkillId) -> f64 {
        self.character.skill_cooldowns().get_cooldown(skill_id)
    }

    fn is_skill_ready(&self, skill_id: &SkillId) -> bool {
        self.character.skill_cooldowns().is_skill_ready(skill_id)
    }

    fn use_skill(&mut self, skill_id: &SkillId) -> GameResult<()> {
        // FIXME: This is a placeholder. The actual mana cost and cooldown
        // should be retrieved from the skill's definition.
        let mana_cost = 10;
        let cooldown = 5.0;
        self.character.use_skill(*skill_id, mana_cost, cooldown)
    }

    fn set_skill_cooldown(&mut self, skill_id: SkillId, cooldown: f64) -> GameResult<()> {
        self.character.skill_cooldowns_mut().set_cooldown(skill_id, cooldown)
    }

    fn update_skill_cooldowns(&mut self, delta_time: f32) {
        self.character.skill_cooldowns_mut().update_cooldowns(delta_time);
    }
    
    fn add_buff(&mut self, buff: Buff) -> GameResult<()> {
        self.character.add_buff(buff)
    }

    fn get_buffs(&self) -> Vec<Buff> {
        self.character.buff_manager().get_buffs().to_vec()
    }
}

impl TimeAware for SimplifiedCharacter {
    fn update(&mut self, delta_seconds: f32) {
        let _ = self.character.update(delta_seconds);
    }
}

impl FullBattleUnit for SimplifiedCharacter {}
