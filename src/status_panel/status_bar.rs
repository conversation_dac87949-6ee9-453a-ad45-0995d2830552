use crate::shared::types::*;
use crate::skill::buff::Buff;
use crate::status_panel::status::*;
use std::collections::HashMap;

/// 状态栏结构体，用于存储角色的各种状态信息
#[derive(Debug, Clone)]
pub struct StatusBar {
    /// 当前激活的buff列表
    pub active_buffs: Vec<Buff>,
    /// 技能冷却时间映射表 (技能ID -> 剩余冷却时间)
    pub skill_cooldowns: HashMap<ID, CooldownTime>,
    /// 当前状态效果列表 (如眩晕、沉默等)
    pub status_effects: Vec<CharacterState>,
    /// 状态效果名称列表（用于快速字符串查询）
    pub status_names: Vec<String>,
    /// 是否处于战斗状态
    pub in_combat: bool,
    /// 是否处于移动状态
    pub is_moving: bool,
    /// 是否处于施法状态
    pub is_casting: bool,
}

impl StatusBar {
    /// 创建一个新的状态栏实例
    pub fn new() -> Self {
        StatusBar {
            active_buffs: Vec::new(),
            skill_cooldowns: HashMap::new(),
            status_effects: Vec::new(),
            status_names: Vec::new(),
            in_combat: false,
            is_moving: false,
            is_casting: false,
        }
    }

    pub fn get_buffs(&mut self) -> Vec<Buff> {
        self.active_buffs.clone()
    }

    /// 添加一个buff到状态栏
    pub fn add_buff(&mut self, buff: Buff) {
        self.active_buffs.push(buff);
    }

    /// 移除指定的buff
    pub fn remove_buff(&mut self, buff_id: ID) {
        self.active_buffs.retain(|b| b.id != buff_id);
    }

    /// 更新技能冷却时间
    pub fn update_skill_cooldown(&mut self, skill_id: ID, cooldown: CooldownTime) {
        self.skill_cooldowns.insert(skill_id, cooldown);
    }

    /// 检查技能是否在冷却中
    pub fn is_skill_on_cooldown(&self, skill_id: ID) -> bool {
        self.skill_cooldowns.contains_key(&skill_id)
    }

    /// 添加状态效果
    pub fn add_status_effect(&mut self, effect: CharacterState) {
        if !self.status_effects.contains(&effect) {
            let effect_name = effect.to_string();
            self.status_effects.push(effect);
            if !self.status_names.contains(&effect_name) {
                self.status_names.push(effect_name);
            }
        }
    }

    /// 移除状态效果
    pub fn remove_status_effect(&mut self, effect: &CharacterState) {
        let effect_name = effect.to_string();
        self.status_effects.retain(|e| e != effect);
        // 检查是否还有相同类型的效果
        if !self
            .status_effects
            .iter()
            .any(|e| e.to_string() == effect_name)
        {
            self.status_names.retain(|name| name != &effect_name);
        }
    }

    /// 检查是否具有某个状态效果
    pub fn has_status_effect(&self, effect: &CharacterState) -> bool {
        self.status_effects.contains(&effect)
    }

    /// 更新战斗状态
    pub fn set_combat_state(&mut self, in_combat: bool) {
        self.in_combat = in_combat;
    }

    /// 更新移动状态
    pub fn set_moving_state(&mut self, is_moving: bool) {
        self.is_moving = is_moving;
    }

    /// 更新施法状态
    pub fn set_casting_state(&mut self, is_casting: bool) {
        self.is_casting = is_casting;
    }

    /// 清除所有状态
    pub fn clear_all(&mut self) {
        self.active_buffs.clear();
        self.skill_cooldowns.clear();
        self.status_effects.clear();
        self.status_names.clear();
        self.in_combat = false;
        self.is_moving = false;
        self.is_casting = false;
    }
}
