/// Character 相关值对象
///
/// 按照DDD原则定义不可变的值对象，确保业务规则的封装
/// 这些值对象从 character/domain/value_objects.rs 迁移而来，保持兼容性
use crate::attribute::{Attribute, AttributeSet, AttributeType, CoreAttribute};
use crate::shared::{Exp, GameError, GameResult};
use crate::{Attack, Attr, Health, Level, Mana};
use serde::{Deserialize, Serialize};

// ============================================================================
// 生命值对象 - 值对象，封装生命值的业务规则
// ============================================================================

/// 生命值 - 值对象，封装生命值的业务规则
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct HealthValue {
    current: Health,
    maximum: Health,
}

impl HealthValue {
    /// 创建新的生命值对象
    pub fn new(maximum: Health) -> GameResult<Self> {
        if maximum <= 0 {
            return Err(GameError::validation_error(
                "maximum",
                "最大生命值必须大于0",
            ));
        }

        Ok(Self {
            current: maximum,
            maximum,
        })
    }

    /// 从当前值和最大值创建
    pub fn from_values(current: Health, maximum: Health) -> GameResult<Self> {
        if maximum <= 0 {
            return Err(GameError::validation_error(
                "maximum",
                "最大生命值必须大于0",
            ));
        }
        if current < 0 {
            return Err(GameError::validation_error("current", "当前生命值不能为负"));
        }

        Ok(Self {
            current: current.min(maximum),
            maximum,
        })
    }

    /// 获取当前生命值
    pub fn current(&self) -> Health {
        self.current
    }

    /// 获取最大生命值
    pub fn maximum(&self) -> Health {
        self.maximum
    }

    /// 获取生命值百分比
    pub fn percentage(&self) -> f32 {
        if self.maximum == 0 {
            0.0
        } else {
            (self.current as f32) / (self.maximum as f32)
        }
    }

    /// 是否存活
    pub fn is_alive(&self) -> bool {
        self.current > 0
    }

    /// 是否满血
    pub fn is_full(&self) -> bool {
        self.current == self.maximum
    }

    /// 受到伤害
    pub fn take_damage(&self, damage: Health) -> GameResult<Self> {
        if damage < 0 {
            return Err(GameError::validation_error("damage", "伤害值不能为负"));
        }

        Ok(Self {
            current: (self.current - damage).max(0),
            maximum: self.maximum,
        })
    }

    /// 恢复生命值
    pub fn heal(&self, amount: Health) -> GameResult<Self> {
        if amount < 0 {
            return Err(GameError::validation_error("amount", "恢复量不能为负"));
        }

        Ok(Self {
            current: (self.current + amount).min(self.maximum),
            maximum: self.maximum,
        })
    }

    /// 设置最大生命值
    pub fn set_maximum(&self, new_maximum: Health) -> GameResult<Self> {
        if new_maximum <= 0 {
            return Err(GameError::validation_error(
                "new_maximum",
                "最大生命值必须大于0",
            ));
        }

        Ok(Self {
            current: self.current.min(new_maximum),
            maximum: new_maximum,
        })
    }
}

// ============================================================================
// 法力值对象 - 值对象，封装法力值的业务规则
// ============================================================================

/// 法力值 - 值对象，封装法力值的业务规则
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct ManaValue {
    current: Mana,
    maximum: Mana,
}

impl ManaValue {
    /// 创建新的法力值对象
    pub fn new(maximum: Mana) -> GameResult<Self> {
        if maximum < 0 {
            return Err(GameError::validation_error("maximum", "最大法力值不能为负"));
        }

        Ok(Self {
            current: maximum,
            maximum,
        })
    }

    /// 从当前值和最大值创建
    pub fn from_values(current: Mana, maximum: Mana) -> GameResult<Self> {
        if maximum < 0 {
            return Err(GameError::validation_error("maximum", "最大法力值不能为负"));
        }
        if current < 0 {
            return Err(GameError::validation_error("current", "当前法力值不能为负"));
        }

        Ok(Self {
            current: current.min(maximum),
            maximum,
        })
    }

    /// 获取当前法力值
    pub fn current(&self) -> Mana {
        self.current
    }

    /// 获取最大法力值
    pub fn maximum(&self) -> Mana {
        self.maximum
    }

    /// 获取法力值百分比
    pub fn percentage(&self) -> f32 {
        if self.maximum == 0 {
            1.0 // 如果最大法力为0，视为满法力
        } else {
            (self.current as f32) / (self.maximum as f32)
        }
    }

    /// 是否有足够法力
    pub fn has_enough(&self, required: Mana) -> bool {
        self.current >= required
    }

    /// 是否满法力
    pub fn is_full(&self) -> bool {
        self.current == self.maximum
    }

    /// 消耗法力
    pub fn consume(&self, amount: Mana) -> GameResult<Self> {
        if amount < 0 {
            return Err(GameError::validation_error("amount", "消耗量不能为负"));
        }
        if self.current < amount {
            return Err(GameError::insufficient_mana_error(amount, self.current));
        }

        Ok(Self {
            current: self.current - amount,
            maximum: self.maximum,
        })
    }

    /// 恢复法力
    pub fn restore(&self, amount: Mana) -> GameResult<Self> {
        if amount < 0 {
            return Err(GameError::validation_error("amount", "恢复量不能为负"));
        }

        Ok(Self {
            current: (self.current + amount).min(self.maximum),
            maximum: self.maximum,
        })
    }

    /// 设置最大法力值
    pub fn set_maximum(&self, new_maximum: Mana) -> GameResult<Self> {
        if new_maximum < 0 {
            return Err(GameError::validation_error(
                "new_maximum",
                "最大法力值不能为负",
            ));
        }

        Ok(Self {
            current: self.current.min(new_maximum),
            maximum: new_maximum,
        })
    }
}

// ============================================================================
// 基础属性值对象 - 值对象，封装角色的基础属性
// ============================================================================

/// 基础属性 - 值对象，封装角色的基础属性
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct BasicAttributes {
    /// 体质 - 影响生命值和抗性
    constitution: Attr,
    /// 力量 - 影响物理攻击力
    strength: Attr,
    /// 精神 - 影响法力值和法术攻击力
    spirit: Attr,
}

impl BasicAttributes {
    /// 创建基础属性
    pub fn new(constitution: Attr, strength: Attr, spirit: Attr) -> Self {
        Self {
            constitution,
            strength,
            spirit,
        }
    }

    /// 获取体质
    pub fn constitution(&self) -> Attr {
        self.constitution
    }

    /// 获取力量
    pub fn strength(&self) -> Attr {
        self.strength
    }

    /// 获取精神
    pub fn spirit(&self) -> Attr {
        self.spirit
    }

    /// 增加体质
    pub fn add_constitution(&self, amount: Attr) -> Self {
        Self {
            constitution: self.constitution.saturating_add(amount),
            strength: self.strength,
            spirit: self.spirit,
        }
    }

    /// 增加力量
    pub fn add_strength(&self, amount: Attr) -> Self {
        Self {
            constitution: self.constitution,
            strength: self.strength.saturating_add(amount),
            spirit: self.spirit,
        }
    }

    /// 增加精神
    pub fn add_spirit(&self, amount: Attr) -> Self {
        Self {
            constitution: self.constitution,
            strength: self.strength,
            spirit: self.spirit.saturating_add(amount),
        }
    }

    /// 计算生命值加成
    pub fn health_bonus(&self) -> Health {
        (self.constitution * 10) as Health
    }

    /// 计算法力值加成
    pub fn mana_bonus(&self) -> Mana {
        (self.spirit * 5) as Mana
    }

    /// 计算物理攻击力
    pub fn physical_attack(&self) -> Attack {
        self.strength as Attack
    }

    /// 计算法术攻击力
    pub fn magical_attack(&self) -> Attack {
        self.spirit as Attack
    }
}

impl Default for BasicAttributes {
    fn default() -> Self {
        Self::new(10, 10, 10)
    }
}

// ============================================================================
// 经验值对象 - 值对象，封装经验和等级的业务逻辑
// ============================================================================

/// 经验值 - 值对象，封装经验和等级的业务逻辑
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct ExperienceValue {
    current_exp: Exp,
    level: Level,
}

impl ExperienceValue {
    /// 创建经验值对象
    pub fn new(level: Level, current_exp: Exp) -> GameResult<Self> {
        if level == 0 {
            return Err(GameError::validation_error("level", "等级不能为0"));
        }

        let max_exp_for_level = Self::max_exp_for_level(level);
        if current_exp > max_exp_for_level {
            return Err(GameError::validation_error(
                "current_exp",
                "当前经验超过等级上限",
            ));
        }

        Ok(Self { current_exp, level })
    }

    /// 获取当前经验
    pub fn current_exp(&self) -> Exp {
        self.current_exp
    }

    /// 获取等级
    pub fn level(&self) -> Level {
        self.level
    }

    /// 获取升级所需经验
    pub fn exp_to_next_level(&self) -> Exp {
        Self::max_exp_for_level(self.level).saturating_sub(self.current_exp)
    }

    /// 获取经验百分比
    pub fn exp_percentage(&self) -> f32 {
        let max_exp = Self::max_exp_for_level(self.level);
        let prev_level_exp = if self.level > 1 {
            Self::max_exp_for_level(self.level - 1)
        } else {
            0
        };

        let level_exp_range = max_exp - prev_level_exp;
        let current_level_exp = self.current_exp - prev_level_exp;

        if level_exp_range == 0 {
            1.0
        } else {
            (current_level_exp as f32) / (level_exp_range as f32)
        }
    }

    /// 添加经验，返回新的经验值对象和升级信息
    pub fn add_exp(&self, amount: Exp) -> GameResult<(Self, Vec<LevelUpEvent>)> {
        if amount == 0 {
            return Ok((self.clone(), Vec::new()));
        }

        let mut new_exp = self.current_exp.saturating_add(amount);
        let mut new_level = self.level;
        let mut level_ups = Vec::new();

        // 检查是否升级
        while new_level < 100 && new_exp >= Self::max_exp_for_level(new_level) {
            let old_level = new_level;
            new_level += 1;

            level_ups.push(LevelUpEvent {
                from_level: old_level,
                to_level: new_level,
                gained_points: Self::attribute_points_per_level(),
            });
        }

        // 如果达到最高级，限制经验值
        if new_level >= 100 {
            new_exp = Self::max_exp_for_level(100);
        }

        let new_exp_value = Self::new(new_level, new_exp)?;
        Ok((new_exp_value, level_ups))
    }

    /// 计算等级对应的经验上限
    fn max_exp_for_level(level: Level) -> Exp {
        // 使用指数增长公式：基础经验 * 1.2^(level-1)
        let base_exp = 100u32;
        let multiplier = 1.2f64;

        (base_exp as f64 * multiplier.powi((level - 1) as i32)) as Exp
    }

    /// 每级获得的属性点
    fn attribute_points_per_level() -> u32 {
        3
    }
}

/// 升级事件
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct LevelUpEvent {
    pub from_level: Level,
    pub to_level: Level,
    pub gained_points: u32,
}

impl Default for ExperienceValue {
    fn default() -> Self {
        Self::new(1, 0).unwrap()
    }
}

// ============================================================================
// 五行灵气值对象 - 值对象，管理角色的五行属性灵气
// ============================================================================

/// 五行灵气 - 值对象，管理角色的五行属性灵气
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SpiritualEnergy {
    essence_map: AttributeSet,
    total_essence: u64,
}

impl SpiritualEnergy {
    /// 创建五行灵气对象
    pub fn new() -> Self {
        Self {
            essence_map: AttributeSet::new(),
            total_essence: 0,
        }
    }

    /// 获取指定属性的灵气值
    pub fn get_essence(&self, attribute: &AttributeType) -> f64 {
        self.essence_map
            .get(attribute)
            .map_or(0.0, |attr| attr.value)
    }

    /// 获取总灵气
    pub fn total_essence(&self) -> u64 {
        self.total_essence
    }

    /// 获取属性集合的引用
    pub fn attribute_set(&self) -> &AttributeSet {
        &self.essence_map
    }

    /// 增加指定属性的灵气
    pub fn add_essence(&self, attribute: AttributeType, amount: f64) -> GameResult<Self> {
        if amount < 0.0 {
            return Err(GameError::validation_error("amount", "灵气数量不能为负"));
        }

        let mut new_essence_map = self.essence_map.clone();
        let current_value = self.get_essence(&attribute);
        new_essence_map.add(Attribute::new(attribute, current_value + amount));

        Ok(Self {
            essence_map: new_essence_map,
            total_essence: self.total_essence + (amount as u64),
        })
    }

    /// 消耗指定属性的灵气
    pub fn consume_essence(&self, attribute: AttributeType, amount: f64) -> GameResult<Self> {
        if amount < 0.0 {
            return Err(GameError::validation_error("amount", "消耗量不能为负"));
        }

        let current_value = self.get_essence(&attribute);
        if current_value < amount {
            return Err(GameError::validation_error("amount", "灵气不足"));
        }

        let mut new_essence_map = self.essence_map.clone();
        new_essence_map.add(Attribute::new(attribute, current_value - amount));

        Ok(Self {
            essence_map: new_essence_map,
            total_essence: self.total_essence.saturating_sub(amount as u64),
        })
    }

    /// 获取主要属性（灵气最多的属性）
    pub fn primary_attribute(&self) -> Option<AttributeType> {
        self.essence_map
            .attributes
            .iter()
            .max_by(|a, b| {
                a.1.value
                    .partial_cmp(&b.1.value)
                    .unwrap_or(std::cmp::Ordering::Equal)
            })
            .map(|(attr_type, _)| *attr_type)
    }

    /// 检查属性平衡度
    pub fn balance_score(&self) -> f64 {
        let values: Vec<f64> = CoreAttribute::all()
            .iter()
            .map(|attr| self.get_essence(&AttributeType::Base(*attr)))
            .collect();

        if values.is_empty() {
            return 1.0;
        }

        let max_value = values.iter().fold(0.0f64, |a, &b| a.max(b));
        let min_value = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));

        if max_value == 0.0 {
            1.0
        } else {
            min_value / max_value
        }
    }
}

impl Default for SpiritualEnergy {
    fn default() -> Self {
        Self::new()
    }
}
