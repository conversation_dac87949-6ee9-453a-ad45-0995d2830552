use crate::material::material_core::{
    AttributeInteraction, ElementalAttribute, Material, MaterialAttribute, MaterialUsage,
};
use crate::MaterialGrade;
use chrono::{DateTime, Utc};
/// 材料合成炼制系统
/// 实现炼丹、炼器、布阵等材料合成功能
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
// ============================================================================
// 合成配方系统
// ============================================================================

/// 合成配方类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RecipeType {
    /// 炼丹配方
    Alchemy,
    /// 炼器配方
    Crafting,
    /// 布阵配方
    Formation,
    /// 符箓配方
    Talisman,
    /// 材料精炼
    Refinement,
    /// 材料融合
    Fusion,
}

impl RecipeType {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            RecipeType::Alchemy => "炼丹",
            RecipeType::Crafting => "炼器",
            RecipeType::Formation => "布阵",
            RecipeType::Talisman => "符箓",
            RecipeType::Refinement => "精炼",
            RecipeType::Fusion => "融合",
        }
    }

    /// 获取配方类型对应的主要用途
    pub fn primary_usage(&self) -> MaterialUsage {
        match self {
            RecipeType::Alchemy => MaterialUsage::Alchemy,
            RecipeType::Crafting => MaterialUsage::Crafting,
            RecipeType::Formation => MaterialUsage::Formation,
            RecipeType::Talisman => MaterialUsage::Crafting,
            RecipeType::Refinement => MaterialUsage::Crafting,
            RecipeType::Fusion => MaterialUsage::Crafting,
        }
    }
}

/// 材料配方 - 定义合成规则
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MaterialRecipe {
    /// 配方ID
    pub id: String,
    /// 配方名称
    pub name: String,
    /// 配方类型
    pub recipe_type: RecipeType,
    /// 配方等级
    pub level: u8,
    /// 主要材料
    pub primary_materials: Vec<RecipeIngredient>,
    /// 辅助材料
    pub auxiliary_materials: Vec<RecipeIngredient>,
    /// 催化剂
    pub catalysts: Vec<RecipeIngredient>,
    /// 产出物
    pub outputs: Vec<RecipeOutput>,
    /// 成功率基础值
    pub base_success_rate: f64,
    /// 所需技能
    pub required_skills: HashMap<String, u8>,
    /// 所需工具
    pub required_tools: Vec<String>,
    /// 制作时间（分钟）
    pub crafting_time: u32,
    /// 特殊条件
    pub special_conditions: Vec<CraftingCondition>,
    /// 配方描述
    pub description: String,
}

/// 配方材料
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct RecipeIngredient {
    /// 材料ID
    pub material_id: String,
    /// 需要数量
    pub quantity: u32,
    /// 最低品阶要求
    pub min_grade: Option<MaterialGrade>,
    /// 属性要求
    pub required_attributes: Vec<MaterialAttribute>,
    /// 是否可替代
    pub substitutable: bool,
    /// 替代材料
    pub substitutes: Vec<String>,
}

/// 配方产出
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct RecipeOutput {
    /// 产出材料ID
    pub material_id: String,
    /// 基础产出数量
    pub base_quantity: u32,
    /// 数量变动范围
    pub quantity_variation: (u32, u32), // (min_extra, max_extra)
    /// 产出概率
    pub probability: f64,
    /// 品质影响因素
    pub quality_factors: Vec<QualityFactor>,
}

/// 品质影响因素
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum QualityFactor {
    /// 材料品阶影响
    MaterialGrade {
        material_id: String,
        grade_bonus: f64,
    },
    /// 属性相性影响
    AttributeCompatibility { bonus_per_match: f64 },
    /// 技能等级影响
    SkillLevel {
        skill_name: String,
        level_bonus: f64,
    },
    /// 工具品质影响
    ToolQuality {
        tool_type: String,
        quality_bonus: f64,
    },
}

/// 制作条件
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CraftingCondition {
    /// 时间条件
    TimeRequirement { hour_range: (u8, u8) },
    /// 月相条件
    MoonPhase { required_phase: String },
    /// 环境条件
    Environment {
        location_type: String,
        spiritual_density_min: f64,
    },
    /// 季节条件
    Season { required_seasons: Vec<String> },
    /// 五行环境
    ElementalEnvironment {
        dominant_element: ElementalAttribute,
        purity_min: f64,
    },
}

// ============================================================================
// 合成过程系统
// ============================================================================

/// 合成活动 - 描述一次合成过程
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SynthesisActivity {
    /// 活动ID
    pub id: String,
    /// 合成者ID
    pub synthesizer_id: String,
    /// 使用的配方
    pub recipe_id: String,
    /// 投入的材料
    pub input_materials: HashMap<String, (Material, u32)>, // material_id -> (material, quantity)
    /// 使用的工具
    pub tools_used: Vec<String>,
    /// 开始时间
    pub start_time: DateTime<Utc>,
    /// 预计结束时间
    pub expected_end_time: DateTime<Utc>,
    /// 当前阶段
    pub current_stage: SynthesisStage,
    /// 活动状态
    pub status: SynthesisStatus,
    /// 当前进度 (0.0 - 1.0)
    pub progress: f64,
    /// 品质调整因子
    pub quality_modifiers: Vec<QualityModifier>,
}

/// 合成阶段
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SynthesisStage {
    /// 准备阶段 - 检查材料和条件
    Preparation,
    /// 材料处理 - 预处理原材料
    MaterialProcessing,
    /// 主要合成 - 核心炼制过程
    MainSynthesis,
    /// 属性融合 - 不同属性材料的融合
    AttributeFusion,
    /// 品质提升 - 提升成品品质
    QualityEnhancement,
    /// 最终成型 - 完成最终产品
    Finalization,
    /// 完成 - 合成结束
    Completed,
}

impl SynthesisStage {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            SynthesisStage::Preparation => "准备阶段",
            SynthesisStage::MaterialProcessing => "材料处理",
            SynthesisStage::MainSynthesis => "主要合成",
            SynthesisStage::AttributeFusion => "属性融合",
            SynthesisStage::QualityEnhancement => "品质提升",
            SynthesisStage::Finalization => "最终成型",
            SynthesisStage::Completed => "已完成",
        }
    }

    /// 获取阶段进度权重
    pub fn progress_weight(&self) -> f64 {
        match self {
            SynthesisStage::Preparation => 0.1,
            SynthesisStage::MaterialProcessing => 0.15,
            SynthesisStage::MainSynthesis => 0.4,
            SynthesisStage::AttributeFusion => 0.2,
            SynthesisStage::QualityEnhancement => 0.1,
            SynthesisStage::Finalization => 0.05,
            SynthesisStage::Completed => 0.0,
        }
    }
}

/// 合成状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SynthesisStatus {
    /// 准备中
    Preparing,
    /// 进行中
    InProgress,
    /// 已完成
    Completed,
    /// 失败
    Failed,
    /// 已取消
    Cancelled,
    /// 暂停
    Paused,
}

impl SynthesisStatus {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            SynthesisStatus::Preparing => "准备中",
            SynthesisStatus::InProgress => "进行中",
            SynthesisStatus::Completed => "已完成",
            SynthesisStatus::Failed => "失败",
            SynthesisStatus::Cancelled => "已取消",
            SynthesisStatus::Paused => "暂停",
        }
    }
}

/// 品质调整因子
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct QualityModifier {
    /// 修正类型
    pub modifier_type: String,
    /// 修正值
    pub modifier_value: f64,
    /// 修正描述
    pub description: String,
}

// ============================================================================
// 属性相互作用系统
// ============================================================================

/// 属性融合结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AttributeFusionResult {
    /// 融合的属性
    pub fused_attributes: Vec<MaterialAttribute>,
    /// 融合效果
    pub fusion_effect: FusionEffect,
    /// 效果强度
    pub effect_strength: f64,
    /// 副作用
    pub side_effects: Vec<String>,
}

/// 融合效果
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum FusionEffect {
    /// 完美融合 - 所有属性和谐统一
    PerfectHarmony,
    /// 强化融合 - 某些属性得到增强
    Enhancement,
    /// 稳定融合 - 属性平衡稳定
    Stabilization,
    /// 中性融合 - 无特殊效果
    Neutral,
    /// 冲突抑制 - 相克属性被压制
    ConflictSuppression,
    /// 属性分离 - 属性无法融合
    Separation,
    /// 失控反应 - 属性冲突导致不稳定
    Instability,
}

impl FusionEffect {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            FusionEffect::PerfectHarmony => "完美融合",
            FusionEffect::Enhancement => "强化融合",
            FusionEffect::Stabilization => "稳定融合",
            FusionEffect::Neutral => "中性融合",
            FusionEffect::ConflictSuppression => "冲突抑制",
            FusionEffect::Separation => "属性分离",
            FusionEffect::Instability => "失控反应",
        }
    }

    /// 获取对最终品质的影响
    pub fn quality_impact(&self) -> f64 {
        match self {
            FusionEffect::PerfectHarmony => 1.5,
            FusionEffect::Enhancement => 1.25,
            FusionEffect::Stabilization => 1.1,
            FusionEffect::Neutral => 1.0,
            FusionEffect::ConflictSuppression => 0.8,
            FusionEffect::Separation => 0.6,
            FusionEffect::Instability => 0.3,
        }
    }
}

/// 属性相互作用分析器
pub struct AttributeInteractionAnalyzer;

impl AttributeInteractionAnalyzer {
    /// 分析材料间的属性相互作用
    pub fn analyze_materials(materials: &[(Material, u32)]) -> AttributeFusionResult {
        let mut all_attributes = Vec::new();
        let mut attribute_counts = HashMap::new();

        // 收集所有属性
        for (material, quantity) in materials {
            let weight = *quantity as f64;
            all_attributes.push((material.primary_attribute, weight));
            *attribute_counts
                .entry(material.primary_attribute)
                .or_insert(0.0) += weight;

            for &attr in &material.secondary_attributes {
                all_attributes.push((attr, weight * 0.5)); // 次要属性权重减半
                *attribute_counts.entry(attr).or_insert(0.0) += weight * 0.5;
            }
        }

        // 分析主导属性
        let dominant_attribute = attribute_counts
            .iter()
            .max_by(|a, b| a.1.partial_cmp(b.1).unwrap())
            .map(|(&attr, _)| attr);

        // 分析属性相互作用
        let fusion_effect = Self::determine_fusion_effect(&all_attributes);
        let effect_strength = Self::calculate_effect_strength(&all_attributes);

        // 确定最终融合属性
        let fused_attributes = if matches!(fusion_effect, FusionEffect::Separation) {
            // 分离状态下保持原有属性
            attribute_counts.keys().cloned().collect()
        } else {
            // 其他情况下可能产生新的融合属性
            vec![dominant_attribute.unwrap_or(MaterialAttribute::Chaos)]
        };

        AttributeFusionResult {
            fused_attributes,
            fusion_effect,
            effect_strength,
            side_effects: Self::determine_side_effects(&fusion_effect),
        }
    }

    /// 确定融合效果
    fn determine_fusion_effect(attributes: &[(MaterialAttribute, f64)]) -> FusionEffect {
        if attributes.len() <= 1 {
            return FusionEffect::Neutral;
        }

        let mut harmony_score = 0.0;
        let mut conflict_score = 0.0;
        let total_pairs = attributes.len() * (attributes.len() - 1) / 2;

        // 分析所有属性对的相互作用
        for i in 0..attributes.len() {
            for j in (i + 1)..attributes.len() {
                let (attr1, weight1) = attributes[i];
                let (attr2, weight2) = attributes[j];
                let interaction = attr1.interaction_with(&attr2);
                let combined_weight = (weight1 * weight2).sqrt();

                match interaction {
                    AttributeInteraction::Resonance => harmony_score += combined_weight * 2.0,
                    AttributeInteraction::Enhancement => harmony_score += combined_weight * 1.5,
                    AttributeInteraction::Harmonization => harmony_score += combined_weight * 1.2,
                    AttributeInteraction::Neutral => {}
                    AttributeInteraction::Conflict => conflict_score += combined_weight * 1.5,
                    AttributeInteraction::Opposition => conflict_score += combined_weight * 2.0,
                }
            }
        }

        let net_score = harmony_score - conflict_score;
        let score_per_pair = net_score / total_pairs as f64;

        match score_per_pair {
            s if s >= 2.0 => FusionEffect::PerfectHarmony,
            s if s >= 1.0 => FusionEffect::Enhancement,
            s if s >= 0.5 => FusionEffect::Stabilization,
            s if s >= -0.5 => FusionEffect::Neutral,
            s if s >= -1.0 => FusionEffect::ConflictSuppression,
            s if s >= -2.0 => FusionEffect::Separation,
            _ => FusionEffect::Instability,
        }
    }

    /// 计算效果强度
    fn calculate_effect_strength(attributes: &[(MaterialAttribute, f64)]) -> f64 {
        let total_weight: f64 = attributes.iter().map(|(_, weight)| weight).sum();
        let max_single_weight = attributes
            .iter()
            .map(|(_, weight)| *weight)
            .fold(0.0, f64::max);

        // 基于属性多样性和权重分布计算强度
        let diversity = attributes.len() as f64;
        let concentration = max_single_weight / total_weight;

        (diversity.log2() + concentration).min(3.0).max(0.1)
    }

    /// 确定副作用
    fn determine_side_effects(fusion_effect: &FusionEffect) -> Vec<String> {
        match fusion_effect {
            FusionEffect::PerfectHarmony => vec!["属性纯化".to_string()],
            FusionEffect::Enhancement => vec!["能量增幅".to_string()],
            FusionEffect::Stabilization => vec!["性质稳定".to_string()],
            FusionEffect::Neutral => vec![],
            FusionEffect::ConflictSuppression => vec!["部分属性被压制".to_string()],
            FusionEffect::Separation => vec!["属性分层".to_string(), "效能不稳定".to_string()],
            FusionEffect::Instability => vec![
                "能量波动".to_string(),
                "不可预测效果".to_string(),
                "可能损坏设备".to_string(),
            ],
        }
    }
}

// ============================================================================
// 合成结果系统
// ============================================================================

/// 合成结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SynthesisResult {
    /// 是否成功
    pub success: bool,
    /// 产出材料
    pub output_materials: Vec<(Material, u32)>,
    /// 副产品
    pub byproducts: Vec<(Material, u32)>,
    /// 获得经验
    pub experience_gained: HashMap<String, u32>, // skill_name -> experience
    /// 属性融合结果
    pub attribute_fusion: Option<AttributeFusionResult>,
    /// 品质等级
    pub final_quality: MaterialGrade,
    /// 特殊效果
    pub special_effects: Vec<String>,
    /// 失败原因（如果失败）
    pub failure_reason: Option<String>,
    /// 结果描述
    pub description: String,
}

impl SynthesisResult {
    /// 创建成功的合成结果
    pub fn success(
        outputs: Vec<(Material, u32)>,
        experience: HashMap<String, u32>,
        quality: MaterialGrade,
        description: String,
    ) -> Self {
        Self {
            success: true,
            output_materials: outputs,
            byproducts: Vec::new(),
            experience_gained: experience,
            attribute_fusion: None,
            final_quality: quality,
            special_effects: Vec::new(),
            failure_reason: None,
            description,
        }
    }

    /// 创建失败的合成结果
    pub fn failure(reason: String, partial_experience: HashMap<String, u32>) -> Self {
        Self {
            success: false,
            output_materials: Vec::new(),
            byproducts: Vec::new(),
            experience_gained: partial_experience,
            attribute_fusion: None,
            final_quality: MaterialGrade::Mortal,
            special_effects: Vec::new(),
            failure_reason: Some(reason),
            description: "合成失败".to_string(),
        }
    }

    /// 添加副产品
    pub fn add_byproduct(&mut self, material: Material, quantity: u32) {
        self.byproducts.push((material, quantity));
    }

    /// 设置属性融合结果
    pub fn set_attribute_fusion(&mut self, fusion_result: AttributeFusionResult) {
        self.attribute_fusion = Some(fusion_result);
    }

    /// 添加特殊效果
    pub fn add_special_effect(&mut self, effect: String) {
        self.special_effects.push(effect);
    }
}

// ============================================================================
// 合成系统管理器
// ============================================================================

/// 合成系统管理器 - 管理所有合成相关功能
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct SynthesisManager {
    /// 所有配方
    pub recipes: HashMap<String, MaterialRecipe>,
    /// 进行中的合成活动
    pub active_syntheses: HashMap<String, SynthesisActivity>,
    /// 合成统计
    pub synthesis_statistics: SynthesisStatistics,
}

/// 合成统计
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct SynthesisStatistics {
    /// 总合成次数
    pub total_attempts: u32,
    /// 成功次数
    pub successful_attempts: u32,
    /// 按配方类型的统计
    pub attempts_by_type: HashMap<RecipeType, (u32, u32)>, // (attempts, successes)
    /// 经验获得统计
    pub experience_by_skill: HashMap<String, u32>,
}

impl SynthesisManager {
    /// 创建新的合成管理器
    pub fn new() -> Self {
        Self::default()
    }

    /// 添加配方
    pub fn add_recipe(&mut self, recipe: MaterialRecipe) {
        self.recipes.insert(recipe.id.clone(), recipe);
    }

    /// 获取配方
    pub fn get_recipe(&self, recipe_id: &str) -> Option<&MaterialRecipe> {
        self.recipes.get(recipe_id)
    }

    /// 按类型获取配方
    pub fn get_recipes_by_type(&self, recipe_type: RecipeType) -> Vec<&MaterialRecipe> {
        self.recipes
            .values()
            .filter(|recipe| recipe.recipe_type == recipe_type)
            .collect()
    }

    /// 开始合成活动
    pub fn start_synthesis(
        &mut self,
        synthesizer_id: String,
        recipe_id: String,
        materials: HashMap<String, (Material, u32)>,
        tools: Vec<String>,
    ) -> Result<String, String> {
        let recipe = self.get_recipe(&recipe_id).ok_or("配方不存在")?;

        // 验证材料需求
        self.validate_materials(recipe, &materials)?;

        // 创建合成活动
        let activity_id = format!("synthesis_{}_{}", synthesizer_id, Utc::now().timestamp());
        let start_time = Utc::now();
        let end_time = start_time + chrono::Duration::minutes(recipe.crafting_time as i64);

        let activity = SynthesisActivity {
            id: activity_id.clone(),
            synthesizer_id,
            recipe_id,
            input_materials: materials,
            tools_used: tools,
            start_time,
            expected_end_time: end_time,
            current_stage: SynthesisStage::Preparation,
            status: SynthesisStatus::Preparing,
            progress: 0.0,
            quality_modifiers: Vec::new(),
        };

        self.active_syntheses.insert(activity_id.clone(), activity);
        Ok(activity_id)
    }

    /// 验证材料需求
    fn validate_materials(
        &self,
        recipe: &MaterialRecipe,
        materials: &HashMap<String, (Material, u32)>,
    ) -> Result<(), String> {
        // 检查主要材料
        for ingredient in &recipe.primary_materials {
            if let Some((material, quantity)) = materials.get(&ingredient.material_id) {
                if *quantity < ingredient.quantity {
                    return Err(format!("材料 {} 数量不足", material.name));
                }

                if let Some(min_grade) = &ingredient.min_grade {
                    if material.grade < *min_grade {
                        return Err(format!("材料 {} 品阶不足", material.name));
                    }
                }
            } else {
                return Err(format!("缺少必需材料: {}", ingredient.material_id));
            }
        }

        Ok(())
    }

    /// 更新合成进度
    pub fn update_synthesis_progress(&mut self, activity_id: &str) -> Option<SynthesisStatus> {
        if let Some(activity) = self.active_syntheses.get_mut(activity_id) {
            let current_time = Utc::now();

            if current_time >= activity.expected_end_time {
                activity.status = SynthesisStatus::Completed;
                activity.progress = 1.0;
                activity.current_stage = SynthesisStage::Completed;
            } else {
                let elapsed = current_time - activity.start_time;
                let total_duration = activity.expected_end_time - activity.start_time;
                activity.progress =
                    elapsed.num_seconds() as f64 / total_duration.num_seconds() as f64;

                // 更新当前阶段
                activity.current_stage = Self::determine_current_stage(activity.progress);
            }

            Some(activity.status)
        } else {
            None
        }
    }

    /// 确定当前阶段
    fn determine_current_stage(progress: f64) -> SynthesisStage {
        match progress {
            p if p < 0.1 => SynthesisStage::Preparation,
            p if p < 0.25 => SynthesisStage::MaterialProcessing,
            p if p < 0.65 => SynthesisStage::MainSynthesis,
            p if p < 0.85 => SynthesisStage::AttributeFusion,
            p if p < 0.95 => SynthesisStage::QualityEnhancement,
            p if p < 1.0 => SynthesisStage::Finalization,
            _ => SynthesisStage::Completed,
        }
    }

    /// 完成合成
    pub fn complete_synthesis(&mut self, activity_id: &str) -> Result<SynthesisResult, String> {
        let activity = self
            .active_syntheses
            .remove(activity_id)
            .ok_or("合成活动不存在")?;

        let recipe = self
            .get_recipe(&activity.recipe_id)
            .ok_or("配方不存在")?
            .clone();

        // 计算成功率
        let success_rate = self.calculate_success_rate(&activity, &recipe);
        let is_success = rand::random::<f64>() < success_rate;

        // 保存配方类型用于后续使用
        let recipe_type = recipe.recipe_type;

        // 更新统计
        self.synthesis_statistics.total_attempts += 1;
        if is_success {
            self.synthesis_statistics.successful_attempts += 1;
        }

        let type_stats = self
            .synthesis_statistics
            .attempts_by_type
            .entry(recipe_type)
            .or_insert((0, 0));
        type_stats.0 += 1;
        if is_success {
            type_stats.1 += 1;
        }

        if is_success {
            self.create_success_result(&activity, &recipe)
        } else {
            Ok(SynthesisResult::failure(
                "合成过程中发生意外".to_string(),
                HashMap::new(),
            ))
        }
    }

    /// 计算成功率
    fn calculate_success_rate(&self, activity: &SynthesisActivity, recipe: &MaterialRecipe) -> f64 {
        let mut success_rate = recipe.base_success_rate;

        // 材料品质加成
        for (material, _) in activity.input_materials.values() {
            let grade_bonus = match material.grade {
                MaterialGrade::Mortal => 0.0,
                MaterialGrade::Spiritual => 0.05,
                MaterialGrade::Immortal => 0.1,
                MaterialGrade::Divine => 0.15,
                MaterialGrade::Sacred => 0.2,
                MaterialGrade::Chaos => 0.25,
            };
            success_rate += grade_bonus;
        }

        // 属性相性加成
        let materials: Vec<_> = activity
            .input_materials
            .values()
            .map(|(material, quantity)| (material.clone(), *quantity))
            .collect();
        let fusion_result = AttributeInteractionAnalyzer::analyze_materials(&materials);
        success_rate *= fusion_result.fusion_effect.quality_impact();

        success_rate.clamp(0.05, 0.95)
    }

    /// 创建成功结果
    fn create_success_result(
        &self,
        activity: &SynthesisActivity,
        recipe: &MaterialRecipe,
    ) -> Result<SynthesisResult, String> {
        let mut outputs = Vec::new();

        // 生成产出物
        for output in &recipe.outputs {
            if rand::random::<f64>() < output.probability {
                let quantity = output.base_quantity
                    + rand::random::<u32>()
                        % (output.quantity_variation.1 - output.quantity_variation.0 + 1)
                    + output.quantity_variation.0;

                // 根据material_id创建实际的Material对象
                let output_material = self.create_output_material(
                    &output.material_id,
                    &activity.input_materials,
                    recipe.recipe_type,
                );
                outputs.push((output_material, quantity));
            }
        }

        // 生成经验奖励
        let mut experience = HashMap::new();
        for skill in recipe.required_skills.keys() {
            experience.insert(skill.clone(), 100); // 基础经验值
        }

        let quality = MaterialGrade::Spiritual; // 暂时设定

        Ok(SynthesisResult::success(
            outputs,
            experience,
            quality,
            format!("成功炼制 {}", recipe.name),
        ))
    }

    /// 创建输出材料
    fn create_output_material(
        &self,
        material_id: &str,
        input_materials: &HashMap<String, (Material, u32)>,
        recipe_type: RecipeType,
    ) -> Material {
        // 根据材料ID和配方类型创建输出材料
        match material_id {
            "basic_healing_pill" => Material::new(
                "basic_healing_pill".to_string(),
                "基础治疗丹".to_string(),
                "能够快速恢复伤势的基础丹药".to_string(),
                MaterialGrade::Mortal,
                MaterialAttribute::Elemental(ElementalAttribute::Wood),
            ),
            "spiritual_enhancement_pill" => Material::new(
                "spiritual_enhancement_pill".to_string(),
                "增灵丹".to_string(),
                "提升修为者灵气运转速度的丹药".to_string(),
                MaterialGrade::Spiritual,
                MaterialAttribute::Elemental(ElementalAttribute::Metal),
            ),
            "iron_sword" => Material::new(
                "iron_sword".to_string(),
                "铁剑".to_string(),
                "由精铁锻造而成的利剑".to_string(),
                MaterialGrade::Mortal,
                MaterialAttribute::Elemental(ElementalAttribute::Metal),
            ),
            "spirit_sword" => Material::new(
                "spirit_sword".to_string(),
                "灵剑".to_string(),
                "注入灵气的特殊兵器，具有神奇力量".to_string(),
                MaterialGrade::Spiritual,
                MaterialAttribute::Elemental(ElementalAttribute::Metal),
            ),
            "basic_protection_formation" => Material::new(
                "basic_protection_formation".to_string(),
                "基础防护阵法".to_string(),
                "能够提供基础防护的阵法石".to_string(),
                MaterialGrade::Mortal,
                MaterialAttribute::Elemental(ElementalAttribute::Earth),
            ),
            "spirit_gathering_formation" => Material::new(
                "spirit_gathering_formation".to_string(),
                "聚灵阵".to_string(),
                "能够聚集天地灵气的高级阵法".to_string(),
                MaterialGrade::Spiritual,
                MaterialAttribute::Chaos,
            ),
            "fire_talisman" => Material::new(
                "fire_talisman".to_string(),
                "火焰符".to_string(),
                "能够释放火焰攻击的符箸".to_string(),
                MaterialGrade::Mortal,
                MaterialAttribute::Elemental(ElementalAttribute::Fire),
            ),
            "thunder_talisman" => Material::new(
                "thunder_talisman".to_string(),
                "雷电符".to_string(),
                "能够召唤雷电攻击的强力符箸".to_string(),
                MaterialGrade::Spiritual,
                MaterialAttribute::Elemental(ElementalAttribute::Fire),
            ),
            _ => {
                // 根据输入材料和配方类型生成通用结果
                let base_grade = self.determine_output_grade(input_materials);
                let primary_attribute =
                    self.determine_output_attribute(input_materials, recipe_type);

                let (name, description) = match recipe_type {
                    RecipeType::Alchemy => {
                        ("炼制丹药".to_string(), "通过炼丹术制作的丹药".to_string())
                    }
                    RecipeType::Crafting => {
                        ("锻造器具".to_string(), "通过锻造术制作的器具".to_string())
                    }
                    RecipeType::Formation => {
                        ("阵法石".to_string(), "布置阵法的核心器具".to_string())
                    }
                    RecipeType::Talisman => ("符箸".to_string(), "具有特殊效果的符箸".to_string()),
                    RecipeType::Refinement => {
                        ("精炼材料".to_string(), "经过精炼的高品质材料".to_string())
                    }
                    RecipeType::Fusion => {
                        ("融合材料".to_string(), "多种材料融合后的结果".to_string())
                    }
                };

                Material::new(
                    material_id.to_string(),
                    name,
                    description,
                    base_grade,
                    primary_attribute,
                )
            }
        }
    }

    /// 确定输出品阶
    fn determine_output_grade(
        &self,
        input_materials: &HashMap<String, (Material, u32)>,
    ) -> MaterialGrade {
        let mut max_grade = MaterialGrade::Mortal;
        let mut total_weight = 0u32;
        let mut grade_weighted_sum = 0u32;

        for (material, quantity) in input_materials.values() {
            let grade_value = match material.grade {
                MaterialGrade::Mortal => 1,
                MaterialGrade::Spiritual => 2,
                MaterialGrade::Immortal => 3,
                MaterialGrade::Divine => 4,
                MaterialGrade::Sacred => 5,
                MaterialGrade::Chaos => 6,
            };

            grade_weighted_sum += grade_value * quantity;
            total_weight += quantity;

            if material.grade > max_grade {
                max_grade = material.grade;
            }
        }

        // 计算加权平均品阶
        let avg_grade_value = if total_weight > 0 {
            grade_weighted_sum / total_weight
        } else {
            1
        };

        let output_grade = match avg_grade_value {
            1 => MaterialGrade::Mortal,
            2 => MaterialGrade::Spiritual,
            3 => MaterialGrade::Immortal,
            4 => MaterialGrade::Divine,
            5 => MaterialGrade::Sacred,
            _ => MaterialGrade::Chaos,
        };

        // 输出品阶不能超过最高输入品阶
        if output_grade > max_grade {
            max_grade
        } else {
            output_grade
        }
    }

    /// 确定输出属性
    fn determine_output_attribute(
        &self,
        input_materials: &HashMap<String, (Material, u32)>,
        recipe_type: RecipeType,
    ) -> MaterialAttribute {
        let materials: Vec<_> = input_materials
            .values()
            .map(|(material, quantity)| (material.clone(), *quantity))
            .collect();

        let fusion_result = AttributeInteractionAnalyzer::analyze_materials(&materials);

        // 根据融合结果和配方类型确定最终属性
        if !fusion_result.fused_attributes.is_empty() {
            fusion_result.fused_attributes[0]
        } else {
            // 根据配方类型返回默认属性
            match recipe_type {
                RecipeType::Alchemy => MaterialAttribute::Elemental(ElementalAttribute::Wood),
                RecipeType::Crafting => MaterialAttribute::Elemental(ElementalAttribute::Metal),
                RecipeType::Formation => MaterialAttribute::Elemental(ElementalAttribute::Earth),
                RecipeType::Talisman => MaterialAttribute::Elemental(ElementalAttribute::Fire),
                RecipeType::Refinement => MaterialAttribute::Chaos,
                RecipeType::Fusion => MaterialAttribute::Chaos,
            }
        }
    }
}
