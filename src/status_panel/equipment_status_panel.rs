use crate::equipment::Equipment;
use crate::status_panel::StatusPanel;
use std::collections::HashMap;

impl StatusPanel for Equipment {
    fn print_status(&self) {
        println!("装备：{}", self.name);
        println!("类型：{:?}", self.eq_type);
        println!("属性：");
        for (k, v) in &self.base_attributes.attributes {
            println!("  {:?}: {}", k, v.value);
        }
    }

    fn get_status_data(&self) -> HashMap<String, serde_json::Value> {
        let mut data = HashMap::new();
        
        // 基本信息
        data.insert("id".to_string(), serde_json::Value::Number(serde_json::Number::from(self.id)));
        data.insert("name".to_string(), serde_json::Value::String(self.name.clone()));
        data.insert("type".to_string(), serde_json::Value::String(format!("{:?}", self.eq_type)));

        // 属性信息
        let attributes: HashMap<String, serde_json::Value> = self.base_attributes.attributes
            .iter()
            .map(|(k, v)| (format!("{:?}", k), serde_json::Value::Number(serde_json::Number::from_f64(v.value).unwrap())))
            .collect();
        data.insert("attributes".to_string(), serde_json::Value::Object(attributes.into_iter().collect()));

        data
    }
}

