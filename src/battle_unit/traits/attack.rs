use super::health::HealthSystem;
use super::resistance::ResistanceSystem;
use super::spatial::SpatialEntity;
use crate::battle_unit::{AttackResult, AttackType, DamageCalculation, WeaponInfo};
/// 攻击能力trait
/// 
/// 处理各种攻击相关的能力，包括伤害计算和攻击执行

use crate::shared::*;

/// 攻击能力 - 处理各种攻击相关的能力
pub trait AttackCapabilities {
    /// 获取基础攻击力
    fn base_attack_power(&self) -> Attack;
    
    /// 获取当前攻击力（考虑装备和状态）
    fn current_attack_power(&self) -> Attack;
    
    /// 获取攻击类型
    fn attack_type(&self) -> AttackType;
    
    /// 获取攻击范围
    fn attack_range(&self) -> Range;
    
    /// 获取攻击速度
    fn attack_speed(&self) -> Speed;
    
    /// 执行基础攻击
    fn basic_attack(&mut self, target: &mut dyn HealthSystem) -> GameResult<AttackResult>;
    
    /// 计算攻击伤害
    fn calculate_damage(&self, target: &dyn ResistanceSystem) -> DamageCalculation;
    
    /// 检查是否能攻击目标
    fn can_attack_target<T: SpatialEntity>(&self, target: &T) -> bool;
    
    /// 获取武器信息
    fn weapon_info(&self) -> Option<WeaponInfo>;
    
    /// 检查是否为暴击
    fn is_critical_hit(&self) -> bool;
    
    /// 获取连击次数
    fn combo_count(&self) -> u32;
}