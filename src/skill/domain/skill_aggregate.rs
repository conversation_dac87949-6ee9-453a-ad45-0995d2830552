use super::entities::*;
/// 技能聚合根
///
/// 技能聚合负责：
/// - 技能的基本信息管理
/// - 技能等级和成长
/// - 冷却状态管理
/// - 效果组合和执行
/// - 业务规则验证
use super::value_objects::*;
use crate::material::{CollectionSkill, SkillLevel};
use crate::shared::{GameError, GameResult};
use crate::world_map::{AccessCondition, AccessContext, PlayerId};
use crate::{BattleUnitId, Exp, Health, Level, Mana, Position, ResourceCost, SkillId};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 技能聚合根
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Skill {
    /// 技能唯一标识
    id: SkillId,

    /// 技能基础信息
    name: String,
    description: String,

    /// 技能分类和标签
    category: SkillCategory,
    tags: Vec<SkillTag>,

    /// 技能等级信息
    level: SkillLevel,
    max_level: Level,

    /// 效果组合
    effects: SkillEffectComposition,

    /// 目标定位信息
    targeting: TargetingInfo,

    /// 资源消耗
    resource_cost: ResourceCost,

    /// 冷却信息
    cooldown_base: f64,

    /// 学习要求
    requirements: Vec<AccessCondition>,

    /// 版本控制（并发安全）
    version: u64,
}

impl Skill {
    /// 创建新技能
    pub fn new(id: SkillId, name: String, description: String, category: SkillCategory) -> Self {
        Self {
            id,
            name,
            description,
            category,
            tags: Vec::new(),
            //TODO: CollectionSkill 这个初始化有问题
            level: SkillLevel::new(CollectionSkill::Appraisal),
            max_level: 10, // 默认最大等级
            effects: SkillEffectComposition::new(),
            targeting: TargetingInfo::default(),
            resource_cost: ResourceCost::none(),
            cooldown_base: 1.0,
            requirements: Vec::new(),
            version: 1,
        }
    }

    // ============================================================================
    // 聚合标识和基本信息
    // ============================================================================

    pub fn id(&self) -> SkillId {
        self.id
    }

    pub fn name(&self) -> &str {
        &self.name
    }

    pub fn description(&self) -> &str {
        &self.description
    }

    pub fn category(&self) -> SkillCategory {
        self.category
    }

    pub fn version(&self) -> u64 {
        self.version
    }

    // ============================================================================
    // 技能等级管理
    // ============================================================================

    pub fn level(&self) -> &SkillLevel {
        &self.level
    }

    pub fn current_level(&self) -> Level {
        self.level.current()
    }

    pub fn max_level(&self) -> Level {
        self.max_level
    }

    /// 技能升级
    pub fn level_up(&mut self) -> GameResult<()> {
        if self.level.current() >= self.max_level {
            return Err(GameError::validation_error(
                "skill_level",
                "技能已达到最大等级",
            ));
        }

        self.level.level_up();
        self.version += 1;

        // 触发技能升级事件
        // TODO: 发布领域事件

        Ok(())
    }

    /// 检查是否可以升级
    pub fn can_level_up(&self) -> bool {
        self.level.current() < self.max_level
    }

    /// 增加技能经验
    pub fn gain_experience(&mut self, amount: Exp) -> GameResult<bool> {
        let leveled_up = self.level.add_experience(amount);
        if leveled_up {
            self.version += 1;
        }
        Ok(leveled_up)
    }

    // ============================================================================
    // 效果管理
    // ============================================================================

    pub fn effects(&self) -> &SkillEffectComposition {
        &self.effects
    }

    /// 添加技能效果
    pub fn add_effect(&mut self, effect: SkillEffect) -> GameResult<()> {
        self.effects.add_effect(effect)?;
        self.version += 1;
        Ok(())
    }

    /// 移除技能效果
    pub fn remove_effect(&mut self, effect_id: &str) -> GameResult<bool> {
        let removed = self.effects.remove_effect(effect_id)?;
        if removed {
            self.version += 1;
        }
        Ok(removed)
    }

    /// 获取当前等级的效果强度
    pub fn get_effect_power(&self, effect_id: &str) -> f64 {
        self.effects
            .get_effect_power(effect_id, self.level.current())
    }

    // ============================================================================
    // 目标和范围
    // ============================================================================

    pub fn targeting(&self) -> &TargetingInfo {
        &self.targeting
    }

    pub fn set_targeting(&mut self, targeting: TargetingInfo) {
        self.targeting = targeting;
        self.version += 1;
    }

    /// 检查目标是否有效
    pub fn is_valid_target(&self, caster_pos: Position, target_pos: Position) -> bool {
        let distance = caster_pos.distance_to(&target_pos);
        distance <= self.targeting.range()
    }

    /// 获取技能作用范围内的位置
    pub fn get_affected_positions(
        &self,
        caster_pos: Position,
        target_pos: Position,
    ) -> Vec<Position> {
        self.targeting
            .get_affected_positions(caster_pos, target_pos)
    }

    // ============================================================================
    // 资源消耗
    // ============================================================================

    pub fn resource_cost(&self) -> &ResourceCost {
        &self.resource_cost
    }

    pub fn set_resource_cost(&mut self, cost: ResourceCost) {
        self.resource_cost = cost;
        self.version += 1;
    }

    /// 获取当前等级的资源消耗
    pub fn get_current_cost(&self) -> ResourceCost {
        self.resource_cost.scaled_by_level(self.level.current())
    }

    // ============================================================================
    // 冷却管理
    // ============================================================================

    pub fn base_cooldown(&self) -> f64 {
        self.cooldown_base
    }

    pub fn set_cooldown(&mut self, cooldown: f64) {
        self.cooldown_base = cooldown;
        self.version += 1;
    }

    /// 获取当前等级的冷却时间
    pub fn get_current_cooldown(&self) -> f64 {
        // 高等级技能冷却时间可能会减少
        let level_reduction = (self.level.current() - 1) as f64 * 0.05; // 每级减少5%
        self.cooldown_base * (1.0 - level_reduction).max(0.5) // 最多减少50%
    }

    // ============================================================================
    // 学习要求
    // ============================================================================

    pub fn requirements(&self) -> &[AccessCondition] {
        &self.requirements
    }

    pub fn add_requirement(&mut self, requirement: AccessCondition) {
        self.requirements.push(requirement);
        self.version += 1;
    }

    /// 检查是否满足学习要求
    pub fn check_requirements(
        &self,
        character_level: Level,
        learned_skills: &[SkillId],
    ) -> GameResult<()> {
        for requirement in &self.requirements {
            requirement.check(&AccessContext::new(
                PlayerId::new(1),
                // TODO: 填充其他上下文信息
            ));
        }
        Ok(())
    }

    // ============================================================================
    // 标签管理
    // ============================================================================

    pub fn tags(&self) -> &[SkillTag] {
        &self.tags
    }

    pub fn add_tag(&mut self, tag: SkillTag) {
        if !self.tags.contains(&tag) {
            self.tags.push(tag);
            self.version += 1;
        }
    }

    pub fn remove_tag(&mut self, tag: &SkillTag) -> bool {
        if let Some(pos) = self.tags.iter().position(|t| t == tag) {
            self.tags.remove(pos);
            self.version += 1;
            true
        } else {
            false
        }
    }

    pub fn has_tag(&self, tag: &SkillTag) -> bool {
        self.tags.contains(tag)
    }

    // ============================================================================
    // 业务规则验证
    // ============================================================================

    /// 验证技能数据的一致性
    pub fn validate(&self) -> GameResult<()> {
        // 检查基本信息
        if self.name.trim().is_empty() {
            return Err(GameError::validation_error("name", "技能名称不能为空"));
        }

        // 检查等级范围
        if self.level.current() > self.max_level {
            return Err(GameError::validation_error(
                "level",
                "当前等级不能超过最大等级",
            ));
        }

        // 检查效果组合
        self.effects.validate()?;

        // 检查目标信息
        self.targeting.validate()?;

        // 检查资源消耗
        self.resource_cost.validate()?;

        Ok(())
    }

    /// 检查技能是否可以对指定目标使用
    pub fn can_use_on_target(
        &self,
        caster_pos: Position,
        target_pos: Option<Position>,
        caster_resources: &ResourceState,
    ) -> GameResult<()> {
        // 检查资源消耗
        let cost = self.get_current_cost();
        if !caster_resources.can_afford(&cost) {
            return Err(GameError::insufficient_mana_error(
                cost.mana_cost,
                caster_resources.mana,
            ));
        }

        // 检查目标位置
        if let Some(target_pos) = target_pos {
            if !self.is_valid_target(caster_pos, target_pos) {
                return Err(GameError::validation_error(
                    "target_range",
                    "目标超出技能作用范围",
                ));
            }
        } else if self.targeting.requires_target() {
            return Err(GameError::validation_error(
                "target_required",
                "该技能需要指定目标",
            ));
        }

        Ok(())
    }

    // ============================================================================
    // 技能使用
    // ============================================================================

    /// 创建技能实例（用于实际执行）
    pub fn create_instance(
        &self,
        caster_id: BattleUnitId,
        target_pos: Option<Position>,
    ) -> GameResult<SkillInstance> {
        // 验证技能可用性（这里可以扩展更多验证逻辑）
        self.validate()?;

        Ok(SkillInstance::new(
            self.id,
            caster_id,
            target_pos,
            self.level.current(),
            self.get_current_cooldown(),
            self.get_current_cost(),
            vec![self.effects.clone()],
        ))
    }

    // ============================================================================
    // 辅助方法
    // ============================================================================

    /// 获取技能的完整状态信息
    pub fn get_status(&self) -> SkillStatus {
        SkillStatus {
            id: self.id,
            name: self.name.clone(),
            level: self.level.current(),
            max_level: self.max_level,
            category: self.category,
            cooldown: self.get_current_cooldown(),
            cost: self.get_current_cost(),
            tags: self.tags.clone(),
            effect_count: self.effects.effect_count(),
            version: self.version,
        }
    }

    /// 克隆技能（用于技能模板）
    pub fn clone_as_template(&self) -> Self {
        let mut template = self.clone();
        // template.level = None; // 重置为1级
        template.version = 1;
        template
    }
}

// ============================================================================
// 相关数据结构
// ============================================================================

/// 技能分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SkillCategory {
    /// 攻击技能
    Offensive,
    /// 防御技能
    Defensive,
    /// 辅助技能
    Support,
    /// 移动技能
    Movement,
    /// 召唤技能
    Summoning,
    /// 变形技能
    Transformation,
    /// 被动技能
    Passive,
    /// 特殊技能
    Special,
}

/// 技能标签
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SkillTag {
    /// 即时释放
    Instant,
    /// 引导技能
    Channeled,
    /// 持续施法
    Concentration,
    /// 范围攻击
    AreaOfEffect,
    /// 单体目标
    SingleTarget,
    /// 自我增强
    SelfBuff,
    /// 团队增益
    TeamBuff,
    /// 控制技能
    CrowdControl,
    /// 治疗技能
    Healing,
    /// 伤害技能
    Damage,
    /// 元素技能
    Elemental(ElementType),
    /// 自定义标签
    Custom(String),
}

/// 技能状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillStatus {
    pub id: SkillId,
    pub name: String,
    pub level: Level,
    pub max_level: Level,
    pub category: SkillCategory,
    pub cooldown: f64,
    pub cost: ResourceCost,
    pub tags: Vec<SkillTag>,
    pub effect_count: usize,
    pub version: u64,
}

/// 资源状态（用于检查技能使用条件）
#[derive(Debug, Clone)]
pub struct ResourceState {
    pub health: Health,
    pub mana: Mana,
    pub stamina: Mana, // 复用Mana类型表示体力
    pub special_resources: HashMap<String, i32>,
}

impl ResourceState {
    pub fn new(health: Health, mana: Mana, stamina: Mana) -> Self {
        Self {
            health,
            mana,
            stamina,
            special_resources: HashMap::new(),
        }
    }

    pub fn can_afford(&self, cost: &ResourceCost) -> bool {
        self.health >= cost.health_cost
            && self.mana >= cost.mana_cost
            && self.stamina >= cost.stamina_cost
    }
}

// ============================================================================
// 构建器模式
// ============================================================================

/// 技能构建器
pub struct SkillBuilder {
    skill: Skill,
}

impl SkillBuilder {
    pub fn new(id: SkillId, name: impl Into<String>, category: SkillCategory) -> Self {
        Self {
            skill: Skill::new(id, name.into(), String::new(), category),
        }
    }

    pub fn with_description(mut self, description: impl Into<String>) -> Self {
        self.skill.description = description.into();
        self
    }

    pub fn with_max_level(mut self, max_level: Level) -> Self {
        self.skill.max_level = max_level;
        self
    }

    pub fn with_cooldown(mut self, cooldown: f64) -> Self {
        self.skill.cooldown_base = cooldown;
        self
    }

    pub fn with_resource_cost(mut self, cost: ResourceCost) -> Self {
        self.skill.resource_cost = cost;
        self
    }

    pub fn with_targeting(mut self, targeting: TargetingInfo) -> Self {
        self.skill.targeting = targeting;
        self
    }

    pub fn with_effect(mut self, effect: SkillEffect) -> Self {
        self.skill.effects.add_effect(effect).unwrap(); // 构建时假设一定成功
        self
    }

    pub fn with_tag(mut self, tag: SkillTag) -> Self {
        self.skill.add_tag(tag);
        self
    }

    pub fn with_requirement(mut self, requirement: SkillRequirement) -> Self {
        // self.skill.add_requirement(requirement);
        self
    }

    pub fn build(self) -> GameResult<Skill> {
        self.skill.validate()?;
        Ok(self.skill)
    }
}
