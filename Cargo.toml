[package]
name = "game"
version = "0.1.0"
edition = "2021"

[dependencies]
futures = "0.3"
tokio = { version = "1", features = ["full"] }
rand = "0.8"
crossbeam-queue = "0.3"
parking_lot = "0.12"
once_cell = "1.18"
# json操作
serde_json = "1.0"
# 序列化与反序列化
serde = { version = "1.0", features = ["derive"] }
# 枚举增强
strum = "0.25"
strum_macros = "0.25"
# TOML配置文件支持
toml = "0.8"

# 统一事件系统依赖
async-trait = "0.1"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
log = "0.4"
hostname = "0.3"
local-ip-address = "0.2"
regex = "1.0"

[dev-dependencies]
env_logger = "0.10"

[[example]]
name = "battle_demo_systems"
path = "src/bin/battle_demo_systems.rs"

[[example]]
name = "equipment_demo"
path = "src/bin/equipment_demo.rs"

[[example]]
name = "experience_system_demo"
path = "src/bin/experience_system_demo.rs"

[[example]]
name = "material_system_demo"
path = "src/bin/material_system_demo.rs"

[[example]]
name = "skill_system_test"
path = "src/bin/skill_system_test.rs"

[[example]]
name = "status_effect_test"
path = "src/bin/status_effect_test.rs"

[[example]]
name = "world_map_demo"
path = "src/bin/world_map_demo.rs"