use serde::{Deserialize, Serialize};
/// 统一错误处理系统
///
/// 此模块提供了游戏系统中所有错误的统一定义和处理，
/// 遵循错误处理的最佳实践，支持错误链和上下文信息
use thiserror::Error;

/// 游戏系统的统一错误类型
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum GameError {
    // ============================================================================
    // 战斗系统错误
    // ============================================================================
    /// 不可变引用修改错误
    #[error("尝试修改不可变引用: {operation}")]
    ImmutableModification { operation: String },

    /// 从不可变引用获取可变借用错误
    #[error("尝试从不可变引用获取可变借用: {field}")]
    MutableBorrowFromImmutable { field: String },

    /// 无效目标错误
    #[error("无效的目标: {target_id}")]
    InvalidTarget { target_id: String },

    /// 技能冷却中错误
    #[error("技能 {skill_id} 仍在冷却中，剩余时间: {remaining_time}秒")]
    SkillOnCooldown {
        skill_id: String,
        remaining_time: f64,
    },

    /// 法力不足错误
    #[error("法力不足，需要 {required}，当前 {current}")]
    InsufficientMana { required: i32, current: i32 },

    /// 战利品规则克隆错误
    #[error("战利品规则克隆失败: {reason}")]
    LootRuleCloneError { reason: String },

    // ============================================================================
    // 角色系统错误
    // ============================================================================
    /// 角色未找到
    #[error("角色未找到: {character_id}")]
    CharacterNotFound { character_id: String },

    /// 战斗单位未找到
    #[error("战斗单位未找到: {unit_id}")]
    UnitNotFound { unit_id: String },

    /// 角色等级不足
    #[error("角色等级不足，需要等级 {required}，当前等级 {current}")]
    InsufficientLevel { required: u32, current: u32 },

    /// 经验值溢出
    #[error("经验值溢出，尝试添加 {amount}")]
    ExperienceOverflow { amount: u32 },

    // ============================================================================
    // 材料系统错误
    // ============================================================================
    /// 材料未找到
    #[error("材料未找到: {material_id}")]
    MaterialNotFound { material_id: String },

    /// 材料品质不匹配
    #[error("材料品质不匹配，期望 {expected}，实际 {actual}")]
    MaterialGradeMismatch { expected: String, actual: String },

    /// 材料合成失败
    #[error("材料合成失败: {reason}")]
    MaterialSynthesisFailed { reason: String },

    /// 材料堆叠超限
    #[error("材料 {material_id} 堆叠超限，最大 {max_stack}，尝试堆叠 {attempted}")]
    MaterialStackOverflow {
        material_id: String,
        max_stack: u32,
        attempted: u32,
    },

    // ============================================================================
    // 装备系统错误
    // ============================================================================
    /// 装备未找到
    #[error("装备未找到: {equipment_id}")]
    EquipmentNotFound { equipment_id: String },

    /// 装备类型不匹配
    #[error("装备类型不匹配，期望 {expected}，实际 {actual}")]
    EquipmentTypeMismatch { expected: String, actual: String },

    /// 装备已损坏
    #[error("装备 {equipment_id} 已损坏，无法使用")]
    EquipmentBroken { equipment_id: String },

    // ============================================================================
    // 技能系统错误
    // ============================================================================
    /// 技能错误
    #[error("技能错误 [{skill_id}]: {message}")]
    SkillError { skill_id: String, message: String },

    /// 技能未找到
    #[error("技能未找到: {skill_id}")]
    SkillNotFound { skill_id: String },

    /// 技能释放条件不满足
    #[error("技能 {skill_id} 释放条件不满足: {condition}")]
    SkillConditionNotMet { skill_id: u32, condition: String },

    /// 技能目标无效
    #[error("技能 {skill_id} 的目标无效: {reason}")]
    SkillInvalidTarget { skill_id: u32, reason: String },

    // ============================================================================
    // 世界地图系统错误
    // ============================================================================
    /// 位置无效
    #[error("位置无效: ({x}, {y})")]
    InvalidPosition { x: f32, y: f32 },

    /// 位置被占用
    #[error("位置被占用: ({x}, {y})")]
    PositionOccupied { x: f32, y: f32 },

    /// 移动距离过远
    #[error("移动距离过远，最大距离 {max_distance}，尝试移动 {attempted_distance}")]
    MovementTooFar {
        max_distance: f32,
        attempted_distance: f32,
    },

    // ============================================================================
    // 存储系统错误
    // ============================================================================
    /// 存储空间不足
    #[error("存储空间不足，需要 {required} 格，可用 {available} 格")]
    InsufficientStorage { required: u32, available: u32 },

    /// 物品不存在
    #[error("物品不存在: {item_id}")]
    ItemNotFound { item_id: String },

    /// 物品数量不足
    #[error("物品 {item_id} 数量不足，需要 {required}，拥有 {available}")]
    InsufficientItem {
        item_id: String,
        required: u32,
        available: u32,
    },

    // ============================================================================
    // 配置和数据错误
    // ============================================================================
    /// 配置文件错误
    #[error("配置文件错误: {path} - {reason}")]
    ConfigError { path: String, reason: String },

    /// 数据序列化错误
    #[error("数据序列化错误: {reason}")]
    SerializationError { reason: String },

    /// 数据反序列化错误
    #[error("数据反序列化错误: {reason}")]
    DeserializationError { reason: String },

    /// 数据验证错误
    #[error("数据验证错误: {field} - {reason}")]
    ValidationError { field: String, reason: String },

    // ============================================================================
    // 系统级错误
    // ============================================================================
    /// 内存不足
    #[error("内存不足")]
    OutOfMemory,

    /// 网络错误
    #[error("网络错误: {reason}")]
    NetworkError { reason: String },

    /// 数据库错误
    #[error("数据库错误: {reason}")]
    DatabaseError { reason: String },

    /// 文件系统错误
    #[error("文件系统错误: {path} - {reason}")]
    FileSystemError { path: String, reason: String },

    /// 权限错误
    #[error("权限不足: {operation}")]
    PermissionDenied { operation: String },

    /// 未实现的功能
    #[error("功能未实现: {feature}")]
    NotImplemented { feature: String },

    /// 内部错误（不应该发生的错误）
    #[error("内部错误: {message}")]
    InternalError { message: String },
}

/// 游戏结果类型，统一使用GameError
pub type GameResult<T> = Result<T, GameError>;

// ============================================================================
// 错误构造辅助函数
// ============================================================================

impl GameError {
    /// 创建不可变修改错误
    pub fn immutable_modification_error(operation: impl Into<String>) -> Self {
        Self::ImmutableModification {
            operation: operation.into(),
        }
    }

    /// 创建可变借用错误
    pub fn mutable_borrow_error(field: impl Into<String>) -> Self {
        Self::MutableBorrowFromImmutable {
            field: field.into(),
        }
    }

    /// 创建无效目标错误
    pub fn invalid_target_error(target_id: impl Into<String>) -> Self {
        Self::InvalidTarget {
            target_id: target_id.into(),
        }
    }

    /// 创建技能冷却错误
    pub fn skill_on_cooldown_error(skill_id: impl Into<String>, remaining_time: f64) -> Self {
        Self::SkillOnCooldown {
            skill_id: skill_id.into(),
            remaining_time,
        }
    }

    /// 创建法力不足错误
    pub fn insufficient_mana_error(required: i32, current: i32) -> Self {
        Self::InsufficientMana { required, current }
    }

    /// 创建角色未找到错误
    pub fn character_not_found_error(character_id: impl Into<String>) -> Self {
        Self::CharacterNotFound {
            character_id: character_id.into(),
        }
    }

    /// 创建战斗单位未找到错误
    pub fn unit_not_found_error(unit_id: impl Into<String>) -> Self {
        Self::UnitNotFound {
            unit_id: unit_id.into(),
        }
    }

    /// 创建材料未找到错误
    pub fn material_not_found_error(material_id: impl Into<String>) -> Self {
        Self::MaterialNotFound {
            material_id: material_id.into(),
        }
    }

    /// 创建配置错误
    pub fn config_error(path: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ConfigError {
            path: path.into(),
            reason: reason.into(),
        }
    }

    /// 创建验证错误
    pub fn validation_error(field: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ValidationError {
            field: field.into(),
            reason: reason.into(),
        }
    }

    /// 创建内部错误
    pub fn internal_error(message: impl Into<String>) -> Self {
        Self::InternalError {
            message: message.into(),
        }
    }

    /// 创建技能未找到错误
    pub fn skill_not_found_error(skill_id: impl Into<String>) -> Self {
        Self::SkillNotFound {
            skill_id: skill_id.into(),
        }
    }
}

// ============================================================================
// 错误分类和处理
// ============================================================================

impl GameError {
    /// 判断是否是可恢复的错误
    pub fn is_recoverable(&self) -> bool {
        match self {
            // 用户操作错误，可恢复
            GameError::SkillOnCooldown { .. }
            | GameError::InsufficientMana { .. }
            | GameError::InsufficientLevel { .. }
            | GameError::InvalidTarget { .. }
            | GameError::PositionOccupied { .. }
            | GameError::InsufficientStorage { .. }
            | GameError::InsufficientItem { .. } => true,

            // 系统错误，通常不可恢复
            GameError::OutOfMemory | GameError::InternalError { .. } => false,

            // 其他错误可能可恢复
            _ => true,
        }
    }

    /// 获取错误的严重级别
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            // 严重错误
            GameError::OutOfMemory | GameError::InternalError { .. } => ErrorSeverity::Critical,

            // 高级错误
            GameError::DatabaseError { .. }
            | GameError::NetworkError { .. }
            | GameError::FileSystemError { .. } => ErrorSeverity::High,

            // 中级错误
            GameError::ConfigError { .. }
            | GameError::SerializationError { .. }
            | GameError::DeserializationError { .. }
            | GameError::ValidationError { .. } => ErrorSeverity::Medium,

            // 低级错误（用户操作错误）
            _ => ErrorSeverity::Low,
        }
    }

    /// 获取错误分类
    pub fn category(&self) -> ErrorCategory {
        match self {
            GameError::ImmutableModification { .. }
            | GameError::MutableBorrowFromImmutable { .. }
            | GameError::InvalidTarget { .. }
            | GameError::SkillOnCooldown { .. }
            | GameError::InsufficientMana { .. }
            | GameError::LootRuleCloneError { .. }
            | GameError::UnitNotFound { .. } => ErrorCategory::Battle,

            GameError::CharacterNotFound { .. }
            | GameError::InsufficientLevel { .. }
            | GameError::ExperienceOverflow { .. } => ErrorCategory::Character,

            GameError::MaterialNotFound { .. }
            | GameError::MaterialGradeMismatch { .. }
            | GameError::MaterialSynthesisFailed { .. }
            | GameError::MaterialStackOverflow { .. } => ErrorCategory::Material,

            GameError::EquipmentNotFound { .. }
            | GameError::EquipmentTypeMismatch { .. }
            | GameError::EquipmentBroken { .. } => ErrorCategory::Equipment,

            GameError::SkillError { .. }
            | GameError::SkillNotFound { .. }
            | GameError::SkillConditionNotMet { .. }
            | GameError::SkillInvalidTarget { .. } => ErrorCategory::Skill,

            GameError::InvalidPosition { .. }
            | GameError::PositionOccupied { .. }
            | GameError::MovementTooFar { .. } => ErrorCategory::WorldMap,

            GameError::InsufficientStorage { .. }
            | GameError::ItemNotFound { .. }
            | GameError::InsufficientItem { .. } => ErrorCategory::Storage,

            GameError::ConfigError { .. }
            | GameError::SerializationError { .. }
            | GameError::DeserializationError { .. }
            | GameError::ValidationError { .. } => ErrorCategory::Data,

            GameError::OutOfMemory
            | GameError::NetworkError { .. }
            | GameError::DatabaseError { .. }
            | GameError::FileSystemError { .. }
            | GameError::PermissionDenied { .. }
            | GameError::NotImplemented { .. }
            | GameError::InternalError { .. } => ErrorCategory::System,
        }
    }
}

/// 错误严重级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 错误分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ErrorCategory {
    Battle,
    Character,
    Material,
    Equipment,
    Skill,
    WorldMap,
    Storage,
    Data,
    System,
}

// ============================================================================
// 兼容性类型别名（保持向后兼容）
// ============================================================================

/// 战斗系统错误类型别名（向后兼容）
pub type BattleError = GameError;

/// 战斗系统结果类型别名（向后兼容）
pub type BattleResult<T> = GameResult<T>;

// ============================================================================
// 快捷构造函数（向后兼容）
// ============================================================================

/// 创建不可变修改错误（向后兼容）
pub fn immutable_modification_error(operation: &str) -> GameError {
    GameError::immutable_modification_error(operation)
}

/// 创建可变借用错误（向后兼容）
pub fn mutable_borrow_error(field: &str) -> GameError {
    GameError::mutable_borrow_error(field)
}
