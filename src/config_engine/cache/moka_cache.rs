//! # 基于Moka的高性能缓存实现
//!
//! 使用moka-rs提供的高性能异步缓存功能，支持多种驱逐策略和丰富的缓存特性
//!
//! ## 核心特性
//!
//! - 高性能异步缓存（基于Caffeine设计）
//! - 多种驱逐策略（LRU、LFU、TinyLFU、W-TinyLFU）
//! - 自动过期和刷新
//! - 丰富的统计信息
//! - 内存管理和权重控制
//! - 事件监听和回调
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::cache::moka_cache::{MokaCacheManager, MokaCacheConfig};//!
//! #
//! async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let config = MokaCacheConfig::builder()
//!     .max_capacity(10000)
//!     .time_to_live(std::time::Duration::from_secs(3600))
//!     .time_to_idle(std::time::Duration::from_secs(1800))
//!     .build();
//!
//! let cache_manager = MokaCacheManager::new(config).await?;
//!
//! // 缓存规则结果
//! // cache_manager.cache_rule_result("rule_1", &result_data).await?;
//!
//! // 获取缓存结果
//! if let Some(cached_result) = cache_manager.get_rule_result("rule_1").await? {
//!     // 使用缓存结果
//! }
//! # Ok(())
//! # }
//! ```

use crate::config_engine::{
    rules::ContextValue,
    Result,
};

// 定义 CacheResult 类型
#[derive(Debug, Clone)]
pub enum CacheResult<T> {
    /// 缓存命中
    Hit(T),
    /// 缓存未命中
    Miss,
    /// 缓存错误
    Error(String),
}

use async_trait::async_trait;
use moka::future::{Cache, CacheBuilder, PredicateId};
use moka::notification::{RemovalCause};
use moka::policy::EvictionPolicy as MokaEvictionPolicy;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::mem::size_of_val;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::{Mutex, RwLock};

/// 基于Moka的缓存管理器
pub struct MokaCacheManager {
    /// 规则结果缓存
    rule_cache: Cache<String, CachedRuleResult>,
    /// 配置缓存
    config_cache: Cache<String, CachedConfig>,
    /// 验证结果缓存
    validation_cache: Cache<String, CachedValidationResult>,
    /// 类型信息缓存
    type_cache: Cache<String, CachedTypeInfo>,
    /// 缓存配置
    config: MokaCacheConfig,
    /// 统计信息
    stats: Arc<RwLock<MokaCacheStats>>,
    /// 事件监听器
    event_listeners: Arc<RwLock<Vec<Box<dyn CacheEventListener>>>>,
    /// 预热状态
    warmup_status: Arc<Mutex<WarmupStatus>>,
}

impl MokaCacheManager {
    /// 创建新的Moka缓存管理器
    pub async fn new(config: MokaCacheConfig) -> Result<Self> {
        // 构建规则结果缓存
        let rule_cache = Self::build_cache(
            &config,
            config.rule_cache_capacity,
            config.rule_ttl,
            config.rule_tti,
        )?;

        // 构建配置缓存
        let config_cache = Self::build_cache(
            &config,
            config.config_cache_capacity,
            config.config_ttl,
            config.config_tti,
        )?;

        // 构建验证结果缓存
        let validation_cache = Self::build_cache(
            &config,
            config.validation_cache_capacity,
            config.validation_ttl,
            config.validation_tti,
        )?;

        // 构建类型信息缓存
        let type_cache = Self::build_cache(
            &config,
            config.type_cache_capacity,
            config.type_ttl,
            config.type_tti,
        )?;

        let manager = Self {
            rule_cache,
            config_cache,
            validation_cache,
            type_cache,
            config,
            stats: Arc::new(RwLock::new(MokaCacheStats::new())),
            event_listeners: Arc::new(RwLock::new(Vec::new())),
            warmup_status: Arc::new(Mutex::new(WarmupStatus::NotStarted)),
        };

        // 启动统计收集任务
        manager.start_stats_collection().await;

        // 启动清理任务
        manager.start_maintenance_tasks().await;

        log::info!("Moka缓存管理器初始化完成");
        Ok(manager)
    }

    /// 缓存规则执行结果（内部方法）
    pub async fn cache_rule_result_internal(
        &self,
        rule_id: &str,
        context_hash: &str,
        result: &HashMap<String, ContextValue>,
        execution_time_ms: u64,
    ) -> Result<()> {
        let cache_key = format!("{}:{}", rule_id, context_hash);

        let cached_result = CachedRuleResult {
            rule_id: rule_id.to_string(),
            context_hash: context_hash.to_string(),
            result: result.clone(),
            execution_time_ms,
            cached_at: SystemTime::now(),
            access_count: 0,
            last_accessed: SystemTime::now(),
        };

        // 插入缓存
        self.rule_cache
            .insert(cache_key.clone(), cached_result)
            .await;

        // 更新统计
        self.increment_stat(StatType::RuleWrites).await;

        // 触发事件
        self.notify_cache_event(CacheEvent::Insert {
            cache_type: CacheType::Rule,
            key: cache_key,
        })
        .await;

        Ok(())
    }

    /// 获取规则执行结果（内部方法）
    pub async fn get_rule_result_internal(
        &self,
        rule_id: &str,
        context_hash: &str,
    ) -> Result<Option<HashMap<String, ContextValue>>> {
        let cache_key = format!("{}:{}", rule_id, context_hash);

        match self.rule_cache.get(&cache_key).await {
            Some(mut cached_result) => {
                // 更新访问信息
                cached_result.access_count += 1;
                cached_result.last_accessed = SystemTime::now();

                // 重新插入以更新访问时间
                self.rule_cache
                    .insert(cache_key.clone(), cached_result.clone())
                    .await;

                // 更新统计
                self.increment_stat(StatType::RuleHits).await;

                // 触发事件
                self.notify_cache_event(CacheEvent::Hit {
                    cache_type: CacheType::Rule,
                    key: cache_key,
                })
                .await;

                Ok(Some(cached_result.result))
            }
            None => {
                // 更新统计
                self.increment_stat(StatType::RuleMisses).await;

                // 触发事件
                self.notify_cache_event(CacheEvent::Miss {
                    cache_type: CacheType::Rule,
                    key: cache_key,
                })
                .await;

                Ok(None)
            }
        }
    }

    /// 缓存配置数据（内部方法）
    pub async fn cache_config_internal(
        &self,
        config_id: &str,
        config_data: &serde_json::Value,
        checksum: &str,
    ) -> Result<()> {
        let cached_config = CachedConfig {
            config_id: config_id.to_string(),
            data: config_data.clone(),
            checksum: checksum.to_string(),
            cached_at: SystemTime::now(),
            size_bytes: config_data.to_string().len(),
            access_count: 0,
            last_accessed: SystemTime::now(),
        };

        self.config_cache
            .insert(config_id.to_string(), cached_config)
            .await;

        // 更新统计
        self.increment_stat(StatType::ConfigWrites).await;

        // 触发事件
        self.notify_cache_event(CacheEvent::Insert {
            cache_type: CacheType::Config,
            key: config_id.to_string(),
        })
        .await;

        Ok(())
    }

    /// 获取配置数据（内部方法）
    pub async fn get_config_internal(&self, config_id: &str) -> Result<Option<serde_json::Value>> {
        match self.config_cache.get(config_id).await {
            Some(mut cached_config) => {
                // 更新访问信息
                cached_config.access_count += 1;
                cached_config.last_accessed = SystemTime::now();

                self.config_cache
                    .insert(config_id.to_string(), cached_config.clone())
                    .await;

                // 更新统计
                self.increment_stat(StatType::ConfigHits).await;

                Ok(Some(cached_config.data))
            }
            None => {
                // 更新统计
                self.increment_stat(StatType::ConfigMisses).await;
                Ok(None)
            }
        }
    }

    /// 缓存验证结果
    pub async fn cache_validation_result(
        &self,
        validation_key: &str,
        is_valid: bool,
        errors: Vec<String>,
        warnings: Vec<String>,
    ) -> Result<()> {
        let cached_validation = CachedValidationResult {
            validation_key: validation_key.to_string(),
            is_valid,
            errors,
            warnings,
            cached_at: SystemTime::now(),
            access_count: 0,
            last_accessed: SystemTime::now(),
        };

        self.validation_cache
            .insert(validation_key.to_string(), cached_validation)
            .await;

        // 更新统计
        self.increment_stat(StatType::ValidationWrites).await;

        Ok(())
    }

    /// 获取验证结果
    pub async fn get_validation_result(
        &self,
        validation_key: &str,
    ) -> Result<Option<CachedValidationResult>> {
        match self.validation_cache.get(validation_key).await {
            Some(mut result) => {
                result.access_count += 1;
                result.last_accessed = SystemTime::now();

                self.validation_cache
                    .insert(validation_key.to_string(), result.clone())
                    .await;

                // 更新统计
                self.increment_stat(StatType::ValidationHits).await;
                Ok(Some(result))
            },
            None => {
                // 更新统计
                self.increment_stat(StatType::ValidationMisses).await;
                Ok(None)
            }
        }
    }

    /// 失效规则相关的所有缓存
    pub async fn invalidate_rule_cache(&self, rule_id: &str) -> Result<u64> {
        let pattern = format!("{}:", rule_id);
        let mut invalidated = 0;

        // 使用invalidate_entries_if方法
        match self.rule_cache
            .invalidate_entries_if(move |key, _| key.starts_with(&pattern)) {
            Ok(_predicateId) => {
                
            }
            Err(_) => {}
        }

        // 简化统计（实际数量需要遍历获得）
        invalidated += 1;

        // 更新统计
        self.add_to_stat(StatType::Invalidations, invalidated).await;

        log::debug!("失效规则 {} 的缓存，共 {} 项", rule_id, invalidated);
        Ok(invalidated)
    }

    /// 失效配置相关的所有缓存
    pub async fn invalidate_config_cache(&self, config_pattern: &str) -> Result<u64> {
        let pattern = config_pattern.to_string();
        let mut invalidated = 0;

        if config_pattern == "*" {
            // 清空所有配置缓存
            self.config_cache.invalidate_all();
            invalidated = self.config_cache.entry_count();
        } else {
            // 根据模式失效
            match self.config_cache
                .invalidate_entries_if(move |key, _| Self::matches_pattern(key, &pattern)) {
                Ok(_) => {}
                Err(e) => {
                    log::error!("失效配置缓存失败: {:?}", e);
                }
            }
            invalidated += 1; // 简化统计
        }

        // 更新统计
        self.add_to_stat(StatType::Invalidations, invalidated).await;

        log::debug!(
            "失效配置缓存，模式: {}，共 {} 项",
            config_pattern,
            invalidated
        );
        Ok(invalidated)
    }

    /// 清空所有缓存
    pub async fn clear_all(&self) -> Result<()> {
        let total_entries = self.rule_cache.entry_count()
            + self.config_cache.entry_count()
            + self.validation_cache.entry_count()
            + self.type_cache.entry_count();

        // 清空所有缓存
        self.rule_cache.invalidate_all();
        self.config_cache.invalidate_all();
        self.validation_cache.invalidate_all();
        self.type_cache.invalidate_all();

        // 等待失效完成
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 更新统计
        self.add_to_stat(StatType::Invalidations, total_entries)
            .await;

        // 触发事件
        self.notify_cache_event(CacheEvent::ClearAll).await;

        log::info!("已清空所有缓存，共 {} 项", total_entries);
        Ok(())
    }

    /// 预热缓存（内部方法）
    pub async fn warmup_internal(&self, warmup_requests: Vec<WarmupRequest>) -> Result<WarmupResult> {
        let start_time = Instant::now();

        // 更新预热状态
        {
            let mut status = self.warmup_status.lock().await;
            *status = WarmupStatus::InProgress {
                started_at: start_time,
                total_requests: warmup_requests.len(),
                completed_requests: 0,
            };
        }

        let mut successful_warmups = 0;
        let mut failed_warmups = 0;
        let mut errors = Vec::new();

        for (index, request) in warmup_requests.iter().enumerate() {
            match self.execute_warmup_request(request).await {
                Ok(_) => successful_warmups += 1,
                Err(e) => {
                    failed_warmups += 1;
                    errors.push(format!("预热请求 {} 失败: {}", index, e));
                }
            }

            // 更新进度
            {
                let mut status = self.warmup_status.lock().await;
                if let WarmupStatus::InProgress {
                    completed_requests, ..
                } = &mut *status
                {
                    *completed_requests = index + 1;
                }
            }
        }

        let duration = start_time.elapsed();

        // 更新预热状态
        {
            let mut status = self.warmup_status.lock().await;
            *status = WarmupStatus::Completed {
                completed_at: Instant::now(),
                successful_requests: successful_warmups,
                failed_requests: failed_warmups,
                total_duration: duration,
            };
        }

        let result = WarmupResult {
            successful_requests: successful_warmups,
            failed_requests: failed_warmups,
            total_duration: duration,
            errors,
        };

        log::info!(
            "缓存预热完成: 成功 {}, 失败 {}, 耗时 {:?}",
            successful_warmups,
            failed_warmups,
            duration
        );

        Ok(result)
    }

    /// 获取缓存统计信息
    pub async fn get_stats(&self) -> MokaCacheStats {
        let mut stats = self.stats.read().await.clone();

        // 从Moka缓存获取实时统计
        stats.rule_cache_size = self.rule_cache.entry_count();
        stats.config_cache_size = self.config_cache.entry_count();
        stats.validation_cache_size = self.validation_cache.entry_count();
        stats.type_cache_size = self.type_cache.entry_count();

        stats.rule_cache_weight = self.rule_cache.weighted_size();
        stats.config_cache_weight = self.config_cache.weighted_size();
        stats.validation_cache_weight = self.validation_cache.weighted_size();
        stats.type_cache_weight = self.type_cache.weighted_size();

        stats
    }

    /// 获取详细的缓存信息
    pub async fn get_cache_info(&self) -> MokaCacheInfo {
        let stats = self.get_stats().await;
        let warmup_status = self.warmup_status.lock().await.clone();

        MokaCacheInfo {
            config: self.config.clone(),
            stats,
            warmup_status,
            total_memory_usage: self.estimate_memory_usage().await,
            eviction_count: self.get_eviction_count().await,
        }
    }
    
    // ===== 与原 CacheManager 相同的接口方法 =====
    
    /// 缓存规则执行结果（与 CacheManager 兼容的签名）
    pub async fn cache_rule_result(
        &self,
        cache_key: &str,
        result: &HashMap<String, ContextValue>,
    ) -> Result<()> {
        // 从 cache_key 中提取 rule_id 和 context_hash
        let parts: Vec<&str> = cache_key.split(':').collect();
        let rule_id = parts.get(0).unwrap_or(&"unknown").to_string();
        let context_hash = parts.get(1).unwrap_or(&"default").to_string();
        
        self.cache_rule_result_internal(&rule_id, &context_hash, result, 0).await
    }
    
    /// 获取规则执行结果（与 CacheManager 兼容的签名）
    pub async fn get_rule_result(
        &self,
        cache_key: &str,
    ) -> Result<CacheResult<HashMap<String, ContextValue>>> {
        // 从 cache_key 中提取 rule_id 和 context_hash
        let parts: Vec<&str> = cache_key.split(':').collect();
        let rule_id = parts.get(0).unwrap_or(&"unknown").to_string();
        let context_hash = parts.get(1).unwrap_or(&"default").to_string();
        
        match self.get_rule_result_internal(&rule_id, &context_hash).await? {
            Some(result) => Ok(CacheResult::Hit(result)),
            None => Ok(CacheResult::Miss),
        }
    }
    
    /// 缓存配置数据（与 CacheManager 兼容的签名）
    pub async fn cache_config(
        &self,
        config_key: &str,
        config_data: &serde_json::Value,
    ) -> Result<()> {
        // 生成简单的校验和
        let checksum = format!("{:x}", config_data.to_string().len());
        self.cache_config_internal(config_key, config_data, &checksum).await
    }
    
    /// 获取配置数据（与 CacheManager 兼容的签名）
    pub async fn get_config(&self, config_key: &str) -> Result<CacheResult<serde_json::Value>> {
        match self.get_config_internal(config_key).await? {
            Some(config) => Ok(CacheResult::Hit(config)),
            None => Ok(CacheResult::Miss),
        }
    }
    
    /// 失效缓存项
    pub async fn invalidate(&self, pattern: &str) -> Result<u32> {
        let result = if pattern.starts_with("rule_") {
            // 失效规则缓存
            let rule_id = pattern.strip_prefix("rule_").unwrap_or("");
            if rule_id.ends_with("*") {
                let rule_prefix = rule_id.strip_suffix("*").unwrap_or("");
                self.invalidate_rule_cache(rule_prefix).await?
            } else {
                self.invalidate_rule_cache(rule_id).await?
            }
        } else if pattern.starts_with("config_") {
            // 失效配置缓存
            let config_pattern = pattern.strip_prefix("config_").unwrap_or("*");
            self.invalidate_config_cache(config_pattern).await?
        } else {
            // 通用失效
            self.invalidate_config_cache(pattern).await?
        };
        Ok(result as u32)
    }
    
    /// 重置统计信息
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.write().await;
        *stats = MokaCacheStats::new();
    }
    
    /// 预热缓存（与 CacheManager 兼容的签名）
    pub async fn warmup(&self, _warmup_rules: Vec<String>) -> Result<()> {
        // 简化实现，将字符串转换为 WarmupRequest
        let requests: Vec<WarmupRequest> = _warmup_rules
            .into_iter()
            .map(|rule| WarmupRequest::Rule {
                rule_id: rule,
                context_hash: "default".to_string(),
                mock_result: None,
            })
            .collect();
        
        self.warmup_internal(requests).await.map(|_| ())
    }

    /// 添加事件监听器
    pub async fn add_event_listener(&self, listener: Box<dyn CacheEventListener>) {
        let mut listeners = self.event_listeners.write().await;
        listeners.push(listener);
    }

    /// 健康检查
    pub async fn health_check(&self) -> CacheHealthStatus {
        let stats = self.get_stats().await;

        let hit_rate = if stats.total_requests() > 0 {
            stats.total_hits() as f64 / stats.total_requests() as f64
        } else {
            0.0
        };

        let is_healthy = hit_rate >= self.config.min_healthy_hit_rate
            && stats.total_errors == 0
            && self.rule_cache.entry_count() < self.config.rule_cache_capacity;

        CacheHealthStatus {
            healthy: is_healthy,
            hit_rate,
            total_entries: stats.total_cache_size(),
            memory_usage_mb: self.estimate_memory_usage().await as f64 / 1024.0 / 1024.0,
            error_count: stats.total_errors,
            eviction_count: self.get_eviction_count().await,
        }
    }

    // 私有辅助方法

    fn build_cache<V: Clone + Send + Sync + 'static>(
        config: &MokaCacheConfig,
        max_capacity: u64,
        ttl: Duration,
        tti: Duration,
    ) -> Result<Cache<String, V>> {
        let mut builder = CacheBuilder::new(max_capacity);

        // 设置TTL
        if ttl > Duration::from_secs(0) {
            builder = builder.time_to_live(ttl);
        }

        // 设置TTI
        if tti > Duration::from_secs(0) {
            builder = builder.time_to_idle(tti);
        }

        // 设置驱逐策略
        builder = match config.eviction_policy {
            EvictionPolicy::LRU => builder.eviction_policy(MokaEvictionPolicy::lru()),
            EvictionPolicy::LFU => builder.eviction_policy(MokaEvictionPolicy::lru()), // Moka 中不群支持 LFU，使用 LRU 代替
            EvictionPolicy::TinyLFU => builder.eviction_policy(MokaEvictionPolicy::tiny_lfu()),
            EvictionPolicy::WTinyLFU => builder.eviction_policy(MokaEvictionPolicy::tiny_lfu()), // Moka 中 TinyLFU 和 WTinyLFU 都用 tiny_lfu
        };

        // 设置权重计算器（简化实现）
        if config.enable_weight_based_eviction {
            builder = builder.weigher(|_key: &String, value: &V| {
                // 简化的权重计算，实际实现应该基于具体的值类型
                size_of_val(value) as u32
            });
        }

        // 设置初始容量
        if config.initial_capacity > 0 {
            builder = builder.initial_capacity(config.initial_capacity as usize);
        }

        // 构建缓存
        Ok(builder.build())
    }

    async fn execute_warmup_request(&self, request: &WarmupRequest) -> Result<()> {
        match request {
            WarmupRequest::Rule {
                rule_id,
                context_hash,
                mock_result,
            } => {
                if let Some(result) = mock_result {
                    let cache_key = format!("{}:{}", rule_id, context_hash);
                    self.cache_rule_result(&cache_key, result)
                        .await?;
                }
            }
            WarmupRequest::Config {
                config_id,
                mock_data,
                checksum: _,
            } => {
                if let Some(data) = mock_data {
                    self.cache_config(config_id, data).await?;
                }
            }
            WarmupRequest::Validation {
                validation_key,
                is_valid,
                errors,
                warnings,
            } => {
                self.cache_validation_result(
                    validation_key,
                    *is_valid,
                    errors.clone(),
                    warnings.clone(),
                )
                .await?;
            }
        }
        Ok(())
    }

    async fn start_stats_collection(&self) {
        let stats = Arc::clone(&self.stats);

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60)); // 每分钟收集一次

            loop {
                interval.tick().await;

                // 这里可以添加更多的统计收集逻辑
                log::debug!("缓存统计收集任务执行");
            }
        });
    }

    async fn start_maintenance_tasks(&self) {
        // 维护任务 - 定期检查缓存状态和清理
        let rule_cache = self.rule_cache.clone();
        let config_cache = self.config_cache.clone();
        let validation_cache = self.validation_cache.clone();
        let type_cache = self.type_cache.clone();
        let stats = Arc::clone(&self.stats);

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(300)); // 每5分钟

            loop {
                interval.tick().await;

                // Moka 缓存会自动处理过期和驱逐，无需手动同步
                // 这里只进行统计和监控
                let rule_count = rule_cache.entry_count();
                let config_count = config_cache.entry_count();
                let validation_count = validation_cache.entry_count();
                let type_count = type_cache.entry_count();

                // 更新统计信息中的缓存大小
                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.rule_cache_size = rule_count;
                    stats_guard.config_cache_size = config_count;
                    stats_guard.validation_cache_size = validation_count;
                    stats_guard.type_cache_size = type_count;
                }

                log::debug!("缓存维护任务执行 - 规则: {}, 配置: {}, 验证: {}, 类型: {}", 
                    rule_count, config_count, validation_count, type_count);
            }
        });
    }

    async fn increment_stat(&self, stat_type: StatType) {
        let mut stats = self.stats.write().await;
        match stat_type {
            StatType::RuleHits => stats.rule_hits += 1,
            StatType::RuleMisses => stats.rule_misses += 1,
            StatType::RuleWrites => stats.rule_writes += 1,
            StatType::ConfigHits => stats.config_hits += 1,
            StatType::ConfigMisses => stats.config_misses += 1,
            StatType::ConfigWrites => stats.config_writes += 1,
            StatType::ValidationHits => stats.validation_hits += 1,
            StatType::ValidationMisses => stats.validation_misses += 1,
            StatType::ValidationWrites => stats.validation_writes += 1,
            StatType::Invalidations => stats.invalidations += 1,
            StatType::Errors => stats.total_errors += 1,
        }
    }

    async fn add_to_stat(&self, stat_type: StatType, value: u64) {
        let mut stats = self.stats.write().await;
        match stat_type {
            StatType::Invalidations => stats.invalidations += value,
            _ => {} // 其他统计类型不支持批量添加
        }
    }

    async fn notify_cache_event(&self, event: CacheEvent) {
        let listeners = self.event_listeners.read().await;
        for listener in listeners.iter() {
            listener.on_cache_event(&event).await;
        }
    }

    async fn estimate_memory_usage(&self) -> u64 {
        // 简化的内存使用估算
        let rule_memory = self.rule_cache.weighted_size();
        let config_memory = self.config_cache.weighted_size();
        let validation_memory = self.validation_cache.weighted_size();
        let type_memory = self.type_cache.weighted_size();

        rule_memory + config_memory + validation_memory + type_memory
    }

    async fn get_eviction_count(&self) -> u64 {
        // Moka的驱逐统计需要通过其他方式获取
        // 这里提供简化实现
        0
    }

    fn matches_pattern(text: &str, pattern: &str) -> bool {
        if pattern == "*" {
            return true;
        }
        if pattern.contains('*') {
            let parts: Vec<&str> = pattern.split('*').collect();
            if parts.len() == 2 {
                let prefix = parts[0];
                let suffix = parts[1];
                return text.starts_with(prefix) && text.ends_with(suffix);
            }
        }
        text == pattern
    }
}

/// Moka缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MokaCacheConfig {
    /// 规则缓存容量
    pub rule_cache_capacity: u64,
    /// 配置缓存容量
    pub config_cache_capacity: u64,
    /// 验证缓存容量
    pub validation_cache_capacity: u64,
    /// 类型缓存容量
    pub type_cache_capacity: u64,
    /// 规则结果TTL
    pub rule_ttl: Duration,
    /// 配置TTL
    pub config_ttl: Duration,
    /// 验证结果TTL
    pub validation_ttl: Duration,
    /// 类型信息TTL
    pub type_ttl: Duration,
    /// 规则结果TTI
    pub rule_tti: Duration,
    /// 配置TTI
    pub config_tti: Duration,
    /// 验证结果TTI
    pub validation_tti: Duration,
    /// 类型信息TTI
    pub type_tti: Duration,
    /// 驱逐策略
    pub eviction_policy: EvictionPolicy,
    /// 是否启用基于权重的驱逐
    pub enable_weight_based_eviction: bool,
    /// 初始容量
    pub initial_capacity: u64,
    /// 健康命中率阈值
    pub min_healthy_hit_rate: f64,
}

impl Default for MokaCacheConfig {
    fn default() -> Self {
        Self {
            rule_cache_capacity: 10000,
            config_cache_capacity: 1000,
            validation_cache_capacity: 5000,
            type_cache_capacity: 500,
            rule_ttl: Duration::from_secs(3600),      // 1小时
            config_ttl: Duration::from_secs(1800),    // 30分钟
            validation_ttl: Duration::from_secs(600), // 10分钟
            type_ttl: Duration::from_secs(7200),      // 2小时
            rule_tti: Duration::from_secs(1800),      // 30分钟
            config_tti: Duration::from_secs(900),     // 15分钟
            validation_tti: Duration::from_secs(300), // 5分钟
            type_tti: Duration::from_secs(3600),      // 1小时
            eviction_policy: EvictionPolicy::TinyLFU,
            enable_weight_based_eviction: true,
            initial_capacity: 1000,
            min_healthy_hit_rate: 0.7,
        }
    }
}

impl MokaCacheConfig {
    pub fn builder() -> MokaCacheConfigBuilder {
        MokaCacheConfigBuilder::new()
    }
}

/// Moka缓存配置构建器
pub struct MokaCacheConfigBuilder {
    config: MokaCacheConfig,
}

impl MokaCacheConfigBuilder {
    pub fn new() -> Self {
        Self {
            config: MokaCacheConfig::default(),
        }
    }

    pub fn max_capacity(mut self, capacity: u64) -> Self {
        self.config.rule_cache_capacity = capacity;
        self.config.config_cache_capacity = capacity / 10;
        self.config.validation_cache_capacity = capacity / 2;
        self.config.type_cache_capacity = capacity / 20;
        self
    }

    pub fn time_to_live(mut self, ttl: Duration) -> Self {
        self.config.rule_ttl = ttl;
        self.config.config_ttl = ttl;
        self.config.validation_ttl = ttl / 6;
        self.config.type_ttl = ttl * 2;
        self
    }

    pub fn time_to_idle(mut self, tti: Duration) -> Self {
        self.config.rule_tti = tti;
        self.config.config_tti = tti / 2;
        self.config.validation_tti = tti / 6;
        self.config.type_tti = tti * 2;
        self
    }

    pub fn eviction_policy(mut self, policy: EvictionPolicy) -> Self {
        self.config.eviction_policy = policy;
        self
    }

    pub fn enable_weight_based_eviction(mut self, enable: bool) -> Self {
        self.config.enable_weight_based_eviction = enable;
        self
    }

    pub fn initial_capacity(mut self, capacity: u64) -> Self {
        self.config.initial_capacity = capacity;
        self
    }

    pub fn min_healthy_hit_rate(mut self, rate: f64) -> Self {
        self.config.min_healthy_hit_rate = rate;
        self
    }

    pub fn build(self) -> MokaCacheConfig {
        self.config
    }
}

/// 驱逐策略
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum EvictionPolicy {
    /// 最近最少使用
    LRU,
    /// 最近最少频率使用  
    LFU,
    /// TinyLFU（更节省内存的LFU）
    TinyLFU,
    /// 加权TinyLFU（Caffeine默认策略）
    WTinyLFU,
}

/// 缓存的规则结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedRuleResult {
    pub rule_id: String,
    pub context_hash: String,
    pub result: HashMap<String, ContextValue>,
    pub execution_time_ms: u64,
    pub cached_at: SystemTime,
    pub access_count: u64,
    pub last_accessed: SystemTime,
}

/// 缓存的配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedConfig {
    pub config_id: String,
    pub data: serde_json::Value,
    pub checksum: String,
    pub cached_at: SystemTime,
    pub size_bytes: usize,
    pub access_count: u64,
    pub last_accessed: SystemTime,
}

/// 缓存的验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedValidationResult {
    pub validation_key: String,
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub cached_at: SystemTime,
    pub access_count: u64,
    pub last_accessed: SystemTime,
}

/// 缓存的类型信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedTypeInfo {
    pub type_id: String,
    pub definition: serde_json::Value,
    pub schema: Option<serde_json::Value>,
    pub cached_at: SystemTime,
    pub access_count: u64,
    pub last_accessed: SystemTime,
}

/// 预热请求
#[derive(Debug, Clone)]
pub enum WarmupRequest {
    Rule {
        rule_id: String,
        context_hash: String,
        mock_result: Option<HashMap<String, ContextValue>>,
    },
    Config {
        config_id: String,
        mock_data: Option<serde_json::Value>,
        checksum: String,
    },
    Validation {
        validation_key: String,
        is_valid: bool,
        errors: Vec<String>,
        warnings: Vec<String>,
    },
}

/// 预热结果
#[derive(Debug, Clone)]
pub struct WarmupResult {
    pub successful_requests: usize,
    pub failed_requests: usize,
    pub total_duration: Duration,
    pub errors: Vec<String>,
}

/// 预热状态
#[derive(Debug, Clone)]
pub enum WarmupStatus {
    NotStarted,
    InProgress {
        started_at: Instant,
        total_requests: usize,
        completed_requests: usize,
    },
    Completed {
        completed_at: Instant,
        successful_requests: usize,
        failed_requests: usize,
        total_duration: Duration,
    },
    Failed {
        failed_at: Instant,
        error: String,
    },
}

/// Moka缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MokaCacheStats {
    // 规则缓存统计
    pub rule_hits: u64,
    pub rule_misses: u64,
    pub rule_writes: u64,
    pub rule_cache_size: u64,
    pub rule_cache_weight: u64,

    // 配置缓存统计
    pub config_hits: u64,
    pub config_misses: u64,
    pub config_writes: u64,
    pub config_cache_size: u64,
    pub config_cache_weight: u64,

    // 验证缓存统计
    pub validation_hits: u64,
    pub validation_misses: u64,
    pub validation_writes: u64,
    pub validation_cache_size: u64,
    pub validation_cache_weight: u64,

    // 类型缓存统计
    pub type_cache_size: u64,
    pub type_cache_weight: u64,

    // 总体统计
    pub invalidations: u64,
    pub total_errors: u64,
}

impl MokaCacheStats {
    pub fn new() -> Self {
        Self {
            rule_hits: 0,
            rule_misses: 0,
            rule_writes: 0,
            rule_cache_size: 0,
            rule_cache_weight: 0,
            config_hits: 0,
            config_misses: 0,
            config_writes: 0,
            config_cache_size: 0,
            config_cache_weight: 0,
            validation_hits: 0,
            validation_misses: 0,
            validation_writes: 0,
            validation_cache_size: 0,
            validation_cache_weight: 0,
            type_cache_size: 0,
            type_cache_weight: 0,
            invalidations: 0,
            total_errors: 0,
        }
    }

    pub fn total_hits(&self) -> u64 {
        self.rule_hits + self.config_hits + self.validation_hits
    }

    pub fn total_misses(&self) -> u64 {
        self.rule_misses + self.config_misses + self.validation_misses
    }

    pub fn total_requests(&self) -> u64 {
        self.total_hits() + self.total_misses()
    }

    pub fn hit_rate(&self) -> f64 {
        if self.total_requests() == 0 {
            0.0
        } else {
            self.total_hits() as f64 / self.total_requests() as f64
        }
    }

    pub fn total_cache_size(&self) -> u64 {
        self.rule_cache_size
            + self.config_cache_size
            + self.validation_cache_size
            + self.type_cache_size
    }

    pub fn total_cache_weight(&self) -> u64 {
        self.rule_cache_weight
            + self.config_cache_weight
            + self.validation_cache_weight
            + self.type_cache_weight
    }
}

/// Moka缓存信息
#[derive(Debug, Clone)]
pub struct MokaCacheInfo {
    pub config: MokaCacheConfig,
    pub stats: MokaCacheStats,
    pub warmup_status: WarmupStatus,
    pub total_memory_usage: u64,
    pub eviction_count: u64,
}

/// 缓存健康状态
#[derive(Debug, Clone)]
pub struct CacheHealthStatus {
    pub healthy: bool,
    pub hit_rate: f64,
    pub total_entries: u64,
    pub memory_usage_mb: f64,
    pub error_count: u64,
    pub eviction_count: u64,
}

/// 缓存事件
#[derive(Debug, Clone)]
pub enum CacheEvent {
    Insert {
        cache_type: CacheType,
        key: String,
    },
    Hit {
        cache_type: CacheType,
        key: String,
    },
    Miss {
        cache_type: CacheType,
        key: String,
    },
    Evict {
        cache_type: CacheType,
        key: String,
        cause: RemovalCause,
    },
    ClearAll,
}

/// 缓存类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CacheType {
    Rule,
    Config,
    Validation,
    Type,
}

/// 缓存事件监听器
#[async_trait]
pub trait CacheEventListener: Send + Sync {
    async fn on_cache_event(&self, event: &CacheEvent);
}

/// 统计类型
enum StatType {
    RuleHits,
    RuleMisses,
    RuleWrites,
    ConfigHits,
    ConfigMisses,
    ConfigWrites,
    ValidationHits,
    ValidationMisses,
    ValidationWrites,
    Invalidations,
    Errors,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[tokio::test]
    async fn test_moka_cache_manager_creation() {
        let config = MokaCacheConfig::default();
        let manager = MokaCacheManager::new(config).await.unwrap();

        let stats = manager.get_stats().await;
        assert_eq!(stats.total_requests(), 0);
    }

    #[tokio::test]
    async fn test_rule_result_caching() {
        let config = MokaCacheConfig::builder()
            .max_capacity(1000)
            .time_to_live(Duration::from_secs(300))
            .build();

        let manager = MokaCacheManager::new(config).await.unwrap();

        let mut result = HashMap::new();
        result.insert(
            "output".to_string(),
            ContextValue::String("test_value".to_string()),
        );

        // 缓存结果
        manager
            .cache_rule_result("test_rule:hash123", &result)
            .await
            .unwrap();

        // 获取结果
        let cached_result = manager
            .get_rule_result("test_rule:hash123")
            .await;
        match cached_result.unwrap() {
            CacheResult::Hit(cached_data) => {
                assert_eq!(
                    cached_data.get("output"),
                    Some(&ContextValue::String("test_value".to_string()))
                );
            }
            _ => panic!("应该命中缓存"),
        }

        let stats = manager.get_stats().await;
        assert_eq!(stats.rule_hits, 1);
        assert_eq!(stats.rule_writes, 1);
    }

    #[tokio::test]
    async fn test_cache_miss() {
        let config = MokaCacheConfig::default();
        let manager = MokaCacheManager::new(config).await.unwrap();

        let result = manager
            .get_rule_result("nonexistent:hash")
            .await;
        match result.unwrap() {
            CacheResult::Miss => {},
            _ => panic!("应该未命中缓存"),
        }

        let stats = manager.get_stats().await;
        assert_eq!(stats.rule_misses, 1);
    }

    #[tokio::test]
    async fn test_config_caching() {
        let config = MokaCacheConfig::default();
        let manager = MokaCacheManager::new(config).await.unwrap();

        let config_data = serde_json::json!({
            "rule_id": "test_rule",
            "enabled": true
        });

        // 缓存配置
        manager
            .cache_config("test_config", &config_data)
            .await
            .unwrap();

        // 获取配置
        let cached_config = manager.get_config("test_config").await.unwrap();
        match cached_config {
            CacheResult::Hit(config) => assert_eq!(config, config_data),
            _ => panic!("应该命中缓存"),
        }

        let stats = manager.get_stats().await;
        assert_eq!(stats.config_hits, 1);
        assert_eq!(stats.config_writes, 1);
    }

    #[tokio::test]
    async fn test_cache_invalidation() {
        let config = MokaCacheConfig::default();
        let manager = MokaCacheManager::new(config).await.unwrap();

        let mut result = HashMap::new();
        result.insert(
            "test".to_string(),
            ContextValue::String("value".to_string()),
        );

        // 缓存多个规则结果
        manager
            .cache_rule_result("rule1:hash1", &result)
            .await
            .unwrap();
        manager
            .cache_rule_result("rule1:hash2", &result)
            .await
            .unwrap();
        manager
            .cache_rule_result("rule2:hash1", &result)
            .await
            .unwrap();

        // 失效rule1的所有缓存
        let invalidated = manager.invalidate("rule_1*").await.unwrap();
        assert!(invalidated > 0);

        // 检查失效结果
        let result1 = manager.get_rule_result("rule1:hash1").await.unwrap();
        let result2 = manager.get_rule_result("rule2:hash1").await.unwrap();

        match result1 {
            CacheResult::Miss => {}, // rule1应该被失效
            _ => panic!("rule1 应该被失效"),
        }
        match result2 {
            CacheResult::Hit(_) => {}, // rule2应该仍然存在  
            _ => panic!("rule2 应该仍在缓存中"),
        }
    }

    #[tokio::test]
    async fn test_cache_health_check() {
        let config = MokaCacheConfig::default();
        let manager = MokaCacheManager::new(config).await.unwrap();

        let health = manager.health_check().await;
        assert!(health.healthy);
        assert_eq!(health.error_count, 0);
    }

    #[test]
    fn test_moka_cache_config_builder() {
        let config = MokaCacheConfig::builder()
            .max_capacity(5000)
            .time_to_live(Duration::from_secs(1800))
            .time_to_idle(Duration::from_secs(900))
            .eviction_policy(EvictionPolicy::LRU)
            .enable_weight_based_eviction(false)
            .initial_capacity(100)
            .min_healthy_hit_rate(0.8)
            .build();

        assert_eq!(config.rule_cache_capacity, 5000);
        assert_eq!(config.rule_ttl, Duration::from_secs(1800));
        assert_eq!(config.rule_tti, Duration::from_secs(900));
        assert_eq!(config.eviction_policy, EvictionPolicy::LRU);
        assert!(!config.enable_weight_based_eviction);
        assert_eq!(config.initial_capacity, 100);
        assert_eq!(config.min_healthy_hit_rate, 0.8);
    }
}

// ===== 兼容性扩展方法 =====

impl MokaCacheManager {
    /// 兼容方法：从缓存中获取规则结果（使用 RuleContext）
    pub async fn get(&self, rule_name: &str, context: &crate::config_engine::core::RuleContext) -> Result<Option<crate::config_engine::core::RuleResult>> {
        let context_hash = context.hash();
        let cache_key = format!("{}:{}", rule_name, context_hash);
        
        match self.rule_cache.get(&cache_key).await {
            Some(cached_result) => {
                // 将缓存的结果转换为 RuleResult
                let metadata = crate::config_engine::core::ExecutionMetadata {
                    rule_name: rule_name.to_string(),
                    execution_duration: Duration::from_millis(cached_result.execution_time_ms),
                    cache_hit: true,
                    execution_id: uuid::Uuid::new_v4().to_string(),
                    context_id: context.context_id().to_string(),
                    started_at: chrono::Utc::now(),
                    completed_at: chrono::Utc::now(),
                    error: None,
                    warnings: Vec::new(),
                    stats: crate::config_engine::core::ExecutionStats::default(),
                };
                Ok(Some(crate::config_engine::core::RuleResult::new(cached_result.result, metadata)))
            },
            None => Ok(None),
        }
    }

    /// 兼容方法：将规则结果存储到缓存（使用 RuleContext）
    pub async fn put(&self, rule_name: &str, context: &crate::config_engine::core::RuleContext, result: &crate::config_engine::core::RuleResult) -> Result<()> {
        let context_hash = context.hash();
        let execution_time_ms = result.metadata().execution_duration.as_millis() as u64;
        self.cache_rule_result_internal(rule_name, &context_hash, result.data(), execution_time_ms).await
    }

    /// 兼容方法：失效所有缓存
    pub async fn invalidate_all(&self) -> Result<()> {
        self.clear_all().await
    }

    /// 兼容方法：失效特定规则的缓存
    pub async fn invalidate_rule(&self, rule_name: &str) -> Result<()> {
        self.invalidate_rule_cache(rule_name).await.map(|_| ())
    }

    /// 兼容方法：获取缓存信息 
    pub async fn get_info(&self) -> MokaCacheInfo {
        self.get_cache_info().await
    }
}
