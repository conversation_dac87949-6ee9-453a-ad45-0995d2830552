/// 战斗单位特有的类型定义
/// 
/// 定义战斗单位系统中特有的类型，避免与现有类型重复

use serde::{Serialize, Deserialize};
use crate::shared::types::*;
use std::collections::HashMap;

// ============================================================================
// 扩展的数值类型（基于现有类型）
// ============================================================================

pub type Stamina = Mana; // 复用Mana类型作为耐力
pub type AttributeValue = i32;
pub type Resistance = i32;
pub type Accuracy = i32;
pub type Evasion = i32;
pub type CriticalRate = i32;
pub type CriticalDamage = i32;
pub type ActionPoints = u32;
pub type Priority = i32;

// ============================================================================
// 方向枚举（扩展Position）
// ============================================================================

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum Direction {
    North,
    South,
    East,
    West,
    NorthEast,
    NorthWest,
    SouthEast,
    SouthWest,
}

impl Direction {
    pub fn to_vector(&self) -> (f32, f32) {
        match self {
            Direction::North => (0.0, 1.0),
            Direction::South => (0.0, -1.0),
            Direction::East => (1.0, 0.0),
            Direction::West => (-1.0, 0.0),
            Direction::NorthEast => (0.707, 0.707),
            Direction::NorthWest => (-0.707, 0.707),
            Direction::SouthEast => (0.707, -0.707),
            Direction::SouthWest => (-0.707, -0.707),
        }
    }
}

// ============================================================================
// 战斗特有的枚举类型
// ============================================================================

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EntityType {
    PlayerCharacter,
    NPC,
    Monster,
    Boss,
    Summon,
    Trap,
    Environmental,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Faction {
    Player,
    Enemy,
    Neutral,
    Ally,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Rarity {
    Common,
    Uncommon,
    Rare,
    Epic,
    Legendary,
    Mythic,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SpaceSize {
    Tiny,    // 1x1
    Small,   // 2x2
    Medium,  // 3x3
    Large,   // 4x4
    Huge,    // 5x5
    Gargantuan, // 6x6+
}

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum FatigueLevel {
    Fresh,
    Fatigued,
    Tired,
    Exhausted,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DamageType {
    Physical,
    Fire,
    Ice,
    Lightning,
    Poison,
    Holy,
    Dark,
    Chaos,
}

impl DamageType {
    pub fn all_types() -> &'static [DamageType] {
        &[
            DamageType::Physical,
            DamageType::Fire,
            DamageType::Ice,
            DamageType::Lightning,
            DamageType::Poison,
            DamageType::Holy,
            DamageType::Dark,
            DamageType::Chaos,
        ]
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum StatusType {
    Stun,
    Sleep,
    Silence,
    Root,
    Slow,
    Haste,
    Poison,
    Burn,
    Freeze,
    Paralysis,
    Confusion,
    Charm,
    Fear,
    Blind,
    Disarm,
    Invulnerable,
    Invisible,
    Shield,
    Regeneration,
    Berserk,
}

impl StatusType {
    pub fn is_debuff(&self) -> bool {
        matches!(self, 
            StatusType::Stun | StatusType::Sleep | StatusType::Silence |
            StatusType::Root | StatusType::Slow | StatusType::Poison |
            StatusType::Burn | StatusType::Freeze | StatusType::Paralysis |
            StatusType::Confusion | StatusType::Charm | StatusType::Fear |
            StatusType::Blind | StatusType::Disarm
        )
    }
    
    pub fn is_buff(&self) -> bool {
        !self.is_debuff()
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MovementType {
    Walking,
    Flying,
    Swimming,
    Teleportation,
    Phase,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AttackType {
    Melee,
    Ranged,
    Magic,
    Hybrid,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ActionType {
    Move,
    Attack,
    CastSpell,
    UseItem,
    Defend,
    Wait,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ActionRestrictionType {
    Stun,
    Silence,
    Disarm,
    Root,
    Sleep,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EquipmentSlot {
    MainHand,
    OffHand,
    Head,
    Chest,
    Legs,
    Feet,
    Hands,
    Ring1,
    Ring2,
    Necklace,
}

impl EquipmentSlot {
    pub fn all_slots() -> &'static [EquipmentSlot] {
        &[
            EquipmentSlot::MainHand,
            EquipmentSlot::OffHand,
            EquipmentSlot::Head,
            EquipmentSlot::Chest,
            EquipmentSlot::Legs,
            EquipmentSlot::Feet,
            EquipmentSlot::Hands,
            EquipmentSlot::Ring1,
            EquipmentSlot::Ring2,
            EquipmentSlot::Necklace,
        ]
    }
}

// ============================================================================
// 复杂数据结构
// ============================================================================

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Area {
    pub center: Position,
    pub size: SpaceSize,
    pub shape: AreaShape,
}

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum AreaShape {
    Circle { radius: f32 },
    Rectangle { width: f32, height: f32 },
    Cone { radius: f32, angle: f32 },
}

impl Area {
    pub fn contains_point(&self, point: Position) -> bool {
        match self.shape {
            AreaShape::Circle { radius } => {
                self.center.distance_to(&point) <= radius
            },
            AreaShape::Rectangle { width, height } => {
                let dx = (point.x - self.center.x).abs();
                let dy = (point.y - self.center.y).abs();
                dx <= width / 2.0 && dy <= height / 2.0
            },
            AreaShape::Cone { radius, angle: _ } => {
                // 简化实现，当作圆形处理
                self.center.distance_to(&point) <= radius
            },
        }
    }
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StatusEffect {
    pub id: ID,
    pub status_type: StatusType,
    pub duration: Duration,
    pub intensity: f32,
    pub source: Option<BattleUnitId>,
    pub is_permanent: bool,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ActionRestriction {
    pub restriction_type: ActionRestrictionType,
    pub duration: Duration,
    pub source: Option<BattleUnitId>,
}

impl ActionRestriction {
    pub fn blocks_action(&self, action_type: ActionType) -> bool {
        match self.restriction_type {
            ActionRestrictionType::Stun => true, // 眩晕阻止所有行动
            ActionRestrictionType::Silence => matches!(action_type, ActionType::CastSpell),
            ActionRestrictionType::Disarm => matches!(action_type, ActionType::Attack | ActionType::UseItem),
            ActionRestrictionType::Root => matches!(action_type, ActionType::Move),
            ActionRestrictionType::Sleep => true, // 睡眠阻止所有行动
        }
    }
    
    pub fn update(&mut self, delta_time: f32) {
        self.duration = (self.duration - delta_time).max(0.0);
    }
    
    pub fn is_expired(&self) -> bool {
        self.duration <= 0.0
    }
}

// ============================================================================
// 资源消耗结构
// ============================================================================

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct ResourceCost {
    pub mana: Mana,
    pub stamina: Stamina,
    pub action_points: ActionPoints,
}

impl ResourceCost {
    pub fn new(mana: Mana, stamina: Stamina, action_points: ActionPoints) -> Self {
        Self { mana, stamina, action_points }
    }
    
    pub fn none() -> Self {
        Self { mana: 0, stamina: 0, action_points: 0 }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct MovementCost {
    pub stamina_cost: Stamina,
    pub action_points_cost: ActionPoints,
    pub time_cost: f32,
}

// ============================================================================
// 武器和装备信息
// ============================================================================

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct WeaponInfo {
    pub weapon_type: AttackType,
    pub damage_range: (Attack, Attack),
    pub critical_rate_bonus: CriticalRate,
    pub range: Range,
    pub attack_speed: Speed,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct EquipmentBonuses {
    pub attribute_bonuses: HashMap<crate::attribute::AttributeType, AttributeValue>,
    pub resistance_bonuses: HashMap<DamageType, Resistance>,
    pub special_effects: Vec<String>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SetBonus {
    pub set_name: String,
    pub pieces_equipped: u32,
    pub pieces_required: u32,
    pub bonus_description: String,
    pub attribute_bonuses: HashMap<crate::attribute::AttributeType, AttributeValue>,
}

// ============================================================================
// 状态和效果集合
// ============================================================================

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BuffEffects {
    pub attribute_bonuses: HashMap<crate::attribute::AttributeType, AttributeValue>,
    pub resistance_bonuses: HashMap<DamageType, Resistance>,
    pub speed_multiplier: f32,
    pub damage_multiplier: f32,
    pub healing_multiplier: f32,
}

impl Default for BuffEffects {
    fn default() -> Self {
        Self {
            attribute_bonuses: HashMap::new(),
            resistance_bonuses: HashMap::new(),
            speed_multiplier: 1.0,
            damage_multiplier: 1.0,
            healing_multiplier: 1.0,
        }
    }
}

// ============================================================================
// ID类型扩展（基于现有BattleUnitId）
// ============================================================================

// 复用现有的BattleUnitId定义
pub type ExtendedBattleUnitId = BattleUnitId;