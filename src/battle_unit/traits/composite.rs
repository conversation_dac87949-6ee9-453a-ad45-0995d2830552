/// 组合trait定义
///
/// 将多个相关trait组合成完整的功能集合
use super::*;
use crate::attribute::AttributeType;
use crate::shared::{ActionCapabilityFlags, BattleIdentity, GameResult, VitalStatus};
use crate::{AttributeValue, Position, StatusEffect, TemporalEntity};
use action::ActionCapabilities;
use attack::AttackCapabilities;
use attributes::BaseAttributes;
use buff_manager::BuffManager;
use derived_attributes::DerivedAttributes;
use equipment::EquipmentManager;
use health::HealthSystem;
use identity::BattleEntityIdentity;
use mana::ManaSystem;
use movement::MovementCapabilities;
use resistance::ResistanceSystem;
use serde::{Deserialize, Serialize};
use skill_execution::SkillExecution;
use skill_learning::SkillLearning;
use spatial::SpatialEntity;
use stamina::StaminaSystem;
use status_effects::StatusEffectManager;
use std::collections::HashMap;

/// 完整生命实体 - 具备完整生存能力的实体
pub trait CompleteLivingEntity:
    BattleEntityIdentity + SpatialEntity + TemporalEntity + HealthSystem + ManaSystem + StaminaSystem
{
    /// 获取完整生存状态
    fn vital_status(&self) -> VitalStatus {
        VitalStatus {
            health: (self.current_health(), self.max_health()),
            mana: (self.current_mana(), self.max_mana()),
            stamina: (self.current_stamina(), self.max_stamina()),
            is_alive: self.is_alive(),
            fatigue_level: self.fatigue_level(),
        }
    }

    /// 执行生存状态更新
    fn update_vitals(&mut self, delta_time: f32) -> GameResult<()>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompleteBattleStatus {
    /// 战斗身份信息
    pub identity: BattleIdentity,
    /// 生命状态
    pub vital_status: VitalStatus,
    /// 位置
    pub position: Position,
    /// 属性加成
    pub attributes: HashMap<AttributeType, AttributeValue>,
    /// 行动能力
    pub capabilities: ActionCapabilityFlags,
    /// 状态效果
    pub active_effects: Vec<StatusEffect>,
    /// 装备等级
    pub equipment_level: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CombatPowerRating {
    /// 总战斗力
    pub total: u32,
    /// 基础攻击力
    pub base_power: u32,
    /// 等级倍数
    pub level_multiplier: u32,
    /// 装备等级
    pub equipment_bonus: u32,
    /// 技能等级
    pub skill_bonus: u32,
}

/// 完整战斗单位 - 具备所有战斗功能的单位
pub trait CompleteBattleUnit:
    CompleteLivingEntity
    + BaseAttributes
    + DerivedAttributes
    + ResistanceSystem
    + ActionCapabilities
    + MovementCapabilities
    + AttackCapabilities
    + SkillLearning
    + SkillExecution
    + StatusEffectManager
    + BuffManager
    + EquipmentManager
{
    /// 获取完整战斗状态
    fn complete_battle_status(&self) -> CompleteBattleStatus {
        CompleteBattleStatus {
            identity: BattleIdentity {
                entity_id: self.entity_id(),
                name: self.display_name().to_string(),
                entity_type: self.entity_type(),
                faction: self.faction(),
                level: self.level(),
            },
            vital_status: self.vital_status(),
            position: self.position(),
            attributes: self.attribute_bonuses(),
            capabilities: ActionCapabilityFlags {
                can_move: self.can_move(),
                can_attack: self.can_attack(),
                can_cast_spells: self.can_cast_spells(),
                can_use_items: self.can_use_items(),
            },
            active_effects: self.active_statuses(),
            equipment_level: self.equipment_level(),
        }
    }

    /// 执行完整更新
    fn complete_update(&mut self, delta_time: f32) -> GameResult<()> {
        self.update_vitals(delta_time)?;
        self.update_skill_cooldowns(delta_time);
        self.update_status_effects(delta_time);
        self.update_buffs(delta_time);
        self.update_by_time(delta_time)?;
        Ok(())
    }

    /// 计算综合战斗力
    fn combat_power_rating(&self) -> CombatPowerRating {
        let base_power =
            (self.current_attack_power() + self.physical_defense() + self.magical_defense()) as u32;
        let level_multiplier = self.level() as u32;
        let equipment_bonus = self.equipment_level();
        let skill_bonus = self.learned_skills().len() as u32 * 10;

        CombatPowerRating {
            total: base_power * level_multiplier + equipment_bonus + skill_bonus,
            base_power,
            level_multiplier,
            equipment_bonus,
            skill_bonus,
        }
    }
}
