
use crate::shared::types::*;
use crate::battle_system::battle_event::DamageType;
use crate::battle_system::simplified_battle_traits::{BattleEntity, CombatCapable, SkillUser};
use crate::battle_system::battle_config::BattleConfig;
use rand::Rng;
use std::collections::HashMap;

/// 伤害修饰符类型
#[derive(Debug, Clone, PartialEq)]
pub enum DamageModifierType {
    /// 增加伤害（百分比）
    DamageIncrease(f32),
    /// 减少伤害（百分比）
    DamageReduction(f32),
    /// 暴击率加成
    CriticalRateBonus(f32),
    /// 暴击伤害加成
    CriticalDamageBonus(f32),
    /// 格挡率加成
    BlockRateBonus(f32),
    /// 穿透效果
    ArmorPenetration(f32),
    /// 元素抗性
    ElementalResistance(DamageType, f32),
    /// 元素强化
    ElementalAmplification(DamageType, f32),
    /// 伤害免疫
    DamageImmunity(DamageType),
    /// 伤害反射
    DamageReflection(f32),
}

/// 伤害修饰符
#[derive(Debug, Clone)]
pub struct DamageModifier {
    pub modifier_type: DamageModifierType,
    pub source: String,
    pub duration: Option<u32>,
    pub priority: i32, // 优先级，数值越高越先应用
}

/// 增强的伤害计算结果
#[derive(Debug, Clone)]
pub struct EnhancedDamageResult {
    /// 原始伤害
    pub raw_damage: Health,
    /// 最终伤害
    pub final_damage: Health,
    /// 伤害类型
    pub damage_type: DamageType,
    /// 是否暴击
    pub is_critical: bool,
    /// 是否格挡
    pub is_blocked: bool,
    /// 是否闪避
    pub is_dodged: bool,
    /// 是否免疫
    pub is_immune: bool,
    /// 是否是技能伤害
    pub is_skill_damage: bool,
    /// 暴击倍数
    pub critical_multiplier: f32,
    /// 类型有效性
    pub type_effectiveness: f32,
    /// 反射伤害
    pub reflected_damage: Health,
    /// 应用的修饰符描述
    pub applied_modifiers: Vec<String>,
}

impl EnhancedDamageResult {
    pub fn new() -> Self {
        Self {
            raw_damage: 0,
            final_damage: 0,
            damage_type: DamageType::Physical,
            is_critical: false,
            is_blocked: false,
            is_dodged: false,
            is_immune: false,
            is_skill_damage: false,
            critical_multiplier: 1.0,
            type_effectiveness: 1.0,
            reflected_damage: 0,
            applied_modifiers: Vec::new(),
        }
    }
}

/// 增强的治疗结果
#[derive(Debug, Clone)]
pub struct EnhancedHealResult {
    /// 原始治疗量
    pub raw_heal: Health,
    /// 最终治疗量
    pub final_heal: Health,
    /// 应用的修饰符描述
    pub applied_modifiers: Vec<String>,
    /// 是否暴击治疗
    pub is_critical: bool,
    /// 暴击倍数
    pub critical_multiplier: f32,
}

/// 伤害计算器
#[derive(Debug)]
pub struct DamageCalculator {
    /// 战斗配置
    pub config: BattleConfig,
    /// 全局伤害修饰符
    pub global_modifiers: Vec<DamageModifier>,
    /// 单位特定修饰符
    pub unit_modifiers: HashMap<String, Vec<DamageModifier>>,
    /// 伤害类型有效性表
    pub type_effectiveness: HashMap<(DamageType, DamageType), f32>,
}

impl Default for DamageCalculator {
    fn default() -> Self {
        let mut type_effectiveness = HashMap::new();
        
        // 设置伤害类型相克关系
        // 物理 vs 其他
        type_effectiveness.insert((DamageType::Physical, DamageType::Physical), 1.0);
        type_effectiveness.insert((DamageType::Physical, DamageType::Magical), 1.0);
        
        // 魔法 vs 其他
        type_effectiveness.insert((DamageType::Magical, DamageType::Physical), 1.0);
        type_effectiveness.insert((DamageType::Magical, DamageType::Magical), 1.0);
        
        // 元素相克
        type_effectiveness.insert((DamageType::Burn, DamageType::Magical), 1.2); // 火克魔法
        type_effectiveness.insert((DamageType::Magical, DamageType::Burn), 0.8); // 魔法弱于火
        
        // 毒素和流血对生物单位更有效
        type_effectiveness.insert((DamageType::Poison, DamageType::Physical), 1.3);
        type_effectiveness.insert((DamageType::Bleed, DamageType::Physical), 1.3);
        
        // 真实伤害对所有类型都是1.0（无视抗性）
        type_effectiveness.insert((DamageType::True, DamageType::Physical), 1.0);
        type_effectiveness.insert((DamageType::True, DamageType::Magical), 1.0);
        type_effectiveness.insert((DamageType::True, DamageType::Burn), 1.0);
        type_effectiveness.insert((DamageType::True, DamageType::Poison), 1.0);
        type_effectiveness.insert((DamageType::True, DamageType::Bleed), 1.0);
        
        Self {
            config: BattleConfig::default(),
            global_modifiers: Vec::new(),
            unit_modifiers: HashMap::new(),
            type_effectiveness,
        }
    }
}

impl DamageCalculator {
    /// 创建新的伤害计算器
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 使用指定配置创建伤害计算器
    pub fn with_config(config: BattleConfig) -> Self {
        let mut calculator = Self::default();
        calculator.config = config;
        calculator
    }
    
    /// 添加全局伤害修饰符
    pub fn add_global_modifier(&mut self, modifier: DamageModifier) {
        self.global_modifiers.push(modifier);
    }
    
    /// 添加单位特定修饰符
    pub fn add_unit_modifier(&mut self, unit_name: &str, modifier: DamageModifier) {
        self.unit_modifiers
            .entry(unit_name.to_string())
            .or_insert_with(Vec::new)
            .push(modifier);
    }
    
    /// 移除过期的修饰符
    pub fn update_modifiers(&mut self) {
        // 更新全局修饰符
        self.global_modifiers.retain_mut(|modifier| {
            if let Some(ref mut duration) = modifier.duration {
                if *duration > 0 {
                    *duration -= 1;
                    true
                } else {
                    false
                }
            } else {
                true // 永久修饰符
            }
        });
        
        // 更新单位修饰符
        for modifiers in self.unit_modifiers.values_mut() {
            modifiers.retain_mut(|modifier| {
                if let Some(ref mut duration) = modifier.duration {
                    if *duration > 0 {
                        *duration -= 1;
                        true
                    } else {
                        false
                    }
                } else {
                    true
                }
            });
        }
        
        // 移除空的单位修饰符列表
        self.unit_modifiers.retain(|_, modifiers| !modifiers.is_empty());
    }
    
    /// 计算基础攻击伤害
    pub fn calculate_basic_damage<
        T: BattleEntity + CombatCapable + SkillUser,
        U: BattleEntity + CombatCapable + SkillUser,
    >(
        &self,
        attacker: &T,
        target: &U,
        damage_type: DamageType,
    ) -> DamageResult {
        let raw_damage = self.calculate_raw_damage(attacker, target, &damage_type);
        let (final_damage, is_critical, is_blocked) = self.apply_modifiers(
            raw_damage,
            attacker,
            target,
            &damage_type,
        );
        
        DamageResult {
            raw_damage,
            final_damage,
            damage_type,
            is_critical,
            is_blocked,
            is_dodged: false, // 基础攻击不考虑闪避
        }
    }
    
    /// 计算技能伤害
    pub fn calculate_skill_damage<
        T: BattleEntity + CombatCapable + SkillUser,
        U: BattleEntity + CombatCapable + SkillUser,
    >(
        &self,
        attacker: &T,
        target: &U,
        skill_damage: Health,
        damage_type: DamageType,
    ) -> DamageResult {
        // 技能伤害 = 基础技能伤害 + 攻击力加成
        let attack_bonus = (attacker.attack_power() as f32 * 0.5) as Health; // 50%攻击力加成
        let raw_damage = skill_damage + attack_bonus;
        
        let (final_damage, is_critical, is_blocked) = self.apply_modifiers(
            raw_damage,
            attacker,
            target,
            &damage_type,
        );
        
        DamageResult {
            raw_damage,
            final_damage,
            damage_type,
            is_critical,
            is_blocked,
            is_dodged: false, // 技能伤害不考虑闪避
        }
    }
    
    /// 计算原始伤害（攻击力 - 防御力）
    fn calculate_raw_damage<
        T: BattleEntity + CombatCapable,
        U: BattleEntity + CombatCapable,
    >(
        &self,
        attacker: &T,
        target: &U,
        damage_type: &DamageType,
    ) -> Health {
        let attack = attacker.attack_power();
        let defense = match damage_type {
            DamageType::Physical => target.defense_power(),
            DamageType::Magical => target.defense_power() / 2, // 魔法伤害减半防御
            DamageType::True => 0,                            // 真实伤害无视防御
            DamageType::Burn | DamageType::Poison | DamageType::Bleed => {
                target.defense_power() / 3 // DOT伤害受少量防御影响
            }
        };
        
        // 确保伤害至少为1
        std::cmp::max(1, attack.saturating_sub(defense))
    }
    
    /// 应用各种修正因子（暴击、格挡、状态效果等）
    fn apply_modifiers<
        T: BattleEntity + CombatCapable + SkillUser,
        U: BattleEntity + CombatCapable + SkillUser,
    >(
        &self,
        raw_damage: Health,
        attacker: &T,
        target: &U,
        damage_type: &DamageType,
    ) -> (Health, bool, bool) {
        let mut final_damage = raw_damage as f32;
        let mut is_critical = false;
        let mut is_blocked = false;
        
        // 检查暴击
        if self.roll_critical(attacker) {
            is_critical = true;
            final_damage *= self.config.damage.critical_multiplier;
        }
        
        // 检查格挡（真实伤害无法格挡）
        if !matches!(damage_type, DamageType::True) && self.roll_block(target) {
            is_blocked = true;
            final_damage *= 1.0 - self.config.damage.block_reduction;
        }
        
        // 应用攻击者状态效果
        final_damage = self.apply_attacker_status_effects(final_damage, attacker);
        
        // 应用目标状态效果
        final_damage = self.apply_target_status_effects(final_damage, target);
        
        // 确保最终伤害至少为1
        let final_damage = std::cmp::max(1, final_damage as Health);
        
        (final_damage, is_critical, is_blocked)
    }
    
    /// 判定暴击
    fn roll_critical<T: BattleEntity + CombatCapable>(&self, attacker: &T) -> bool {
        let mut rng = rand::thread_rng();
        let roll: f32 = rng.gen();

        // 基础暴击率 + 等级加成
        let critical_rate = self.config.damage.base_critical_rate
            + (attacker.level() as f32 * self.config.damage.level_critical_bonus);
        roll < critical_rate.min(self.config.damage.max_critical_rate)
    }
    
    /// 判定格挡
    fn roll_block<T: CombatCapable>(&self, _target: &T) -> bool {
        let mut rng = rand::thread_rng();
        let roll: f32 = rng.gen();

        // 基础格挡率
        let block_rate = self.config.damage.base_block_rate;
        roll < block_rate.min(self.config.damage.max_block_rate)
    }

    /// 应用攻击者的状态效果
    fn apply_attacker_status_effects<T: SkillUser>(&self, damage: f32, attacker: &T) -> f32 {
        let final_damage = damage;
        for buff in attacker.get_buffs() {
            if buff.is_active() {
                // ...
            }
        }
        final_damage
    }

    /// 应用目标的状态效果
    fn apply_target_status_effects<T: SkillUser>(&self, damage: f32, target: &T) -> f32 {
        let final_damage = damage;
        for buff in target.get_buffs() {
            if buff.is_active() {
                // ...
            }
        }
        final_damage
    }

    /// 计算治疗量
    pub fn calculate_heal_amount<T: CombatCapable>(&self, healer: &T, base_heal: Health) -> Health {
        // 治疗量可以受益于攻击力（法强）
        let power_bonus = (healer.attack_power() as f32 * 0.3) as Health;
        base_heal + power_bonus
    }

    /// 应用状态效果到伤害
    pub fn apply_status_effects_to_damage<T: SkillUser>(
        &self,
        unit: &T,
        base_damage: f32,
    ) -> f32 {
        let modified_damage = base_damage;
        for buff in unit.get_buffs() {
            if buff.is_active() {
                // ...
            }
        }
        modified_damage
    }
}

/// 临时的伤害结果结构体
pub struct DamageResult {
    /// 原始伤害
    pub raw_damage: Health,
    /// 最终伤害
    pub final_damage: Health,
    /// 伤害类型
    pub damage_type: DamageType,
    /// 是否暴击
    pub is_critical: bool,
    /// 是否格挡
    pub is_blocked: bool,
    /// 是否闪避
    pub is_dodged: bool,
}

impl DamageResult {
    /// 创建新的伤害结果
    pub fn new(
        raw_damage: Health,
        final_damage: Health,
        damage_type: DamageType,
    ) -> Self {
        Self {
            raw_damage,
            final_damage,
            damage_type,
            is_critical: false,
            is_blocked: false,
            is_dodged: false,
        }
    }

    /// 设置暴击
    pub fn with_critical(mut self, is_critical: bool) -> Self {
        self.is_critical = is_critical;
        self
    }

    /// 设置格挡
    pub fn with_block(mut self, is_blocked: bool) -> Self {
        self.is_blocked = is_blocked;
        self
    }

    /// 设置闪避
    pub fn with_dodge(mut self, is_dodged: bool) -> Self {
        self.is_dodged = is_dodged;
        self
    }
}
