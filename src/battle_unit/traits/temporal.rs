/// 时间感知trait
/// 
/// 处理时间相关的更新和状态管理

use crate::shared::*;
use crate::battle_system::advanced_battle_types::GameTime;

/// 时间感知 - 处理时间相关的更新和状态
pub trait TemporalEntity {
    /// 根据时间更新状态
    fn update_by_time(&mut self, delta_seconds: f32) -> GameResult<()>;
    
    /// 获取创建时间
    fn creation_time(&self) -> GameTime;
    
    /// 获取存在时长
    fn age(&self, current_time: GameTime) -> f32;
    
    /// 是否为临时实体
    fn is_temporary(&self) -> bool;
    
    /// 获取生存时间限制（如果有）
    fn lifetime_limit(&self) -> Option<f32>;
}