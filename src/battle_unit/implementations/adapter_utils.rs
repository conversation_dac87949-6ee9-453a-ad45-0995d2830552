/// 适配器工具方法
/// 
/// 提供通用的计算和辅助方法

use super::adapter_struct::{BattleUnitAdapter, CombatModifiers};
use crate::shared::*;

use crate::attribute::{AttributeType, CoreAttribute};
use std::collections::HashMap;

impl BattleUnitAdapter {
    /// 获取总属性值（基础 + 装备 + Buff）
    pub fn calculate_total_attribute(&self, attr_type: AttributeType) -> i32 {
        let base = self.attribute_set.get(&attr_type)
            .map(|attr| attr.value as i32)
            .unwrap_or(10);
        
        let bonus = self.attribute_bonuses.get(&attr_type).copied().unwrap_or(0);
        let equipment_bonus = self.equipment_bar.get_total_equipment_attributes()
            .get(&attr_type)
            .map(|attr| attr.value as i32)
            .unwrap_or(0);
        
        base + bonus + equipment_bonus
    }
    
    /// 计算D&D风格的属性修正值
    pub fn calculate_modifier(attribute_value: i32) -> i32 {
        (attribute_value - 10) / 2
    }
    
    /// 更新buff状态
    pub fn update_buffs(&mut self, delta_time: f32) {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs_f64();
        
        self.buff_manager.update(current_time);
        
        // 应用buff效果到属性加成
        self.apply_buff_effects();
    }
    
    /// 应用buff效果
    pub fn apply_buff_effects(&mut self) {
        // 重置属性加成
        self.attribute_bonuses.clear();
        self.combat_modifiers = CombatModifiers::default();
        
        // 应用所有active buff的效果
        for buff in &self.buff_manager.active_buffs {
            if let Some(attributes) = &buff.attributes {
                for (attr_type, attr) in &attributes.attributes {
                    *self.attribute_bonuses.entry(*attr_type).or_insert(0) += attr.value as i32;
                }
            }
        }
    }
    
    /// 基于五行属性计算战斗属性
    pub fn calculate_combat_attributes(&self) -> (Attack, Defense, Speed) {
        let fire_value = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Fire)
        );
        let earth_value = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Earth)
        );
        let water_value = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Water)
        );
        
        let attack = (fire_value * 2 + self.combat_modifiers.attack_bonus) as Attack;
        let defense = (earth_value * 2 + self.combat_modifiers.defense_bonus) as Defense;
        let speed = ((water_value as f32 * 1.5 * self.combat_modifiers.speed_multiplier) as Speed)
            .max(1); // 最低速度为1
        
        (attack, defense, speed)
    }
    
    /// 检查是否有指定状态
    pub fn has_status(&self, status_type: StatusType) -> bool {
        self.status_effects.iter().any(|effect| effect.status_type == status_type)
    }
    
    /// 获取疲劳等级
    pub fn get_fatigue_level(&self) -> FatigueLevel {
        match self.movement_fatigue {
            f if f >= 0.8 => FatigueLevel::Exhausted,
            f if f >= 0.6 => FatigueLevel::Tired,
            f if f >= 0.3 => FatigueLevel::Fatigued,
            _ => FatigueLevel::Fresh,
        }
    }
}