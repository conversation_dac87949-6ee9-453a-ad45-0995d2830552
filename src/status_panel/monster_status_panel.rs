use crate::status_panel::StatusPanel;
use crate::Monster;

impl StatusPanel for Monster {
    fn print_status(&self) {
        println!("怪物：{} (Lv.{}) [{:?}]", self.profile.name, self.profile.level, self.profile.kind);
        println!("生命值：{}/{}", self.hp, self.max_hp);
        println!("基础属性：");
        println!("  主属性：{:?}", self.profile.main_attribute);
        println!("  品阶：{}", self.profile.grade);
        if !self.equipment.is_empty() {
            println!("装备（掉落）：");
            for eq in &self.equipment {
                println!("  - {} [{:?}]", eq.name, eq.eq_type);
            }
        }
    }

    fn get_status_data(&self) -> std::collections::HashMap<String, serde_json::Value> {
        let mut map = std::collections::HashMap::new();
        map.insert("name".to_string(), serde_json::Value::String(self.profile.name.clone()));
        map.insert("level".to_string(), serde_json::Value::Number(self.profile.level.into()));
        map.insert("kind".to_string(), serde_json::Value::String(format!("{:?}", self.profile.kind)));
        map.insert("main_attribute".to_string(), serde_json::Value::String(format!("{:?}", self.profile.main_attribute)));
        map.insert("grade".to_string(), serde_json::Value::String(self.profile.grade.clone()));
        map.insert(
            "equipment".to_string(),
            serde_json::Value::Array(
                self.equipment
                    .iter()
                    .map(|eq| serde_json::Value::String(eq.name.clone()))
                    .collect(),
            ),
        );
        map
    }
}