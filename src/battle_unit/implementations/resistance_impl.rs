/// 抗性系统trait实现
/// 
/// 实现ResistanceSystem trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;
use crate::battle_unit::types::*;
use crate::battle_unit::traits::ResistanceSystem;
use crate::attribute::{AttributeType, CoreAttribute};
use crate::skill::domain::value_objects::ElementType;

impl ResistanceSystem for BattleUnitAdapter {
    fn elemental_resistance(&self, element: ElementType) -> Resistance {
        let mut resistance = 0.0;
        
        // 基础五行抗性（相克相生关系）
        let base_resistance = match element {
            ElementType::Metal => {
                let fire_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Fire)
                );
                // 火克金，火属性越高对金系伤害抗性越低
                -(fire_attr as f32 * 0.01)
            },
            ElementType::Wood => {
                let metal_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Metal)
                );
                // 金克木
                -(metal_attr as f32 * 0.01)
            },
            ElementType::Water => {
                let earth_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Earth)
                );
                // 土克水
                -(earth_attr as f32 * 0.01)
            },
            ElementType::Fire => {
                let water_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Water)
                );
                // 水克火
                -(water_attr as f32 * 0.01)
            },
            ElementType::Earth => {
                let wood_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Wood)
                );
                // 木克土
                -(wood_attr as f32 * 0.01)
            },
            ElementType::Pure => 0.0, // 纯净伤害无基础抗性
            ElementType::Chaos => -0.1, // 混沌伤害基础易伤
        };
        
        resistance += base_resistance;
        
        // 装备抗性加成
        let equipment_resistance = self.get_equipment_resistance(element);
        resistance += equipment_resistance;
        
        // Buff抗性加成
        let buff_resistance = self.get_buff_resistance(element);
        resistance += buff_resistance;
        
        resistance.clamp(-1.0, 0.95) // 最大95%抗性，可以有易伤
    }
    
    fn physical_resistance(&self) -> Resistance {
        let mut resistance = 0.0;
        
        // 基于金属性和护甲
        let metal_attr = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Metal)
        );
        resistance += (metal_attr as f32 * 0.005); // 每点金属性0.5%物理抗性
        
        // 装备物理抗性
        resistance += self.get_equipment_physical_resistance();
        
        // Buff物理抗性
        resistance += self.get_buff_physical_resistance();
        
        resistance.clamp(0.0, 0.9) // 最大90%物理抗性
    }
    
    fn magical_resistance(&self) -> Resistance {
        let mut resistance = 0.0;
        
        // 基于水属性（智力/魔法抗性）
        let water_attr = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Water)
        );
        resistance += (water_attr as f32 * 0.005); // 每点水属性0.5%魔法抗性
        
        // 装备魔法抗性
        resistance += self.get_equipment_magical_resistance();
        
        // Buff魔法抗性
        resistance += self.get_buff_magical_resistance();
        
        resistance.clamp(0.0, 0.9) // 最大90%魔法抗性
    }
    
    fn status_resistance(&self, status: StatusType) -> Resistance {
        let mut resistance = 0.0;
        
        // 基于对应属性的状态抗性
        match status {
            StatusType::Burn => {
                let water_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Water)
                );
                resistance += (water_attr as f32 * 0.008); // 水克火
            },
            StatusType::Poison => {
                let metal_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Metal)
                );
                resistance += (metal_attr as f32 * 0.008); // 金属性抗毒
            },
            StatusType::Freeze => {
                let fire_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Fire)
                );
                resistance += (fire_attr as f32 * 0.008); // 火克冰
            },
            StatusType::Paralysis => {
                let wood_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Wood)
                );
                resistance += (wood_attr as f32 * 0.008); // 木属性活力抗麻痹
            },
            StatusType::Sleep => {
                let earth_attr = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Earth)
                );
                resistance += (earth_attr as f32 * 0.008); // 土属性稳重抗睡眠
            },
            StatusType::Stun => {
                // 综合抗性
                let total_attrs = self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Metal)
                ) + self.calculate_total_attribute(
                    AttributeType::Base(CoreAttribute::Earth)
                );
                resistance += (total_attrs as f32 * 0.004);
            },
        }
        
        // 装备状态抗性
        resistance += self.get_equipment_status_resistance(status);
        
        // Buff状态抗性
        resistance += self.get_buff_status_resistance(status);
        
        resistance.clamp(0.0, 0.85) // 最大85%状态抗性
    }
    
    fn vulnerability(&self, damage_type: DamageType) -> Resistance {
        match damage_type {
            DamageType::Physical => -self.physical_resistance().min(0.0),
            DamageType::Elemental(element) => -self.elemental_resistance(element).min(0.0),
            DamageType::Magical => -self.magical_resistance().min(0.0),
            DamageType::Pure => 0.0, // 纯净伤害无易伤
        }
    }
    
    fn calculate_damage_reduction(&self, damage_type: DamageType) -> Resistance {
        match damage_type {
            DamageType::Physical => self.physical_resistance(),
            DamageType::Elemental(element) => self.elemental_resistance(element),
            DamageType::Magical => self.magical_resistance(),
            DamageType::Pure => 0.0, // 纯净伤害无减免
        }
    }
}

impl BattleUnitAdapter {
    fn get_equipment_resistance(&self, element: ElementType) -> Resistance {
        // TODO: 实现装备元素抗性计算
        0.0
    }
    
    fn get_buff_resistance(&self, element: ElementType) -> Resistance {
        // TODO: 实现buff元素抗性计算
        0.0
    }
    
    fn get_equipment_physical_resistance(&self) -> Resistance {
        // TODO: 实现装备物理抗性计算
        0.0
    }
    
    fn get_buff_physical_resistance(&self) -> Resistance {
        // TODO: 实现buff物理抗性计算
        0.0
    }
    
    fn get_equipment_magical_resistance(&self) -> Resistance {
        // TODO: 实现装备魔法抗性计算
        0.0
    }
    
    fn get_buff_magical_resistance(&self) -> Resistance {
        // TODO: 实现buff魔法抗性计算
        0.0
    }
    
    fn get_equipment_status_resistance(&self, status: StatusType) -> Resistance {
        // TODO: 实现装备状态抗性计算
        0.0
    }
    
    fn get_buff_status_resistance(&self, status: StatusType) -> Resistance {
        // TODO: 实现buff状态抗性计算
        0.0
    }
}