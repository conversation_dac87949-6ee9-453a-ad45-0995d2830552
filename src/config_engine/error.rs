//! # 错误处理系统
//!
//! 定义配置引擎的所有错误类型和处理机制

use thiserror::Error;

/// 配置引擎的主要错误类型
#[derive(Error, Debug)]
pub enum ConfigEngineError {
    /// 配置相关错误
    #[error("配置错误: {0}")]
    Configuration(#[from] ConfigurationError),

    /// 规则执行错误
    #[error("规则执行错误: {0}")]
    RuleExecution(#[from] RuleExecutionError),

    /// 类型系统错误
    #[error("类型错误: {0}")]
    TypeSystem(#[from] TypeSystemError),

    /// 验证错误
    #[error("验证错误: {0}")]
    Validation(#[from] ValidationError),

    /// 缓存系统错误
    #[error("缓存错误: {0}")]
    Cache(#[from] CacheError),

    /// IO错误
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    /// 序列化/反序列化错误
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),

    /// 网络错误
    #[cfg(feature = "reqwest")]
    #[error("网络错误: {0}")]
    Network(#[from] reqwest::Error),

    /// 数据库错误
    #[cfg(feature = "sqlx")]
    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),

    /// 通用错误
    #[error("内部错误: {message}")]
    Internal { message: String },

    /// 无效配置错误
    #[error("配置无效: {0}")]
    InvalidConfiguration(String),

    /// 超时错误
    #[error("操作超时: {operation}")]
    Timeout { operation: String },

    /// 资源不足错误
    #[error("资源不足: {resource}")]
    ResourceExhausted { resource: String },
}

/// 配置相关错误
#[derive(Error, Debug)]
pub enum ConfigurationError {
    #[error("配置源不可用: {source}")]
    SourceUnavailable { source: String },

    #[error("配置文件格式错误: {file} - {reason}")]
    InvalidFormat { file: String, reason: String },

    #[error("配置文件不存在: {file}")]
    FileNotFound { file: String },

    #[error("配置解析错误: {details}")]
    ParseError { details: String },

    #[error("配置加载失败: {source} - {reason}")]
    LoadFailure { source: String, reason: String },

    #[error("配置版本不兼容: 需要版本 {required}, 但找到版本 {found}")]
    VersionMismatch { required: String, found: String },

    #[error("配置合并冲突: {conflict}")]
    MergeConflict { conflict: String },

    #[error("热重载失败: {reason}")]
    HotReloadFailure { reason: String },

    #[error("版本未找到: {version}")]
    VersionNotFound { version: String },

    #[error("标签未找到: {tag}")]
    TagNotFound { tag: String },

    #[error("无法删除当前版本: {version}")]
    CannotDeleteCurrentVersion { version: String },

    #[error("序列化错误: {reason}")]
    SerializationError { reason: String },

    #[error("回滚超时")]
    RollbackTimeout,

    #[error("验证超时")]
    ValidationTimeout,

    #[error("没有当前版本")]
    NoCurrentVersion,

    #[error("没有上一个版本")]
    NoPreviousVersion,

    #[error("验证失败: {errors:?}")]
    ValidationFailure { errors: Vec<String> },
}

/// 规则执行错误
#[derive(Error, Debug)]
pub enum RuleExecutionError {
    #[error("规则未找到: {rule_id}")]
    RuleNotFound { rule_id: String, rule_name: String },

    #[error("规则编译失败: {rule_name} - {reason}")]
    CompilationFailure { rule_name: String, reason: String },

    #[error("执行超时: {rule_name} - 超过 {timeout_ms}ms")]
    ExecutionTimeout { rule_name: String, timeout_ms: u64 },

    #[error("输入参数无效: {rule_name} - {parameter}: {reason}")]
    InvalidInput {
        rule_name: String,
        parameter: String,
        reason: String,
    },

    #[error("规则逻辑错误: {rule_name} - {details}")]
    LogicError { rule_name: String, details: String },

    #[error("依赖规则失败: {rule_name} 依赖 {dependency}")]
    DependencyFailure {
        rule_name: String,
        dependency: String,
    },

    #[error("并发执行限制: 超过最大并发数 {max_concurrent}")]
    ConcurrencyLimitExceeded { max_concurrent: usize },

    #[error("规则执行异常: {rule_name} - {error}")]
    RuntimeError { rule_name: String, error: String },
}

/// 类型系统错误
#[derive(Error, Debug)]
pub enum TypeSystemError {
    #[error("类型未注册: {type_name}")]
    TypeNotRegistered { type_name: String },

    #[error("类型冲突: {type_name} - {conflict}")]
    TypeConflict { type_name: String, conflict: String },

    #[error("类型转换失败: 无法将 {from_type} 转换为 {to_type}")]
    ConversionFailure { from_type: String, to_type: String },

    #[error("类型定义无效: {type_name} - {reason}")]
    InvalidTypeDefinition { type_name: String, reason: String },

    #[error("字段不存在: 类型 {type_name} 中没有字段 {field_name}")]
    FieldNotFound {
        type_name: String,
        field_name: String,
    },

    #[error("字段类型不匹配: {type_name}.{field_name} 期望 {expected}, 得到 {actual}")]
    FieldTypeMismatch {
        type_name: String,
        field_name: String,
        expected: String,
        actual: String,
    },
}

/// 验证错误
#[derive(Error, Debug)]
pub enum ValidationError {
    #[error("Schema验证失败: {path}")]
    SchemaValidation { path: String, errors: Vec<String> },

    #[error("必填字段缺失: {field_name}")]
    RequiredFieldMissing { field_name: String },

    #[error("字段值超出范围: {field_name} 值 {value} 不在范围 [{min}, {max}]")]
    ValueOutOfRange {
        field_name: String,
        value: String,
        min: String,
        max: String,
    },

    #[error("字段值格式错误: {field_name} - {pattern}")]
    InvalidFormat { field_name: String, pattern: String },

    #[error("引用完整性错误: {reference} 指向不存在的对象")]
    ReferenceIntegrityError { reference: String },

    #[error("循环依赖检测: {dependency_chain}")]
    CircularDependency { dependency_chain: String },

    #[error("业务规则违反: {rule_name} - {violation}")]
    BusinessRuleViolation {
        rule_name: String,
        violation: String,
    },
}

/// 缓存错误
#[derive(Error, Debug)]
pub enum CacheError {
    #[error("缓存初始化失败: {reason}")]
    InitializationFailure { reason: String },

    #[error("缓存存储失败: {key} - {reason}")]
    StorageFailure { key: String, reason: String },

    #[error("缓存检索失败: {key} - {reason}")]
    RetrievalFailure { key: String, reason: String },

    #[error("缓存过期: {key}")]
    CacheExpired { key: String },

    #[error("缓存容量不足: 当前 {current_size}, 最大 {max_size}")]
    CapacityExceeded {
        current_size: usize,
        max_size: usize,
    },

    #[error("缓存键冲突: {key}")]
    KeyConflict { key: String },

    #[error("缓存失效失败: {pattern} - {reason}")]
    InvalidationFailure { pattern: String, reason: String },
}

/// 配置引擎结果类型
pub type Result<T> = std::result::Result<T, ConfigEngineError>;

/// 错误上下文信息
#[derive(Debug, Clone)]
pub struct ErrorContext {
    pub operation: String,
    pub rule_name: Option<String>,
    pub config_source: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub additional_info: std::collections::HashMap<String, String>,
}

impl ErrorContext {
    pub fn new(operation: impl Into<String>) -> Self {
        Self {
            operation: operation.into(),
            rule_name: None,
            config_source: None,
            timestamp: chrono::Utc::now(),
            additional_info: std::collections::HashMap::new(),
        }
    }

    pub fn with_rule(mut self, rule_name: impl Into<String>) -> Self {
        self.rule_name = Some(rule_name.into());
        self
    }

    pub fn with_source(mut self, source: impl Into<String>) -> Self {
        self.config_source = Some(source.into());
        self
    }

    pub fn with_info(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.additional_info.insert(key.into(), value.into());
        self
    }
}

/// 错误报告构建器
pub struct ErrorReporter {
    errors: Vec<(ConfigEngineError, ErrorContext)>,
}

impl ErrorReporter {
    pub fn new() -> Self {
        Self { errors: Vec::new() }
    }

    pub fn add_error(&mut self, error: ConfigEngineError, context: ErrorContext) {
        self.errors.push((error, context));
    }

    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }

    pub fn error_count(&self) -> usize {
        self.errors.len()
    }

    pub fn generate_report(&self) -> String {
        let mut report = String::new();
        report.push_str(&format!(
            "配置引擎错误报告 ({} 个错误)\n",
            self.errors.len()
        ));
        report.push_str("=".repeat(50).as_str());
        report.push('\n');

        for (i, (error, context)) in self.errors.iter().enumerate() {
            report.push_str(&format!("\n错误 {}: {}\n", i + 1, error));
            report.push_str(&format!("  操作: {}\n", context.operation));

            if let Some(rule) = &context.rule_name {
                report.push_str(&format!("  规则: {}\n", rule));
            }

            if let Some(source) = &context.config_source {
                report.push_str(&format!("  配置源: {}\n", source));
            }

            report.push_str(&format!(
                "  时间: {}\n",
                context.timestamp.format("%Y-%m-%d %H:%M:%S UTC")
            ));

            if !context.additional_info.is_empty() {
                report.push_str("  附加信息:\n");
                for (key, value) in &context.additional_info {
                    report.push_str(&format!("    {}: {}\n", key, value));
                }
            }
        }

        report
    }
}

impl Default for ErrorReporter {
    fn default() -> Self {
        Self::new()
    }
}

/// 可恢复错误处理
#[derive(Debug)]
pub enum RecoveryAction {
    /// 重试操作
    Retry { max_attempts: usize, delay_ms: u64 },
    /// 使用默认值
    UseDefault { value: serde_json::Value },
    /// 跳过该项
    Skip,
    /// 降级处理
    Fallback { alternative: String },
    /// 停止处理
    Abort,
}

/// 错误恢复策略
pub trait ErrorRecoveryStrategy {
    fn handle_error(&self, error: &ConfigEngineError, context: &ErrorContext) -> RecoveryAction;
}

/// 默认错误恢复策略
pub struct DefaultRecoveryStrategy;

impl ErrorRecoveryStrategy for DefaultRecoveryStrategy {
    fn handle_error(&self, error: &ConfigEngineError, _context: &ErrorContext) -> RecoveryAction {
        match error {
            ConfigEngineError::Configuration(ConfigurationError::SourceUnavailable { .. }) => {
                RecoveryAction::Retry {
                    max_attempts: 3,
                    delay_ms: 1000,
                }
            }
            ConfigEngineError::RuleExecution(RuleExecutionError::ExecutionTimeout { .. }) => {
                RecoveryAction::Retry {
                    max_attempts: 1,
                    delay_ms: 0,
                }
            }
            ConfigEngineError::Cache(CacheError::CapacityExceeded { .. }) => RecoveryAction::Skip,
            _ => RecoveryAction::Abort,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_context_creation() {
        let context = ErrorContext::new("test_operation")
            .with_rule("test_rule")
            .with_source("test_source")
            .with_info("key1", "value1");

        assert_eq!(context.operation, "test_operation");
        assert_eq!(context.rule_name, Some("test_rule".to_string()));
        assert_eq!(context.config_source, Some("test_source".to_string()));
        assert_eq!(
            context.additional_info.get("key1"),
            Some(&"value1".to_string())
        );
    }

    #[test]
    fn test_error_reporter() {
        let mut reporter = ErrorReporter::new();
        assert!(!reporter.has_errors());

        let error = ConfigEngineError::InvalidConfiguration("test error".to_string());
        let context = ErrorContext::new("test");

        reporter.add_error(error, context);
        assert!(reporter.has_errors());
        assert_eq!(reporter.error_count(), 1);

        let report = reporter.generate_report();
        assert!(report.contains("配置引擎错误报告"));
        assert!(report.contains("test error"));
    }

    #[test]
    fn test_recovery_strategy() {
        let strategy = DefaultRecoveryStrategy;
        let error = ConfigEngineError::Configuration(ConfigurationError::SourceUnavailable {
            source: "test".to_string(),
        });
        let context = ErrorContext::new("test");

        match strategy.handle_error(&error, &context) {
            RecoveryAction::Retry {
                max_attempts,
                delay_ms,
            } => {
                assert_eq!(max_attempts, 3);
                assert_eq!(delay_ms, 1000);
            }
            _ => panic!("Expected Retry action"),
        }
    }
}
