use crate::skill::Skill;
use crate::skill::SkillType;

/// 技能状态枚举
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum SkillState {
    Ready,           // 就绪状态
    Casting,         // 施法中
    Cooldown(u32),   // 冷却中(剩余持续时间)
    Disabled,        // 禁用状态
    Channeling(u32), // 引导中(剩余持续时间)
}

/// 技能状态管理trait
pub trait SkillStateManage {
    /// 获取技能的当前状态
    fn get_state(&self) -> SkillState;

    /// 设置技能的状态
    fn set_state(&mut self, state: SkillState);

    /// 检查技能是否处于就绪状态
    fn is_ready(&self) -> bool;

    /// 更新技能状态
    /// 包括冷却时间、引导时间等状态的更新
    fn update_state(&mut self);
}

impl SkillStateManage for Skill {
    fn get_state(&self) -> SkillState {
        self.state
    }

    fn set_state(&mut self, state: SkillState) {
        self.state = state;
    }

    fn is_ready(&self) -> bool {
        matches!(self.state, SkillState::Ready)
    }

    fn update_state(&mut self) {
        match self.state {
            SkillState::Cooldown(remaining) if remaining > 0 => {
                self.state = SkillState::Cooldown(remaining - 1);
            }
            SkillState::Cooldown(0) => {
                self.state = SkillState::Ready;
            }
            SkillState::Channeling(remaining) if remaining > 0 => {
                self.state = SkillState::Channeling(remaining - 1);
            }
            SkillState::Channeling(0) => {
                self.state = SkillState::Ready;
            }
            _ => {}
        }
    }
}

/// 技能状态事件
#[derive(Debug, Clone)]
/// 技能状态事件，用于记录技能状态变化时的详细信息
pub struct SkillStateEvent {
    /// 技能唯一标识
    pub skill_id: u32,
    /// 技能状态变化前的状态
    pub old_state: SkillState,
    /// 技能状态变化后的状态
    pub new_state: SkillState,
    /// 事件发生的时间戳
    pub timestamp: u64,
    /// 技能名称
    pub skill_name: String,
    /// 技能等级
    pub skill_level: u32,
    /// 技能类型（主动、被动等）
    pub skill_type: SkillType,
    /// 剩余冷却时间（如果有）
    pub remaining_cooldown: Option<u32>,
    /// 剩余引导时间（如果有）
    pub remaining_channeling: Option<u32>,
    /// 技能释放者ID
    pub skill_owner_id: u32,
    /// 技能释放者名称
    pub skill_owner_name: String,
    /// 技能目标ID列表（可能有多个目标）
    pub skill_target_ids: Vec<u32>,
    /// 技能消耗（法力/能量等）
    pub skill_cost: Option<f64>,
    /// 技能造成的伤害值（如果有）
    pub skill_damage: Option<f64>,
    /// 技能造成的治疗值（如果有）
    pub skill_healing: Option<f64>,
    /// 技能施加的增益效果列表
    pub skill_buffs: Vec<Buff>,
    /// 技能施加的减益效果列表
    pub skill_debuffs: Vec<Buff>,
    /// 技能是否成功释放
    pub skill_success: bool,
    /// 技能释放失败的原因（如果失败）
    pub skill_failure_reason: Option<String>,
    /// 技能造成的效果列表
    pub skill_effects: Vec<SkillEffect>,
    /// 技能造成的元素效果（如火焰、冰霜等）
    pub element_effects: Vec<String>,
    /// 技能造成的状态效果（如眩晕、沉默等）
    pub status_effects: Vec<String>,
    /// 技能造成的环境效果（如地形改变、天气变化等）
    pub environment_effects: Vec<String>,
    /// 技能造成的连锁效果（如弹射、分裂等）
    pub chain_effects: Vec<String>,
    /// 技能造成的召唤效果（如召唤物、幻象等）
    pub summon_effects: Vec<String>,
    /// 技能造成的位移效果（如击退、拉近等）
    pub movement_effects: Vec<String>,
    /// 技能造成的属性变化效果（如力量提升、防御降低等）
    pub attribute_effects: Vec<String>,
    /// 技能造成的资源效果（如法力恢复、能量消耗等）
    pub resource_effects: Vec<String>,
    /// 技能造成的特殊效果（如无敌、隐身等）
    pub special_effects: Vec<String>,
}
