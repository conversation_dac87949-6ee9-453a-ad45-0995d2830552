use crate::battle_unit::{Resistance, StatusType};
/// 抗性系统trait
/// 
/// 各种伤害类型的抗性和状态抗性

use crate::shared::*;


/// 抗性系统 - 各种伤害类型的抗性
pub trait ResistanceSystem {
    /// 获取物理抗性
    fn physical_resistance(&self) -> Resistance;
    
    /// 获取火焰抗性
    fn fire_resistance(&self) -> Resistance;
    
    /// 获取冰霜抗性
    fn ice_resistance(&self) -> Resistance;
    
    /// 获取雷电抗性
    fn lightning_resistance(&self) -> Resistance;
    
    /// 获取毒素抗性
    fn poison_resistance(&self) -> Resistance;
    
    /// 获取神圣抗性
    fn holy_resistance(&self) -> Resistance;
    
    /// 获取暗黑抗性
    fn dark_resistance(&self) -> Resistance;
    
    /// 获取混乱抗性
    fn chaos_resistance(&self) -> Resistance;
    
    /// 获取指定类型的抗性
    fn get_resistance(&self, damage_type: DamageType) -> Resistance;
    
    /// 计算伤害减免
    fn calculate_damage_reduction(&self, damage_type: DamageType, damage: Health) -> Health;
    
    /// 获取状态抗性
    fn status_resistance(&self, status_type: StatusType) -> Resistance;
}