/// 世界地图配置系统
/// 负责加载和管理世界地图的各种配置参数

use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;
use crate::world_map::domain::{WorldLayer, TerrainType, DangerLevel};

/// 世界地图配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorldMapConfig {
    pub world_settings: WorldSettings,
    pub exploration: ExplorationConfig,
    pub resource_nodes: ResourceNodeConfig,
    pub buildings: BuildingConfig,
    pub spiritual_flow: SpiritualFlowConfig,
    pub world_events: WorldEventConfig,
    pub regions: Vec<RegionConfig>,
}

/// 基础世界设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorldSettings {
    pub default_spiritual_density: f32,
    pub time_scale: f32,
    pub auto_save_interval: u32,
}

/// 探索配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ExplorationConfig {
    pub base_discovery_chance: f32,
    pub skill_bonus_multiplier: f32,
    pub fatigue_penalty: f32,
}

/// 资源节点配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ResourceNodeConfig {
    pub default_regeneration_hours: u32,
    pub quality_upgrade_chance: f32,
    pub depletion_rate: f32,
}

/// 建筑配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildingConfig {
    pub max_buildings_per_region: u32,
    pub maintenance_interval_hours: u32,
    pub feng_shui_effect_radius: f32,
}

/// 灵气流动配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpiritualFlowConfig {
    pub flow_update_interval: u32,
    pub base_flow_speed: f32,
    pub disturbance_decay_rate: f32,
}

/// 世界事件配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorldEventConfig {
    pub event_check_interval: u32,
    pub rare_event_base_chance: f32,
}

/// 区域配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionConfig {
    pub name: String,
    pub layer: String, // 将在加载时转换为WorldLayer
    pub min_x: i32,
    pub max_x: i32,
    pub min_y: i32,
    pub max_y: i32,
    pub terrain_type: String, // 将在加载时转换为TerrainType
    pub danger_level: u8,
    pub spiritual_density: f32,
}

impl WorldMapConfig {
    /// 从TOML文件加载配置
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self, ConfigError> {
        let content = fs::read_to_string(path)
            .map_err(|e| ConfigError::FileRead(e.to_string()))?;
        
        let config: WorldMapConfig = toml::from_str(&content)
            .map_err(|e| ConfigError::ParseError(e.to_string()))?;
        
        // 验证配置
        config.validate()?;
        
        Ok(config)
    }
    
    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<(), ConfigError> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| ConfigError::SerializeError(e.to_string()))?;
        
        fs::write(path, content)
            .map_err(|e| ConfigError::FileWrite(e.to_string()))?;
        
        Ok(())
    }
    
    /// 验证配置的有效性
    pub fn validate(&self) -> Result<(), ConfigError> {
        // 验证基础设置
        if self.world_settings.default_spiritual_density < 0.0 {
            return Err(ConfigError::InvalidValue("default_spiritual_density must be >= 0".to_string()));
        }
        
        if self.world_settings.time_scale <= 0.0 {
            return Err(ConfigError::InvalidValue("time_scale must be > 0".to_string()));
        }
        
        // 验证探索配置
        if self.exploration.base_discovery_chance < 0.0 || self.exploration.base_discovery_chance > 1.0 {
            return Err(ConfigError::InvalidValue("base_discovery_chance must be between 0 and 1".to_string()));
        }
        
        // 验证区域配置
        for region in &self.regions {
            if region.min_x >= region.max_x || region.min_y >= region.max_y {
                return Err(ConfigError::InvalidValue(format!("Invalid region bounds for {}", region.name)));
            }
            
            if region.danger_level > 10 {
                return Err(ConfigError::InvalidValue(format!("Danger level too high for region {}", region.name)));
            }
        }
        
        Ok(())
    }
    
    /// 获取默认配置
    pub fn default() -> Self {
        Self {
            world_settings: WorldSettings {
                default_spiritual_density: 1.0,
                time_scale: 1.0,
                auto_save_interval: 300,
            },
            exploration: ExplorationConfig {
                base_discovery_chance: 0.1,
                skill_bonus_multiplier: 0.05,
                fatigue_penalty: 0.02,
            },
            resource_nodes: ResourceNodeConfig {
                default_regeneration_hours: 24,
                quality_upgrade_chance: 0.05,
                depletion_rate: 0.1,
            },
            buildings: BuildingConfig {
                max_buildings_per_region: 10,
                maintenance_interval_hours: 168,
                feng_shui_effect_radius: 50.0,
            },
            spiritual_flow: SpiritualFlowConfig {
                flow_update_interval: 60,
                base_flow_speed: 1.0,
                disturbance_decay_rate: 0.9,
            },
            world_events: WorldEventConfig {
                event_check_interval: 3600,
                rare_event_base_chance: 0.001,
            },
            regions: vec![
                RegionConfig {
                    name: "新手村".to_string(),
                    layer: "Mortal".to_string(),
                    min_x: 0,
                    max_x: 100,
                    min_y: 0,
                    max_y: 100,
                    terrain_type: "Plains".to_string(),
                    danger_level: 1,
                    spiritual_density: 1.0,
                },
            ],
        }
    }
}

impl RegionConfig {
    /// 转换为WorldLayer
    pub fn parse_world_layer(&self) -> Result<WorldLayer, ConfigError> {
        match self.layer.as_str() {
            "Mortal" => Ok(WorldLayer::Mortal),
            "Spirit" => Ok(WorldLayer::Spirit),
            "Immortal" => Ok(WorldLayer::Immortal),
            "Chaos" => Ok(WorldLayer::Chaos),
            layer if layer.starts_with("SecretRealm") => {
                // 解析 "SecretRealm(123)" 格式
                if let Some(id_str) = layer.strip_prefix("SecretRealm(").and_then(|s| s.strip_suffix(")")) {
                    let id: u32 = id_str.parse()
                        .map_err(|_| ConfigError::InvalidValue(format!("Invalid SecretRealm ID: {}", id_str)))?;
                    Ok(WorldLayer::SecretRealm(id))
                } else {
                    Err(ConfigError::InvalidValue(format!("Invalid SecretRealm format: {}", layer)))
                }
            }
            _ => Err(ConfigError::InvalidValue(format!("Unknown world layer: {}", self.layer))),
        }
    }
    
    /// 转换为TerrainType
    pub fn parse_terrain_type(&self) -> Result<TerrainType, ConfigError> {
        match self.terrain_type.as_str() {
            "Plains" => Ok(TerrainType::Plains),
            "Forest" => Ok(TerrainType::Forest),
            "Mountains" => Ok(TerrainType::Mountains),
            "Desert" => Ok(TerrainType::Desert),
            "Water" => Ok(TerrainType::Water),
            "Swamp" => Ok(TerrainType::Swamp),
            "Tundra" => Ok(TerrainType::Tundra),
            "Volcanic" => Ok(TerrainType::Volcanic),
            "SpiritualVein" => Ok(TerrainType::SpiritualVein),
            "Void" => Ok(TerrainType::Void),
            _ => Err(ConfigError::InvalidValue(format!("Unknown terrain type: {}", self.terrain_type))),
        }
    }
    
    /// 转换为DangerLevel
    pub fn parse_danger_level(&self) -> DangerLevel {
        DangerLevel::from_numeric(self.danger_level)
    }
}

/// 配置错误类型
#[derive(Debug, Clone)]
pub enum ConfigError {
    FileRead(String),
    FileWrite(String),
    ParseError(String),
    SerializeError(String),
    InvalidValue(String),
}

impl std::fmt::Display for ConfigError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ConfigError::FileRead(msg) => write!(f, "Failed to read config file: {}", msg),
            ConfigError::FileWrite(msg) => write!(f, "Failed to write config file: {}", msg),
            ConfigError::ParseError(msg) => write!(f, "Failed to parse config: {}", msg),
            ConfigError::SerializeError(msg) => write!(f, "Failed to serialize config: {}", msg),
            ConfigError::InvalidValue(msg) => write!(f, "Invalid config value: {}", msg),
        }
    }
}

impl std::error::Error for ConfigError {}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_config() {
        let config = WorldMapConfig::default();
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_world_layer_parsing() {
        let region = RegionConfig {
            name: "Test".to_string(),
            layer: "Mortal".to_string(),
            min_x: 0, max_x: 10, min_y: 0, max_y: 10,
            terrain_type: "Plains".to_string(),
            danger_level: 1,
            spiritual_density: 1.0,
        };
        
        assert_eq!(region.parse_world_layer().unwrap(), WorldLayer::Mortal);
    }
    
    #[test]
    fn test_secret_realm_parsing() {
        let region = RegionConfig {
            name: "Test".to_string(),
            layer: "SecretRealm(42)".to_string(),
            min_x: 0, max_x: 10, min_y: 0, max_y: 10,
            terrain_type: "Plains".to_string(),
            danger_level: 1,
            spiritual_density: 1.0,
        };
        
        assert_eq!(region.parse_world_layer().unwrap(), WorldLayer::SecretRealm(42));
    }
}