use crate::material::{
    CollectionSkill, DiscoveryMethod, ElementalAttribute, MaterialAttribute,
    MaterialDiscoveryEvent, MaterialDiscoveryManager, ResourceNodeStatus, ResourceNodeType,
    WorldResourceNode,
};
use crate::world_map::DangerLevel;
use crate::{MaterialGrade, Position, WorldLayer, WorldLayerType};
use chrono::{DateTime, Utc};
use num::pow::Pow;
/// 世界地图与材料系统集成服务
/// 连接世界地图探索与材料发现采集功能
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
// ============================================================================
// 世界地图材料集成服务
// ============================================================================

/// 世界地图材料集成服务 - 统一管理地图探索中的材料发现
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorldMapMaterialService {
    /// 材料发现管理器
    pub discovery_manager: MaterialDiscoveryManager,
    /// 区域材料配置
    pub regional_material_configs: HashMap<String, RegionalMaterialConfig>,
    /// 世界层级影响配置
    pub layer_effects: HashMap<WorldLayerType, LayerMaterialEffect>,
    /// 活跃的材料发现事件
    pub active_discoveries: HashMap<String, ActiveDiscoverySession>,
}

/// 区域材料配置 - 不同区域的材料产出特性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionalMaterialConfig {
    /// 区域ID
    pub region_id: String,
    /// 区域名称
    pub region_name: String,
    /// 主导属性
    pub dominant_attributes: Vec<MaterialAttribute>,
    /// 材料丰富度
    pub material_abundance: f64,
    /// 特有材料类型
    pub unique_materials: Vec<String>,
    /// 危险等级影响
    pub danger_level_modifier: f64,
    /// 季节影响
    pub seasonal_effects: HashMap<String, SeasonalEffect>,
}

/// 季节效果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SeasonalEffect {
    /// 影响的属性
    pub affected_attributes: Vec<MaterialAttribute>,
    /// 产量修正
    pub yield_modifier: f64,
    /// 品质修正
    pub quality_modifier: f64,
    /// 特殊材料出现率
    pub special_material_rate: f64,
}

/// 世界层级材料效果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayerMaterialEffect {
    /// 基础材料品质提升
    pub base_quality_boost: u8,
    /// 稀有材料出现率
    pub rare_material_multiplier: f64,
    /// 元素能量浓度
    pub elemental_concentration: f64,
    /// 危险等级基数
    pub base_danger_level: u8,
    /// 特殊规则
    pub special_rules: Vec<String>,
}

/// 活跃的发现会话
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActiveDiscoverySession {
    /// 会话ID
    pub session_id: String,
    /// 探索者ID
    pub explorer_id: String,
    /// 探索位置
    pub exploration_area: ExplorationArea,
    /// 已发现的材料
    pub discoveries: Vec<String>, // MaterialDiscoveryEvent IDs
    /// 会话开始时间
    pub session_start: DateTime<Utc>,
    /// 剩余探索时间
    pub remaining_time: chrono::Duration,
    /// 使用的技能
    pub active_skills: Vec<CollectionSkill>,
}

/// 探索区域
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExplorationArea {
    /// 中心位置
    pub center: Position,
    /// 探索半径
    pub radius: u32,
    /// 世界层级
    pub world_layer: WorldLayerType,
    /// 区域特性
    pub area_traits: Vec<AreaTrait>,
}

/// 区域特征
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AreaTrait {
    /// 元素富集区
    ElementalRich(ElementalAttribute),
    /// 古老遗迹
    AncientRuins,
    /// 灵兽栖息地
    BeastHabitat,
    /// 药草丰富
    HerbRich,
    /// 矿产丰富
    MineralRich,
    /// 危险区域
    Dangerous,
    /// 神秘区域
    Mysterious,
}

impl WorldMapMaterialService {
    /// 创建新的材料集成服务
    pub fn new() -> Self {
        Self {
            discovery_manager: MaterialDiscoveryManager::new(),
            regional_material_configs: HashMap::new(),
            layer_effects: Self::create_default_layer_effects(),
            active_discoveries: HashMap::new(),
        }
    }

    /// 创建默认的世界层级效果
    fn create_default_layer_effects() -> HashMap<WorldLayerType, LayerMaterialEffect> {
        let mut effects = HashMap::new();

        effects.insert(
            WorldLayerType::Mortal,
            LayerMaterialEffect {
                base_quality_boost: 0,
                rare_material_multiplier: 1.0,
                elemental_concentration: 1.0,
                base_danger_level: 1,
                special_rules: vec!["基础材料较多".to_string()],
            },
        );

        effects.insert(
            WorldLayerType::Spirit,
            LayerMaterialEffect {
                base_quality_boost: 1,
                rare_material_multiplier: 2.0,
                elemental_concentration: 2.0,
                base_danger_level: 3,
                special_rules: vec!["灵气材料增多".to_string(), "需要灵识感知".to_string()],
            },
        );

        effects.insert(
            WorldLayerType::Immortal,
            LayerMaterialEffect {
                base_quality_boost: 2,
                rare_material_multiplier: 5.0,
                elemental_concentration: 5.0,
                base_danger_level: 6,
                special_rules: vec!["仙品材料出现".to_string(), "需要仙识".to_string()],
            },
        );

        effects.insert(
            WorldLayerType::Chaos,
            LayerMaterialEffect {
                base_quality_boost: 3,
                rare_material_multiplier: 10.0,
                elemental_concentration: 10.0,
                base_danger_level: 9,
                special_rules: vec!["混沌材料".to_string(), "极其危险".to_string()],
            },
        );

        effects
    }

    /// 在指定位置开始材料探索
    pub fn start_material_exploration(
        &mut self,
        explorer_id: String,
        position: Position,
        exploration_radius: u32,
        exploration_duration: chrono::Duration,
        skills: Vec<CollectionSkill>,
    ) -> Result<String, String> {
        // 创建探索区域
        let exploration_area = ExplorationArea {
            center: position,
            radius: exploration_radius,
            world_layer: position.layer.unwrap_or(WorldLayerType::Mortal),
            area_traits: self.analyze_area_traits(position, exploration_radius),
        };

        // 创建探索会话
        let session_id = format!("exploration_{}_{}", explorer_id, Utc::now().timestamp());
        let session = ActiveDiscoverySession {
            session_id: session_id.clone(),
            explorer_id: explorer_id.clone(),
            exploration_area,
            discoveries: Vec::new(),
            session_start: Utc::now(),
            remaining_time: exploration_duration,
            active_skills: skills.clone(),
        };

        self.active_discoveries.insert(session_id.clone(), session);

        // 立即进行一次发现尝试
        self.attempt_discovery_in_session(&session_id)?;

        Ok(session_id)
    }

    /// 分析区域特征
    fn analyze_area_traits(&self, position: Position, radius: u32) -> Vec<AreaTrait> {
        let mut traits = Vec::new();

        // 根据位置和世界层级分析区域特征
        match position.layer {
            Some(layer) => match layer {
                WorldLayerType::Mortal => {
                    traits.push(AreaTrait::HerbRich);
                    if position.x % 10.0 == 0.0 {
                        traits.push(AreaTrait::MineralRich);
                    }
                }
                WorldLayerType::Spirit => {
                    traits.push(AreaTrait::ElementalRich(ElementalAttribute::Wood));
                    if radius > 50 {
                        traits.push(AreaTrait::BeastHabitat);
                    }
                }
                WorldLayerType::Immortal => {
                    traits.push(AreaTrait::AncientRuins);
                    traits.push(AreaTrait::Mysterious);
                }
                WorldLayerType::Chaos => {
                    traits.push(AreaTrait::Dangerous);
                    traits.push(AreaTrait::Mysterious);
                }
                WorldLayerType::SecretRealm(_) => {
                    traits.push(AreaTrait::Mysterious);
                    traits.push(AreaTrait::AncientRuins);
                }
            },
            None => {
                traits.push(AreaTrait::HerbRich);
            }
        }

        traits
    }

    /// 在会话中尝试发现
    fn attempt_discovery_in_session(&mut self, session_id: &str) -> Result<Vec<String>, String> {
        // 先获取会话信息，避免借用冲突
        let (explorer_id, center_position, active_skills) = {
            let session = self
                .active_discoveries
                .get(session_id)
                .ok_or("探索会话不存在")?;
            (
                session.explorer_id.clone(),
                session.exploration_area.center,
                session.active_skills.clone(),
            )
        };

        let mut new_discoveries = Vec::new();

        // 根据技能和区域特征进行多次发现尝试
        for skill in &active_skills {
            let discoveries = self.discovery_manager.search_for_materials(
                explorer_id.clone(),
                center_position,
                self.determine_discovery_method(&skill),
                Some(*skill),
            );

            for discovery in discoveries {
                new_discoveries.push(discovery.id.clone());
            }
        }

        // 更新会话
        if let Some(session) = self.active_discoveries.get_mut(session_id) {
            session.discoveries.extend(new_discoveries.clone());
        }

        Ok(new_discoveries)
    }

    /// 根据技能确定发现方法
    fn determine_discovery_method(&self, skill: &CollectionSkill) -> DiscoveryMethod {
        match skill {
            CollectionSkill::Herbalism => DiscoveryMethod::ActiveSearch,
            CollectionSkill::Mining => DiscoveryMethod::ActiveSearch,
            CollectionSkill::TreasureHunting => DiscoveryMethod::ToolAssisted,
            CollectionSkill::BeastHunting => DiscoveryMethod::ClueFollowing,
            CollectionSkill::BasicRefinement => DiscoveryMethod::RandomEncounter,
            CollectionSkill::Appraisal => DiscoveryMethod::InspirationGuided,
        }
    }

    /// 更新探索会话
    pub fn update_exploration_sessions(&mut self) {
        let current_time = Utc::now();
        let mut expired_sessions = Vec::new();
        let mut sessions_to_discover = Vec::new();

        // 第一步：更新时间和收集需要处理的会话
        for (session_id, session) in &mut self.active_discoveries {
            let elapsed = current_time - session.session_start;
            session.remaining_time = session.remaining_time - elapsed;

            if session.remaining_time <= chrono::Duration::zero() {
                expired_sessions.push(session_id.clone());
            } else {
                sessions_to_discover.push(session_id.clone());
            }
        }

        // 第二步：处理发现逻辑
        for session_id in sessions_to_discover {
            if let Ok(new_discoveries) = self.attempt_discovery_in_session(&session_id) {
                if let Some(session) = self.active_discoveries.get_mut(&session_id) {
                    session.discoveries.extend(new_discoveries);
                }
            }
        }

        // 第三步：移除过期的会话
        for session_id in expired_sessions {
            self.active_discoveries.remove(&session_id);
        }
    }

    /// 获取区域材料信息
    pub fn get_regional_material_info(&self, position: Position) -> RegionalMaterialInfo {
        match &position.layer {
            Some(layer) => {
                // 根据世界层级获取材料信息
                RegionalMaterialInfo {
                    dominant_attributes: self.get_regional_attributes(position),
                    estimated_abundance: self.calculate_abundance(position),
                    danger_level: DangerLevel::from_numeric(layer.danger_multiplier() as u8),
                    special_features: self.identify_special_features(position),
                    recommended_skills: self.get_recommended_skills(position),
                }
            }
            None => {
                // 默认为凡间
                RegionalMaterialInfo {
                    dominant_attributes: vec![
                        MaterialAttribute::Elemental(ElementalAttribute::Wood),
                        MaterialAttribute::Elemental(ElementalAttribute::Earth),
                    ],
                    estimated_abundance: 1.0,
                    danger_level: DangerLevel::Safe,
                    special_features: Vec::new(),
                    recommended_skills: Vec::new(),
                }
            }
        }
    }

    /// 获取区域属性
    fn get_regional_attributes(&self, position: Position) -> Vec<MaterialAttribute> {
        match &position.layer {
            Some(layer) => match layer {
                WorldLayerType::Mortal => vec![
                    MaterialAttribute::Elemental(ElementalAttribute::Wood),
                    MaterialAttribute::Elemental(ElementalAttribute::Earth),
                ],
                WorldLayerType::Spirit => vec![
                    MaterialAttribute::Elemental(ElementalAttribute::Water),
                    MaterialAttribute::Elemental(ElementalAttribute::Fire),
                ],
                WorldLayerType::Immortal => vec![
                    MaterialAttribute::Elemental(ElementalAttribute::Metal),
                    MaterialAttribute::Chaos,
                ],
                WorldLayerType::Chaos => vec![MaterialAttribute::Chaos],
                WorldLayerType::SecretRealm(_) => vec![MaterialAttribute::Chaos],
            },
            None => vec![], // 或者返回默认的 MaterialAttribute 列表
        }
    }

    /// 计算区域丰富度
    fn calculate_abundance(&self, position: Position) -> f64 {
        let base_abundance = match &position.layer {
            Some(layer) => match layer {
                WorldLayerType::Mortal => 1.0,
                WorldLayerType::Spirit => 0.8,
                WorldLayerType::Immortal => 0.5,
                WorldLayerType::Chaos => 0.2,
                WorldLayerType::SecretRealm(_) => 0.3,
            },
            None => 1.0, // 默认为凡间
        };

        // 根据位置添加随机变化
        let position_modifier = ((position.x + position.y) as i32 % 100) as f64 / 100.0;
        base_abundance * (0.5 + position_modifier)
    }

    /// 识别特殊特征
    fn identify_special_features(&self, position: Position) -> Vec<String> {
        let mut features = Vec::new();

        // 根据坐标模式识别特殊特征
        if position.x as i32 % 100 == 0 && position.y as i32 % 100 == 0 {
            features.push("古老遗迹".to_string());
        }

        if (position.x + position.y) as i32 % 50 == 0 {
            features.push("元素汇聚点".to_string());
        }

        match &position.layer {
            Some(layer) => match layer {
                WorldLayerType::Spirit => features.push("灵气浓郁".to_string()),
                WorldLayerType::Immortal => features.push("仙韵缭绕".to_string()),
                WorldLayerType::Chaos => features.push("混沌气息".to_string()),
                WorldLayerType::SecretRealm(_) => features.push("神秘的空间".to_string()),
                WorldLayerType::Mortal => features.push("普通区域".to_string()),
                _ => {}
            },
            None => {}
        }

        features
    }

    /// 获取推荐技能
    fn get_recommended_skills(&self, position: Position) -> Vec<CollectionSkill> {
        match &position.layer {
            Some(layer) => match layer {
                WorldLayerType::Mortal => vec![CollectionSkill::Herbalism, CollectionSkill::Mining],
                WorldLayerType::Spirit => vec![
                    CollectionSkill::TreasureHunting,
                    CollectionSkill::BeastHunting,
                ],
                WorldLayerType::Immortal => {
                    vec![CollectionSkill::Appraisal, CollectionSkill::TreasureHunting]
                }
                WorldLayerType::Chaos => {
                    vec![CollectionSkill::Appraisal, CollectionSkill::BasicRefinement]
                }
                WorldLayerType::SecretRealm(_) => vec![
                    CollectionSkill::Appraisal,
                    CollectionSkill::TreasureHunting,
                    CollectionSkill::BasicRefinement,
                ],
            },
            None => {
                log::warn!("未找到位置的层级，无法推荐技能");
                vec![]
            }
        }
    }

    /// 添加区域材料配置
    pub fn add_regional_config(&mut self, config: RegionalMaterialConfig) {
        self.regional_material_configs
            .insert(config.region_id.clone(), config);
    }

    /// 添加资源点
    pub fn add_resource_node(&mut self, node: WorldResourceNode) {
        self.discovery_manager.add_resource_node(node);
    }

    /// 获取附近的材料发现
    pub fn get_nearby_discoveries(
        &self,
        position: Position,
        range: i32,
    ) -> Vec<&MaterialDiscoveryEvent> {
        self.discovery_manager
            .discovery_events
            .values()
            .filter(|event| {
                let distance = (((event.location.x - position.x).pow(2)
                    + (event.location.y - position.y).pow(2))
                    as f64)
                    .sqrt();
                distance <= range as f64 && !event.collected
            })
            .collect()
    }

    /// 获取探索会话信息
    pub fn get_exploration_session(&self, session_id: &str) -> Option<&ActiveDiscoverySession> {
        self.active_discoveries.get(session_id)
    }

    /// 结束探索会话
    pub fn end_exploration_session(&mut self, session_id: &str) -> Option<ExplorationSummary> {
        if let Some(session) = self.active_discoveries.remove(session_id) {
            let discovered_materials: Vec<_> = session
                .discoveries
                .iter()
                .filter_map(|discovery_id| {
                    self.discovery_manager.discovery_events.get(discovery_id)
                })
                .collect();

            Some(ExplorationSummary {
                session_id: session.session_id,
                explorer_id: session.explorer_id,
                total_discoveries: discovered_materials.len(),
                rare_discoveries: discovered_materials
                    .iter()
                    .filter(|d| {
                        matches!(
                            d.discovered_material.material.grade,
                            MaterialGrade::Immortal
                                | MaterialGrade::Divine
                                | MaterialGrade::Sacred
                                | MaterialGrade::Chaos
                        )
                    })
                    .count(),
                exploration_duration: Utc::now() - session.session_start,
                area_explored: session.exploration_area,
            })
        } else {
            None
        }
    }
}

/// 区域材料信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionalMaterialInfo {
    /// 主导属性
    pub dominant_attributes: Vec<MaterialAttribute>,
    /// 估计丰富度
    pub estimated_abundance: f64,
    /// 危险等级
    pub danger_level: DangerLevel,
    /// 特殊特征
    pub special_features: Vec<String>,
    /// 推荐技能
    pub recommended_skills: Vec<CollectionSkill>,
}

/// 探索总结
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExplorationSummary {
    /// 会话ID
    pub session_id: String,
    /// 探索者ID
    pub explorer_id: String,
    /// 总发现数
    pub total_discoveries: usize,
    /// 稀有发现数
    pub rare_discoveries: usize,
    /// 探索时长
    pub exploration_duration: chrono::Duration,
    /// 探索区域
    pub area_explored: ExplorationArea,
}

impl Default for WorldMapMaterialService {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 便利函数
// ============================================================================

/// 创建基础资源点
pub fn create_basic_resource_nodes() -> Vec<WorldResourceNode> {
    vec![
        WorldResourceNode {
            id: "herb_garden_001".to_string(),
            name: "凡间药草园".to_string(),
            position: Position::new_grid(100, 100, WorldLayerType::Mortal),
            node_type: ResourceNodeType::HerbGarden,
            material_types: vec!["common_herb".to_string(), "healing_grass".to_string()],
            refresh_cycle: 6, // 6小时刷新
            last_refresh: Utc::now(),
            status: ResourceNodeStatus::Normal,
            environmental_modifiers: Vec::new(),
            access_requirements: Vec::new(),
        },
        WorldResourceNode {
            id: "mineral_vein_001".to_string(),
            name: "铁矿脉".to_string(),
            position: Position::new_grid(200, 150, WorldLayerType::Mortal),
            node_type: ResourceNodeType::MineralVein,
            material_types: vec!["iron_ore".to_string(), "copper_ore".to_string()],
            refresh_cycle: 12, // 12小时刷新
            last_refresh: Utc::now(),
            status: ResourceNodeStatus::Abundant,
            environmental_modifiers: Vec::new(),
            access_requirements: Vec::new(),
        },
        WorldResourceNode {
            id: "spirit_spring_001".to_string(),
            name: "灵泉".to_string(),
            position: Position::new_grid(300, 250, WorldLayerType::Spirit),
            node_type: ResourceNodeType::SpiritualSpring,
            material_types: vec!["spirit_water".to_string(), "pure_essence".to_string()],
            refresh_cycle: 24, // 24小时刷新
            last_refresh: Utc::now(),
            status: ResourceNodeStatus::Normal,
            environmental_modifiers: Vec::new(),
            access_requirements: Vec::new(),
        },
    ]
}
