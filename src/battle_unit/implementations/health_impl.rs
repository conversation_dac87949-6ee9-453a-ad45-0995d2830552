/// 生命值系统trait实现
/// 
/// 实现HealthSystem trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;

use crate::battle_unit::traits::{HealthSystem, ResistanceSystem};
use crate::battle_system::simplified_battle_traits::HealResult;
use crate::attribute::{AttributeType, CoreAttribute};

impl HealthSystem for BattleUnitAdapter {
    fn current_health(&self) -> Health {
        self.character.current_health()
    }
    
    fn max_health(&self) -> Health {
        self.character.max_health()
    }
    
    fn base_health(&self) -> Health {
        // 基于体质计算基础生命值
        let constitution = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Earth)
        );
        constitution * 10 + 50 // 基础50 + 体质*10
    }
    
    fn health_bonus(&self) -> Health {
        self.max_health() - self.base_health()
    }
    
    fn health_regeneration_rate(&self) -> Health {
        let constitution_mod = BattleUnitAdapter::calculate_modifier(
            self.calculate_total_attribute(
                AttributeType::Base(CoreAttribute::Earth)
            )
        );
        (1 + constitution_mod).max(0) as Health // 每秒恢复1+体质修正
    }
    
    fn is_alive(&self) -> bool {
        self.current_health() > 0
    }
    
    fn is_near_death(&self) -> bool {
        let health_percent = self.current_health() as f32 / self.max_health() as f32;
        health_percent <= 0.25 // 25%以下算濒死
    }
    
    fn health_percentage(&self) -> f32 {
        if self.max_health() > 0 {
            self.current_health() as f32 / self.max_health() as f32
        } else {
            0.0
        }
    }
    
    fn take_direct_damage(&mut self, damage: Health) -> GameResult<crate::battle_system::simplified_battle_traits::DamageResult> {
        let old_health = self.current_health();
        let actual_damage = damage.min(old_health);
        
        // TODO: 使用Character的伤害方法
        // self.character.take_damage(actual_damage)?;
        
        Ok(crate::battle_system::simplified_battle_traits::DamageResult {
            actual_damage,
            is_critical: false,
            remaining_health: old_health - actual_damage,
            is_fatal: old_health - actual_damage <= 0,
        })
    }
    
    fn take_calculated_damage(&mut self, damage_info: DamageInfo) -> GameResult<DetailedDamageResult> {
        let resistance = self.get_resistance(damage_info.damage_type);
        let reduced_damage = self.calculate_damage_reduction(
            damage_info.damage_type, 
            damage_info.base_damage
        );
        
        let result = self.take_direct_damage(reduced_damage)?;
        
        Ok(DetailedDamageResult {
            original_damage: damage_info.base_damage,
            final_damage: result.actual_damage,
            damage_type: damage_info.damage_type,
            resistance_applied: resistance,
            is_critical: result.is_critical,
            is_fatal: result.is_fatal,
            source: damage_info.source,
        })
    }
    
    fn heal(&mut self, amount: Health) -> GameResult<HealResult> {
        let old_health = self.current_health();
        let max_health = self.max_health();
        let actual_heal = amount.min(max_health - old_health);
        
        // TODO: 使用Character的heal方法
        // self.character.heal(actual_heal)?;
        
        Ok(HealResult {
            amount_healed: actual_heal,
            new_health: old_health + actual_heal,
            was_at_full_health: old_health == max_health,
        })
    }
    
    fn set_health(&mut self, health: Health) -> GameResult<()> {
        // TODO: 实现Character的set_health方法
        Ok(())
    }
    
    fn damage_history(&self) -> Vec<DamageRecord> {
        // TODO: 从Character获取伤害历史
        Vec::new()
    }
    
    fn heal_history(&self) -> Vec<HealRecord> {
        // TODO: 从Character获取治疗历史
        Vec::new()
    }
}