//! # 规则执行器
//!
//! 提供规则执行的具体实现，支持不同类型的规则执行逻辑

use serde::{Deserialize, Serialize};
use serde_json::Value as JsonValue;
use std::collections::HashMap;
use std::time::Instant;

use crate::config_engine::{
    config::RuleDefinition,
    error::{ConfigEngineError, RuleExecutionError},
    rules::{ConditionEvaluation, ContextValue, ExecutionContext, ExecutionStep, ExecutionTrace},
    Result,
};

/// 执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionResult {
    /// 是否执行成功
    pub success: bool,
    /// 输出数据
    pub output: HashMap<String, ContextValue>,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    /// 错误信息
    pub error: Option<String>,
    /// 执行追踪
    pub trace: Option<ExecutionTrace>,
    /// 执行元数据
    pub metadata: ExecutionMetadata,
}

impl ExecutionResult {
    /// 创建成功结果
    pub fn success(output: HashMap<String, ContextValue>, execution_time_ms: u64) -> Self {
        Self {
            success: true,
            output,
            execution_time_ms,
            error: None,
            trace: None,
            metadata: ExecutionMetadata::new(),
        }
    }

    /// 创建带追踪的成功结果
    pub fn success_with_trace(
        output: HashMap<String, ContextValue>,
        execution_time_ms: u64,
        trace: ExecutionTrace,
    ) -> Self {
        Self {
            success: true,
            output,
            execution_time_ms,
            error: None,
            trace: Some(trace),
            metadata: ExecutionMetadata::new(),
        }
    }

    /// 创建失败结果
    pub fn failure(error: String, execution_time_ms: u64) -> Self {
        Self {
            success: false,
            output: HashMap::new(),
            execution_time_ms,
            error: Some(error),
            trace: None,
            metadata: ExecutionMetadata::new(),
        }
    }

    /// 检查是否成功
    pub fn is_success(&self) -> bool {
        self.success
    }

    /// 检查是否失败
    pub fn is_failure(&self) -> bool {
        !self.success
    }

    /// 获取输出值
    pub fn get_output(&self, key: &str) -> Option<&ContextValue> {
        self.output.get(key)
    }

    /// 添加输出值
    pub fn add_output(&mut self, key: String, value: ContextValue) {
        self.output.insert(key, value);
    }

    /// 设置元数据
    pub fn with_metadata(mut self, metadata: ExecutionMetadata) -> Self {
        self.metadata = metadata;
        self
    }
}

/// 执行元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionMetadata {
    /// 执行器名称
    pub executor_name: String,
    /// 执行器版本
    pub executor_version: String,
    /// 规则版本
    pub rule_version: String,
    /// 执行环境
    pub execution_environment: String,
    /// 性能指标
    pub performance_metrics: PerformanceMetrics,
}

impl ExecutionMetadata {
    pub fn new() -> Self {
        Self {
            executor_name: "unknown".to_string(),
            executor_version: "1.0.0".to_string(),
            rule_version: "1.0.0".to_string(),
            execution_environment: "production".to_string(),
            performance_metrics: PerformanceMetrics::new(),
        }
    }

    pub fn with_executor(mut self, name: String, version: String) -> Self {
        self.executor_name = name;
        self.executor_version = version;
        self
    }

    pub fn with_rule_version(mut self, version: String) -> Self {
        self.rule_version = version;
        self
    }

    pub fn with_performance_metrics(mut self, metrics: PerformanceMetrics) -> Self {
        self.performance_metrics = metrics;
        self
    }
}

impl Default for ExecutionMetadata {
    fn default() -> Self {
        Self::new()
    }
}

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// 解析时间（微秒）
    pub parse_time_us: u64,
    /// 验证时间（微秒）
    pub validation_time_us: u64,
    /// 执行时间（微秒）
    pub execution_time_us: u64,
    /// 序列化时间（微秒）
    pub serialization_time_us: u64,
    /// 内存使用量（字节）
    pub memory_usage_bytes: Option<usize>,
    /// 节点访问次数
    pub nodes_visited: u32,
    /// 条件评估次数
    pub conditions_evaluated: u32,
}

impl PerformanceMetrics {
    pub fn new() -> Self {
        Self {
            parse_time_us: 0,
            validation_time_us: 0,
            execution_time_us: 0,
            serialization_time_us: 0,
            memory_usage_bytes: None,
            nodes_visited: 0,
            conditions_evaluated: 0,
        }
    }

    /// 获取总时间（微秒）
    pub fn total_time_us(&self) -> u64 {
        self.parse_time_us
            + self.validation_time_us
            + self.execution_time_us
            + self.serialization_time_us
    }

    /// 获取总时间（毫秒）
    pub fn total_time_ms(&self) -> f64 {
        self.total_time_us() as f64 / 1000.0
    }
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self::new()
    }
}

/// 高级JDM执行器
///
/// 基于zen-engine的完整JDM规则执行器
pub struct AdvancedJdmExecutor {
    /// 执行器配置
    config: JdmExecutorConfig,
    /// 执行统计
    stats: ExecutorStats,
}

impl AdvancedJdmExecutor {
    /// 创建新的JDM执行器
    pub fn new(config: JdmExecutorConfig) -> Self {
        Self {
            config,
            stats: ExecutorStats::new(),
        }
    }

    /// 执行JDM规则
    pub async fn execute(
        &mut self,
        rule_def: &RuleDefinition,
        context: &ExecutionContext,
    ) -> Result<ExecutionResult> {
        let start_time = Instant::now();
        let mut metrics = PerformanceMetrics::new();
        let mut trace = if context.is_tracing_enabled() {
            Some(ExecutionTrace {
                steps: Vec::new(),
                node_visits: HashMap::new(),
                condition_evaluations: Vec::new(),
            })
        } else {
            None
        };

        // 1. 解析JDM
        let parse_start = Instant::now();
        let jdm = self.parse_jdm(&rule_def.jdm_content)?;
        metrics.parse_time_us = parse_start.elapsed().as_micros() as u64;

        // 2. 验证JDM结构
        let validation_start = Instant::now();
        self.validate_jdm(&jdm)?;
        metrics.validation_time_us = validation_start.elapsed().as_micros() as u64;

        // 3. 执行规则
        let execution_start = Instant::now();
        let result = self
            .execute_jdm(&jdm, context, &mut trace, &mut metrics)
            .await?;
        metrics.execution_time_us = execution_start.elapsed().as_micros() as u64;

        // 4. 序列化结果
        let serialization_start = Instant::now();
        let output = self.serialize_result(&result)?;
        metrics.serialization_time_us = serialization_start.elapsed().as_micros() as u64;

        // 5. 更新统计
        self.stats.total_executions += 1;
        self.stats.total_execution_time_ms += start_time.elapsed().as_millis() as u64;

        let execution_time_ms = start_time.elapsed().as_millis() as u64;

        let mut execution_result = ExecutionResult::success(output, execution_time_ms)
            .with_metadata(
                ExecutionMetadata::new()
                    .with_executor("advanced_jdm".to_string(), "1.0.0".to_string())
                    .with_rule_version(rule_def.version.clone())
                    .with_performance_metrics(metrics),
            );

        if let Some(trace) = trace {
            execution_result.trace = Some(trace);
        }

        Ok(execution_result)
    }

    /// 解析JDM内容
    fn parse_jdm(&self, jdm_content: &str) -> Result<JsonValue> {
        serde_json::from_str(jdm_content).map_err(|e| {
            ConfigEngineError::RuleExecution(RuleExecutionError::InvalidJdm {
                reason: format!("JDM解析失败: {}", e),
            })
        })
    }

    /// 验证JDM结构
    fn validate_jdm(&self, jdm: &JsonValue) -> Result<()> {
        // 检查contentType
        if let Some(content_type) = jdm.get("contentType").and_then(|v| v.as_str()) {
            if content_type != "application/vnd.gorules.decision" {
                return Err(ConfigEngineError::RuleExecution(
                    RuleExecutionError::InvalidJdm {
                        reason: format!("不支持的contentType: {}", content_type),
                    },
                ));
            }
        } else {
            return Err(ConfigEngineError::RuleExecution(
                RuleExecutionError::InvalidJdm {
                    reason: "缺少contentType字段".to_string(),
                },
            ));
        }

        // 检查必需字段
        let required_fields = ["nodes", "edges"];
        for field in &required_fields {
            if !jdm.get(field).is_some() {
                return Err(ConfigEngineError::RuleExecution(
                    RuleExecutionError::InvalidJdm {
                        reason: format!("缺少必需字段: {}", field),
                    },
                ));
            }
        }

        Ok(())
    }

    /// 执行JDM规则
    async fn execute_jdm(
        &self,
        jdm: &JsonValue,
        context: &ExecutionContext,
        trace: &mut Option<ExecutionTrace>,
        metrics: &mut PerformanceMetrics,
    ) -> Result<serde_json::Value> {
        // 获取节点和边
        let nodes = jdm.get("nodes").and_then(|v| v.as_array()).ok_or_else(|| {
            ConfigEngineError::RuleExecution(RuleExecutionError::InvalidJdm {
                reason: "nodes字段不是数组".to_string(),
            })
        })?;

        let edges = jdm.get("edges").and_then(|v| v.as_array()).ok_or_else(|| {
            ConfigEngineError::RuleExecution(RuleExecutionError::InvalidJdm {
                reason: "edges字段不是数组".to_string(),
            })
        })?;

        // 构建节点映射
        let mut node_map = HashMap::new();
        for node in nodes {
            if let Some(id) = node.get("id").and_then(|v| v.as_str()) {
                node_map.insert(id.to_string(), node.clone());
            }
        }

        // 构建边映射
        let mut edge_map: HashMap<String, Vec<&JsonValue>> = HashMap::new();
        for edge in edges {
            if let Some(source_id) = edge.get("sourceId").and_then(|v| v.as_str()) {
                edge_map
                    .entry(source_id.to_string())
                    .or_insert_with(Vec::new)
                    .push(edge);
            }
        }

        // 找到起始节点（通常是inputNode）
        let start_node = nodes
            .iter()
            .find(|node| node.get("type").and_then(|v| v.as_str()) == Some("inputNode"))
            .ok_or_else(|| {
                ConfigEngineError::RuleExecution(RuleExecutionError::InvalidJdm {
                    reason: "未找到输入节点".to_string(),
                })
            })?;

        let start_node_id = start_node
            .get("id")
            .and_then(|v| v.as_str())
            .ok_or_else(|| {
                ConfigEngineError::RuleExecution(RuleExecutionError::InvalidJdm {
                    reason: "输入节点缺少ID".to_string(),
                })
            })?;

        // 执行决策流程
        let mut execution_context = ExecutionGraphContext::new(context.clone());
        let result = self
            .execute_node(
                start_node_id,
                &node_map,
                &edge_map,
                &mut execution_context,
                trace,
                metrics,
            )
            .await?;

        Ok(result)
    }

    /// 执行节点
    async fn execute_node(
        &self,
        node_id: &str,
        node_map: &HashMap<String, JsonValue>,
        edge_map: &HashMap<String, Vec<&JsonValue>>,
        context: &mut ExecutionGraphContext,
        trace: &mut Option<ExecutionTrace>,
        metrics: &mut PerformanceMetrics,
    ) -> Result<JsonValue> {
        let node = node_map.get(node_id).ok_or_else(|| {
            ConfigEngineError::RuleExecution(RuleExecutionError::InvalidJdm {
                reason: format!("未找到节点: {}", node_id),
            })
        })?;

        let node_type = node.get("type").and_then(|v| v.as_str()).ok_or_else(|| {
            ConfigEngineError::RuleExecution(RuleExecutionError::InvalidJdm {
                reason: format!("节点 {} 缺少类型", node_id),
            })
        })?;

        // 记录节点访问
        metrics.nodes_visited += 1;
        if let Some(trace) = trace {
            *trace.node_visits.entry(node_id.to_string()).or_insert(0) += 1;
        }

        // 根据节点类型执行
        let result = match node_type {
            "inputNode" => self.execute_input_node(node, context).await?,
            "outputNode" => self.execute_output_node(node, context).await?,
            "decisionTableNode" => self.execute_decision_table_node(node, context).await?,
            "switchNode" => self.execute_switch_node(node, context, metrics).await?,
            "functionNode" => self.execute_function_node(node, context).await?,
            _ => {
                return Err(ConfigEngineError::RuleExecution(
                    RuleExecutionError::InvalidJdm {
                        reason: format!("不支持的节点类型: {}", node_type),
                    },
                ));
            }
        };

        // 记录执行步骤
        if let Some(trace) = trace {
            let step = ExecutionStep {
                step_id: uuid::Uuid::new_v4().to_string(),
                node_id: node_id.to_string(),
                step_type: node_type.to_string(),
                input: context.get_all_values(),
                output: self.json_to_context_values(&result)?,
                duration_us: 0, // 实际实现中应该测量时间
            };
            trace.steps.push(step);
        }

        // 如果不是输出节点，继续执行下一个节点
        if node_type != "outputNode" {
            if let Some(edges) = edge_map.get(node_id) {
                for edge in edges {
                    if let Some(target_id) = edge.get("targetId").and_then(|v| v.as_str()) {
                        // 检查边的条件（如果有）
                        if self
                            .evaluate_edge_condition(edge, context, trace, metrics)
                            .await?
                        {
                            return self
                                .execute_node(
                                    target_id, node_map, edge_map, context, trace, metrics,
                                )
                                .await;
                        }
                    }
                }
            }
        }

        Ok(result)
    }

    /// 执行输入节点
    async fn execute_input_node(
        &self,
        node: &JsonValue,
        context: &mut ExecutionGraphContext,
    ) -> Result<JsonValue> {
        // 输入节点将上下文数据传递给下游
        Ok(serde_json::to_value(context.get_all_values())?)
    }

    /// 执行输出节点
    async fn execute_output_node(
        &self,
        node: &JsonValue,
        context: &ExecutionGraphContext,
    ) -> Result<JsonValue> {
        // 输出节点返回最终结果
        if let Some(content) = node.get("content") {
            // 如果节点定义了输出内容，使用该内容
            Ok(content.clone())
        } else {
            // 否则返回当前上下文
            Ok(serde_json::to_value(context.get_all_values())?)
        }
    }

    /// 执行决策表节点
    async fn execute_decision_table_node(
        &self,
        node: &JsonValue,
        context: &mut ExecutionGraphContext,
    ) -> Result<JsonValue> {
        // 简化的决策表实现
        // 实际应该解析决策表并执行规则匹配

        if let Some(content) = node.get("content") {
            // 模拟决策表执行
            if let Some(rules) = content.get("rules").and_then(|v| v.as_array()) {
                for rule in rules {
                    if self.match_decision_rule(rule, context).await? {
                        if let Some(output) = rule.get("output") {
                            return Ok(output.clone());
                        }
                    }
                }
            }
        }

        // 默认输出
        Ok(serde_json::json!({
            "decision_result": "no_match",
            "context": context.get_all_values()
        }))
    }

    /// 执行开关节点
    async fn execute_switch_node(
        &self,
        node: &JsonValue,
        context: &mut ExecutionGraphContext,
        metrics: &mut PerformanceMetrics,
    ) -> Result<JsonValue> {
        // 开关节点根据条件选择不同的执行路径
        if let Some(condition) = node.get("condition").and_then(|v| v.as_str()) {
            metrics.conditions_evaluated += 1;

            let condition_result = self.evaluate_condition(condition, context).await?;

            context.set(
                "switch_result".to_string(),
                ContextValue::Bool(condition_result),
            );

            Ok(serde_json::json!({
                "switch_condition": condition,
                "switch_result": condition_result
            }))
        } else {
            Ok(serde_json::json!({
                "switch_result": false
            }))
        }
    }

    /// 执行函数节点
    async fn execute_function_node(
        &self,
        node: &JsonValue,
        context: &mut ExecutionGraphContext,
    ) -> Result<JsonValue> {
        // 函数节点执行内置或自定义函数
        if let Some(function_name) = node.get("function").and_then(|v| v.as_str()) {
            match function_name {
                "material_discovery" => {
                    self.execute_material_discovery_function(node, context)
                        .await
                }
                "tool_requirements" => self.execute_tool_requirements_function(node, context).await,
                "rarity_calculation" => {
                    self.execute_rarity_calculation_function(node, context)
                        .await
                }
                _ => Ok(serde_json::json!({
                    "function_result": format!("未知函数: {}", function_name),
                    "success": false
                })),
            }
        } else {
            Ok(serde_json::json!({
                "function_result": "未指定函数",
                "success": false
            }))
        }
    }

    /// 执行材料发现函数
    async fn execute_material_discovery_function(
        &self,
        node: &JsonValue,
        context: &ExecutionGraphContext,
    ) -> Result<JsonValue> {
        let terrain = context.get("terrain_type").unwrap_or("unknown");
        let tool = context.get("tool_type").unwrap_or("unknown");

        let (materials, success_rate) = match (terrain, tool) {
            ("forest", "herb_spade") => (vec!["common_herb", "healing_herb", "magic_herb"], 0.8),
            ("mountain", "pickaxe") => (vec!["iron_ore", "gold_ore", "gemstone"], 0.7),
            ("desert", "shovel") => (
                vec!["sand_crystal", "rare_mineral", "ancient_artifact"],
                0.6,
            ),
            ("swamp", "herb_spade") => (vec!["poison_herb", "swamp_moss", "bog_iron"], 0.5),
            ("cave", "pickaxe") => (vec!["crystal", "precious_metal", "underground_water"], 0.9),
            _ => (vec!["unknown_material"], 0.1),
        };

        Ok(serde_json::json!({
            "discovered_materials": materials,
            "success_rate": success_rate,
            "terrain_type": terrain,
            "tool_type": tool,
            "discovery_success": success_rate > 0.5
        }))
    }

    /// 执行工具需求函数
    async fn execute_tool_requirements_function(
        &self,
        node: &JsonValue,
        context: &ExecutionGraphContext,
    ) -> Result<JsonValue> {
        let resource_type = context.get("resource_type").unwrap_or("unknown");

        let required_tools = match resource_type {
            "herb" => vec!["herb_spade", "gathering_knife"],
            "ore" => vec!["pickaxe", "mining_hammer"],
            "crystal" => vec!["crystal_pick", "precision_chisel"],
            "liquid" => vec!["collection_bottle", "spirit_water_bottle"],
            "rare_material" => vec!["enchanted_tool", "master_equipment"],
            _ => vec!["basic_tool"],
        };

        Ok(serde_json::json!({
            "required_tools": required_tools,
            "resource_type": resource_type,
            "tool_count": required_tools.len()
        }))
    }

    /// 执行稀有度计算函数
    async fn execute_rarity_calculation_function(
        &self,
        node: &JsonValue,
        context: &ExecutionGraphContext,
    ) -> Result<JsonValue> {
        let base_rarity = context.get("base_rarity").unwrap_or("common");
        let location_modifier = context
            .get("location_modifier")
            .unwrap_or("1.0")
            .parse::<f64>()
            .unwrap_or(1.0);
        let tool_modifier = context
            .get("tool_modifier")
            .unwrap_or("1.0")
            .parse::<f64>()
            .unwrap_or(1.0);

        let rarity_score = match base_rarity {
            "common" => 1.0,
            "uncommon" => 3.0,
            "rare" => 10.0,
            "epic" => 30.0,
            "legendary" => 100.0,
            _ => 0.5,
        };

        let final_score = rarity_score * location_modifier * tool_modifier;

        let final_rarity = match final_score {
            score if score < 2.0 => "common",
            score if score < 6.0 => "uncommon",
            score if score < 20.0 => "rare",
            score if score < 60.0 => "epic",
            _ => "legendary",
        };

        Ok(serde_json::json!({
            "base_rarity": base_rarity,
            "location_modifier": location_modifier,
            "tool_modifier": tool_modifier,
            "rarity_score": final_score,
            "final_rarity": final_rarity
        }))
    }

    /// 匹配决策规则
    async fn match_decision_rule(
        &self,
        rule: &JsonValue,
        context: &ExecutionGraphContext,
    ) -> Result<bool> {
        if let Some(condition) = rule.get("condition").and_then(|v| v.as_str()) {
            self.evaluate_condition(condition, context).await
        } else {
            Ok(false)
        }
    }

    /// 评估边条件
    async fn evaluate_edge_condition(
        &self,
        edge: &JsonValue,
        context: &ExecutionGraphContext,
        trace: &mut Option<ExecutionTrace>,
        metrics: &mut PerformanceMetrics,
    ) -> Result<bool> {
        if let Some(condition) = edge.get("condition").and_then(|v| v.as_str()) {
            metrics.conditions_evaluated += 1;

            let result = self.evaluate_condition(condition, context).await?;

            // 记录条件评估
            if let Some(trace) = trace {
                trace.condition_evaluations.push(ConditionEvaluation {
                    condition: condition.to_string(),
                    result,
                    context: context.get_all_values(),
                });
            }

            Ok(result)
        } else {
            Ok(true) // 无条件的边总是为真
        }
    }

    /// 评估条件表达式
    async fn evaluate_condition(
        &self,
        condition: &str,
        context: &ExecutionGraphContext,
    ) -> Result<bool> {
        // 简化的条件评估
        // 实际实现应该支持完整的表达式语法

        if condition == "true" {
            return Ok(true);
        }

        if condition == "false" {
            return Ok(false);
        }

        // 检查上下文变量
        if condition.starts_with("$") {
            let var_name = &condition[1..];
            if let Some(value) = context.get_raw(var_name) {
                match value {
                    ContextValue::Bool(b) => return Ok(*b),
                    ContextValue::String(s) => return Ok(!s.is_empty()),
                    ContextValue::Int(i) => return Ok(*i != 0),
                    ContextValue::Float(f) => return Ok(*f != 0.0),
                    _ => return Ok(true),
                }
            }
        }

        // 简单的比较操作
        if condition.contains("==") {
            let parts: Vec<&str> = condition.split("==").collect();
            if parts.len() == 2 {
                let left = parts[0].trim();
                let right = parts[1].trim().trim_matches('"');

                if left.starts_with("$") {
                    let var_name = &left[1..];
                    let value = context.get(var_name);
                    return Ok(value == right);
                }
            }
        }

        // 默认返回false
        Ok(false)
    }

    /// 序列化结果
    fn serialize_result(&self, result: &JsonValue) -> Result<HashMap<String, ContextValue>> {
        self.json_to_context_values(result)
    }

    /// 将JSON值转换为上下文值
    fn json_to_context_values(&self, json: &JsonValue) -> Result<HashMap<String, ContextValue>> {
        let mut result = HashMap::new();

        match json {
            JsonValue::Object(obj) => {
                for (key, value) in obj {
                    let context_value = match value {
                        JsonValue::String(s) => ContextValue::String(s.clone()),
                        JsonValue::Number(n) => {
                            if let Some(i) = n.as_i64() {
                                ContextValue::Int(i)
                            } else if let Some(f) = n.as_f64() {
                                ContextValue::Float(f)
                            } else {
                                ContextValue::String(n.to_string())
                            }
                        }
                        JsonValue::Bool(b) => ContextValue::Bool(*b),
                        JsonValue::Array(arr) => {
                            let mut context_array = Vec::new();
                            for item in arr {
                                match item {
                                    JsonValue::String(s) => {
                                        context_array.push(ContextValue::String(s.clone()))
                                    }
                                    JsonValue::Number(n) => {
                                        if let Some(i) = n.as_i64() {
                                            context_array.push(ContextValue::Int(i));
                                        } else if let Some(f) = n.as_f64() {
                                            context_array.push(ContextValue::Float(f));
                                        }
                                    }
                                    JsonValue::Bool(b) => {
                                        context_array.push(ContextValue::Bool(*b))
                                    }
                                    _ => {} // 跳过复杂类型
                                }
                            }
                            ContextValue::Array(context_array)
                        }
                        JsonValue::Null => ContextValue::Null,
                        _ => ContextValue::String(value.to_string()),
                    };
                    result.insert(key.clone(), context_value);
                }
            }
            _ => {
                result.insert(
                    "result".to_string(),
                    match json {
                        JsonValue::String(s) => ContextValue::String(s.clone()),
                        JsonValue::Number(n) => {
                            if let Some(i) = n.as_i64() {
                                ContextValue::Int(i)
                            } else if let Some(f) = n.as_f64() {
                                ContextValue::Float(f)
                            } else {
                                ContextValue::String(n.to_string())
                            }
                        }
                        JsonValue::Bool(b) => ContextValue::Bool(*b),
                        JsonValue::Null => ContextValue::Null,
                        _ => ContextValue::String(json.to_string()),
                    },
                );
            }
        }

        Ok(result)
    }

    /// 获取执行统计
    pub fn get_stats(&self) -> &ExecutorStats {
        &self.stats
    }

    /// 重置统计
    pub fn reset_stats(&mut self) {
        self.stats = ExecutorStats::new();
    }
}

/// JDM执行器配置
#[derive(Debug, Clone)]
pub struct JdmExecutorConfig {
    /// 最大执行深度
    pub max_execution_depth: u32,
    /// 启用优化
    pub enable_optimizations: bool,
    /// 启用调试模式
    pub debug_mode: bool,
    /// 内存限制（字节）
    pub memory_limit_bytes: Option<usize>,
}

impl Default for JdmExecutorConfig {
    fn default() -> Self {
        Self {
            max_execution_depth: 100,
            enable_optimizations: true,
            debug_mode: false,
            memory_limit_bytes: Some(10 * 1024 * 1024), // 10MB
        }
    }
}

/// 执行器统计
#[derive(Debug, Clone)]
pub struct ExecutorStats {
    /// 总执行次数
    pub total_executions: u64,
    /// 成功执行次数
    pub successful_executions: u64,
    /// 失败执行次数
    pub failed_executions: u64,
    /// 总执行时间（毫秒）
    pub total_execution_time_ms: u64,
    /// 平均执行时间（毫秒）
    pub average_execution_time_ms: f64,
}

impl ExecutorStats {
    pub fn new() -> Self {
        Self {
            total_executions: 0,
            successful_executions: 0,
            failed_executions: 0,
            total_execution_time_ms: 0,
            average_execution_time_ms: 0.0,
        }
    }

    /// 更新平均执行时间
    pub fn update_average(&mut self) {
        if self.total_executions > 0 {
            self.average_execution_time_ms =
                self.total_execution_time_ms as f64 / self.total_executions as f64;
        }
    }
}

impl Default for ExecutorStats {
    fn default() -> Self {
        Self::new()
    }
}

/// 执行图上下文
///
/// 在JDM图执行过程中维护的上下文状态
struct ExecutionGraphContext {
    /// 原始执行上下文
    base_context: ExecutionContext,
    /// 执行过程中的临时变量
    temp_variables: HashMap<String, ContextValue>,
    /// 执行历史
    execution_history: Vec<String>,
}

impl ExecutionGraphContext {
    fn new(base_context: ExecutionContext) -> Self {
        Self {
            base_context,
            temp_variables: HashMap::new(),
            execution_history: Vec::new(),
        }
    }

    fn get(&self, key: &str) -> Option<&str> {
        // 首先检查临时变量
        if let Some(value) = self.temp_variables.get(key) {
            return Some(value.as_str());
        }

        // 然后检查基础上下文
        self.base_context.get(key)
    }

    fn get_raw(&self, key: &str) -> Option<&ContextValue> {
        // 首先检查临时变量
        if let Some(value) = self.temp_variables.get(key) {
            return Some(value);
        }

        // 然后检查基础上下文
        self.base_context.get_raw(key)
    }

    fn set(&mut self, key: String, value: ContextValue) {
        self.temp_variables.insert(key, value);
    }

    fn get_all_values(&self) -> HashMap<String, ContextValue> {
        let mut result = self.base_context.get_all_values();
        result.extend(self.temp_variables.clone());
        result
    }

    fn add_to_history(&mut self, node_id: String) {
        self.execution_history.push(node_id);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config_engine::rules::ExecutionContext;

    #[tokio::test]
    async fn test_execution_result_creation() {
        let mut output = HashMap::new();
        output.insert(
            "test".to_string(),
            ContextValue::String("value".to_string()),
        );

        let result = ExecutionResult::success(output, 100);

        assert!(result.is_success());
        assert!(!result.is_failure());
        assert_eq!(result.execution_time_ms, 100);
        assert_eq!(
            result.get_output("test"),
            Some(&ContextValue::String("value".to_string()))
        );
    }

    #[tokio::test]
    async fn test_performance_metrics() {
        let mut metrics = PerformanceMetrics::new();
        metrics.parse_time_us = 1000;
        metrics.validation_time_us = 500;
        metrics.execution_time_us = 2000;
        metrics.serialization_time_us = 300;

        assert_eq!(metrics.total_time_us(), 3800);
        assert_eq!(metrics.total_time_ms(), 3.8);
    }

    #[test]
    fn test_jdm_executor_config() {
        let config = JdmExecutorConfig::default();

        assert_eq!(config.max_execution_depth, 100);
        assert!(config.enable_optimizations);
        assert!(!config.debug_mode);
        assert_eq!(config.memory_limit_bytes, Some(10 * 1024 * 1024));
    }

    #[test]
    fn test_executor_stats() {
        let mut stats = ExecutorStats::new();
        stats.total_executions = 10;
        stats.total_execution_time_ms = 1000;
        stats.update_average();

        assert_eq!(stats.average_execution_time_ms, 100.0);
    }

    #[tokio::test]
    async fn test_advanced_jdm_executor_creation() {
        let config = JdmExecutorConfig::default();
        let executor = AdvancedJdmExecutor::new(config);

        assert_eq!(executor.get_stats().total_executions, 0);
    }

    #[test]
    fn test_execution_graph_context() {
        let base_context = ExecutionContext::new();
        let mut graph_context = ExecutionGraphContext::new(base_context);

        graph_context.set(
            "test_var".to_string(),
            ContextValue::String("test_value".to_string()),
        );

        assert_eq!(graph_context.get("test_var"), Some("test_value"));
        assert_eq!(graph_context.get_all_values().len(), 1);
    }
}
