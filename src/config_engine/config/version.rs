//! # 配置版本管理模块
//!
//! 提供配置的版本控制、快照管理和历史追踪功能

use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use std::collections::{BTreeMap, HashMap};
use std::fmt;
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::config_engine::{
    config::{MergedConfig, RuleDefinition, TypeDefinition},
    error::{ConfigEngineError, ConfigurationError},
    Result,
};

/// 配置版本管理器
///
/// 负责管理配置的多个版本，提供版本快照、比较和回滚功能
pub struct ConfigVersionManager {
    /// 版本快照存储
    versions: Arc<RwLock<BTreeMap<Version, ConfigSnapshot>>>,
    /// 当前版本
    current_version: Arc<RwLock<Option<Version>>>,
    /// 最大保存版本数
    max_versions: usize,
    /// 版本计数器
    version_counter: Arc<RwLock<u64>>,
    /// 版本标签映射
    version_tags: Arc<RwLock<HashMap<String, Version>>>,
}

impl ConfigVersionManager {
    /// 创建新的版本管理器
    ///
    /// # 参数
    /// - `max_versions`: 最大保存版本数，超过时会自动清理最旧的版本
    pub fn new(max_versions: usize) -> Self {
        Self {
            versions: Arc::new(RwLock::new(BTreeMap::new())),
            current_version: Arc::new(RwLock::new(None)),
            max_versions: if max_versions == 0 { 10 } else { max_versions },
            version_counter: Arc::new(RwLock::new(0)),
            version_tags: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 创建配置快照
    ///
    /// # 参数
    /// - `config`: 要保存的配置
    /// - `description`: 版本描述
    /// - `tag`: 可选的版本标签
    pub async fn create_snapshot(
        &self,
        config: &MergedConfig,
        description: Option<String>,
        tag: Option<String>,
    ) -> Result<Version> {
        // 生成新版本号
        let version = {
            let mut counter = self.version_counter.write().await;
            *counter += 1;
            Version::new(*counter)
        };

        // 计算配置哈希
        let config_hash = self.calculate_config_hash(config)?;

        // 创建快照
        let snapshot = ConfigSnapshot {
            version: version.clone(),
            timestamp: chrono::Utc::now(),
            config_data: config.clone(),
            checksum: config_hash,
            description: description.unwrap_or_else(|| format!("自动快照 v{}", version.major)),
            metadata: SnapshotMetadata::new(),
            size_bytes: self.calculate_config_size(config),
        };

        // 保存快照
        {
            let mut versions = self.versions.write().await;
            versions.insert(version.clone(), snapshot);

            // 清理旧版本
            self.cleanup_old_versions(&mut versions).await;
        }

        // 设置为当前版本
        {
            let mut current = self.current_version.write().await;
            *current = Some(version.clone());
        }

        // 保存标签映射
        if let Some(tag_name) = tag {
            let mut tags = self.version_tags.write().await;
            tags.insert(tag_name, version.clone());
        }

        log::info!("创建配置快照: v{}", version.major);
        Ok(version)
    }

    /// 获取指定版本的快照
    pub async fn get_snapshot(&self, version: &Version) -> Option<ConfigSnapshot> {
        let versions = self.versions.read().await;
        versions.get(version).cloned()
    }

    /// 获取当前版本的快照
    pub async fn get_current_snapshot(&self) -> Option<ConfigSnapshot> {
        let current_version = {
            let current = self.current_version.read().await;
            current.clone()
        };

        if let Some(version) = current_version {
            self.get_snapshot(&version).await
        } else {
            None
        }
    }

    /// 获取最新版本的快照
    pub async fn get_latest_snapshot(&self) -> Option<ConfigSnapshot> {
        let versions = self.versions.read().await;
        versions.values().last().cloned()
    }

    /// 通过标签获取快照
    pub async fn get_snapshot_by_tag(&self, tag: &str) -> Option<ConfigSnapshot> {
        let tags = self.version_tags.read().await;
        if let Some(version) = tags.get(tag) {
            drop(tags); // 释放锁
            self.get_snapshot(version).await
        } else {
            None
        }
    }

    /// 获取所有版本列表
    pub async fn list_versions(&self) -> Vec<VersionInfo> {
        let versions = self.versions.read().await;
        let tags = self.version_tags.read().await;

        versions
            .values()
            .map(|snapshot| {
                // 查找这个版本的标签
                let version_tags: Vec<String> = tags
                    .iter()
                    .filter(|(_, v)| *v == &snapshot.version)
                    .map(|(tag, _)| tag.clone())
                    .collect();

                VersionInfo {
                    version: snapshot.version.clone(),
                    timestamp: snapshot.timestamp,
                    description: snapshot.description.clone(),
                    checksum: snapshot.checksum.clone(),
                    size_bytes: snapshot.size_bytes,
                    tags: version_tags,
                    rule_count: snapshot.config_data.rules.len(),
                    type_count: snapshot.config_data.types.len(),
                }
            })
            .collect()
    }

    /// 比较两个版本的差异
    pub async fn compare_versions(
        &self,
        from_version: &Version,
        to_version: &Version,
    ) -> Result<ConfigDiff> {
        let from_snapshot = self.get_snapshot(from_version).await.ok_or_else(|| {
            ConfigEngineError::Configuration(ConfigurationError::VersionNotFound {
                version: from_version.to_string(),
            })
        })?;

        let to_snapshot = self.get_snapshot(to_version).await.ok_or_else(|| {
            ConfigEngineError::Configuration(ConfigurationError::VersionNotFound {
                version: to_version.to_string(),
            })
        })?;

        self.generate_diff(&from_snapshot.config_data, &to_snapshot.config_data)
    }

    /// 回滚到指定版本
    pub async fn rollback_to_version(&self, target_version: &Version) -> Result<MergedConfig> {
        let snapshot = self.get_snapshot(target_version).await.ok_or_else(|| {
            ConfigEngineError::Configuration(ConfigurationError::VersionNotFound {
                version: target_version.to_string(),
            })
        })?;

        // 设置为当前版本
        {
            let mut current = self.current_version.write().await;
            *current = Some(target_version.clone());
        }

        log::info!("回滚到版本: v{}", target_version.major);
        Ok(snapshot.config_data)
    }

    /// 回滚到标签版本
    pub async fn rollback_to_tag(&self, tag: &str) -> Result<MergedConfig> {
        let tags = self.version_tags.read().await;
        let version = tags
            .get(tag)
            .ok_or_else(|| {
                ConfigEngineError::Configuration(ConfigurationError::TagNotFound {
                    tag: tag.to_string(),
                })
            })?
            .clone();
        drop(tags);

        self.rollback_to_version(&version).await
    }

    /// 删除指定版本
    pub async fn delete_version(&self, version: &Version) -> Result<()> {
        // 检查是否为当前版本
        {
            let current = self.current_version.read().await;
            if let Some(current_version) = current.as_ref() {
                if current_version == version {
                    return Err(ConfigEngineError::Configuration(
                        ConfigurationError::CannotDeleteCurrentVersion {
                            version: version.to_string(),
                        },
                    ));
                }
            }
        }

        // 删除版本
        {
            let mut versions = self.versions.write().await;
            versions.remove(version);
        }

        // 删除相关标签
        {
            let mut tags = self.version_tags.write().await;
            tags.retain(|_, v| v != version);
        }

        log::info!("删除版本: v{}", version.major);
        Ok(())
    }

    /// 为版本添加标签
    pub async fn tag_version(&self, version: &Version, tag: String) -> Result<()> {
        // 检查版本是否存在
        {
            let versions = self.versions.read().await;
            if !versions.contains_key(version) {
                return Err(ConfigEngineError::Configuration(
                    ConfigurationError::VersionNotFound {
                        version: version.to_string(),
                    },
                ));
            }
        }

        // 添加标签
        {
            let mut tags = self.version_tags.write().await;
            tags.insert(tag.clone(), version.clone());
        }

        log::info!("为版本 v{} 添加标签: {}", version.major, tag);
        Ok(())
    }

    /// 删除标签
    pub async fn remove_tag(&self, tag: &str) -> Result<()> {
        let mut tags = self.version_tags.write().await;
        if tags.remove(tag).is_some() {
            log::info!("删除标签: {}", tag);
            Ok(())
        } else {
            Err(ConfigEngineError::Configuration(
                ConfigurationError::TagNotFound {
                    tag: tag.to_string(),
                },
            ))
        }
    }

    /// 获取当前版本号
    pub async fn get_current_version(&self) -> Option<Version> {
        let current = self.current_version.read().await;
        current.clone()
    }

    /// 获取版本统计信息
    pub async fn get_version_stats(&self) -> VersionStats {
        let versions = self.versions.read().await;
        let tags = self.version_tags.read().await;
        let current = self.current_version.read().await;

        let total_size: u64 = versions.values().map(|snapshot| snapshot.size_bytes).sum();

        VersionStats {
            total_versions: versions.len(),
            current_version: current.clone(),
            latest_version: versions.keys().last().cloned(),
            total_size_bytes: total_size,
            tag_count: tags.len(),
            oldest_timestamp: versions.values().next().map(|s| s.timestamp),
            newest_timestamp: versions.values().last().map(|s| s.timestamp),
        }
    }

    /// 计算配置哈希值
    fn calculate_config_hash(&self, config: &MergedConfig) -> Result<String> {
        let serialized = serde_json::to_string(config).map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::SerializationError {
                reason: format!("序列化配置失败: {}", e),
            })
        })?;

        let mut hasher = Sha256::new();
        hasher.update(serialized.as_bytes());
        let result = hasher.finalize();
        Ok(format!("{:x}", result))
    }

    /// 计算配置大小
    fn calculate_config_size(&self, config: &MergedConfig) -> u64 {
        // 简化的大小计算，实际项目中可能需要更精确的计算
        let serialized = serde_json::to_string(config).unwrap_or_default();
        serialized.len() as u64
    }

    /// 清理旧版本
    async fn cleanup_old_versions(&self, versions: &mut BTreeMap<Version, ConfigSnapshot>) {
        while versions.len() > self.max_versions {
            if let Some((oldest_version, _)) = versions.iter().next() {
                let version_to_remove = oldest_version.clone();
                versions.remove(&version_to_remove);
                log::debug!("清理旧版本: v{}", version_to_remove.major);
            } else {
                break;
            }
        }
    }

    /// 生成配置差异
    fn generate_diff(
        &self,
        from_config: &MergedConfig,
        to_config: &MergedConfig,
    ) -> Result<ConfigDiff> {
        let mut diff = ConfigDiff::new();

        // 比较规则
        self.compare_rules(&from_config.rules, &to_config.rules, &mut diff);

        // 比较类型
        self.compare_types(&from_config.types, &to_config.types, &mut diff);

        Ok(diff)
    }

    /// 比较规则差异
    fn compare_rules(
        &self,
        from_rules: &HashMap<String, RuleDefinition>,
        to_rules: &HashMap<String, RuleDefinition>,
        diff: &mut ConfigDiff,
    ) {
        // 新增的规则
        for (id, rule) in to_rules {
            if !from_rules.contains_key(id) {
                diff.added_rules.push(rule.clone());
            }
        }

        // 删除的规则
        for (id, rule) in from_rules {
            if !to_rules.contains_key(id) {
                diff.removed_rules.push(rule.clone());
            }
        }

        // 修改的规则
        for (id, from_rule) in from_rules {
            if let Some(to_rule) = to_rules.get(id) {
                if from_rule.jdm_content != to_rule.jdm_content
                    || from_rule.version != to_rule.version
                    || from_rule.enabled != to_rule.enabled
                {
                    diff.modified_rules.push(RuleChange {
                        id: id.clone(),
                        from: from_rule.clone(),
                        to: to_rule.clone(),
                    });
                }
            }
        }
    }

    /// 比较类型差异
    fn compare_types(
        &self,
        from_types: &HashMap<String, TypeDefinition>,
        to_types: &HashMap<String, TypeDefinition>,
        diff: &mut ConfigDiff,
    ) {
        // 新增的类型
        for (id, type_def) in to_types {
            if !from_types.contains_key(id) {
                diff.added_types.push(type_def.clone());
            }
        }

        // 删除的类型
        for (id, type_def) in from_types {
            if !to_types.contains_key(id) {
                diff.removed_types.push(type_def.clone());
            }
        }

        // 修改的类型
        for (id, from_type) in from_types {
            if let Some(to_type) = to_types.get(id) {
                if from_type.fields != to_type.fields
                    || from_type.validation_rules != to_type.validation_rules
                {
                    diff.modified_types.push(TypeChange {
                        id: id.clone(),
                        from: from_type.clone(),
                        to: to_type.clone(),
                    });
                }
            }
        }
    }
}

/// 版本号
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub struct Version {
    pub major: u64,
}

impl Version {
    pub fn new(major: u64) -> Self {
        Self { major }
    }
}

impl fmt::Display for Version {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.major)
    }
}

/// 配置快照
#[derive(Debug, Clone)]
pub struct ConfigSnapshot {
    /// 版本号
    pub version: Version,
    /// 创建时间
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 配置数据
    pub config_data: MergedConfig,
    /// 校验和
    pub checksum: String,
    /// 描述信息
    pub description: String,
    /// 快照元数据
    pub metadata: SnapshotMetadata,
    /// 大小（字节）
    pub size_bytes: u64,
}

/// 快照元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnapshotMetadata {
    /// 创建者
    pub created_by: Option<String>,
    /// 创建原因
    pub creation_reason: Option<String>,
    /// 自定义属性
    pub custom_properties: HashMap<String, String>,
}

impl SnapshotMetadata {
    pub fn new() -> Self {
        Self {
            created_by: None,
            creation_reason: None,
            custom_properties: HashMap::new(),
        }
    }

    pub fn with_creator(mut self, creator: String) -> Self {
        self.created_by = Some(creator);
        self
    }

    pub fn with_reason(mut self, reason: String) -> Self {
        self.creation_reason = Some(reason);
        self
    }

    pub fn with_property(mut self, key: String, value: String) -> Self {
        self.custom_properties.insert(key, value);
        self
    }
}

impl Default for SnapshotMetadata {
    fn default() -> Self {
        Self::new()
    }
}

/// 版本信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionInfo {
    /// 版本号
    pub version: Version,
    /// 创建时间
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 描述
    pub description: String,
    /// 校验和
    pub checksum: String,
    /// 大小
    pub size_bytes: u64,
    /// 标签列表
    pub tags: Vec<String>,
    /// 规则数量
    pub rule_count: usize,
    /// 类型数量
    pub type_count: usize,
}

/// 版本统计信息
#[derive(Debug, Clone)]
pub struct VersionStats {
    /// 总版本数
    pub total_versions: usize,
    /// 当前版本
    pub current_version: Option<Version>,
    /// 最新版本
    pub latest_version: Option<Version>,
    /// 总大小
    pub total_size_bytes: u64,
    /// 标签数量
    pub tag_count: usize,
    /// 最早时间戳
    pub oldest_timestamp: Option<chrono::DateTime<chrono::Utc>>,
    /// 最新时间戳
    pub newest_timestamp: Option<chrono::DateTime<chrono::Utc>>,
}

/// 配置差异
#[derive(Debug, Clone)]
pub struct ConfigDiff {
    /// 新增的规则
    pub added_rules: Vec<RuleDefinition>,
    /// 删除的规则
    pub removed_rules: Vec<RuleDefinition>,
    /// 修改的规则
    pub modified_rules: Vec<RuleChange>,
    /// 新增的类型
    pub added_types: Vec<TypeDefinition>,
    /// 删除的类型
    pub removed_types: Vec<TypeDefinition>,
    /// 修改的类型
    pub modified_types: Vec<TypeChange>,
}

impl ConfigDiff {
    pub fn new() -> Self {
        Self {
            added_rules: Vec::new(),
            removed_rules: Vec::new(),
            modified_rules: Vec::new(),
            added_types: Vec::new(),
            removed_types: Vec::new(),
            modified_types: Vec::new(),
        }
    }

    /// 检查是否有变更
    pub fn has_changes(&self) -> bool {
        !self.added_rules.is_empty()
            || !self.removed_rules.is_empty()
            || !self.modified_rules.is_empty()
            || !self.added_types.is_empty()
            || !self.removed_types.is_empty()
            || !self.modified_types.is_empty()
    }

    /// 获取变更总数
    pub fn total_changes(&self) -> usize {
        self.added_rules.len()
            + self.removed_rules.len()
            + self.modified_rules.len()
            + self.added_types.len()
            + self.removed_types.len()
            + self.modified_types.len()
    }
}

impl Default for ConfigDiff {
    fn default() -> Self {
        Self::new()
    }
}

/// 规则变更
#[derive(Debug, Clone)]
pub struct RuleChange {
    /// 规则ID
    pub id: String,
    /// 变更前版本
    pub from: RuleDefinition,
    /// 变更后版本
    pub to: RuleDefinition,
}

/// 类型变更
#[derive(Debug, Clone)]
pub struct TypeChange {
    /// 类型ID
    pub id: String,
    /// 变更前版本
    pub from: TypeDefinition,
    /// 变更后版本
    pub to: TypeDefinition,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config_engine::config::RuleDefinition;

    #[tokio::test]
    async fn test_version_manager_creation() {
        let manager = ConfigVersionManager::new(5);
        assert_eq!(manager.max_versions, 5);

        let stats = manager.get_version_stats().await;
        assert_eq!(stats.total_versions, 0);
        assert!(stats.current_version.is_none());
    }

    #[tokio::test]
    async fn test_create_snapshot() {
        let manager = ConfigVersionManager::new(5);
        let mut config = MergedConfig::new();

        // 添加一个测试规则
        let rule = RuleDefinition::new(
            "test_rule".to_string(),
            "Test Rule".to_string(),
            "{}".to_string(),
        );
        config.rules.insert("test_rule".to_string(), rule);

        let version = manager
            .create_snapshot(
                &config,
                Some("First snapshot".to_string()),
                Some("v1.0".to_string()),
            )
            .await
            .unwrap();

        assert_eq!(version.major, 1);

        let snapshot = manager.get_snapshot(&version).await.unwrap();
        assert_eq!(snapshot.description, "First snapshot");
        assert_eq!(snapshot.config_data.rules.len(), 1);
    }

    #[tokio::test]
    async fn test_version_tagging() {
        let manager = ConfigVersionManager::new(5);
        let config = MergedConfig::new();

        let version = manager.create_snapshot(&config, None, None).await.unwrap();

        // 添加标签
        manager
            .tag_version(&version, "stable".to_string())
            .await
            .unwrap();

        // 通过标签获取快照
        let snapshot = manager.get_snapshot_by_tag("stable").await.unwrap();
        assert_eq!(snapshot.version, version);
    }

    #[tokio::test]
    async fn test_rollback() {
        let manager = ConfigVersionManager::new(5);

        // 创建第一个版本
        let mut config1 = MergedConfig::new();
        let rule1 =
            RuleDefinition::new("rule1".to_string(), "Rule 1".to_string(), "{}".to_string());
        config1.rules.insert("rule1".to_string(), rule1);

        let version1 = manager
            .create_snapshot(&config1, None, Some("v1".to_string()))
            .await
            .unwrap();

        // 创建第二个版本
        let mut config2 = MergedConfig::new();
        let rule2 =
            RuleDefinition::new("rule2".to_string(), "Rule 2".to_string(), "{}".to_string());
        config2.rules.insert("rule2".to_string(), rule2);

        let _version2 = manager
            .create_snapshot(&config2, None, Some("v2".to_string()))
            .await
            .unwrap();

        // 回滚到第一个版本
        let rolled_back_config = manager.rollback_to_tag("v1").await.unwrap();
        assert_eq!(rolled_back_config.rules.len(), 1);
        assert!(rolled_back_config.rules.contains_key("rule1"));

        let current_version = manager.get_current_version().await.unwrap();
        assert_eq!(current_version, version1);
    }

    #[tokio::test]
    async fn test_version_comparison() {
        let manager = ConfigVersionManager::new(5);

        // 创建两个不同的配置版本
        let mut config1 = MergedConfig::new();
        config1.rules.insert(
            "rule1".to_string(),
            RuleDefinition::new("rule1".to_string(), "Rule 1".to_string(), "{}".to_string()),
        );

        let mut config2 = MergedConfig::new();
        config2.rules.insert(
            "rule2".to_string(),
            RuleDefinition::new("rule2".to_string(), "Rule 2".to_string(), "{}".to_string()),
        );

        let version1 = manager.create_snapshot(&config1, None, None).await.unwrap();
        let version2 = manager.create_snapshot(&config2, None, None).await.unwrap();

        // 比较版本差异
        let diff = manager
            .compare_versions(&version1, &version2)
            .await
            .unwrap();

        assert_eq!(diff.added_rules.len(), 1);
        assert_eq!(diff.removed_rules.len(), 1);
        assert_eq!(diff.added_rules[0].id, "rule2");
        assert_eq!(diff.removed_rules[0].id, "rule1");
    }

    #[tokio::test]
    async fn test_version_cleanup() {
        let manager = ConfigVersionManager::new(2); // 最多保存2个版本
        let config = MergedConfig::new();

        // 创建3个版本
        let _v1 = manager
            .create_snapshot(&config, Some("Version 1".to_string()), None)
            .await
            .unwrap();
        let _v2 = manager
            .create_snapshot(&config, Some("Version 2".to_string()), None)
            .await
            .unwrap();
        let _v3 = manager
            .create_snapshot(&config, Some("Version 3".to_string()), None)
            .await
            .unwrap();

        // 应该只保留最新的2个版本
        let versions = manager.list_versions().await;
        assert_eq!(versions.len(), 2);
        assert_eq!(versions[0].description, "Version 2");
        assert_eq!(versions[1].description, "Version 3");
    }

    #[test]
    fn test_version_ordering() {
        let v1 = Version::new(1);
        let v2 = Version::new(2);
        let v3 = Version::new(3);

        assert!(v1 < v2);
        assert!(v2 < v3);
        assert!(v1 < v3);
    }

    #[test]
    fn test_config_diff() {
        let mut diff = ConfigDiff::new();
        assert!(!diff.has_changes());
        assert_eq!(diff.total_changes(), 0);

        diff.added_rules.push(RuleDefinition::new(
            "test".to_string(),
            "Test".to_string(),
            "{}".to_string(),
        ));

        assert!(diff.has_changes());
        assert_eq!(diff.total_changes(), 1);
    }
}
