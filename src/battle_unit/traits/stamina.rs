use crate::battle_unit::{FatigueLevel, Stamina};
/// 耐力系统trait
/// 
/// 管理耐力和疲劳状态

use crate::shared::*;


/// 耐力系统 - 管理耐力和疲劳
pub trait StaminaSystem {
    /// 获取当前耐力
    fn current_stamina(&self) -> Stamina;
    
    /// 获取最大耐力
    fn max_stamina(&self) -> Stamina;
    
    /// 获取耐力恢复速度
    fn stamina_regeneration_rate(&self) -> Stamina;
    
    /// 消耗耐力
    fn consume_stamina(&mut self, amount: Stamina) -> GameResult<()>;
    
    /// 恢复耐力
    fn restore_stamina(&mut self, amount: Stamina) -> GameResult<()>;
    
    /// 检查是否疲劳
    fn is_exhausted(&self) -> bool;
    
    /// 获取疲劳程度
    fn fatigue_level(&self) -> FatigueLevel;
}