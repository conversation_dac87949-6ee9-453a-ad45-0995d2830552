/// 基础数值类型定义
///
/// 此模块定义了游戏中使用的所有基础数值类型，
/// 确保类型使用的一致性和语义清晰性

// ============================================================================
// 基础数值类型
// ============================================================================

/// 实体唯一标识符类型
pub type ID = u32;

/// 冷却时间类型（秒）
pub type CooldownTime = f64;

/// 生命值类型
pub type Health = i32;

/// 法力值类型  
pub type Mana = i32;

/// 体力值类型（复用法力值类型）
pub type Stamina = Mana;

/// 经验值类型
pub type Exp = u32;

/// 攻击力类型
pub type Attack = i32;

/// 防御力类型
pub type Defense = i32;

/// 移动速度类型
pub type Speed = f32;

/// 攻击范围类型
pub type Range = f32;

/// 属性数值类型
pub type Attr = u32;

/// 消耗类型
pub type Cost = i32;

/// 距离类型
pub type Distance = f32;

/// 百分比类型
pub type Percent = f32;

/// 攻击速度
pub type AttackSpeed = f32;

/// 等级类型
pub type Level = u32;

/// 属性值类型
pub type AttributeValue = i32;

/// 抗性值类型
pub type Resistance = i32;

/// 精度值类型
pub type Accuracy = i32;

/// 闪避值类型
pub type Evasion = i32;

/// 暴击率类型
pub type CriticalRate = i32;

/// 暴击伤害类型
pub type CriticalDamage = i32;

/// 行动点数类型
pub type ActionPoints = u32;

/// 优先级类型
pub type Priority = i32;

/// 状态效果ID类型
pub type StatusEffectId = ID;
