//! # 配置热重载模块
//!
//! 提供配置文件变更检测和自动重载功能

use async_trait::async_trait;
use futures::channel::mpsc as notify_mpsc;
use futures::StreamExt;
use notify::{Event, EventKind, RecursiveMode, Watcher};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::fs;
use tokio::sync::{mpsc, RwLock};
use tokio::time::interval;

use crate::config_engine::{
    config::{manager::ConfigChangeEvent, ConfigSource},
    error::{ConfigEngineError, ConfigurationError},
    Result,
};

/// 热重载管理器
///
/// 负责监控配置文件变更并触发重载
pub struct HotReloadManager {
    /// 文件监视器映射
    watchers: HashMap<PathBuf, FileWatcher>,
    /// 重载处理器列表
    reload_handlers: Vec<Box<dyn ReloadHandler + Send + Sync>>,
    /// 防抖时间
    debounce_duration: Duration,
    /// 变更事件发送器
    change_sender: mpsc::UnboundedSender<ConfigChangeEvent>,
    /// 监控状态
    status: Arc<RwLock<HotReloadStatus>>,
    /// 是否运行中
    is_running: Arc<RwLock<bool>>,
}

impl HotReloadManager {
    /// 创建新的热重载管理器
    pub fn new(change_sender: mpsc::UnboundedSender<ConfigChangeEvent>) -> Self {
        Self {
            watchers: HashMap::new(),
            reload_handlers: Vec::new(),
            debounce_duration: Duration::from_millis(500), // 默认500ms防抖
            change_sender,
            status: Arc::new(RwLock::new(HotReloadStatus::new())),
            is_running: Arc::new(RwLock::new(false)),
        }
    }

    /// 设置防抖时间
    pub fn with_debounce_duration(mut self, duration: Duration) -> Self {
        self.debounce_duration = duration;
        self
    }

    /// 添加重载处理器
    pub fn add_reload_handler(&mut self, handler: Box<dyn ReloadHandler + Send + Sync>) {
        self.reload_handlers.push(handler);
    }

    /// 开始监控指定路径
    pub async fn watch_path(&mut self, path: PathBuf) -> Result<()> {
        if self.watchers.contains_key(&path) {
            log::warn!("路径 {:?} 已在监控中", path);
            return Ok(());
        }

        let file_watcher = FileWatcher::new(
            path.clone(),
            self.debounce_duration,
            self.change_sender.clone(),
        )
        .await?;

        self.watchers.insert(path.clone(), file_watcher);

        // 更新状态
        {
            let mut status = self.status.write().await;
            status.watched_paths.push(path.clone());
            status.last_activity = Some(chrono::Utc::now());
        }

        log::info!("开始监控路径: {:?}", path);
        Ok(())
    }

    /// 停止监控指定路径
    pub async fn unwatch_path(&mut self, path: &Path) -> Result<()> {
        if let Some(mut watcher) = self.watchers.remove(path) {
            watcher.stop().await?;

            // 更新状态
            {
                let mut status = self.status.write().await;
                status.watched_paths.retain(|p| p != path);
                status.last_activity = Some(chrono::Utc::now());
            }

            log::info!("停止监控路径: {:?}", path);
        }

        Ok(())
    }

    /// 启动热重载管理器
    pub async fn start(&self) -> Result<()> {
        {
            let mut is_running = self.is_running.write().await;
            if *is_running {
                return Err(ConfigEngineError::Configuration(
                    ConfigurationError::HotReloadFailure {
                        reason: "热重载管理器已在运行".to_string(),
                    },
                ));
            }
            *is_running = true;
        }

        // 启动所有文件监视器
        for watcher in self.watchers.values() {
            watcher.start().await?;
        }

        // 更新状态
        {
            let mut status = self.status.write().await;
            status.is_active = true;
            status.started_at = Some(chrono::Utc::now());
        }

        log::info!("热重载管理器已启动，监控 {} 个路径", self.watchers.len());
        Ok(())
    }

    /// 停止热重载管理器
    pub async fn stop(&mut self) -> Result<()> {
        {
            let mut is_running = self.is_running.write().await;
            if !*is_running {
                return Ok(());
            }
            *is_running = false;
        }

        // 停止所有文件监视器
        for watcher in self.watchers.values_mut() {
            watcher.stop().await?;
        }

        // 更新状态
        {
            let mut status = self.status.write().await;
            status.is_active = false;
            status.stopped_at = Some(chrono::Utc::now());
        }

        log::info!("热重载管理器已停止");
        Ok(())
    }

    /// 手动触发重载
    pub async fn trigger_reload(&self, source_path: &Path) -> Result<()> {
        log::info!("手动触发重载: {:?}", source_path);

        for handler in &self.reload_handlers {
            if let Err(e) = handler
                .on_config_changed(source_path, serde_json::Value::Null)
                .await
            {
                log::error!("重载处理器执行失败: {}", e);

                // 发送错误通知
                let event = ConfigChangeEvent::ValidationFailed {
                    timestamp: chrono::Utc::now(),
                    errors: vec![format!("重载失败: {}", e)],
                };

                if let Err(send_err) = self.change_sender.send(event) {
                    log::error!("发送错误通知失败: {}", send_err);
                }
            }
        }

        // 更新状态
        {
            let mut status = self.status.write().await;
            status.reload_count += 1;
            status.last_reload = Some(chrono::Utc::now());
        }

        Ok(())
    }

    /// 获取热重载状态
    pub async fn get_status(&self) -> HotReloadStatus {
        let status = self.status.read().await;
        status.clone()
    }

    /// 检查是否在运行
    pub async fn is_running(&self) -> bool {
        let is_running = self.is_running.read().await;
        *is_running
    }

    /// 获取监控的路径列表
    pub async fn get_watched_paths(&self) -> Vec<PathBuf> {
        let status = self.status.read().await;
        status.watched_paths.clone()
    }
}

/// 文件监视器
pub struct FileWatcher {
    /// 监控路径
    path: PathBuf,
    /// 文件状态缓存
    file_states: HashMap<PathBuf, FileState>,
    /// 防抖时间
    debounce_duration: Duration,
    /// 变更事件发送器
    change_sender: mpsc::UnboundedSender<ConfigChangeEvent>,
    /// 系统文件监视器
    _watcher: Option<notify::RecommendedWatcher>,
    /// 事件接收器
    event_receiver: Option<notify_mpsc::Receiver<notify::Result<Event>>>,
    /// 是否运行中
    is_running: Arc<RwLock<bool>>,
}

impl FileWatcher {
    /// 创建新的文件监视器
    pub async fn new(
        path: PathBuf,
        debounce_duration: Duration,
        change_sender: mpsc::UnboundedSender<ConfigChangeEvent>,
    ) -> Result<Self> {
        // 初始化文件状态
        let file_states = Self::scan_directory(&path).await?;

        // 创建文件系统监视器
        let (tx, rx) = notify_mpsc::channel(100);

        let mut watcher = notify::recommended_watcher(move |res| {
            if let Err(e) = tx.unbounded_send(res) {
                log::error!("发送文件事件失败: {}", e);
            }
        })
        .map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::HotReloadFailure {
                reason: format!("创建文件监视器失败: {}", e),
            })
        })?;

        // 添加监控路径
        watcher
            .watch(&path, RecursiveMode::Recursive)
            .map_err(|e| {
                ConfigEngineError::Configuration(ConfigurationError::HotReloadFailure {
                    reason: format!("添加监控路径失败: {}", e),
                })
            })?;

        Ok(Self {
            path,
            file_states,
            debounce_duration,
            change_sender,
            _watcher: Some(watcher),
            event_receiver: Some(rx),
            is_running: Arc::new(RwLock::new(false)),
        })
    }

    /// 扫描目录获取文件状态
    async fn scan_directory(path: &Path) -> Result<HashMap<PathBuf, FileState>> {
        let mut file_states = HashMap::new();

        if path.is_file() {
            // 单个文件
            if let Ok(metadata) = fs::metadata(path).await {
                let state = FileState {
                    modified_time: metadata.modified().unwrap_or(SystemTime::UNIX_EPOCH),
                    size: metadata.len(),
                    exists: true,
                };
                file_states.insert(path.to_path_buf(), state);
            }
        } else if path.is_dir() {
            // 目录
            let mut dir_reader = fs::read_dir(path).await.map_err(|e| {
                ConfigEngineError::Configuration(ConfigurationError::HotReloadFailure {
                    reason: format!("读取目录失败 {:?}: {}", path, e),
                })
            })?;

            while let Some(entry) = dir_reader.next_entry().await.map_err(|e| {
                ConfigEngineError::Configuration(ConfigurationError::HotReloadFailure {
                    reason: format!("遍历目录失败: {}", e),
                })
            })? {
                let entry_path = entry.path();

                // 只监控配置文件
                if Self::is_config_file(&entry_path) {
                    if let Ok(metadata) = entry.metadata().await {
                        let state = FileState {
                            modified_time: metadata.modified().unwrap_or(SystemTime::UNIX_EPOCH),
                            size: metadata.len(),
                            exists: true,
                        };
                        file_states.insert(entry_path, state);
                    }
                }

                // 递归处理子目录
                if entry_path.is_dir() {
                    let sub_states = Self::scan_directory(&entry_path).await?;
                    file_states.extend(sub_states);
                }
            }
        }

        Ok(file_states)
    }

    /// 判断是否为配置文件
    fn is_config_file(path: &Path) -> bool {
        if let Some(extension) = path.extension() {
            matches!(
                extension.to_string_lossy().to_lowercase().as_str(),
                "json" | "yaml" | "yml" | "toml" | "jdm"
            )
        } else {
            false
        }
    }

    /// 启动文件监视器
    pub async fn start(&self) -> Result<()> {
        {
            let mut is_running = self.is_running.write().await;
            if *is_running {
                return Ok(());
            }
            *is_running = true;
        }

        // 克隆必要的数据用于异步任务
        let debounce_duration = self.debounce_duration;
        let change_sender = self.change_sender.clone();
        let is_running = Arc::clone(&self.is_running);

        // 这里需要处理事件接收，但由于所有权问题，需要重构
        // 暂时提供框架，具体实现需要调整架构

        tokio::spawn(async move {
            let mut interval = interval(debounce_duration);

            loop {
                interval.tick().await;

                let running = {
                    let is_running = is_running.read().await;
                    *is_running
                };

                if !running {
                    break;
                }

                // 这里应该处理文件变更事件
                // 由于架构限制，暂时作为占位符
            }
        });

        log::debug!("文件监视器已启动: {:?}", self.path);
        Ok(())
    }

    /// 停止文件监视器
    pub async fn stop(&mut self) -> Result<()> {
        {
            let mut is_running = self.is_running.write().await;
            *is_running = false;
        }

        // 清理资源
        self._watcher = None;
        self.event_receiver = None;

        log::debug!("文件监视器已停止: {:?}", self.path);
        Ok(())
    }

    /// 检查文件变更
    pub async fn check_changes(&mut self) -> Result<Vec<PathBuf>> {
        let mut changed_files = Vec::new();
        let current_states = Self::scan_directory(&self.path).await?;

        // 检查新增和修改的文件
        for (path, current_state) in &current_states {
            if let Some(cached_state) = self.file_states.get(path) {
                // 文件已存在，检查是否修改
                if current_state.modified_time != cached_state.modified_time
                    || current_state.size != cached_state.size
                {
                    changed_files.push(path.clone());
                }
            } else {
                // 新文件
                changed_files.push(path.clone());
            }
        }

        // 检查删除的文件
        for path in self.file_states.keys() {
            if !current_states.contains_key(path) {
                changed_files.push(path.clone());
            }
        }

        // 更新文件状态缓存
        self.file_states = current_states;

        Ok(changed_files)
    }
}

/// 文件状态
#[derive(Debug, Clone)]
struct FileState {
    /// 修改时间
    modified_time: SystemTime,
    /// 文件大小
    size: u64,
    /// 是否存在
    exists: bool,
}

/// 重载处理器特征
#[async_trait]
pub trait ReloadHandler {
    /// 处理配置变更
    async fn on_config_changed(&self, path: &Path, config: serde_json::Value) -> Result<()>;

    /// 获取处理器名称
    fn name(&self) -> &str;

    /// 是否处理指定路径
    fn handles_path(&self, path: &Path) -> bool;
}

/// 热重载状态
#[derive(Debug, Clone)]
pub struct HotReloadStatus {
    /// 是否激活
    pub is_active: bool,
    /// 监控的路径列表
    pub watched_paths: Vec<PathBuf>,
    /// 重载次数
    pub reload_count: u64,
    /// 最后重载时间
    pub last_reload: Option<chrono::DateTime<chrono::Utc>>,
    /// 最后活动时间
    pub last_activity: Option<chrono::DateTime<chrono::Utc>>,
    /// 启动时间
    pub started_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 停止时间
    pub stopped_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 错误列表
    pub errors: Vec<String>,
}

impl HotReloadStatus {
    pub fn new() -> Self {
        Self {
            is_active: false,
            watched_paths: Vec::new(),
            reload_count: 0,
            last_reload: None,
            last_activity: None,
            started_at: None,
            stopped_at: None,
            errors: Vec::new(),
        }
    }

    pub fn add_error(&mut self, error: String) {
        self.errors.push(error);
        self.last_activity = Some(chrono::Utc::now());
    }

    pub fn clear_errors(&mut self) {
        self.errors.clear();
    }
}

impl Default for HotReloadStatus {
    fn default() -> Self {
        Self::new()
    }
}

/// 配置管理器重载处理器
pub struct ConfigManagerReloadHandler {
    name: String,
    config_sources: Arc<RwLock<Vec<Box<dyn ConfigSource>>>>,
}

impl ConfigManagerReloadHandler {
    pub fn new(name: String, config_sources: Arc<RwLock<Vec<Box<dyn ConfigSource>>>>) -> Self {
        Self {
            name,
            config_sources,
        }
    }
}

#[async_trait]
impl ReloadHandler for ConfigManagerReloadHandler {
    async fn on_config_changed(&self, path: &Path, _config: serde_json::Value) -> Result<()> {
        log::info!("配置文件变更: {:?}", path);

        // 找到对应的配置源并重新加载
        let sources = self.config_sources.read().await;

        for source in sources.iter() {
            if source.handles_path(path) {
                log::info!("重新加载配置源: {}", source.name());
                // 这里需要实际的重载逻辑
                // 由于架构限制，暂时作为占位符
                break;
            }
        }

        Ok(())
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn handles_path(&self, path: &Path) -> bool {
        // 检查是否为配置文件
        FileWatcher::is_config_file(path)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio::fs;

    #[tokio::test]
    async fn test_hot_reload_manager_creation() {
        let (tx, _rx) = mpsc::unbounded_channel();
        let manager = HotReloadManager::new(tx);

        assert_eq!(manager.debounce_duration, Duration::from_millis(500));
        assert!(!manager.is_running().await);
    }

    #[tokio::test]
    async fn test_file_watcher_creation() {
        let temp_dir = TempDir::new().unwrap();
        let (tx, _rx) = mpsc::unbounded_channel();

        let watcher = FileWatcher::new(
            temp_dir.path().to_path_buf(),
            Duration::from_millis(100),
            tx,
        )
        .await;

        assert!(watcher.is_ok());
    }

    #[tokio::test]
    async fn test_config_file_detection() {
        assert!(FileWatcher::is_config_file(Path::new("test.json")));
        assert!(FileWatcher::is_config_file(Path::new("test.yaml")));
        assert!(FileWatcher::is_config_file(Path::new("test.toml")));
        assert!(FileWatcher::is_config_file(Path::new("test.jdm")));
        assert!(!FileWatcher::is_config_file(Path::new("test.txt")));
        assert!(!FileWatcher::is_config_file(Path::new("test")));
    }

    #[tokio::test]
    async fn test_scan_directory() {
        let temp_dir = TempDir::new().unwrap();

        // 创建测试文件
        let config_file = temp_dir.path().join("config.json");
        fs::write(&config_file, "{}").await.unwrap();

        let states = FileWatcher::scan_directory(temp_dir.path()).await.unwrap();
        assert_eq!(states.len(), 1);
        assert!(states.contains_key(&config_file));
    }

    #[tokio::test]
    async fn test_file_change_detection() {
        let temp_dir = TempDir::new().unwrap();
        let (tx, _rx) = mpsc::unbounded_channel();

        // 创建初始文件
        let config_file = temp_dir.path().join("config.json");
        fs::write(&config_file, "{}").await.unwrap();

        let mut watcher = FileWatcher::new(
            temp_dir.path().to_path_buf(),
            Duration::from_millis(100),
            tx,
        )
        .await
        .unwrap();

        // 初始检查，应该没有变更
        let changes = watcher.check_changes().await.unwrap();
        assert!(changes.is_empty());

        // 修改文件
        tokio::time::sleep(Duration::from_millis(10)).await; // 确保时间戳不同
        fs::write(&config_file, r#"{"test": "value"}"#)
            .await
            .unwrap();

        // 检查变更
        let changes = watcher.check_changes().await.unwrap();
        assert_eq!(changes.len(), 1);
        assert_eq!(changes[0], config_file);
    }

    #[tokio::test]
    async fn test_hot_reload_status() {
        let mut status = HotReloadStatus::new();

        assert!(!status.is_active);
        assert_eq!(status.reload_count, 0);
        assert!(status.errors.is_empty());

        status.add_error("Test error".to_string());
        assert_eq!(status.errors.len(), 1);
        assert!(status.last_activity.is_some());

        status.clear_errors();
        assert!(status.errors.is_empty());
    }
}
