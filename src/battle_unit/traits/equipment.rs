use crate::shared::{EquipmentSlot, GameResult};
use crate::{EquipmentBonuses, SetBonus};
/// 装备管理trait
///
/// 管理装备栏、装备加成和套装效果
use std::collections::HashMap;

/// 装备管理
pub trait EquipmentManager {
    /// 获取装备栏
    fn equipment_slots(&self) -> &HashMap<EquipmentSlot, Option<crate::equipment::Equipment>>;

    /// 装备物品
    fn equip_item(
        &mut self,
        slot: EquipmentSlot,
        equipment: crate::equipment::Equipment,
    ) -> GameResult<Option<crate::equipment::Equipment>>;

    /// 卸下装备
    fn unequip_item(
        &mut self,
        slot: EquipmentSlot,
    ) -> GameResult<Option<crate::equipment::Equipment>>;

    /// 获取指定槽位的装备
    fn get_equipment(&self, slot: EquipmentSlot) -> Option<&crate::equipment::Equipment>;

    /// 检查是否可装备
    fn can_equip(&self, equipment: &crate::equipment::Equipment) -> bool;

    /// 获取装备加成
    fn equipment_bonuses(&self) -> EquipmentBonuses;

    /// 计算装备等级
    fn equipment_level(&self) -> u32;

    /// 获取套装效果
    fn set_bonuses(&self) -> Vec<SetBonus>;
}
