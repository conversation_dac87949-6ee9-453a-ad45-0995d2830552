//! # 配置回滚管理模块
//!
//! 提供安全的配置回滚、自动备份和故障恢复功能
//!
//! ## 核心功能
//!
//! - 安全回滚机制（预检查、分阶段执行）
//! - 自动备份和恢复点管理
//! - 回滚冲突检测和解决
//! - 回滚历史追踪和审计
//! - 热切换和无缝回滚

use crate::config_engine::config::version::{
    ConfigDiff, ConfigSnapshot, ConfigVersionManager, Version,
};
use crate::config_engine::{
    cache::CacheManager,
    config::{ConfigManager, MergedConfig},
    error::{ConfigEngineError, ConfigurationError},
    validation::{ValidationEngine, ValidationResult},
    Result,
};

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, RwLock};

/// 配置回滚管理器
pub struct ConfigRollbackManager {
    /// 版本管理器
    version_manager: Arc<ConfigVersionManager>,
    /// 配置管理器
    config_manager: Arc<ConfigManager>,
    /// 验证引擎
    validation_engine: Arc<ValidationEngine>,
    /// 缓存管理器
    cache_manager: Arc<CacheManager>,
    /// 回滚历史
    rollback_history: Arc<RwLock<Vec<RollbackRecord>>>,
    /// 当前回滚状态
    current_rollback: Arc<Mutex<Option<RollbackOperation>>>,
    /// 自动备份配置
    auto_backup_config: Arc<RwLock<AutoBackupConfig>>,
    /// 回滚策略
    rollback_strategy: Arc<RwLock<RollbackStrategy>>,
}

impl ConfigRollbackManager {
    /// 创建新的回滚管理器
    pub async fn new(
        version_manager: Arc<ConfigVersionManager>,
        config_manager: Arc<ConfigManager>,
        validation_engine: Arc<ValidationEngine>,
        cache_manager: Arc<CacheManager>,
    ) -> Self {
        Self {
            version_manager,
            config_manager,
            validation_engine,
            cache_manager,
            rollback_history: Arc::new(RwLock::new(Vec::new())),
            current_rollback: Arc::new(Mutex::new(None)),
            auto_backup_config: Arc::new(RwLock::new(AutoBackupConfig::default())),
            rollback_strategy: Arc::new(RwLock::new(RollbackStrategy::default())),
        }
    }

    /// 安全回滚到指定版本
    pub async fn safe_rollback_to_version(
        &self,
        target_version: &Version,
    ) -> Result<RollbackResult> {
        // 检查是否有正在进行的回滚
        {
            let current_rollback = self.current_rollback.lock().await;
            if current_rollback.is_some() {
                return Err(ConfigEngineError::Configuration(
                    ConfigurationError::RollbackInProgress,
                ));
            }
        }

        let rollback_id = uuid::Uuid::new_v4().to_string();
        let start_time = Instant::now();

        // 创建回滚操作记录
        let mut rollback_op = RollbackOperation {
            id: rollback_id.clone(),
            target_version: target_version.clone(),
            start_time: chrono::Utc::now(),
            status: RollbackStatus::Preparing,
            steps: Vec::new(),
            error: None,
            validation_results: Vec::new(),
            pre_rollback_snapshot: None,
            post_rollback_snapshot: None,
        };

        // 设置当前回滚操作
        {
            let mut current_rollback = self.current_rollback.lock().await;
            *current_rollback = Some(rollback_op.clone());
        }

        // 执行回滚步骤
        let result = self.execute_rollback_steps(&mut rollback_op).await;

        // 清除当前回滚状态
        {
            let mut current_rollback = self.current_rollback.lock().await;
            *current_rollback = None;
        }

        // 记录回滚历史
        self.record_rollback_operation(&rollback_op).await;

        match result {
            Ok(config) => Ok(RollbackResult {
                success: true,
                rollback_id,
                target_version: target_version.clone(),
                execution_time: start_time.elapsed(),
                new_config: Some(config),
                validation_errors: Vec::new(),
                warnings: rollback_op.extract_warnings(),
            }),
            Err(e) => Ok(RollbackResult {
                success: false,
                rollback_id,
                target_version: target_version.clone(),
                execution_time: start_time.elapsed(),
                new_config: None,
                validation_errors: vec![e.to_string()],
                warnings: rollback_op.extract_warnings(),
            }),
        }
    }

    /// 创建恢复点
    pub async fn create_restore_point(&self, description: String) -> Result<Version> {
        // 获取当前配置
        let current_config = self.config_manager.get_merged_config().await?;

        // 创建快照
        let version = self
            .version_manager
            .create_snapshot(
                &current_config,
                Some(format!("恢复点: {}", description)),
                None,
            )
            .await?;

        log::info!("创建恢复点: v{} - {}", version.major, description);
        Ok(version)
    }

    /// 自动备份当前配置
    pub async fn auto_backup(&self) -> Result<Option<Version>> {
        let backup_config = self.auto_backup_config.read().await;

        if !backup_config.enabled {
            return Ok(None);
        }

        // 检查是否需要备份
        if !self.should_create_backup(&backup_config).await {
            return Ok(None);
        }

        let current_config = self.config_manager.get_merged_config().await?;
        let version = self
            .version_manager
            .create_snapshot(
                &current_config,
                Some("自动备份".to_string()),
                Some(format!(
                    "auto_backup_{}",
                    chrono::Utc::now().format("%Y%m%d_%H%M%S")
                )),
            )
            .await?;

        log::info!("自动备份完成: v{}", version.major);
        Ok(Some(version))
    }

    /// 预检查回滚可行性
    pub async fn precheck_rollback(&self, target_version: &Version) -> Result<RollbackPrecheck> {
        let mut precheck = RollbackPrecheck {
            target_version: target_version.clone(),
            can_rollback: true,
            issues: Vec::new(),
            warnings: Vec::new(),
            estimated_downtime: Duration::from_secs(0),
            required_validations: Vec::new(),
        };

        // 检查目标版本是否存在
        let target_snapshot = self.version_manager.get_snapshot(target_version).await;
        if target_snapshot.is_none() {
            precheck.can_rollback = false;
            precheck.issues.push("目标版本不存在".to_string());
            return Ok(precheck);
        }

        let target_snapshot = target_snapshot.unwrap();

        // 检查配置兼容性
        let current_config = self.config_manager.get_merged_config().await?;
        let compatibility_check = self
            .check_configuration_compatibility(&current_config, &target_snapshot.config_data)
            .await;

        if !compatibility_check.compatible {
            precheck.warnings.extend(compatibility_check.issues);
        }

        // 估算回滚时间
        precheck.estimated_downtime = self
            .estimate_rollback_time(&target_snapshot.config_data)
            .await;

        // 确定需要的验证
        precheck.required_validations = vec![
            "schema_validation".to_string(),
            "business_rules_validation".to_string(),
            "integrity_check".to_string(),
        ];

        Ok(precheck)
    }

    /// 获取回滚历史
    pub async fn get_rollback_history(&self, limit: Option<usize>) -> Vec<RollbackRecord> {
        let history = self.rollback_history.read().await;

        let records: Vec<RollbackRecord> = history.iter().cloned().collect();

        if let Some(limit) = limit {
            records.into_iter().rev().take(limit).collect()
        } else {
            records.into_iter().rev().collect()
        }
    }

    /// 获取当前回滚状态
    pub async fn get_current_rollback_status(&self) -> Option<RollbackStatus> {
        let current_rollback = self.current_rollback.lock().await;
        current_rollback.as_ref().map(|op| op.status.clone())
    }

    /// 取消正在进行的回滚
    pub async fn cancel_rollback(&self) -> Result<()> {
        let mut current_rollback = self.current_rollback.lock().await;

        if let Some(mut rollback_op) = current_rollback.take() {
            rollback_op.status = RollbackStatus::Cancelled;

            // 如果有预回滚快照，尝试恢复
            if let Some(pre_snapshot) = &rollback_op.pre_rollback_snapshot {
                let _ = self
                    .config_manager
                    .update_config(&pre_snapshot.config_data)
                    .await;
            }

            // 记录取消的回滚
            self.record_rollback_operation(&rollback_op).await;

            log::warn!("回滚操作已取消: {}", rollback_op.id);
            Ok(())
        } else {
            Err(ConfigEngineError::Configuration(
                ConfigurationError::NoActiveRollback,
            ))
        }
    }

    /// 配置自动备份
    pub async fn configure_auto_backup(&self, config: AutoBackupConfig) {
        let mut auto_backup_config = self.auto_backup_config.write().await;
        *auto_backup_config = config;
    }

    /// 配置回滚策略
    pub async fn configure_rollback_strategy(&self, strategy: RollbackStrategy) {
        let mut rollback_strategy = self.rollback_strategy.write().await;
        *rollback_strategy = strategy;
    }

    /// 强制回滚（跳过某些安全检查）
    pub async fn force_rollback_to_version(
        &self,
        target_version: &Version,
    ) -> Result<RollbackResult> {
        log::warn!("执行强制回滚到版本: v{}", target_version.major);

        // 获取目标配置
        let target_snapshot = self
            .version_manager
            .get_snapshot(target_version)
            .await
            .ok_or_else(|| {
                ConfigEngineError::Configuration(ConfigurationError::VersionNotFound {
                    version: target_version.to_string(),
                })
            })?;

        let start_time = Instant::now();

        // 直接更新配置
        match self
            .config_manager
            .update_config(&target_snapshot.config_data)
            .await
        {
            Ok(_) => {
                // 清除缓存
                self.cache_manager.clear_all().await;

                Ok(RollbackResult {
                    success: true,
                    rollback_id: uuid::Uuid::new_v4().to_string(),
                    target_version: target_version.clone(),
                    execution_time: start_time.elapsed(),
                    new_config: Some(target_snapshot.config_data),
                    validation_errors: Vec::new(),
                    warnings: vec!["强制回滚已跳过安全检查".to_string()],
                })
            }
            Err(e) => Ok(RollbackResult {
                success: false,
                rollback_id: uuid::Uuid::new_v4().to_string(),
                target_version: target_version.clone(),
                execution_time: start_time.elapsed(),
                new_config: None,
                validation_errors: vec![e.to_string()],
                warnings: vec!["强制回滚失败".to_string()],
            }),
        }
    }

    // 私有辅助方法

    async fn execute_rollback_steps(
        &self,
        rollback_op: &mut RollbackOperation,
    ) -> Result<MergedConfig> {
        // 步骤1: 预回滚快照
        rollback_op.add_step(
            "creating_pre_rollback_snapshot".to_string(),
            RollbackStepStatus::InProgress,
        );

        let current_config = self.config_manager.get_merged_config().await?;
        let pre_snapshot_version = self
            .version_manager
            .create_snapshot(
                &current_config,
                Some(format!("回滚前快照 ({})", rollback_op.id)),
                None,
            )
            .await?;

        rollback_op.pre_rollback_snapshot = self
            .version_manager
            .get_snapshot(&pre_snapshot_version)
            .await;
        rollback_op.complete_step("creating_pre_rollback_snapshot".to_string());

        // 步骤2: 获取目标配置
        rollback_op.add_step(
            "loading_target_config".to_string(),
            RollbackStepStatus::InProgress,
        );

        let target_snapshot = self
            .version_manager
            .get_snapshot(&rollback_op.target_version)
            .await
            .ok_or_else(|| {
                ConfigEngineError::Configuration(ConfigurationError::VersionNotFound {
                    version: rollback_op.target_version.to_string(),
                })
            })?;

        rollback_op.complete_step("loading_target_config".to_string());

        // 步骤3: 配置验证
        rollback_op.add_step(
            "validating_target_config".to_string(),
            RollbackStepStatus::InProgress,
        );
        rollback_op.status = RollbackStatus::Validating;

        let validation_result = self
            .validation_engine
            .validate_config(&target_snapshot.config_data)
            .await?;
        rollback_op
            .validation_results
            .push(validation_result.clone());

        if !validation_result.is_valid {
            rollback_op.fail_step(
                "validating_target_config".to_string(),
                "配置验证失败".to_string(),
            );
            return Err(ConfigEngineError::Configuration(
                ConfigurationError::ValidationFailed {
                    errors: validation_result
                        .errors
                        .iter()
                        .map(|e| e.message.clone())
                        .collect::<Vec<_>>()
                        .join(", "),
                },
            ));
        }

        rollback_op.complete_step("validating_target_config".to_string());

        // 步骤4: 应用配置
        rollback_op.add_step(
            "applying_config".to_string(),
            RollbackStepStatus::InProgress,
        );
        rollback_op.status = RollbackStatus::Applying;

        self.config_manager
            .update_config(&target_snapshot.config_data)
            .await?;
        rollback_op.complete_step("applying_config".to_string());

        // 步骤5: 清除缓存
        rollback_op.add_step("clearing_cache".to_string(), RollbackStepStatus::InProgress);

        self.cache_manager.clear_all().await;
        rollback_op.complete_step("clearing_cache".to_string());

        // 步骤6: 后回滚验证
        rollback_op.add_step(
            "post_rollback_validation".to_string(),
            RollbackStepStatus::InProgress,
        );

        let post_config = self.config_manager.get_merged_config().await?;
        let post_validation = self.validation_engine.validate_config(&post_config).await?;
        rollback_op.validation_results.push(post_validation.clone());

        if !post_validation.is_valid {
            // 尝试恢复到回滚前状态
            if let Some(pre_snapshot) = &rollback_op.pre_rollback_snapshot {
                let _ = self
                    .config_manager
                    .update_config(&pre_snapshot.config_data)
                    .await;
            }

            rollback_op.fail_step(
                "post_rollback_validation".to_string(),
                "回滚后验证失败".to_string(),
            );
            return Err(ConfigEngineError::Configuration(
                ConfigurationError::PostRollbackValidationFailed,
            ));
        }

        rollback_op.complete_step("post_rollback_validation".to_string());

        // 步骤7: 创建后回滚快照
        rollback_op.add_step(
            "creating_post_rollback_snapshot".to_string(),
            RollbackStepStatus::InProgress,
        );

        let post_snapshot_version = self
            .version_manager
            .create_snapshot(
                &post_config,
                Some(format!("回滚后快照 ({})", rollback_op.id)),
                None,
            )
            .await?;

        rollback_op.post_rollback_snapshot = self
            .version_manager
            .get_snapshot(&post_snapshot_version)
            .await;
        rollback_op.complete_step("creating_post_rollback_snapshot".to_string());

        rollback_op.status = RollbackStatus::Completed;
        Ok(target_snapshot.config_data)
    }

    async fn should_create_backup(&self, backup_config: &AutoBackupConfig) -> bool {
        if let Some(last_backup_time) = backup_config.last_backup_time {
            let elapsed = chrono::Utc::now().signed_duration_since(last_backup_time);
            elapsed.num_seconds() >= backup_config.interval_seconds as i64
        } else {
            true // 从未备份，应该创建
        }
    }

    async fn check_configuration_compatibility(
        &self,
        current_config: &MergedConfig,
        target_config: &MergedConfig,
    ) -> CompatibilityCheck {
        let mut check = CompatibilityCheck {
            compatible: true,
            issues: Vec::new(),
        };

        // 检查规则数量变化
        let rule_count_diff = target_config.rules.len() as i32 - current_config.rules.len() as i32;
        if rule_count_diff.abs() > 10 {
            check
                .issues
                .push(format!("规则数量变化较大: {}", rule_count_diff));
        }

        // 检查类型数量变化
        let type_count_diff = target_config.types.len() as i32 - current_config.types.len() as i32;
        if type_count_diff.abs() > 5 {
            check
                .issues
                .push(format!("类型数量变化较大: {}", type_count_diff));
        }

        // 检查是否有关键规则被删除
        for (rule_id, rule) in &current_config.rules {
            if !target_config.rules.contains_key(rule_id) && rule.enabled {
                check.issues.push(format!("活跃规则将被删除: {}", rule_id));
            }
        }

        check
    }

    async fn estimate_rollback_time(&self, _target_config: &MergedConfig) -> Duration {
        // 简化的时间估算，实际实现中应该基于配置复杂度、历史数据等
        Duration::from_secs(30)
    }

    async fn record_rollback_operation(&self, rollback_op: &RollbackOperation) {
        let record = RollbackRecord {
            id: rollback_op.id.clone(),
            target_version: rollback_op.target_version.clone(),
            start_time: rollback_op.start_time,
            end_time: chrono::Utc::now(),
            status: rollback_op.status.clone(),
            success: matches!(rollback_op.status, RollbackStatus::Completed),
            error_message: rollback_op.error.clone(),
            steps_completed: rollback_op
                .steps
                .iter()
                .filter(|s| s.status == RollbackStepStatus::Completed)
                .count(),
            total_steps: rollback_op.steps.len(),
        };

        let mut history = self.rollback_history.write().await;
        history.push(record);

        // 限制历史记录数量
        if history.len() > 100 {
            history.remove(0);
        }
    }
}

/// 回滚操作
#[derive(Debug, Clone)]
pub struct RollbackOperation {
    pub id: String,
    pub target_version: Version,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub status: RollbackStatus,
    pub steps: Vec<RollbackStep>,
    pub error: Option<String>,
    pub validation_results: Vec<ValidationResult>,
    pub pre_rollback_snapshot: Option<ConfigSnapshot>,
    pub post_rollback_snapshot: Option<ConfigSnapshot>,
}

impl RollbackOperation {
    pub fn add_step(&mut self, name: String, status: RollbackStepStatus) {
        self.steps.push(RollbackStep {
            name,
            status,
            start_time: chrono::Utc::now(),
            end_time: None,
            error: None,
        });
    }

    pub fn complete_step(&mut self, name: String) {
        if let Some(step) = self.steps.iter_mut().find(|s| s.name == name) {
            step.status = RollbackStepStatus::Completed;
            step.end_time = Some(chrono::Utc::now());
        }
    }

    pub fn fail_step(&mut self, name: String, error: String) {
        if let Some(step) = self.steps.iter_mut().find(|s| s.name == name) {
            step.status = RollbackStepStatus::Failed;
            step.end_time = Some(chrono::Utc::now());
            step.error = Some(error.clone());
        }
        self.error = Some(error);
        self.status = RollbackStatus::Failed;
    }

    pub fn extract_warnings(&self) -> Vec<String> {
        let mut warnings = Vec::new();

        for validation_result in &self.validation_results {
            for warning in &validation_result.warnings {
                warnings.push(warning.message.clone());
            }
        }

        warnings
    }
}

/// 回滚状态
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum RollbackStatus {
    Preparing,
    Validating,
    Applying,
    Completed,
    Failed,
    Cancelled,
}

/// 回滚步骤
#[derive(Debug, Clone)]
pub struct RollbackStep {
    pub name: String,
    pub status: RollbackStepStatus,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub error: Option<String>,
}

/// 回滚步骤状态
#[derive(Debug, Clone, PartialEq)]
pub enum RollbackStepStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
}

/// 回滚结果
#[derive(Debug, Clone)]
pub struct RollbackResult {
    pub success: bool,
    pub rollback_id: String,
    pub target_version: Version,
    pub execution_time: Duration,
    pub new_config: Option<MergedConfig>,
    pub validation_errors: Vec<String>,
    pub warnings: Vec<String>,
}

/// 回滚预检查结果
#[derive(Debug, Clone)]
pub struct RollbackPrecheck {
    pub target_version: Version,
    pub can_rollback: bool,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
    pub estimated_downtime: Duration,
    pub required_validations: Vec<String>,
}

/// 回滚记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RollbackRecord {
    pub id: String,
    pub target_version: Version,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: chrono::DateTime<chrono::Utc>,
    pub status: RollbackStatus,
    pub success: bool,
    pub error_message: Option<String>,
    pub steps_completed: usize,
    pub total_steps: usize,
}

/// 自动备份配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoBackupConfig {
    pub enabled: bool,
    pub interval_seconds: u64,
    pub max_backups: usize,
    pub backup_on_change: bool,
    pub last_backup_time: Option<chrono::DateTime<chrono::Utc>>,
}

impl Default for AutoBackupConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            interval_seconds: 3600, // 1小时
            max_backups: 24,
            backup_on_change: true,
            last_backup_time: None,
        }
    }
}

/// 回滚策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RollbackStrategy {
    pub require_validation: bool,
    pub allow_data_loss: bool,
    pub max_rollback_age_hours: u64,
    pub require_manual_approval: bool,
    pub create_backup_before_rollback: bool,
}

impl Default for RollbackStrategy {
    fn default() -> Self {
        Self {
            require_validation: true,
            allow_data_loss: false,
            max_rollback_age_hours: 168, // 1周
            require_manual_approval: true,
            create_backup_before_rollback: true,
        }
    }
}

/// 兼容性检查结果
#[derive(Debug, Clone)]
struct CompatibilityCheck {
    compatible: bool,
    issues: Vec<String>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rollback_operation() {
        let mut op = RollbackOperation {
            id: "test".to_string(),
            target_version: Version::new(1),
            start_time: chrono::Utc::now(),
            status: RollbackStatus::Preparing,
            steps: Vec::new(),
            error: None,
            validation_results: Vec::new(),
            pre_rollback_snapshot: None,
            post_rollback_snapshot: None,
        };

        op.add_step("test_step".to_string(), RollbackStepStatus::InProgress);
        assert_eq!(op.steps.len(), 1);
        assert_eq!(op.steps[0].status, RollbackStepStatus::InProgress);

        op.complete_step("test_step".to_string());
        assert_eq!(op.steps[0].status, RollbackStepStatus::Completed);
        assert!(op.steps[0].end_time.is_some());
    }

    #[test]
    fn test_auto_backup_config() {
        let config = AutoBackupConfig::default();
        assert!(config.enabled);
        assert_eq!(config.interval_seconds, 3600);
        assert_eq!(config.max_backups, 24);
        assert!(config.backup_on_change);
    }

    #[test]
    fn test_rollback_strategy() {
        let strategy = RollbackStrategy::default();
        assert!(strategy.require_validation);
        assert!(!strategy.allow_data_loss);
        assert_eq!(strategy.max_rollback_age_hours, 168);
        assert!(strategy.require_manual_approval);
        assert!(strategy.create_backup_before_rollback);
    }
}
