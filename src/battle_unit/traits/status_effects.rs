use crate::shared::GameResult;
use crate::{StatusEffect, StatusEffectId, StatusType};

/// 状态效果trait
///
/// 管理各种状态效果，包括增益、减益和特殊状态
/// 状态效果管理
pub trait StatusEffectManager {
    /// 获取所有活跃状态
    fn active_statuses(&self) -> Vec<StatusEffect>;

    /// 添加状态效果
    fn add_status_effect(&mut self, effect: StatusEffect) -> GameResult<()>;

    /// 移除状态效果
    fn remove_status_effect(&mut self, effect_id: StatusEffectId) -> GameResult<()>;

    /// 检查是否有指定状态
    fn has_status(&self, status_type: StatusType) -> bool;

    /// 获取状态效果强度
    fn status_intensity(&self, status_type: StatusType) -> f32;

    /// 更新状态效果
    fn update_status_effects(&mut self, delta_time: f32);

    /// 清除指定类型的状态
    fn clear_status_type(&mut self, status_type: StatusType) -> GameResult<()>;

    /// 清除所有状态
    fn clear_all_statuses(&mut self) -> GameResult<()>;

    /// 获取状态免疫列表
    fn status_immunities(&self) -> Vec<StatusType>;

    /// 检查是否免疫状态
    fn is_immune_to_status(&self, status_type: StatusType) -> bool;
}
