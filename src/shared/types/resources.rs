/// 资源相关类型定义
///
/// 此模块定义了游戏中所有与资源消耗、管理相关的类型
use super::primitives::*;
use crate::battle_unit::StaminaPoints;
use crate::shared::{GameError, GameResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// ============================================================================
// 资源消耗和管理类型
// ============================================================================

/// 资源消耗
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ResourceCost {
    /// 生命值消耗
    pub health_cost: Health,
    /// 法力值消耗
    pub mana_cost: Mana,
    /// 体力消耗
    pub stamina_cost: Mana,
    /// 特殊资源消耗
    pub special_costs: HashMap<String, i32>,
    pub experience_cost: i32,
    pub other_costs: HashMap<String, i32>,
}

impl ResourceCost {
    /// 创建空的资源消耗
    pub fn none() -> Self {
        Self {
            health_cost: 0,
            mana_cost: 0,
            stamina_cost: 0,
            special_costs: HashMap::new(),
            experience_cost: 0,
            other_costs: HashMap::new(),
        }
    }

    /// 创建法力消耗
    pub fn mana(cost: Mana) -> Self {
        Self {
            health_cost: 0,
            mana_cost: cost,
            stamina_cost: 0,
            special_costs: HashMap::new(),
            experience_cost: 0,
            other_costs: HashMap::new(),
        }
    }

    /// 创建耐力消耗
    pub fn stamina(cost: StaminaPoints) -> Self {
        Self {
            health_cost: 0,
            mana_cost: 0,
            stamina_cost: cost,
            special_costs: HashMap::new(),
            experience_cost: 0,
            other_costs: HashMap::new(),
        }
    }

    /// 创建混合消耗
    pub fn mixed(mana: Mana, stamina: StaminaPoints) -> Self {
        Self {
            health_cost: 0,
            mana_cost: mana,
            stamina_cost: stamina,
            special_costs: HashMap::new(),
            experience_cost: 0,
            other_costs: HashMap::new(),
        }
    }

    /// 添加其他资源消耗
    pub fn with_other_cost(mut self, resource: String, cost: i32) -> Self {
        self.other_costs.insert(resource, cost);
        self
    }

    /// 检查是否有任何消耗
    pub fn has_any_cost(&self) -> bool {
        self.health_cost > 0
            || self.mana_cost > 0
            || self.stamina_cost > 0
            || self.experience_cost > 0
            || !self.other_costs.is_empty()
    }

    pub fn health(cost: Health) -> Self {
        Self {
            health_cost: cost,
            mana_cost: 0,
            stamina_cost: 0,
            special_costs: HashMap::new(),
            experience_cost: 0,
            other_costs: HashMap::new(),
        }
    }

    /// 根据等级缩放消耗
    pub fn scaled_by_level(&self, level: Level) -> Self {
        let scale_factor = 1.0 + (level - 1) as f64 * 0.1; // 每级增加10%消耗

        Self {
            health_cost: (self.health_cost as f64 * scale_factor) as Health,
            mana_cost: (self.mana_cost as f64 * scale_factor) as Mana,
            stamina_cost: (self.stamina_cost as f64 * scale_factor) as Mana,
            special_costs: self
                .special_costs
                .iter()
                .map(|(k, v)| (k.clone(), (*v as f64 * scale_factor) as i32))
                .collect(),
            experience_cost: (self.experience_cost as f64 * scale_factor) as i32,
            other_costs: self
                .other_costs
                .iter()
                .map(|(k, v)| (k.clone(), (*v as f64 * scale_factor) as i32))
                .collect(),
        }
    }

    /// 验证资源消耗配置
    pub fn validate(&self) -> GameResult<()> {
        if self.health_cost < 0 {
            return Err(GameError::validation_error(
                "health_cost",
                "生命值消耗不能为负数",
            ));
        }

        if self.mana_cost < 0 {
            return Err(GameError::validation_error(
                "mana_cost",
                "法力值消耗不能为负数",
            ));
        }

        if self.stamina_cost < 0 {
            return Err(GameError::validation_error(
                "stamina_cost",
                "体力消耗不能为负数",
            ));
        }

        for (name, cost) in &self.special_costs {
            if *cost < 0 {
                return Err(GameError::validation_error(
                    "special_cost",
                    &format!("特殊资源{}的消耗不能为负数", name),
                ));
            }
        }
        if self.experience_cost < 0 {
            return Err(GameError::validation_error(
                "experience_cost",
                "经验值消耗不能为负数",
            ));
        }
        for (name, cost) in &self.other_costs {
            if *cost < 0 {
                return Err(GameError::validation_error(
                    "other_cost",
                    &format!("其他资源{}的消耗不能为负数", name),
                ));
            }
        }
        Ok(())
    }
}

/// 扩展的资源消耗（支持自定义资源）
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ExtendedResourceCost {
    /// 生命值消耗
    pub health_cost: Health,
    /// 法力值消耗
    pub mana_cost: Mana,
    /// 体力消耗
    pub stamina_cost: Stamina,
    /// 行动点数消耗
    pub action_points_cost: ActionPoints,
    /// 特殊资源消耗
    pub special_costs: HashMap<String, i32>,
}

impl ExtendedResourceCost {
    pub fn new() -> Self {
        Self {
            health_cost: 0,
            mana_cost: 0,
            stamina_cost: 0,
            action_points_cost: 0,
            special_costs: HashMap::new(),
        }
    }

    pub fn mana(cost: Mana) -> Self {
        Self {
            health_cost: 0,
            mana_cost: cost,
            stamina_cost: 0,
            action_points_cost: 0,
            special_costs: HashMap::new(),
        }
    }

    pub fn health(cost: Health) -> Self {
        Self {
            health_cost: cost,
            mana_cost: 0,
            stamina_cost: 0,
            action_points_cost: 0,
            special_costs: HashMap::new(),
        }
    }

    pub fn stamina(cost: Stamina) -> Self {
        Self {
            health_cost: 0,
            mana_cost: 0,
            stamina_cost: cost,
            action_points_cost: 0,
            special_costs: HashMap::new(),
        }
    }

    pub fn with_special_cost(mut self, resource_name: String, cost: i32) -> Self {
        self.special_costs.insert(resource_name, cost);
        self
    }

    /// 根据等级缩放消耗
    pub fn scaled_by_level(&self, level: Level) -> Self {
        let scale_factor = 1.0 + (level - 1) as f64 * 0.1; // 每级增加10%消耗

        Self {
            health_cost: (self.health_cost as f64 * scale_factor) as Health,
            mana_cost: (self.mana_cost as f64 * scale_factor) as Mana,
            stamina_cost: (self.stamina_cost as f64 * scale_factor) as Stamina,
            action_points_cost: (self.action_points_cost as f64 * scale_factor) as ActionPoints,
            special_costs: self
                .special_costs
                .iter()
                .map(|(k, v)| (k.clone(), (*v as f64 * scale_factor) as i32))
                .collect(),
        }
    }
}

impl Default for ExtendedResourceCost {
    fn default() -> Self {
        Self::new()
    }
}

/// 资源类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ResourceType {
    Health,
    Mana,
    Stamina,
    ActionPoints,
    Custom(String),
}

impl ResourceType {
    /// 获取资源类型的显示名称
    pub fn display_name(&self) -> &str {
        match self {
            ResourceType::Health => "生命值",
            ResourceType::Mana => "法力值",
            ResourceType::Stamina => "体力",
            ResourceType::ActionPoints => "行动点",
            ResourceType::Custom(name) => name,
        }
    }
}

/// 资源状态
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ResourceStatus {
    pub current: i32,
    pub maximum: i32,
    pub regeneration_rate: f32,
}

impl ResourceStatus {
    pub fn new(maximum: i32) -> Self {
        Self {
            current: maximum,
            maximum,
            regeneration_rate: 0.0,
        }
    }

    pub fn from_values(current: i32, maximum: i32) -> Self {
        Self {
            current: current.max(0).min(maximum),
            maximum,
            regeneration_rate: 0.0,
        }
    }

    pub fn percentage(&self) -> f32 {
        if self.maximum <= 0 {
            0.0
        } else {
            (self.current as f32) / (self.maximum as f32)
        }
    }

    pub fn is_full(&self) -> bool {
        self.current >= self.maximum
    }

    pub fn is_empty(&self) -> bool {
        self.current <= 0
    }

    pub fn with_regeneration(mut self, rate: f32) -> Self {
        self.regeneration_rate = rate;
        self
    }

    /// 恢复资源
    pub fn restore(&mut self, amount: i32) {
        self.current = (self.current + amount).min(self.maximum);
    }

    /// 消耗资源
    pub fn consume(&mut self, amount: i32) -> bool {
        if self.current >= amount {
            self.current -= amount;
            true
        } else {
            false
        }
    }

    /// 设置最大值
    pub fn set_maximum(&mut self, new_maximum: i32) {
        self.maximum = new_maximum.max(0);
        self.current = self.current.min(self.maximum);
    }
}
