/// 战斗系统错误类型定义
use std::fmt;

#[derive(Debug, Clone, PartialEq)]
pub enum BattleError {
    /// 尝试通过不可变引用修改状态
    ImmutableModification { operation: String },
    /// 尝试获取不可变引用的可变引用
    MutableBorrowFromImmutable { field: String },
    /// 无效的目标
    InvalidTarget { target_id: u32 },
    /// 技能冷却中
    SkillOnCooldown { skill_id: u32, remaining: f32 },
    /// 法力值不足
    InsufficientMana { required: i32, available: i32 },
    /// 单位已死亡
    UnitDead { unit_id: u32 },
    /// 超出攻击范围
    OutOfRange { distance: f32, max_range: f32 },
    /// 状态限制（眩晕、沉默等）
    StatusRestriction { status: String },
    /// 自定义loot规则不支持克隆
    LootRuleCloneError,
    /// 不支持的操作（新trait设计限制）
    NotSupported { operation: String, reason: String },
    /// 无效操作
    InvalidOperation { operation: String, reason: String },
    /// 未知错误
    UnknownError { message: String },
}

impl fmt::Display for BattleError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            BattleError::ImmutableModification { operation } => {
                write!(f, "尝试通过不可变引用执行修改操作: {}", operation)
            }
            BattleError::MutableBorrowFromImmutable { field } => {
                write!(f, "尝试从不可变引用获取可变借用: {}", field)
            }
            BattleError::InvalidTarget { target_id } => {
                write!(f, "无效的目标: {}", target_id)
            }
            BattleError::SkillOnCooldown { skill_id, remaining } => {
                write!(f, "技能 {} 冷却中，剩余时间: {:.1}秒", skill_id, remaining)
            }
            BattleError::InsufficientMana { required, available } => {
                write!(f, "法力值不足，需要: {}，当前: {}", required, available)
            }
            BattleError::UnitDead { unit_id } => {
                write!(f, "单位 {} 已死亡", unit_id)
            }
            BattleError::OutOfRange { distance, max_range } => {
                write!(f, "超出攻击范围，距离: {:.1}，最大范围: {:.1}", distance, max_range)
            }
            BattleError::StatusRestriction { status } => {
                write!(f, "受状态限制: {}", status)
            }
            BattleError::LootRuleCloneError => {
                write!(f, "自定义loot规则包含闭包，无法克隆")
            }
            BattleError::NotSupported { operation, reason } => {
                write!(f, "不支持的操作 {}: {}", operation, reason)
            }
            BattleError::InvalidOperation { operation, reason } => {
                write!(f, "无效操作 {}: {}", operation, reason)
            }
            BattleError::UnknownError { message } => {
                write!(f, "未知错误: {}", message)
            }
        }
    }
}

impl std::error::Error for BattleError {}

impl From<crate::shared::errors::GameError> for BattleError {
    fn from(error: crate::shared::errors::GameError) -> Self {
        BattleError::UnknownError {
            message: format!("游戏核心错误: {}", error),
        }
    }
}

/// 战斗系统结果类型
pub type BattleResult<T> = Result<T, BattleError>;

/// 创建不可变修改错误
pub fn immutable_modification_error(operation: &str) -> BattleError {
    BattleError::ImmutableModification {
        operation: operation.to_string(),
    }
}

/// 创建可变借用错误
pub fn mutable_borrow_error(field: &str) -> BattleError {
    BattleError::MutableBorrowFromImmutable {
        field: field.to_string(),
    }
}