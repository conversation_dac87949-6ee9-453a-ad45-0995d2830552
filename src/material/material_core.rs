use crate::shared::MaterialGrade;
/// 材料系统核心定义
/// 基于材料系统设计备忘录，实现五行属性和修仙风格的材料体系
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
// ============================================================================
// 核心五行属性
// ============================================================================

/// 五行基础属性
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ElementalAttribute {
    /// 金 - 锐利、坚固、传导
    Metal,
    /// 木 - 生机、柔韧、生长
    Wood,
    /// 水 - 流动、滋润、至柔
    Water,
    /// 火 - 炽热、爆裂、消耗
    Fire,
    /// 土 - 厚重、稳定、承载
    Earth,
}

impl ElementalAttribute {
    /// 获取相生关系
    pub fn generates(&self) -> ElementalAttribute {
        match self {
            ElementalAttribute::Wood => ElementalAttribute::Fire,
            ElementalAttribute::Fire => ElementalAttribute::Earth,
            ElementalAttribute::Earth => ElementalAttribute::Metal,
            ElementalAttribute::Metal => ElementalAttribute::Water,
            ElementalAttribute::Water => ElementalAttribute::Wood,
        }
    }

    /// 获取相克关系
    pub fn overcomes(&self) -> ElementalAttribute {
        match self {
            ElementalAttribute::Metal => ElementalAttribute::Wood,
            ElementalAttribute::Wood => ElementalAttribute::Earth,
            ElementalAttribute::Earth => ElementalAttribute::Water,
            ElementalAttribute::Water => ElementalAttribute::Fire,
            ElementalAttribute::Fire => ElementalAttribute::Metal,
        }
    }

    /// 检查是否相生
    pub fn is_generative_with(&self, other: &ElementalAttribute) -> bool {
        self.generates() == *other || other.generates() == *self
    }

    /// 检查是否相克
    pub fn is_destructive_with(&self, other: &ElementalAttribute) -> bool {
        self.overcomes() == *other || other.overcomes() == *self
    }
}

// ============================================================================
// 衍生属性
// ============================================================================

/// 衍生属性 - 基于五行的特殊属性
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DerivedAttribute {
    /// 冰 - 水的极致状态
    Ice,
    /// 雷 - 天地间的毁灭之力
    Thunder,
    /// 风 - 无形无相的流动力量
    Wind,
    /// 光 - 净化与破邪之力
    Light,
    /// 暗 - 侵蚀与堕落之力
    Dark,
}

impl DerivedAttribute {
    /// 获取对应的基础五行属性
    pub fn base_element(&self) -> Option<ElementalAttribute> {
        match self {
            DerivedAttribute::Ice => Some(ElementalAttribute::Water),
            DerivedAttribute::Thunder => Some(ElementalAttribute::Metal), // 雷与金属传导相关
            DerivedAttribute::Wind => Some(ElementalAttribute::Wood),     // 风与木的轻盈相关
            DerivedAttribute::Light => None,                              // 独立属性
            DerivedAttribute::Dark => None,                               // 独立属性
        }
    }

    /// 获取克制关系
    pub fn opposes(&self) -> Option<DerivedAttribute> {
        match self {
            DerivedAttribute::Light => Some(DerivedAttribute::Dark),
            DerivedAttribute::Dark => Some(DerivedAttribute::Light),
            _ => None,
        }
    }
}

// ============================================================================
// 材料属性
// ============================================================================

/// 材料属性 - 可以是五行基础属性或衍生属性
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MaterialAttribute {
    Elemental(ElementalAttribute),
    Derived(DerivedAttribute),
    /// 混元 - 多属性融合或无属性
    Chaos,
}

impl MaterialAttribute {
    /// 检查与另一个属性的相互作用
    pub fn interaction_with(&self, other: &MaterialAttribute) -> AttributeInteraction {
        match (self, other) {
            // 相同属性
            (a, b) if a == b => AttributeInteraction::Resonance,

            // 五行相生
            (MaterialAttribute::Elemental(a), MaterialAttribute::Elemental(b))
                if a.is_generative_with(b) =>
            {
                AttributeInteraction::Enhancement
            }

            // 五行相克
            (MaterialAttribute::Elemental(a), MaterialAttribute::Elemental(b))
                if a.is_destructive_with(b) =>
            {
                AttributeInteraction::Conflict
            }

            // 衍生属性对立
            (MaterialAttribute::Derived(a), MaterialAttribute::Derived(b))
                if a.opposes() == Some(*b) =>
            {
                AttributeInteraction::Opposition
            }

            // 混元属性调和其他属性
            (MaterialAttribute::Chaos, _) | (_, MaterialAttribute::Chaos) => {
                AttributeInteraction::Harmonization
            }

            // 其他情况为中性
            _ => AttributeInteraction::Neutral,
        }
    }
}

/// 属性相互作用
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AttributeInteraction {
    /// 共鸣 - 相同属性增强效果
    Resonance,
    /// 增强 - 相生关系产生增益
    Enhancement,
    /// 冲突 - 相克关系产生减益
    Conflict,
    /// 对立 - 完全相反的属性
    Opposition,
    /// 调和 - 混元属性的调和作用
    Harmonization,
    /// 中性 - 无特殊作用
    Neutral,
}

// ============================================================================
// 材料来源
// ============================================================================

/// 材料来源 - 定义材料的获取途径
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MaterialSource {
    /// 怪物掉落
    MonsterDrop {
        monster_type: String,
        drop_conditions: Vec<String>,
    },
    /// 矿场采集
    Mining {
        mine_type: String,
        required_tools: Vec<String>,
    },
    /// 环境采集
    Environmental {
        location_type: String,
        time_conditions: Vec<String>,
    },
    /// 炼制合成
    Synthesis {
        recipe_name: String,
        required_materials: Vec<String>,
    },
    /// 任务奖励
    QuestReward { quest_id: String },
    /// 商店购买
    Merchant {
        merchant_type: String,
        currency_type: String,
    },
}

// ============================================================================
// 材料年份
// ============================================================================

/// 材料年份 - 影响材料的效能和特性
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum MaterialAge {
    /// 初生 - 0-10年
    NewGrowth,
    /// 数十年 - 10-100年
    Decades,
    /// 百年 - 100-1000年
    Century,
    /// 千年 - 1000-10000年
    Millennium,
    /// 万年 - 10000-100000年
    TenThousandYears,
    /// 上古 - 100000年以上
    Ancient,
}

impl MaterialAge {
    /// 获取年份对应的效能倍数
    pub fn potency_multiplier(&self) -> f32 {
        match self {
            MaterialAge::NewGrowth => 1.0,
            MaterialAge::Decades => 1.2,
            MaterialAge::Century => 1.5,
            MaterialAge::Millennium => 2.0,
            MaterialAge::TenThousandYears => 3.0,
            MaterialAge::Ancient => 5.0,
        }
    }

    /// 获取年份的文字描述
    pub fn description(&self) -> &'static str {
        match self {
            MaterialAge::NewGrowth => "初生",
            MaterialAge::Decades => "数十年",
            MaterialAge::Century => "百年",
            MaterialAge::Millennium => "千年",
            MaterialAge::TenThousandYears => "万年",
            MaterialAge::Ancient => "上古",
        }
    }
}

// ============================================================================
// 材料形态
// ============================================================================

/// 材料形态 - 描述材料的物理状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MaterialForm {
    /// 固体形态
    Solid {
        hardness: u8, // 1-10的硬度等级
    },
    /// 液体形态
    Liquid {
        viscosity: u8, // 1-10的粘稠度
    },
    /// 气体形态
    Gas {
        density: u8, // 1-10的密度
    },
    /// 结晶形态
    Crystal {
        clarity: u8, // 1-10的纯净度
    },
    /// 粉末形态
    Powder {
        fineness: u8, // 1-10的细腻度
    },
    /// 精华形态
    Essence {
        concentration: u8, // 1-10的浓度
    },
}

// ============================================================================
// 材料核心结构
// ============================================================================

/// 材料 - 游戏中的核心材料定义
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Material {
    /// 材料唯一标识
    pub id: String,
    /// 材料名称
    pub name: String,
    /// 材料描述
    pub description: String,
    /// 材料品阶
    pub grade: MaterialGrade,
    /// 材料年份
    pub age: MaterialAge,
    /// 主要属性
    pub primary_attribute: MaterialAttribute,
    /// 次要属性（可选）
    pub secondary_attributes: Vec<MaterialAttribute>,
    /// 材料形态
    pub form: MaterialForm,
    /// 材料来源
    pub sources: Vec<MaterialSource>,
    /// 基础价值
    pub base_value: u32,
    /// 堆叠上限
    pub stack_limit: u32,
    /// 特殊属性标签
    pub special_properties: Vec<String>,
    /// 用途分类
    pub usage_categories: Vec<MaterialUsage>,
    /// 保存条件
    pub storage_requirements: Vec<StorageRequirement>,
}

/// 材料用途
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MaterialUsage {
    /// 炼丹
    Alchemy,
    /// 炼器
    Crafting,
    /// 布阵
    Formation,
    /// 辅助修炼
    Cultivation,
    /// 灵宠养育
    PetCare,
    /// 洞府建设
    Construction,
    /// 货币交易
    Currency,
    /// 任务道具
    Quest,
}

/// 存储要求
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum StorageRequirement {
    /// 需要特殊容器
    SpecialContainer { container_type: String },
    /// 环境要求
    EnvironmentalCondition { condition: String, value: String },
    /// 时间限制
    TimeLimit { duration_hours: u32 },
    /// 隔离要求
    Isolation { reason: String },
}

impl Material {
    /// 创建新材料
    pub fn new(
        id: String,
        name: String,
        description: String,
        grade: MaterialGrade,
        primary_attribute: MaterialAttribute,
    ) -> Self {
        Self {
            id,
            name,
            description,
            grade,
            age: MaterialAge::NewGrowth,
            primary_attribute,
            secondary_attributes: Vec::new(),
            form: MaterialForm::Solid { hardness: 5 },
            sources: Vec::new(),
            base_value: 10,
            stack_limit: 99,
            special_properties: Vec::new(),
            usage_categories: Vec::new(),
            storage_requirements: Vec::new(),
        }
    }

    /// 获取材料的实际价值（考虑年份等因素）
    pub fn actual_value(&self) -> u32 {
        let age_multiplier = self.age.potency_multiplier();
        let grade_multiplier = match self.grade {
            MaterialGrade::Mortal => 1.0,
            MaterialGrade::Spiritual => 2.0,
            MaterialGrade::Immortal => 4.0,
            MaterialGrade::Divine => 8.0,
            MaterialGrade::Sacred => 16.0,
            MaterialGrade::Chaos => 32.0,
        };

        (self.base_value as f32 * age_multiplier * grade_multiplier) as u32
    }

    /// 检查是否可以与其他材料组合
    pub fn can_combine_with(&self, other: &Material) -> bool {
        let interaction = self
            .primary_attribute
            .interaction_with(&other.primary_attribute);
        !matches!(
            interaction,
            AttributeInteraction::Opposition | AttributeInteraction::Conflict
        )
    }

    /// 获取与其他材料的组合效果
    pub fn combination_effect(&self, other: &Material) -> CombinationEffect {
        let interaction = self
            .primary_attribute
            .interaction_with(&other.primary_attribute);

        match interaction {
            AttributeInteraction::Resonance => CombinationEffect::StrongEnhancement,
            AttributeInteraction::Enhancement => CombinationEffect::Enhancement,
            AttributeInteraction::Harmonization => CombinationEffect::Stabilization,
            AttributeInteraction::Neutral => CombinationEffect::Neutral,
            AttributeInteraction::Conflict => CombinationEffect::Reduction,
            AttributeInteraction::Opposition => CombinationEffect::Cancellation,
        }
    }
}

/// 材料组合效果
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CombinationEffect {
    /// 强化 - 共鸣产生的强烈增效
    StrongEnhancement,
    /// 增强 - 相生产生的增效
    Enhancement,
    /// 稳定 - 混元属性的调和作用
    Stabilization,
    /// 中性 - 无特殊效果
    Neutral,
    /// 减弱 - 相克产生的减效
    Reduction,
    /// 抵消 - 对立属性的完全抵消
    Cancellation,
}

// ============================================================================
// 材料库
// ============================================================================

/// 材料库 - 管理所有材料定义
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct MaterialLibrary {
    materials: HashMap<String, Material>,
    by_attribute: HashMap<MaterialAttribute, Vec<String>>,
    by_grade: HashMap<MaterialGrade, Vec<String>>,
    by_usage: HashMap<MaterialUsage, Vec<String>>,
}

impl MaterialLibrary {
    /// 创建新的材料库
    pub fn new() -> Self {
        Self::default()
    }

    /// 添加材料
    pub fn add_material(&mut self, material: Material) {
        let id = material.id.clone();

        // 更新属性索引
        self.by_attribute
            .entry(material.primary_attribute)
            .or_insert_with(Vec::new)
            .push(id.clone());

        // 更新品阶索引
        self.by_grade
            .entry(material.grade)
            .or_insert_with(Vec::new)
            .push(id.clone());

        // 更新用途索引
        for usage in &material.usage_categories {
            self.by_usage
                .entry(*usage)
                .or_insert_with(Vec::new)
                .push(id.clone());
        }

        // 添加材料
        self.materials.insert(id, material);
    }

    /// 获取材料
    pub fn get_material(&self, id: &str) -> Option<&Material> {
        self.materials.get(id)
    }

    /// 根据属性获取材料列表
    pub fn get_by_attribute(&self, attribute: MaterialAttribute) -> Vec<&Material> {
        self.by_attribute
            .get(&attribute)
            .map(|ids| ids.iter().filter_map(|id| self.materials.get(id)).collect())
            .unwrap_or_default()
    }

    /// 根据品阶获取材料列表
    pub fn get_by_grade(&self, grade: MaterialGrade) -> Vec<&Material> {
        self.by_grade
            .get(&grade)
            .map(|ids| ids.iter().filter_map(|id| self.materials.get(id)).collect())
            .unwrap_or_default()
    }

    /// 根据用途获取材料列表
    pub fn get_by_usage(&self, usage: MaterialUsage) -> Vec<&Material> {
        self.by_usage
            .get(&usage)
            .map(|ids| ids.iter().filter_map(|id| self.materials.get(id)).collect())
            .unwrap_or_default()
    }

    /// 获取所有材料
    pub fn get_all_materials(&self) -> impl Iterator<Item = &Material> {
        self.materials.values()
    }
}

// ============================================================================
// 具体材料类型
// ============================================================================

/// 预定义的材料类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MaterialType {
    // 草药类
    CommonHerb,
    SpiritualFlower,
    HealingGrass,

    // 木材类
    AncientWood,

    // 矿物类
    IronOre,
    SilverOre,
    CopperOre,

    // 液体类
    CrystalWater,

    // 皮毛类
    BeastHide,

    // 水晶类
    RareCrystal,

    // 元素精华类
    FireEssence,
    EarthEssence,
}

impl MaterialType {
    /// 获取材料类型的字符串ID
    pub fn id(&self) -> &'static str {
        match self {
            MaterialType::CommonHerb => "common_herb",
            MaterialType::SpiritualFlower => "spiritual_flower",
            MaterialType::HealingGrass => "healing_grass",
            MaterialType::AncientWood => "ancient_wood",
            MaterialType::IronOre => "iron_ore",
            MaterialType::SilverOre => "silver_ore",
            MaterialType::CopperOre => "copper_ore",
            MaterialType::CrystalWater => "crystal_water",
            MaterialType::BeastHide => "beast_hide",
            MaterialType::RareCrystal => "rare_crystal",
            MaterialType::FireEssence => "fire_essence",
            MaterialType::EarthEssence => "earth_essence",
        }
    }

    /// 获取材料类型的中文名称
    pub fn chinese_name(&self) -> &'static str {
        match self {
            MaterialType::CommonHerb => "普通草药",
            MaterialType::SpiritualFlower => "灵芝花",
            MaterialType::HealingGrass => "治疗草",
            MaterialType::AncientWood => "古木",
            MaterialType::IronOre => "铁矿石",
            MaterialType::SilverOre => "银矿石",
            MaterialType::CopperOre => "铜矿石",
            MaterialType::CrystalWater => "水晶泉水",
            MaterialType::BeastHide => "野兽皮毛",
            MaterialType::RareCrystal => "稀有水晶",
            MaterialType::FireEssence => "火元素精华",
            MaterialType::EarthEssence => "土元素精华",
        }
    }

    /// 获取材料的默认属性
    pub fn default_attribute(&self) -> MaterialAttribute {
        match self {
            MaterialType::CommonHerb
            | MaterialType::SpiritualFlower
            | MaterialType::HealingGrass
            | MaterialType::AncientWood => MaterialAttribute::Elemental(ElementalAttribute::Wood),
            MaterialType::IronOre | MaterialType::SilverOre | MaterialType::CopperOre => {
                MaterialAttribute::Elemental(ElementalAttribute::Metal)
            }
            MaterialType::CrystalWater => MaterialAttribute::Elemental(ElementalAttribute::Water),
            MaterialType::BeastHide => MaterialAttribute::Elemental(ElementalAttribute::Earth),
            MaterialType::RareCrystal => MaterialAttribute::Chaos,
            MaterialType::FireEssence => MaterialAttribute::Elemental(ElementalAttribute::Fire),
            MaterialType::EarthEssence => MaterialAttribute::Elemental(ElementalAttribute::Earth),
        }
    }

    /// 获取材料的默认品阶
    pub fn default_grade(&self) -> MaterialGrade {
        match self {
            MaterialType::CommonHerb
            | MaterialType::HealingGrass
            | MaterialType::IronOre
            | MaterialType::CopperOre
            | MaterialType::BeastHide => MaterialGrade::Mortal,
            MaterialType::SpiritualFlower
            | MaterialType::AncientWood
            | MaterialType::SilverOre
            | MaterialType::CrystalWater
            | MaterialType::FireEssence
            | MaterialType::EarthEssence => MaterialGrade::Spiritual,
            MaterialType::RareCrystal => MaterialGrade::Immortal,
        }
    }

    /// 根据字符串ID获取材料类型
    pub fn from_id(id: &str) -> Option<Self> {
        match id {
            "common_herb" => Some(MaterialType::CommonHerb),
            "spiritual_flower" => Some(MaterialType::SpiritualFlower),
            "healing_grass" => Some(MaterialType::HealingGrass),
            "ancient_wood" => Some(MaterialType::AncientWood),
            "iron_ore" => Some(MaterialType::IronOre),
            "silver_ore" => Some(MaterialType::SilverOre),
            "copper_ore" => Some(MaterialType::CopperOre),
            "crystal_water" => Some(MaterialType::CrystalWater),
            "beast_hide" => Some(MaterialType::BeastHide),
            "rare_crystal" => Some(MaterialType::RareCrystal),
            "fire_essence" => Some(MaterialType::FireEssence),
            "earth_essence" => Some(MaterialType::EarthEssence),
            _ => None,
        }
    }

    /// 获取所有材料类型
    pub fn all() -> Vec<Self> {
        vec![
            MaterialType::CommonHerb,
            MaterialType::SpiritualFlower,
            MaterialType::HealingGrass,
            MaterialType::AncientWood,
            MaterialType::IronOre,
            MaterialType::SilverOre,
            MaterialType::CopperOre,
            MaterialType::CrystalWater,
            MaterialType::BeastHide,
            MaterialType::RareCrystal,
            MaterialType::FireEssence,
            MaterialType::EarthEssence,
        ]
    }
}
