/// 战斗单位通用 trait，供角色、怪物、召唤物等实现
/// 基础属性 trait
use crate::shared::types::*;
use crate::battle_system::battle_errors::{BattleResult, immutable_modification_error, mutable_borrow_error};
pub trait BasicAttributes {
    /// 获取单位唯一标识符
    fn get_id(&self) -> ID;
    
    /// 获取单位名称
    fn get_name(&self) -> &str;
    
    /// 获取单位等级
    fn get_level(&self) -> Level;
}

/// 位置相关 trait
pub trait Positionable {
    /// 获取单位当前位置坐标
    fn get_position(&self) -> (f32, f32);
    
    /// 设置单位位置坐标
    fn set_position(&mut self, pos: (f32, f32)) -> BattleResult<()>;
}

/// 生命值和法力值相关 trait
pub trait Vitality {
    /// 获取当前生命值
    fn get_hp(&self) -> Health;
    
    /// 设置生命值
    fn set_hp(&mut self, value: Health) -> BattleResult<()>;
    
    /// 获取当前法力值
    fn get_mana(&self) -> Mana;
    
    /// 设置法力值
    fn set_mana(&mut self, value: Mana) -> BattleResult<()>;
    
    /// 获取最大生命值
    fn get_max_hp(&self) -> Health;
    
    /// 获取最大法力值
    fn get_max_mana(&self) -> Mana;
}

/// 经验值相关 trait
pub trait Experience {
    /// 获取当前经验值
    fn get_exp(&self) -> Exp;
    
    /// 增加经验值
    fn add_exp(&mut self, amount: Exp) -> BattleResult<()>;
}

use crate::shared::types::SkillId;

/// 技能和buff相关 trait
pub trait Skillable {
    /// 获取技能冷却时间映射的可变引用
    fn get_skill_cooldowns_mut(&mut self) -> BattleResult<&mut std::collections::HashMap<SkillId, CooldownTime>>;
    
    /// 获取技能冷却时间映射的只读引用
    fn get_skill_cooldowns(&self) -> &std::collections::HashMap<SkillId, CooldownTime>;
    
    /// 获取buff列表的可变引用
    fn get_buffs_mut(&mut self) -> BattleResult<&mut Vec<crate::skill::buff::Buff>>;
    
    /// 获取buff列表的只读引用
    fn get_buffs(&self) -> &Vec<crate::skill::buff::Buff>;
    
    /// 获取技能列表
    fn get_skills(&self) -> Vec<ID>;
}

/// 战斗属性相关 trait
pub trait CombatAttributes {
    /// 获取攻击力
    fn get_attack(&self) -> Attack;
    
    /// 获取防御力
    fn get_defense(&self) -> Defense;
    
    /// 获取移动速度
    fn get_move_speed(&self) -> Speed;
    
    /// 获取攻击范围
    fn get_attack_range(&self) -> Range;
    
    /// 获取行动速度（用于动作条系统）
    fn get_speed(&self) -> Speed {
        self.get_move_speed() // 默认使用移动速度
    }
}

/// 状态效果相关 trait
pub trait StatusEffect {
    /// 获取当前状态(如眩晕、沉默等)
    fn get_status(&self) -> Vec<String>;
    
    /// 添加状态效果
    fn add_status(&mut self, status: String) -> BattleResult<()>;
    
    /// 移除状态效果
    fn remove_status(&mut self, status: &str) -> BattleResult<()>;
}

/// 战斗状态检查 trait
pub trait BattleState {
    /// 检查是否存活
    fn is_alive(&self) -> bool;
    
    /// 检查是否可移动
    fn can_move(&self) -> bool;
    
    /// 检查是否可施法
    fn can_cast(&self) -> bool;
    
    /// 检查是否可攻击
    fn can_attack(&self) -> bool;
    
    /// 受到伤害
    fn take_damage(&mut self, damage: i32) -> BattleResult<()>;
    
    /// 恢复生命值
    fn heal(&mut self, amount: i32) -> BattleResult<()>;
}

/// 战斗单位通用 trait，组合所有子 trait
pub trait BattleUnit: 
    BasicAttributes + 
    Positionable + 
    Vitality + 
    Experience + 
    Skillable + 
    CombatAttributes + 
    StatusEffect + 
    BattleState {}

// ============================================================================
// Blanket implementations for reference types
// 为引用类型提供 blanket implementation，解决借用检查器问题
// ============================================================================

// 为 &T 实现只读 traits
impl<T: BasicAttributes> BasicAttributes for &T {
    fn get_id(&self) -> ID { (*self).get_id() }
    fn get_name(&self) -> &str { (*self).get_name() }
    fn get_level(&self) -> Level { (*self).get_level() }
}

impl<T: Positionable> Positionable for &T {
    fn get_position(&self) -> (f32, f32) { (*self).get_position() }
    fn set_position(&mut self, _pos: (f32, f32)) -> BattleResult<()> { 
        Err(immutable_modification_error("set_position"))
    }
}

impl<T: Vitality> Vitality for &T {
    fn get_hp(&self) -> Health { (*self).get_hp() }
    fn set_hp(&mut self, _value: Health) -> BattleResult<()> { 
        Err(immutable_modification_error("set_hp"))
    }
    fn get_mana(&self) -> Mana { (*self).get_mana() }
    fn set_mana(&mut self, _value: Mana) -> BattleResult<()> { 
        Err(immutable_modification_error("set_mana"))
    }
    fn get_max_hp(&self) -> Health { (*self).get_max_hp() }
    fn get_max_mana(&self) -> Mana { (*self).get_max_mana() }
}

impl<T: Experience> Experience for &T {
    fn get_exp(&self) -> Exp { (*self).get_exp() }
    fn add_exp(&mut self, _amount: Exp) -> BattleResult<()> { 
        Err(immutable_modification_error("add_exp"))
    }
}

impl<T: Skillable> Skillable for &T {
    fn get_skill_cooldowns_mut(&mut self) -> BattleResult<&mut std::collections::HashMap<SkillId, CooldownTime>> {
        Err(mutable_borrow_error("skill_cooldowns"))
    }
    fn get_skill_cooldowns(&self) -> &std::collections::HashMap<SkillId, CooldownTime> {
        (*self).get_skill_cooldowns()
    }
    fn get_buffs_mut(&mut self) -> BattleResult<&mut Vec<crate::skill::buff::Buff>> {
        Err(mutable_borrow_error("buffs"))
    }
    fn get_buffs(&self) -> &Vec<crate::skill::buff::Buff> {
        (*self).get_buffs()
    }
    fn get_skills(&self) -> Vec<ID> { (*self).get_skills() }
}

impl<T: CombatAttributes> CombatAttributes for &T {
    fn get_attack(&self) -> Attack { (*self).get_attack() }
    fn get_defense(&self) -> Defense { (*self).get_defense() }
    fn get_move_speed(&self) -> Speed { (*self).get_move_speed() }
    fn get_attack_range(&self) -> Range { (*self).get_attack_range() }
    fn get_speed(&self) -> Speed { (*self).get_speed() }
}

impl<T: StatusEffect> StatusEffect for &T {
    fn get_status(&self) -> Vec<String> { (*self).get_status() }
    fn add_status(&mut self, _status: String) -> BattleResult<()> { 
        Err(immutable_modification_error("add_status"))
    }
    fn remove_status(&mut self, _status: &str) -> BattleResult<()> { 
        Err(immutable_modification_error("remove_status"))
    }
}

impl<T: BattleState> BattleState for &T {
    fn is_alive(&self) -> bool { (*self).is_alive() }
    fn can_move(&self) -> bool { (*self).can_move() }
    fn can_cast(&self) -> bool { (*self).can_cast() }
    fn can_attack(&self) -> bool { (*self).can_attack() }
    fn take_damage(&mut self, _damage: i32) -> BattleResult<()> { 
        Err(immutable_modification_error("take_damage"))
    }
    fn heal(&mut self, _amount: i32) -> BattleResult<()> { 
        Err(immutable_modification_error("heal"))
    }
}

// 为 &mut T 实现所有 traits
impl<T: BasicAttributes> BasicAttributes for &mut T {
    fn get_id(&self) -> ID { (**self).get_id() }
    fn get_name(&self) -> &str { (**self).get_name() }
    fn get_level(&self) -> Level { (**self).get_level() }
}

impl<T: Positionable> Positionable for &mut T {
    fn get_position(&self) -> (f32, f32) { (**self).get_position() }
    fn set_position(&mut self, pos: (f32, f32)) -> BattleResult<()> { (**self).set_position(pos) }
}

impl<T: Vitality> Vitality for &mut T {
    fn get_hp(&self) -> Health { (**self).get_hp() }
    fn set_hp(&mut self, value: Health) -> BattleResult<()> { (**self).set_hp(value) }
    fn get_mana(&self) -> Mana { (**self).get_mana() }
    fn set_mana(&mut self, value: Mana) -> BattleResult<()> { (**self).set_mana(value) }
    fn get_max_hp(&self) -> Health { (**self).get_max_hp() }
    fn get_max_mana(&self) -> Mana { (**self).get_max_mana() }
}

impl<T: Experience> Experience for &mut T {
    fn get_exp(&self) -> Exp { (**self).get_exp() }
    fn add_exp(&mut self, amount: Exp) -> BattleResult<()> { (**self).add_exp(amount) }
}

impl<T: Skillable> Skillable for &mut T {
    fn get_skill_cooldowns_mut(&mut self) -> BattleResult<&mut std::collections::HashMap<SkillId, CooldownTime>> {
        (**self).get_skill_cooldowns_mut()
    }
    fn get_skill_cooldowns(&self) -> &std::collections::HashMap<SkillId, CooldownTime> {
        (**self).get_skill_cooldowns()
    }
    fn get_buffs_mut(&mut self) -> BattleResult<&mut Vec<crate::skill::buff::Buff>> {
        (**self).get_buffs_mut()
    }
    fn get_buffs(&self) -> &Vec<crate::skill::buff::Buff> {
        (**self).get_buffs()
    }
    fn get_skills(&self) -> Vec<ID> { (**self).get_skills() }
}

impl<T: CombatAttributes> CombatAttributes for &mut T {
    fn get_attack(&self) -> Attack { (**self).get_attack() }
    fn get_defense(&self) -> Defense { (**self).get_defense() }
    fn get_move_speed(&self) -> Speed { (**self).get_move_speed() }
    fn get_attack_range(&self) -> Range { (**self).get_attack_range() }
    fn get_speed(&self) -> Speed { (**self).get_speed() }
}

impl<T: StatusEffect> StatusEffect for &mut T {
    fn get_status(&self) -> Vec<String> { (**self).get_status() }
    fn add_status(&mut self, status: String) -> BattleResult<()> { (**self).add_status(status) }
    fn remove_status(&mut self, status: &str) -> BattleResult<()> { (**self).remove_status(status) }
}

impl<T: BattleState> BattleState for &mut T {
    fn is_alive(&self) -> bool { (**self).is_alive() }
    fn can_move(&self) -> bool { (**self).can_move() }
    fn can_cast(&self) -> bool { (**self).can_cast() }
    fn can_attack(&self) -> bool { (**self).can_attack() }
    fn take_damage(&mut self, damage: i32) -> BattleResult<()> { (**self).take_damage(damage) }
    fn heal(&mut self, amount: i32) -> BattleResult<()> { (**self).heal(amount) }
}

// 为引用类型实现 BattleUnit
impl<T: BattleUnit> BattleUnit for &T {}
impl<T: BattleUnit> BattleUnit for &mut T {}
