/// 战斗单位模块
/// 
/// 实现完整的战斗单位系统，支持复杂的RPG游戏机制
/// 遵循DDD原则和SOLID设计原则，按照最小单元原则组织代码

// 类型定义模块
pub mod types;

// trait定义模块
pub mod traits;

// 具体实现模块
pub mod implementations;

// 导出类型
pub use types::*;

// 导出所有trait
pub use traits::*;

// 导出实现
pub use implementations::*;

/// 战斗单位模块版本
pub const BATTLE_UNIT_VERSION: &str = "1.0.0";

/// 战斗单位模块说明
pub const BATTLE_UNIT_DESCRIPTION: &str = "完整的战斗单位系统，支持复杂的RPG游戏机制，按照最小单元原则组织，复用现有模块";