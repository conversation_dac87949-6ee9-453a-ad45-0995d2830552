
use crate::battle_system::simplified_battle_traits::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>F<PERSON><PERSON>, SkillUser, CombatCapable};
use crate::shared::types::{Health, Mana, CooldownTime, SkillId};
use crate::battle_system::battle_event::{BattleE<PERSON>, BattleLogger, DamageType};
use crate::battle_system::damage_calculator::DamageCalculator;
use crate::battle_system::adapters::{SkillAdapter, SimpleSkillEffect, create_compatible_buff};
use crate::skill::Skill;

/// 技能释放系统
#[derive(Debug)]
pub struct SkillCaster {
    /// 伤害计算器
    damage_calculator: DamageCalculator,
}

impl Default for SkillCaster {
    fn default() -> Self {
        Self {
            damage_calculator: DamageCalculator::new(),
        }
    }
}

impl SkillCaster {
    /// 创建新的技能释放器
    pub fn new() -> Self {
        Self::default()
    }

    /// 检查技能是否可以释放
    pub fn can_cast_skill<T: SkillUser + LifeForce>(
        &self,
        caster: &T,
        skill: &Skill,
    ) -> SkillCastResult {
        // 检查法力值
        if caster.current_mana() < skill.mana_cost {
            return SkillCastResult::InsufficientMana {
                required: skill.mana_cost,
                current: caster.current_mana(),
            };
        }

        // 检查冷却时间
        if !caster.is_skill_ready(&SkillId(skill.id)) {
            return SkillCastResult::OnCooldown(caster.skill_cooldown(&SkillId(skill.id)));
        }

        SkillCastResult::CanCast
    }

    /// 释放技能 - 此方法现在只负责应用效果
    pub fn apply_skill_effects<T: BattleEntity + CombatCapable + SkillUser, U: BattleEntity + LifeForce + CombatCapable + SkillUser>(
        &mut self,
        caster: &T,
        targets: &mut [&mut U],
        skill: &Skill,
        logger: &mut BattleLogger,
    ) {
        let skill_adapter = SkillAdapter::new(skill);
        let effects = skill_adapter.get_effects();
        for effect in effects {
            self.apply_simple_skill_effect(caster, targets, &effect, logger);
        }
    }

    /// 应用简化的技能效果
    fn apply_simple_skill_effect<T: BattleEntity + CombatCapable + SkillUser, U: BattleEntity + LifeForce + CombatCapable + SkillUser>(
        &mut self,
        caster: &T,
        targets: &mut [&mut U],
        effect: &SimpleSkillEffect,
        logger: &mut BattleLogger,
    ) {
        match effect.effect_type.as_str() {
            "damage" => {
                self.apply_simple_damage_effect(caster, targets, effect, logger);
            }
            "heal" => {
                self.apply_simple_heal_effect(caster, targets, effect, logger);
            }
            "buff" | "debuff" => {
                self.apply_simple_buff_effect(caster, targets, effect, logger);
            }
            _ => {}
        }
    }

    /// 应用简化的伤害效果
    fn apply_simple_damage_effect<T: BattleEntity + CombatCapable + SkillUser, U: BattleEntity + LifeForce + CombatCapable + SkillUser>(
        &mut self,
        caster: &T,
        targets: &mut [&mut U],
        effect: &SimpleSkillEffect,
        logger: &mut BattleLogger,
    ) {
        let damage_type = match effect.damage_type.as_deref() {
            Some("physical") => DamageType::Physical,
            Some("magical") => DamageType::Magical,
            _ => DamageType::Physical,
        };

        for target in targets.iter_mut() {
            // 注意：calculate_skill_damage期望 &U，但我们有 &mut &mut U。
            // 通过解引用，我们可以得到 &mut U，它会自动强制转换为 &U。
            let damage_result = self.damage_calculator.calculate_skill_damage(
                caster,
                &**target, // 解引用以匹配trait bound
                effect.value as Health,
                damage_type.clone(),
            );
            
            // take_damage 期望 Health (i32)，而不是 DamageResult
            let _ = target.take_damage(damage_result.final_damage);
            let remaining_hp = target.current_health();

            logger.log_event(BattleEvent::Damage {
                source: caster.display_name().to_string(),
                target: (**target).display_name().to_string(),
                damage_type: damage_type.clone(), // DamageResult没有此字段，我们从之前创建的类型中获取
                raw_damage: damage_result.final_damage, // 简化：暂时将raw和final视为一样
                final_damage: damage_result.final_damage,
                remaining_hp,
            });

            if !target.is_alive() {
                logger.log_event(BattleEvent::Death {
                    unit: (**target).display_name().to_string(),
                    killer: Some(caster.display_name().to_string()),
                });
            }
        }
    }

    /// 应用简化的治疗效果
    fn apply_simple_heal_effect<T: BattleEntity, U: BattleEntity + LifeForce>(
        &mut self,
        _caster: &T,
        targets: &mut [&mut U],
        effect: &SimpleSkillEffect,
        logger: &mut BattleLogger,
    ) {
        for target in targets.iter_mut() {
            let heal_amount = effect.value as Health;

            let _ = target.heal(heal_amount);
            let remaining_hp = target.current_health();

            logger.log_event(BattleEvent::Heal {
                source: "Skill".to_string(), // Simplified source
                target: (**target).display_name().to_string(),
                heal_amount,
                remaining_hp,
            });
        }
    }

    /// 应用简化的Buff效果 (包括Debuff)
    fn apply_simple_buff_effect<T: BattleEntity, U: BattleEntity + SkillUser>(
        &mut self,
        _caster: &T,
        targets: &mut [&mut U],
        effect: &SimpleSkillEffect,
        logger: &mut BattleLogger,
    ) {
        let buff_type = if effect.effect_type == "buff" {
            crate::skill::buff::BuffType::Buff
        } else {
            crate::skill::buff::BuffType::Debuff
        };

        let buff = create_compatible_buff(
            effect.id,
            effect.name.clone(),
            buff_type,
            effect.duration.unwrap_or(3) as f32,
        );

        for target in targets.iter_mut() {
            if let Err(e) = target.add_buff(buff.clone()) {
                logger.log_event(BattleEvent::Message(format!(
                    "Failed to apply buff {} to {}: {:?}",
                    buff.name,
                    (**target).display_name(),
                    e
                )));
            } else {
                 logger.log_event(BattleEvent::Message(format!(
                    "Applied buff {} to {}",
                    buff.name,
                    (**target).display_name()
                )));
            }
        }
    }
}

/// 技能释放结果
#[derive(Debug, Clone)]
pub enum SkillCastResult {
    /// 可以释放
    CanCast,
    /// 无法施法
    CannotCast(String),
    /// 法力值不足
    InsufficientMana {
        required: Mana,
        current: Mana,
    },
    /// 技能冷却中
    OnCooldown(CooldownTime),
}

impl SkillCastResult {
    /// 检查是否可以释放技能
    pub fn is_success(&self) -> bool {
        matches!(self, SkillCastResult::CanCast)
    }
    
    /// 获取错误信息
    pub fn error_message(&self) -> Option<String> {
        match self {
            SkillCastResult::CanCast => None,
            SkillCastResult::CannotCast(msg) => Some(msg.clone()),
            SkillCastResult::InsufficientMana { required, current } => {
                Some(format!("法力值不足：需要 {} 当前 {}", required, current))
            }
            SkillCastResult::OnCooldown(time) => {
                Some(format!("技能冷却中，剩余 {:.1} 回合", time))
            }
        }
    }
}
