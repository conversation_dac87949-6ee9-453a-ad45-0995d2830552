/// 空间相关trait实现
/// 
/// 实现SpatialEntity trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;

use crate::battle_unit::traits::SpatialEntity;

impl SpatialEntity for BattleUnitAdapter {
    fn position(&self) -> Position {
        self.position
    }
    
    fn set_position(&mut self, position: Position) -> GameResult<()> {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs_f64();
        
        self.movement_history.push((self.position, current_time));
        self.position = position;
        
        // 保持历史记录在合理范围
        if self.movement_history.len() > 100 {
            self.movement_history.remove(0);
        }
        
        Ok(())
    }
    
    fn facing_direction(&self) -> Direction {
        self.facing_direction
    }
    
    fn set_facing_direction(&mut self, direction: Direction) -> GameResult<()> {
        self.facing_direction = direction;
        Ok(())
    }
    
    fn occupied_space(&self) -> SpaceSize {
        SpaceSize::Medium // 角色默认中等体型
    }
    
    fn is_in_area(&self, area: &Area) -> bool {
        area.contains_point(self.position)
    }
    
    fn distance_to<T: SpatialEntity>(&self, target: &T) -> f32 {
        let target_pos = target.position();
        let dx = self.position.x - target_pos.x;
        let dy = self.position.y - target_pos.y;
        (dx * dx + dy * dy).sqrt()
    }
    
    fn can_see<T: SpatialEntity>(&self, target: &T) -> bool {
        let distance = self.distance_to(target);
        distance <= 20.0 // 基础视野范围20单位
    }
}