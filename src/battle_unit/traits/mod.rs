/// 战斗单位trait模块
/// 
/// 按照单一职责原则组织的trait定义

// 基础trait
pub mod identity;
pub mod spatial; 
pub mod temporal;

// 生存系统trait
pub mod health;
pub mod mana;
pub mod stamina;

// 属性系统trait
pub mod attributes;
pub mod derived_attributes;
pub mod resistance;

// 行动系统trait
pub mod action;
pub mod movement;
pub mod attack;

// 技能系统trait
pub mod skill_learning;
pub mod skill_execution;

// 状态系统trait
pub mod status_effects;
pub mod buff_manager;

// 装备系统trait
pub mod equipment;

// 组合trait
pub mod composite;

// 重导出所有trait
pub use identity::*;
pub use spatial::*;
pub use temporal::*;
pub use health::*;
pub use mana::*;
pub use stamina::*;
pub use attributes::*;
pub use derived_attributes::*;
pub use resistance::*;
pub use action::*;
pub use movement::*;
pub use attack::*;
pub use skill_learning::*;
pub use skill_execution::*;
pub use status_effects::*;
pub use buff_manager::*;
pub use equipment::*;
pub use composite::*;