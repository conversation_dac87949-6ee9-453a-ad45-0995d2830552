//! # 统一结果类型定义
//!
//! 此模块定义了游戏系统中各种操作的统一结果类型和记录类型，
//! 合并了原有两个模块中的所有功能、属性和方法

use super::{combat::*, identifiers::*, primitives::*, resources::ResourceCost};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime};

// ============================================================================
// 记录类型定义 - 合并两个定义的所有功能
// ============================================================================

/// 伤害记录 - 合并了 shared/types/mod.rs 和 battle_unit/types/result_types.rs 的所有功能
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DamageRecord {
    // 来自 shared/types/mod.rs 的属性
    pub damage: Health,
    pub source: Option<BattleUnitId>,
    pub timestamp: SystemTime,
    pub damage_type: DamageType,
    pub is_critical: bool,
    pub skill_id: Option<String>,
    pub unix_timestamp: f64,
}

impl DamageRecord {
    /// 创建新的伤害记录 - 合并两个构造函数的功能
    pub fn new(damage: i32, damage_type: DamageType) -> Self {
        let now = SystemTime::now();
        let unix_timestamp = now
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f64();

        Self {
            damage,
            damage_type,
            source: None,
            timestamp: now,
            unix_timestamp,
            is_critical: false,
            skill_id: None,
        }
    }

    /// 从伤害值和字符串类型创建（兼容旧接口）
    pub fn from_damage_and_string(
        damage: Health,
        damage_type: String,
        source: Option<BattleUnitId>,
    ) -> Self {
        let now = SystemTime::now();
        let unix_timestamp = now
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f64();

        // 尝试将字符串转换为 DamageType，失败则使用 Physical
        let parsed_damage_type = match damage_type.as_str() {
            "Physical" => DamageType::Physical,
            "Fire" => DamageType::Fire,
            "Ice" => DamageType::Ice,
            "Lightning" => DamageType::Lightning,
            "Poison" => DamageType::Poison,
            "Holy" => DamageType::Holy,
            "Dark" => DamageType::Dark,
            "Magic" => DamageType::Magic,
            _ => DamageType::Physical,
        };

        Self {
            damage,
            damage_type: parsed_damage_type,
            source,
            timestamp: now,
            unix_timestamp,
            is_critical: false,
            skill_id: None,
        }
    }

    /// 设置伤害来源
    pub fn with_source(mut self, source: BattleUnitId) -> Self {
        self.source = Some(source);
        self
    }

    /// 设置暴击状态
    pub fn with_critical(mut self) -> Self {
        self.is_critical = true;
        self
    }

    /// 设置技能ID
    pub fn with_skill(mut self, skill_id: String) -> Self {
        self.skill_id = Some(skill_id);
        self
    }

    /// 获取Unix时间戳
    pub fn get_unix_timestamp(&self) -> f64 {
        self.unix_timestamp
    }

    /// 获取统一的伤害值
    pub fn get_damage(&self) -> Health {
        // 优先使用 amount，如果为0则使用 damage
        self.damage
    }

    /// 获取统一的来源ID
    pub fn get_source(&self) -> Option<BattleUnitId> {
        self.source
    }
}

/// 治疗记录 - 合并了两个定义的所有功能
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct HealRecord {
    // 来自 shared/types/mod.rs 的属性
    pub amount: Health,
    pub source: Option<BattleUnitId>,
    pub timestamp: SystemTime,
    pub actual_heal: Health,
    pub is_critical: bool,
    pub skill_id: Option<String>,
    pub unix_timestamp: f64,
}

impl HealRecord {
    /// 创建新的治疗记录
    pub fn new(heal_amount: Health, actual_heal: Health) -> Self {
        let now = SystemTime::now();
        let unix_timestamp = now
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f64();

        Self {
            amount: heal_amount, // 统一 amount 和 heal_amount
            actual_heal,
            source: None,
            timestamp: now,
            unix_timestamp,
            is_critical: false,
            skill_id: None,
        }
    }

    /// 从基础治疗值创建（兼容旧接口）
    pub fn from_amount(amount: Health, source: Option<BattleUnitId>) -> Self {
        let now = SystemTime::now();
        let unix_timestamp = now
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f64();

        Self {
            amount,
            actual_heal: amount, // 假设完全治疗
            source,
            timestamp: now,
            unix_timestamp,
            is_critical: false,
            skill_id: None,
        }
    }

    /// 设置治疗来源
    pub fn with_source(mut self, source: BattleUnitId) -> Self {
        self.source = Some(source);
        self
    }

    /// 设置暴击状态
    pub fn with_critical(mut self) -> Self {
        self.is_critical = true;
        self
    }

    /// 设置技能ID
    pub fn with_skill(mut self, skill_id: String) -> Self {
        self.skill_id = Some(skill_id);
        self
    }

    /// 获取Unix时间戳
    pub fn get_unix_timestamp(&self) -> f64 {
        self.unix_timestamp
    }

    /// 获取治疗效率
    pub fn efficiency(&self) -> f32 {
        if self.amount > 0 {
            self.actual_heal as f32 / self.amount as f32
        } else {
            0.0
        }
    }

    /// 获取统一的治疗值
    pub fn get_heal_amount(&self) -> Health {
        self.amount
    }

    /// 获取统一的来源ID
    pub fn get_source(&self) -> Option<BattleUnitId> {
        self.source
    }
}

// ============================================================================
// 结果类型定义 - 合并两个定义的所有功能
// ============================================================================

/// 攻击结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AttackResult {
    pub hit: bool,
    pub damage_dealt: Health,
    pub is_critical: bool,
    pub target_defeated: bool,
    pub special_effects: Vec<String>,
}

impl Default for AttackResult {
    fn default() -> Self {
        Self {
            hit: false,
            damage_dealt: 0,
            is_critical: false,
            target_defeated: false,
            special_effects: Vec::new(),
        }
    }
}

/// 移动结果 - 合并了两个定义的所有功能
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MovementResult {
    // 来自两个定义的共同属性
    pub success: bool,
    pub distance_moved: f32,
    pub time_taken: f32,
    pub stamina_consumed: Stamina,

    // 来自 shared/types/mod.rs 的属性
    pub action_points_consumed: ActionPoints,

    // 来自 battle_unit/types/result_types.rs 的属性
    pub mana_consumed: Mana,
    pub movement_type: MovementType,
    pub failure_reason: Option<String>,
    pub triggered_effects: Vec<StatusEffect>,
}

impl Default for MovementResult {
    fn default() -> Self {
        Self {
            success: false,
            distance_moved: 0.0,
            stamina_consumed: 0,
            action_points_consumed: 0,
            time_taken: 0.0,
            mana_consumed: 0,
            movement_type: MovementType::Walking,
            failure_reason: None,
            triggered_effects: Vec::new(),
        }
    }
}

impl MovementResult {
    /// 创建成功的移动结果 - 合并了两个构造函数的功能
    pub fn success(
        distance_moved: f32,
        time_taken: f32,
        stamina_consumed: Stamina,
        movement_type: MovementType,
    ) -> Self {
        Self {
            success: true,
            distance_moved,
            time_taken,
            stamina_consumed,
            action_points_consumed: 0,
            mana_consumed: 0,
            movement_type,
            failure_reason: None,
            triggered_effects: Vec::new(),
        }
    }

    /// 创建失败的移动结果
    pub fn failure(reason: String) -> Self {
        Self {
            success: false,
            distance_moved: 0.0,
            stamina_consumed: 0,
            action_points_consumed: 0,
            time_taken: 0.0,
            mana_consumed: 0,
            movement_type: MovementType::Walking,
            failure_reason: Some(reason),
            triggered_effects: Vec::new(),
        }
    }

    /// 设置行动点消耗
    pub fn with_action_points(mut self, action_points: ActionPoints) -> Self {
        self.action_points_consumed = action_points;
        self
    }

    /// 设置法力消耗
    pub fn with_mana_cost(mut self, mana_cost: Mana) -> Self {
        self.mana_consumed = mana_cost;
        self
    }

    /// 添加触发效果
    pub fn with_effect(mut self, effect: StatusEffect) -> Self {
        self.triggered_effects.push(effect);
        self
    }

    /// 设置移动类型
    pub fn with_movement_type(mut self, movement_type: MovementType) -> Self {
        self.movement_type = movement_type;
        self
    }
}

/// 技能结果 - 合并了两个定义的所有功能
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SkillResult {
    // 来自两个定义的共同属性
    pub success: bool,
    pub cooldown_applied: Duration,

    // 来自 shared/types/mod.rs 的属性
    pub targets_affected: Vec<BattleUnitId>,
    pub damage_dealt: HashMap<BattleUnitId, Health>,
    pub healing_done: HashMap<BattleUnitId, Health>,
    pub status_effects_applied: Vec<(BattleUnitId, StatusType)>,
    pub resource_consumed: ResourceCost,

    // 来自 battle_unit/types/result_types.rs 的属性
    pub skill_id: SkillId,
    pub failure_reason: Option<String>,
    pub detailed_damage_results: Vec<DetailedDamageResult>,
    pub heal_results: Vec<HealResult>,
    pub applied_effects: Vec<StatusEffect>,
    pub additional_effects: HashMap<String, serde_json::Value>,
    pub resource_cost: ResourceCost, // 两个资源消耗字段保持兼容
}

impl Default for SkillResult {
    fn default() -> Self {
        Self {
            success: false,
            skill_id: SkillId::from(1),
            targets_affected: Vec::new(),
            damage_dealt: HashMap::new(),
            healing_done: HashMap::new(),
            status_effects_applied: Vec::new(),
            resource_consumed: ResourceCost::none(),
            resource_cost: ResourceCost::none(),
            cooldown_applied: Duration::from_secs(0),
            failure_reason: None,
            detailed_damage_results: Vec::new(),
            heal_results: Vec::new(),
            applied_effects: Vec::new(),
            additional_effects: HashMap::new(),
        }
    }
}

impl SkillResult {
    /// 创建成功的技能结果 - 合并了两个构造函数的功能
    pub fn success(skill_id: SkillId, resource_cost: ResourceCost) -> Self {
        Self {
            success: true,
            skill_id,
            resource_consumed: resource_cost,
            ..Default::default()
        }
    }

    /// 创建失败的技能结果
    pub fn failure(skill_id: SkillId, reason: String) -> Self {
        Self {
            success: false,
            skill_id,
            failure_reason: Some(reason),
            resource_consumed: ResourceCost::none(),
            resource_cost: ResourceCost::none(),
            ..Default::default()
        }
    }

    /// 添加伤害结果 - 同时更新两个数据结构
    pub fn with_damage(mut self, target: BattleUnitId, damage: Health) -> Self {
        // 更新 shared/types 格式的数据
        if !self.targets_affected.contains(&target) {
            self.targets_affected.push(target);
        }
        self.damage_dealt.insert(target, damage);
        self
    }

    /// 添加治疗结果 - 同时更新两个数据结构
    pub fn with_heal(mut self, target: BattleUnitId, heal: Health) -> Self {
        // 更新 shared/types 格式的数据
        if !self.targets_affected.contains(&target) {
            self.targets_affected.push(target);
        }
        self.healing_done.insert(target, heal);
        self
    }

    /// 添加状态效果 - 同时更新两个数据结构
    pub fn with_status_effect(mut self, target: BattleUnitId, status: StatusType) -> Self {
        self.status_effects_applied.push((target, status));
        self
    }

    /// 设置冷却时间
    pub fn with_cooldown(mut self, cooldown: Duration) -> Self {
        self.cooldown_applied = cooldown;
        self
    }

    /// 添加详细伤害结果
    pub fn with_detailed_damage(mut self, damage: DetailedDamageResult) -> Self {
        self.detailed_damage_results.push(damage);
        self
    }

    /// 添加治疗结果对象
    pub fn with_heal_result(mut self, heal: HealResult) -> Self {
        self.heal_results.push(heal);
        self
    }

    /// 添加状态效果对象
    pub fn with_effect(mut self, effect: StatusEffect) -> Self {
        self.applied_effects.push(effect);
        self
    }

    /// 添加额外效果
    pub fn with_additional_effect(mut self, key: String, value: serde_json::Value) -> Self {
        self.additional_effects.insert(key, value);
        self
    }

    /// 获取总伤害 - 合并两种计算方式
    pub fn total_damage(&self) -> Health {
        let simple_damage: Health = self.damage_dealt.values().sum();
        let detailed_damage: i32 = self
            .detailed_damage_results
            .iter()
            .map(|d| d.final_damage)
            .sum();
        simple_damage + detailed_damage
    }

    /// 获取总治疗 - 合并两种计算方式
    pub fn total_heal(&self) -> Health {
        let simple_heal: Health = self.healing_done.values().sum();
        let detailed_heal: Health = self.heal_results.iter().map(|h| h.actual_heal).sum();
        simple_heal + detailed_heal
    }

    /// 检查是否有暴击
    pub fn has_critical(&self) -> bool {
        self.detailed_damage_results.iter().any(|d| d.is_critical)
            || self.heal_results.iter().any(|h| h.is_critical)
    }

    /// 获取统一的资源消耗
    pub fn get_resource_cost(&self) -> &ResourceCost {
        // 优先使用 resource_consumed，如果为默认值则使用 resource_cost
        if self.resource_consumed != ResourceCost::none() {
            &self.resource_consumed
        } else {
            &self.resource_cost
        }
    }

    /// 获取所有受影响的目标
    pub fn get_all_targets(&self) -> Vec<BattleUnitId> {
        let mut targets = self.targets_affected.clone();

        // 从详细结果中添加目标
        for damage_result in &self.detailed_damage_results {
            if let Some(source) = damage_result.source {
                if !targets.contains(&source) {
                    targets.push(source);
                }
            }
        }

        // 从状态效果中添加目标
        for (target, _) in &self.status_effects_applied {
            if !targets.contains(target) {
                targets.push(*target);
            }
        }

        targets
    }
}

// ============================================================================
// 详细结果类型（从 battle_unit 模块迁移）
// ============================================================================

/// 详细伤害结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DetailedDamageResult {
    /// 原始伤害值
    pub raw_damage: i32,
    /// 最终伤害值
    pub final_damage: i32,
    /// 被吸收的伤害
    pub absorbed_damage: i32,
    /// 被格挡的伤害
    pub blocked_damage: i32,
    /// 被抗性减少的伤害
    pub resisted_damage: i32,
    /// 是否暴击
    pub is_critical: bool,
    /// 暴击倍数
    pub critical_multiplier: f32,
    /// 伤害类型
    pub damage_type: DamageType,
    /// 目标剩余生命值
    pub target_remaining_health: Health,
    /// 是否致命
    pub is_lethal: bool,
    /// 是否致命（兼容性）
    pub is_fatal: bool,
    /// 触发的额外效果
    pub triggered_effects: Vec<StatusEffect>,
    /// 伤害详情
    pub damage_breakdown: HashMap<String, i32>,
    /// 应用的抗性
    pub resistance_applied: Resistance,
    /// 伤害来源
    pub source: Option<BattleUnitId>,
}

impl DetailedDamageResult {
    /// 创建新的详细伤害结果
    pub fn new(
        raw_damage: i32,
        final_damage: i32,
        damage_type: DamageType,
        target_remaining_health: Health,
    ) -> Self {
        Self {
            raw_damage,
            final_damage,
            absorbed_damage: 0,
            blocked_damage: 0,
            resisted_damage: 0,
            is_critical: false,
            critical_multiplier: 1.0,
            damage_type,
            target_remaining_health,
            is_lethal: target_remaining_health <= 0,
            is_fatal: target_remaining_health <= 0,
            triggered_effects: Vec::new(),
            damage_breakdown: HashMap::new(),
            resistance_applied: 0.0 as Resistance,
            source: None,
        }
    }

    /// 设置防御信息
    pub fn with_mitigation(mut self, absorbed: i32, blocked: i32, resisted: i32) -> Self {
        self.absorbed_damage = absorbed;
        self.blocked_damage = blocked;
        self.resisted_damage = resisted;
        self
    }

    /// 设置暴击信息
    pub fn with_critical_info(mut self, is_critical: bool, multiplier: f32) -> Self {
        self.is_critical = is_critical;
        self.critical_multiplier = multiplier;
        self
    }

    /// 添加触发效果
    pub fn with_effect(mut self, effect: StatusEffect) -> Self {
        self.triggered_effects.push(effect);
        self
    }

    /// 设置伤害来源
    pub fn with_source(mut self, source: BattleUnitId) -> Self {
        self.source = Some(source);
        self
    }

    /// 获取伤害减免总量
    pub fn total_mitigation(&self) -> i32 {
        self.absorbed_damage + self.blocked_damage + self.resisted_damage
    }

    /// 获取伤害减免百分比
    pub fn mitigation_percentage(&self) -> f32 {
        if self.raw_damage > 0 {
            self.total_mitigation() as f32 / self.raw_damage as f32
        } else {
            0.0
        }
    }
}

/// 治疗结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct HealResult {
    /// 治疗量
    pub heal_amount: Health,
    /// 实际恢复的生命值
    pub actual_heal: Health,
    /// 过量治疗
    pub overheal: Health,
    /// 是否暴击治疗
    pub is_critical: bool,
    /// 暴击倍数
    pub critical_multiplier: f32,
    /// 目标当前生命值
    pub target_current_health: Health,
    /// 目标最大生命值
    pub target_max_health: Health,
}

impl HealResult {
    /// 创建新的治疗结果
    pub fn new(
        heal_amount: Health,
        target_current_health: Health,
        target_max_health: Health,
    ) -> Self {
        let actual_heal = heal_amount.min(target_max_health - target_current_health);
        let overheal = heal_amount - actual_heal;

        Self {
            heal_amount,
            actual_heal,
            overheal,
            is_critical: false,
            critical_multiplier: 1.0,
            target_current_health: target_current_health + actual_heal,
            target_max_health,
        }
    }

    /// 设置暴击治疗
    pub fn with_critical(mut self, multiplier: f32) -> Self {
        self.is_critical = true;
        self.critical_multiplier = multiplier;
        self
    }

    /// 获取治疗效率
    pub fn efficiency(&self) -> f32 {
        if self.heal_amount > 0 {
            self.actual_heal as f32 / self.heal_amount as f32
        } else {
            0.0
        }
    }
}

/// 简单伤害结果（兼容性）
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DamageResult {
    /// 造成的伤害
    pub damage_dealt: i32,
    /// 是否暴击
    pub is_critical: bool,
    /// 目标是否死亡
    pub target_killed: bool,
    /// 目标剩余生命值
    pub target_remaining_health: Health,
}

impl DamageResult {
    /// 创建新的伤害结果
    pub fn new(damage_dealt: i32, target_remaining_health: Health) -> Self {
        Self {
            damage_dealt,
            is_critical: false,
            target_killed: target_remaining_health <= 0,
            target_remaining_health,
        }
    }

    /// 设置暴击
    pub fn with_critical(mut self) -> Self {
        self.is_critical = true;
        self
    }
}

// ============================================================================
// 生命状态和身份信息（从 shared/types/mod.rs 迁移）
// ============================================================================

/// 生命状态
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct VitalStatus {
    pub health: (Health, Health),    // (current, max)
    pub mana: (Mana, Mana),          // (current, max)
    pub stamina: (Stamina, Stamina), // (current, max)
    pub is_alive: bool,
    pub fatigue_level: FatigueLevel,
}

impl Default for VitalStatus {
    fn default() -> Self {
        Self {
            health: (100, 100),
            mana: (50, 50),
            stamina: (100, 100),
            is_alive: true,
            fatigue_level: FatigueLevel::Fresh,
        }
    }
}

/// 战斗身份
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BattleIdentity {
    pub entity_id: BattleUnitId,
    pub name: String,
    pub entity_type: EntityType,
    pub faction: Faction,
    pub level: Level,
}

/// 行动能力标志
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ActionCapabilityFlags {
    pub can_move: bool,
    pub can_attack: bool,
    pub can_cast_spells: bool,
    pub can_use_items: bool,
}

impl Default for ActionCapabilityFlags {
    fn default() -> Self {
        Self {
            can_move: true,
            can_attack: true,
            can_cast_spells: true,
            can_use_items: true,
        }
    }
}

/// 武器信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct WeaponInfo {
    pub weapon_type: AttackType,
    pub damage_range: (Attack, Attack),
    pub critical_rate_bonus: CriticalRate,
    pub range: Range,
    pub attack_speed: Speed,
}

/// 装备加成
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct EquipmentBonuses {
    pub special_effects: Vec<String>,
}

impl Default for EquipmentBonuses {
    fn default() -> Self {
        Self {
            special_effects: Vec::new(),
        }
    }
}

/// 套装加成
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SetBonus {
    pub set_name: String,
    pub pieces_equipped: u32,
    pub pieces_required: u32,
    pub bonus_description: String,
}

/// Buff效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BuffEffects {
    pub speed_multiplier: f32,
    pub damage_multiplier: f32,
    pub healing_multiplier: f32,
}

impl Default for BuffEffects {
    fn default() -> Self {
        Self {
            speed_multiplier: 1.0,
            damage_multiplier: 1.0,
            healing_multiplier: 1.0,
        }
    }
}

/// 移动消耗（从 battle_unit 模块迁移）
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MovementCost {
    /// 耐力消耗
    pub stamina_cost: Stamina,
    /// 法力消耗（如传送等特殊移动）
    pub mana_cost: Mana,
    /// 时间消耗（秒）
    pub time_cost: f32,
    /// 冷却时间（秒）
    pub cooldown: f32,
}

impl MovementCost {
    /// 创建基础移动消耗
    pub fn basic(stamina_cost: Stamina) -> Self {
        Self {
            stamina_cost,
            mana_cost: 0,
            time_cost: 1.0,
            cooldown: 0.0,
        }
    }

    /// 创建特殊移动消耗
    pub fn special(stamina_cost: Stamina, mana_cost: Mana, cooldown: f32) -> Self {
        Self {
            stamina_cost,
            mana_cost,
            time_cost: 0.5, // 特殊移动通常更快
            cooldown,
        }
    }

    /// 检查是否可以承担消耗
    pub fn can_afford(&self, current_stamina: Stamina, current_mana: Mana) -> bool {
        current_stamina >= self.stamina_cost && current_mana >= self.mana_cost
    }
}
