use crate::material::material_collection::{CollectionSkill, ToolType};
use crate::material::material_core::{ElementalAttribute, Material, MaterialAttribute};
use chrono::{DateTime, Utc};
/// 材料发现与世界地图集成系统
/// 实现在世界地图探索中发现和采集材料的机制
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// ============================================================================
// 发现系统核心
// ============================================================================

/// 材料发现事件
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MaterialDiscoveryEvent {
    /// 事件ID
    pub id: String,
    /// 发现者ID
    pub discoverer_id: String,
    /// 发现位置
    pub location: Position,
    /// 发现时间
    pub discovery_time: DateTime<Utc>,
    /// 发现的材料
    pub discovered_material: DiscoveredMaterial,
    /// 发现方式
    pub discovery_method: DiscoveryMethod,
    /// 发现难度
    pub difficulty: DiscoveryDifficulty,
    /// 是否已采集
    pub collected: bool,
}

/// 发现的材料
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DiscoveredMaterial {
    /// 材料定义
    pub material: Material,
    /// 估计数量
    pub estimated_quantity: u32,
    /// 实际数量（采集后确定）
    pub actual_quantity: Option<u32>,
    /// 品质状态
    pub quality_condition: MaterialQualityCondition,
    /// 采集难度
    pub extraction_difficulty: ExtractionDifficulty,
    /// 所需工具
    pub required_tools: Vec<ToolType>,
    /// 时间限制
    pub time_limit: Option<chrono::Duration>,
}

/// 材料品质状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MaterialQualityCondition {
    /// 完美状态 - 刚好成熟/形成
    Perfect,
    /// 优质状态 - 状态良好
    Excellent,
    /// 良好状态 - 正常状态
    Good,
    /// 一般状态 - 略有不足
    Fair,
    /// 较差状态 - 状态欠佳
    Poor,
    /// 即将过期 - 需要立即采集
    Expiring,
    /// 损坏状态 - 已经受损
    Damaged,
}

impl MaterialQualityCondition {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            MaterialQualityCondition::Perfect => "完美",
            MaterialQualityCondition::Excellent => "优质",
            MaterialQualityCondition::Good => "良好",
            MaterialQualityCondition::Fair => "一般",
            MaterialQualityCondition::Poor => "较差",
            MaterialQualityCondition::Expiring => "即将过期",
            MaterialQualityCondition::Damaged => "损坏",
        }
    }

    /// 获取品质对数量的影响倍数
    pub fn quantity_modifier(&self) -> f64 {
        match self {
            MaterialQualityCondition::Perfect => 1.5,
            MaterialQualityCondition::Excellent => 1.2,
            MaterialQualityCondition::Good => 1.0,
            MaterialQualityCondition::Fair => 0.8,
            MaterialQualityCondition::Poor => 0.6,
            MaterialQualityCondition::Expiring => 0.4,
            MaterialQualityCondition::Damaged => 0.2,
        }
    }

    /// 获取品质对材料价值的影响
    pub fn value_modifier(&self) -> f64 {
        match self {
            MaterialQualityCondition::Perfect => 2.0,
            MaterialQualityCondition::Excellent => 1.5,
            MaterialQualityCondition::Good => 1.0,
            MaterialQualityCondition::Fair => 0.7,
            MaterialQualityCondition::Poor => 0.5,
            MaterialQualityCondition::Expiring => 0.3,
            MaterialQualityCondition::Damaged => 0.1,
        }
    }
}

/// 发现方式
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DiscoveryMethod {
    /// 主动搜索 - 使用技能主动寻找
    ActiveSearch,
    /// 随机遭遇 - 探索中偶然发现
    RandomEncounter,
    /// 工具辅助 - 使用特殊工具发现
    ToolAssisted,
    /// 事件触发 - 特殊事件中发现
    EventTriggered,
    /// 线索追踪 - 根据线索找到
    ClueFollowing,
    /// 灵感指引 - 高级修士的感知发现
    InspirationGuided,
}

impl DiscoveryMethod {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            DiscoveryMethod::ActiveSearch => "主动搜索",
            DiscoveryMethod::RandomEncounter => "偶然发现",
            DiscoveryMethod::ToolAssisted => "工具辅助",
            DiscoveryMethod::EventTriggered => "事件触发",
            DiscoveryMethod::ClueFollowing => "线索追踪",
            DiscoveryMethod::InspirationGuided => "灵感指引",
        }
    }

    /// 获取发现方式的基础成功率
    pub fn base_success_rate(&self) -> f64 {
        match self {
            DiscoveryMethod::ActiveSearch => 0.6,
            DiscoveryMethod::RandomEncounter => 0.1,
            DiscoveryMethod::ToolAssisted => 0.8,
            DiscoveryMethod::EventTriggered => 1.0,
            DiscoveryMethod::ClueFollowing => 0.9,
            DiscoveryMethod::InspirationGuided => 0.7,
        }
    }
}

/// 发现难度
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum DiscoveryDifficulty {
    /// 极易 - 随处可见
    Trivial,
    /// 容易 - 常见材料
    Easy,
    /// 普通 - 需要一定技能
    Normal,
    /// 困难 - 需要高级技能
    Hard,
    /// 极难 - 需要专精技能
    VeryHard,
    /// 传说 - 极其罕见
    Legendary,
    /// 神话 - 几乎不可能
    Mythical,
}

impl DiscoveryDifficulty {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            DiscoveryDifficulty::Trivial => "极易",
            DiscoveryDifficulty::Easy => "容易",
            DiscoveryDifficulty::Normal => "普通",
            DiscoveryDifficulty::Hard => "困难",
            DiscoveryDifficulty::VeryHard => "极难",
            DiscoveryDifficulty::Legendary => "传说",
            DiscoveryDifficulty::Mythical => "神话",
        }
    }

    /// 获取难度对应的技能等级要求
    pub fn required_skill_level(&self) -> u8 {
        match self {
            DiscoveryDifficulty::Trivial => 1,
            DiscoveryDifficulty::Easy => 10,
            DiscoveryDifficulty::Normal => 25,
            DiscoveryDifficulty::Hard => 50,
            DiscoveryDifficulty::VeryHard => 75,
            DiscoveryDifficulty::Legendary => 90,
            DiscoveryDifficulty::Mythical => 95,
        }
    }

    /// 获取发现概率修正
    pub fn discovery_rate_modifier(&self) -> f64 {
        match self {
            DiscoveryDifficulty::Trivial => 2.0,
            DiscoveryDifficulty::Easy => 1.5,
            DiscoveryDifficulty::Normal => 1.0,
            DiscoveryDifficulty::Hard => 0.7,
            DiscoveryDifficulty::VeryHard => 0.4,
            DiscoveryDifficulty::Legendary => 0.1,
            DiscoveryDifficulty::Mythical => 0.01,
        }
    }
}

/// 采集难度
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum ExtractionDifficulty {
    /// 直接采集 - 可直接拾取
    DirectPickup,
    /// 简单采集 - 需要基础工具
    SimpleExtraction,
    /// 技巧采集 - 需要技巧和工具
    SkilledExtraction,
    /// 精密采集 - 需要高级技能和工具
    PrecisionExtraction,
    /// 专家采集 - 需要专家级别
    ExpertExtraction,
    /// 大师采集 - 需要大师级技艺
    MasterExtraction,
}

impl ExtractionDifficulty {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            ExtractionDifficulty::DirectPickup => "直接采集",
            ExtractionDifficulty::SimpleExtraction => "简单采集",
            ExtractionDifficulty::SkilledExtraction => "技巧采集",
            ExtractionDifficulty::PrecisionExtraction => "精密采集",
            ExtractionDifficulty::ExpertExtraction => "专家采集",
            ExtractionDifficulty::MasterExtraction => "大师采集",
        }
    }

    /// 获取采集成功率
    pub fn base_success_rate(&self) -> f64 {
        match self {
            ExtractionDifficulty::DirectPickup => 1.0,
            ExtractionDifficulty::SimpleExtraction => 0.9,
            ExtractionDifficulty::SkilledExtraction => 0.7,
            ExtractionDifficulty::PrecisionExtraction => 0.5,
            ExtractionDifficulty::ExpertExtraction => 0.3,
            ExtractionDifficulty::MasterExtraction => 0.1,
        }
    }

    /// 获取所需技能等级
    pub fn required_skill_level(&self) -> u8 {
        match self {
            ExtractionDifficulty::DirectPickup => 1,
            ExtractionDifficulty::SimpleExtraction => 5,
            ExtractionDifficulty::SkilledExtraction => 20,
            ExtractionDifficulty::PrecisionExtraction => 40,
            ExtractionDifficulty::ExpertExtraction => 70,
            ExtractionDifficulty::MasterExtraction => 90,
        }
    }
}

// ============================================================================
// 世界地图资源点系统
// ============================================================================

/// 世界地图资源点 - 可产生材料的固定位置
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct WorldResourceNode {
    /// 资源点ID
    pub id: String,
    /// 资源点名称
    pub name: String,
    /// 位置
    pub position: Position,
    /// 资源点类型
    pub node_type: ResourceNodeType,
    /// 产出材料类型
    pub material_types: Vec<String>,
    /// 刷新周期（小时）
    pub refresh_cycle: u32,
    /// 上次刷新时间
    pub last_refresh: DateTime<Utc>,
    /// 当前状态
    pub status: ResourceNodeStatus,
    /// 环境属性影响
    pub environmental_modifiers: Vec<EnvironmentalModifier>,
    /// 访问要求
    pub access_requirements: Vec<AccessRequirement>,
}

/// 资源点类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ResourceNodeType {
    /// 药草园 - 产出草药类材料
    HerbGarden,
    /// 矿脉 - 产出矿物类材料
    MineralVein,
    /// 灵泉 - 产出液体类材料
    SpiritualSpring,
    /// 古树 - 产出木系材料
    AncientTree,
    /// 妖兽巢穴 - 产出兽类材料
    BeastLair,
    /// 遗迹废墟 - 产出古物材料
    AncientRuins,
    /// 元素汇聚点 - 产出元素精华
    ElementalNode,
    /// 秘境入口 - 产出稀有材料
    SecretRealmGate,
}

impl ResourceNodeType {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            ResourceNodeType::HerbGarden => "药草园",
            ResourceNodeType::MineralVein => "矿脉",
            ResourceNodeType::SpiritualSpring => "灵泉",
            ResourceNodeType::AncientTree => "古树",
            ResourceNodeType::BeastLair => "妖兽巢穴",
            ResourceNodeType::AncientRuins => "遗迹废墟",
            ResourceNodeType::ElementalNode => "元素汇聚点",
            ResourceNodeType::SecretRealmGate => "秘境入口",
        }
    }

    /// 获取主要产出的材料属性
    pub fn primary_attributes(&self) -> Vec<MaterialAttribute> {
        match self {
            ResourceNodeType::HerbGarden => vec![
                MaterialAttribute::Elemental(ElementalAttribute::Wood),
                MaterialAttribute::Elemental(ElementalAttribute::Water),
            ],
            ResourceNodeType::MineralVein => vec![
                MaterialAttribute::Elemental(ElementalAttribute::Earth),
                MaterialAttribute::Elemental(ElementalAttribute::Metal),
            ],
            ResourceNodeType::SpiritualSpring => {
                vec![MaterialAttribute::Elemental(ElementalAttribute::Water)]
            }
            ResourceNodeType::AncientTree => {
                vec![MaterialAttribute::Elemental(ElementalAttribute::Wood)]
            }
            ResourceNodeType::BeastLair => vec![
                MaterialAttribute::Elemental(ElementalAttribute::Fire),
                MaterialAttribute::Elemental(ElementalAttribute::Earth),
            ],
            ResourceNodeType::AncientRuins => vec![MaterialAttribute::Chaos],
            ResourceNodeType::ElementalNode => vec![
                MaterialAttribute::Elemental(ElementalAttribute::Metal),
                MaterialAttribute::Elemental(ElementalAttribute::Wood),
                MaterialAttribute::Elemental(ElementalAttribute::Water),
                MaterialAttribute::Elemental(ElementalAttribute::Fire),
                MaterialAttribute::Elemental(ElementalAttribute::Earth),
            ],
            ResourceNodeType::SecretRealmGate => vec![MaterialAttribute::Chaos],
        }
    }

    /// 获取适用的采集技能
    pub fn suitable_skills(&self) -> Vec<CollectionSkill> {
        match self {
            ResourceNodeType::HerbGarden => vec![CollectionSkill::Herbalism],
            ResourceNodeType::MineralVein => vec![CollectionSkill::Mining],
            ResourceNodeType::SpiritualSpring => vec![CollectionSkill::Herbalism],
            ResourceNodeType::AncientTree => vec![CollectionSkill::Herbalism],
            ResourceNodeType::BeastLair => vec![CollectionSkill::BeastHunting],
            ResourceNodeType::AncientRuins => {
                vec![CollectionSkill::TreasureHunting, CollectionSkill::Appraisal]
            }
            ResourceNodeType::ElementalNode => vec![CollectionSkill::TreasureHunting],
            ResourceNodeType::SecretRealmGate => {
                vec![CollectionSkill::TreasureHunting, CollectionSkill::Appraisal]
            }
        }
    }
}

/// 资源点状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResourceNodeStatus {
    /// 丰富 - 资源充足
    Abundant,
    /// 正常 - 标准状态
    Normal,
    /// 稀少 - 资源较少
    Scarce,
    /// 枯竭 - 暂时无资源
    Depleted,
    /// 封印 - 无法访问
    Sealed,
    /// 危险 - 有威胁存在
    Dangerous,
}

impl ResourceNodeStatus {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            ResourceNodeStatus::Abundant => "丰富",
            ResourceNodeStatus::Normal => "正常",
            ResourceNodeStatus::Scarce => "稀少",
            ResourceNodeStatus::Depleted => "枯竭",
            ResourceNodeStatus::Sealed => "封印",
            ResourceNodeStatus::Dangerous => "危险",
        }
    }

    /// 获取产量修正
    pub fn yield_modifier(&self) -> f64 {
        match self {
            ResourceNodeStatus::Abundant => 2.0,
            ResourceNodeStatus::Normal => 1.0,
            ResourceNodeStatus::Scarce => 0.5,
            ResourceNodeStatus::Depleted => 0.0,
            ResourceNodeStatus::Sealed => 0.0,
            ResourceNodeStatus::Dangerous => 0.8,
        }
    }
}

/// 环境修正因子
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct EnvironmentalModifier {
    /// 修正类型
    pub modifier_type: String,
    /// 修正值
    pub modifier_value: f64,
    /// 影响的属性
    pub affected_attributes: Vec<MaterialAttribute>,
    /// 时间影响（可选）
    pub time_effect: Option<TimeEffect>,
}

/// 时间效应
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TimeEffect {
    /// 生效时间段（小时）
    pub active_hours: Vec<u8>,
    /// 季节影响
    pub seasonal_effect: Option<String>,
    /// 月相影响
    pub lunar_effect: Option<String>,
}

/// 访问要求
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AccessRequirement {
    /// 技能等级要求
    SkillLevel {
        skill: CollectionSkill,
        min_level: u8,
    },
    /// 特殊工具要求
    SpecialTool { tool_name: String },
    /// 门票或钥匙
    AccessKey { key_item: String },
    /// 修为境界要求
    CultivationLevel { min_level: String },
    /// 势力声望要求
    FactionReputation {
        faction: String,
        min_reputation: i32,
    },
}

// ============================================================================
// 发现系统管理器
// ============================================================================

/// 材料发现系统管理器
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct MaterialDiscoveryManager {
    /// 世界资源点
    pub resource_nodes: HashMap<String, WorldResourceNode>,
    /// 发现事件历史
    pub discovery_events: HashMap<String, MaterialDiscoveryEvent>,
    /// 按位置索引的资源点
    pub nodes_by_location: HashMap<Position, Vec<String>>,
    /// 发现统计
    pub discovery_statistics: DiscoveryStatistics,
}

/// 发现统计
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct DiscoveryStatistics {
    /// 总发现次数
    pub total_discoveries: u32,
    /// 成功采集次数
    pub successful_collections: u32,
    /// 按难度分类的发现次数
    pub discoveries_by_difficulty: HashMap<DiscoveryDifficulty, u32>,
    /// 按方法分类的发现次数
    pub discoveries_by_method: HashMap<DiscoveryMethod, u32>,
    /// 稀有材料发现次数
    pub rare_material_discoveries: u32,
}

impl MaterialDiscoveryManager {
    /// 创建新的发现管理器
    pub fn new() -> Self {
        Self::default()
    }

    /// 添加资源点
    pub fn add_resource_node(&mut self, node: WorldResourceNode) {
        let position = node.position;
        let node_id = node.id.clone();

        // 添加到按位置索引
        self.nodes_by_location
            .entry(position)
            .or_insert_with(Vec::new)
            .push(node_id.clone());

        // 添加资源点
        self.resource_nodes.insert(node_id, node);
    }

    /// 在指定位置搜索材料
    pub fn search_for_materials(
        &mut self,
        searcher_id: String,
        position: Position,
        search_method: DiscoveryMethod,
        used_skill: Option<CollectionSkill>,
    ) -> Vec<MaterialDiscoveryEvent> {
        let mut discoveries = Vec::new();

        // 检查该位置的资源点
        if let Some(node_ids) = self.nodes_by_location.get(&position).cloned() {
            for node_id in &node_ids {
                if let Some(discovery) = self.attempt_discovery_at_node(
                    &searcher_id,
                    &node_id,
                    search_method,
                    used_skill,
                ) {
                    discoveries.push(discovery);
                }
            }
        }

        // 随机发现（不在资源点的材料）
        if let Some(random_discovery) =
            self.attempt_random_discovery(searcher_id, position, search_method, used_skill)
        {
            discoveries.push(random_discovery);
        }

        discoveries
    }

    /// 尝试在资源点发现材料
    fn attempt_discovery_at_node(
        &mut self,
        searcher_id: &str,
        node_id: &str,
        method: DiscoveryMethod,
        skill: Option<CollectionSkill>,
    ) -> Option<MaterialDiscoveryEvent> {
        // 克隆节点信息以避免借用冲突
        let node = self.resource_nodes.get(node_id)?.clone();

        // 检查资源点状态
        if matches!(
            node.status,
            ResourceNodeStatus::Depleted | ResourceNodeStatus::Sealed
        ) {
            return None;
        }

        // 计算发现概率
        let mut base_rate = method.base_success_rate();
        base_rate *= node.status.yield_modifier();

        // 技能匹配加成
        if let Some(used_skill) = skill {
            if node.node_type.suitable_skills().contains(&used_skill) {
                base_rate *= 1.5;
            }
        }

        // 随机判定
        if rand::random::<f64>() < base_rate {
            // 选择一个材料类型
            if let Some(material_type_id) = node.material_types.choose(&mut rand::thread_rng()) {
                return Some(self.create_discovery_event(
                    searcher_id.to_string(),
                    node.position,
                    method,
                    material_type_id,
                    &node,
                ));
            }
        }

        None
    }

    /// 尝试随机发现
    fn attempt_random_discovery(
        &mut self,
        searcher_id: String,
        position: Position,
        method: DiscoveryMethod,
        _skill: Option<CollectionSkill>,
    ) -> Option<MaterialDiscoveryEvent> {
        // 随机发现的基础概率很低
        let base_rate = 0.05 * method.base_success_rate();

        if rand::random::<f64>() < base_rate {
            // 根据地区和环境生成合适的随机材料
            let random_material_id = self.generate_random_material_by_environment(position, method);

            // 创建一个虚拟的资源点用于生成发现事件
            let virtual_node = WorldResourceNode {
                id: "random".to_string(),
                name: "野外发现".to_string(),
                position,
                node_type: ResourceNodeType::HerbGarden,
                material_types: vec![random_material_id.to_string()],
                refresh_cycle: 0,
                last_refresh: Utc::now(),
                status: ResourceNodeStatus::Normal,
                environmental_modifiers: Vec::new(),
                access_requirements: Vec::new(),
            };

            return Some(self.create_discovery_event(
                searcher_id,
                position,
                method,
                &random_material_id,
                &virtual_node,
            ));
        }

        None
    }

    /// 创建发现事件
    fn create_discovery_event(
        &mut self,
        discoverer_id: String,
        position: Position,
        method: DiscoveryMethod,
        material_type_id: &str,
        node: &WorldResourceNode,
    ) -> MaterialDiscoveryEvent {
        // 生成事件ID
        let event_id = format!("discovery_{}_{}", discoverer_id, Utc::now().timestamp());

        // 创建材料实例（这里应该从材料库获取）
        let material = self.create_material_from_type(material_type_id, position);

        // 确定发现难度
        let difficulty = self.determine_discovery_difficulty(&material, node);

        // 创建发现的材料
        let discovered_material = DiscoveredMaterial {
            material: material.clone(),
            estimated_quantity: self.calculate_estimated_quantity(&material, node),
            actual_quantity: None,
            quality_condition: self.determine_quality_condition(&material, node),
            extraction_difficulty: self.determine_extraction_difficulty(&material, difficulty),
            required_tools: self.determine_required_tools(&material, node),
            time_limit: self.determine_time_limit(&material),
        };

        // 创建发现事件
        let event = MaterialDiscoveryEvent {
            id: event_id.clone(),
            discoverer_id,
            location: position,
            discovery_time: Utc::now(),
            discovered_material,
            discovery_method: method,
            difficulty,
            collected: false,
        };

        // 更新统计
        self.discovery_statistics.total_discoveries += 1;
        *self
            .discovery_statistics
            .discoveries_by_difficulty
            .entry(difficulty)
            .or_insert(0) += 1;
        *self
            .discovery_statistics
            .discoveries_by_method
            .entry(method)
            .or_insert(0) += 1;

        // 保存事件
        self.discovery_events.insert(event_id, event.clone());

        event
    }

    /// 从类型创建材料（完整实现）
    fn create_material_from_type(&self, material_type_id: &str, position: Position) -> Material {
        // 先尝试从MaterialType枚举解析
        if let Some(material_type) = MaterialType::from_id(material_type_id) {
            Material::new(
                material_type.id().to_string(),
                material_type.chinese_name().to_string(),
                format!("{}材料", material_type.chinese_name()),
                material_type.default_grade(),
                material_type.default_attribute(),
            )
        } else {
            // 后备方案：根据位置和随机因素生成默认材料
            let grade = if rand::random::<f64>() < 0.8 {
                MaterialGrade::Mortal
            } else {
                MaterialGrade::Spiritual
            };

            let attribute = self.determine_attribute_by_position(position);

            Material::new(
                material_type_id.to_string(),
                "未知材料".to_string(),
                "一种尚未被充分研究的神秘材料".to_string(),
                grade,
                attribute,
            )
        }
    }

    /// 确定发现难度
    fn determine_discovery_difficulty(
        &self,
        material: &Material,
        _node: &WorldResourceNode,
    ) -> DiscoveryDifficulty {
        match material.grade {
            MaterialGrade::Mortal => DiscoveryDifficulty::Easy,
            MaterialGrade::Spiritual => DiscoveryDifficulty::Normal,
            MaterialGrade::Immortal => DiscoveryDifficulty::Hard,
            MaterialGrade::Divine => DiscoveryDifficulty::VeryHard,
            MaterialGrade::Sacred => DiscoveryDifficulty::Legendary,
            MaterialGrade::Chaos => DiscoveryDifficulty::Mythical,
        }
    }

    /// 计算估计数量
    fn calculate_estimated_quantity(&self, material: &Material, node: &WorldResourceNode) -> u32 {
        let base_quantity = match material.grade {
            MaterialGrade::Mortal => 5,
            MaterialGrade::Spiritual => 3,
            MaterialGrade::Immortal => 2,
            MaterialGrade::Divine => 1,
            MaterialGrade::Sacred => 1,
            MaterialGrade::Chaos => 1,
        };

        (base_quantity as f64 * node.status.yield_modifier()) as u32
    }

    /// 确定品质状态
    fn determine_quality_condition(
        &self,
        _material: &Material,
        node: &WorldResourceNode,
    ) -> MaterialQualityCondition {
        match node.status {
            ResourceNodeStatus::Abundant => MaterialQualityCondition::Perfect,
            ResourceNodeStatus::Normal => MaterialQualityCondition::Good,
            ResourceNodeStatus::Scarce => MaterialQualityCondition::Fair,
            ResourceNodeStatus::Dangerous => MaterialQualityCondition::Poor,
            _ => MaterialQualityCondition::Good,
        }
    }

    /// 确定采集难度
    fn determine_extraction_difficulty(
        &self,
        material: &Material,
        discovery_difficulty: DiscoveryDifficulty,
    ) -> ExtractionDifficulty {
        match (material.grade, discovery_difficulty) {
            (MaterialGrade::Mortal, _) => ExtractionDifficulty::DirectPickup,
            (MaterialGrade::Spiritual, DiscoveryDifficulty::Easy) => {
                ExtractionDifficulty::SimpleExtraction
            }
            (MaterialGrade::Spiritual, _) => ExtractionDifficulty::SkilledExtraction,
            (MaterialGrade::Immortal, _) => ExtractionDifficulty::PrecisionExtraction,
            (MaterialGrade::Divine, _) => ExtractionDifficulty::ExpertExtraction,
            (MaterialGrade::Sacred | MaterialGrade::Chaos, _) => {
                ExtractionDifficulty::MasterExtraction
            }
        }
    }

    /// 确定所需工具
    fn determine_required_tools(
        &self,
        _material: &Material,
        node: &WorldResourceNode,
    ) -> Vec<ToolType> {
        match node.node_type {
            ResourceNodeType::HerbGarden => vec![ToolType::HerbSpade],
            ResourceNodeType::MineralVein => vec![ToolType::PickAxe],
            ResourceNodeType::SpiritualSpring => vec![ToolType::SpiritWaterBottle],
            ResourceNodeType::AncientTree => vec![ToolType::SharpKnife],
            ResourceNodeType::BeastLair => vec![ToolType::BeastNet],
            ResourceNodeType::AncientRuins => {
                vec![ToolType::TreasureCompass, ToolType::AppraisalMirror]
            }
            ResourceNodeType::ElementalNode => vec![ToolType::ElementalContainer],
            ResourceNodeType::SecretRealmGate => vec![ToolType::SpecialKey],
        }
    }

    /// 确定时间限制
    fn determine_time_limit(&self, material: &Material) -> Option<chrono::Duration> {
        match material.grade {
            MaterialGrade::Mortal => None,
            MaterialGrade::Spiritual => Some(chrono::Duration::hours(24)),
            MaterialGrade::Immortal => Some(chrono::Duration::hours(12)),
            MaterialGrade::Divine => Some(chrono::Duration::hours(6)),
            MaterialGrade::Sacred => Some(chrono::Duration::hours(3)),
            MaterialGrade::Chaos => Some(chrono::Duration::hours(1)),
        }
    }

    /// 更新资源点状态
    pub fn update_resource_nodes(&mut self) {
        let current_time = Utc::now();

        for node in self.resource_nodes.values_mut() {
            let hours_since_refresh = (current_time - node.last_refresh).num_hours() as u32;

            if hours_since_refresh >= node.refresh_cycle {
                // 刷新资源点
                node.last_refresh = current_time;

                // 恢复状态（简化实现）
                if matches!(node.status, ResourceNodeStatus::Depleted) {
                    node.status = ResourceNodeStatus::Scarce;
                } else if matches!(node.status, ResourceNodeStatus::Scarce) {
                    node.status = ResourceNodeStatus::Normal;
                }
            }
        }
    }

    /// 获取位置附近的资源点
    pub fn get_nearby_resource_nodes(
        &self,
        position: Position,
        range: i32,
    ) -> Vec<&WorldResourceNode> {
        self.resource_nodes
            .values()
            .filter(|node| {
                let distance = (((node.position.x - position.x).powi(2)
                    + (node.position.y - position.y).powi(2))
                    as f64)
                    .sqrt();
                distance <= range as f64
            })
            .collect()
    }

    /// 标记材料已采集
    pub fn mark_material_collected(
        &mut self,
        event_id: &str,
        collected_quantity: u32,
    ) -> Result<(), String> {
        if let Some(event) = self.discovery_events.get_mut(event_id) {
            event.collected = true;
            event.discovered_material.actual_quantity = Some(collected_quantity);

            // 更新统计
            self.discovery_statistics.successful_collections += 1;

            // 检查是否为稀有材料
            if matches!(
                event.discovered_material.material.grade,
                MaterialGrade::Immortal
                    | MaterialGrade::Divine
                    | MaterialGrade::Sacred
                    | MaterialGrade::Chaos
            ) {
                self.discovery_statistics.rare_material_discoveries += 1;
            }

            Ok(())
        } else {
            Err("发现事件不存在".to_string())
        }
    }

    /// 根据环境生成随机材料类型
    fn generate_random_material_by_environment(
        &self,
        position: Position,
        method: DiscoveryMethod,
    ) -> String {
        // 根据位置坐标判断环境类型
        let terrain_type = self.determine_terrain_type(position);

        // 根据地形和发现方式选择材料
        let material_pool = match terrain_type {
            TerrainType::Forest => vec![
                MaterialType::CommonHerb,
                MaterialType::SpiritualFlower,
                MaterialType::AncientWood,
            ],
            TerrainType::Mountains => vec![
                MaterialType::IronOre,
                MaterialType::SilverOre,
                MaterialType::RareCrystal,
            ],
            TerrainType::Water => vec![MaterialType::CrystalWater, MaterialType::CommonHerb],
            TerrainType::Plains => vec![MaterialType::CommonHerb, MaterialType::BeastHide],
            TerrainType::Volcanic => vec![MaterialType::FireEssence, MaterialType::IronOre],
            TerrainType::Desert => vec![MaterialType::RareCrystal, MaterialType::EarthEssence],
            TerrainType::Swamp => vec![MaterialType::CommonHerb, MaterialType::RareCrystal],
            TerrainType::Tundra => vec![MaterialType::IronOre, MaterialType::RareCrystal],
            TerrainType::SpiritualVein => vec![
                MaterialType::SpiritualFlower,
                MaterialType::RareCrystal,
                MaterialType::FireEssence,
            ],
            TerrainType::Void => vec![MaterialType::RareCrystal],
        };

        // 根据发现方式调整概率
        let selected_material = match method {
            DiscoveryMethod::InspirationGuided => {
                // 灵感指引更容易发现稀有材料
                if material_pool.contains(&MaterialType::RareCrystal) && rand::random::<f64>() < 0.3
                {
                    MaterialType::RareCrystal
                } else if material_pool.contains(&MaterialType::SpiritualFlower)
                    && rand::random::<f64>() < 0.4
                {
                    MaterialType::SpiritualFlower
                } else {
                    *material_pool
                        .choose(&mut rand::thread_rng())
                        .unwrap_or(&MaterialType::CommonHerb)
                }
            }
            DiscoveryMethod::ToolAssisted => {
                // 工具辅助提高发现品质材料的概率
                if rand::random::<f64>() < 0.6 {
                    *material_pool
                        .iter()
                        .find(|&&m| m != MaterialType::CommonHerb)
                        .unwrap_or(
                            material_pool
                                .choose(&mut rand::thread_rng())
                                .unwrap_or(&MaterialType::CommonHerb),
                        )
                } else {
                    *material_pool
                        .choose(&mut rand::thread_rng())
                        .unwrap_or(&MaterialType::CommonHerb)
                }
            }
            _ => {
                // 其他方式使用标准随机选择
                *material_pool
                    .choose(&mut rand::thread_rng())
                    .unwrap_or(&MaterialType::CommonHerb)
            }
        };

        selected_material.id().to_string()
    }

    /// 根据位置确定地形类型
    fn determine_terrain_type(&self, position: Position) -> TerrainType {
        // 根据坐标判断地形类型（简化实现）
        let x = position.grid_x;
        let y = position.grid_y;

        // 使用坐标的特征来确定地形
        match ((x % 10).abs(), (y % 10).abs()) {
            (0..=2, 0..=2) => TerrainType::Forest,
            (0..=2, 3..=5) => TerrainType::Mountains,
            (0..=2, 6..=9) => TerrainType::Water,
            (3..=5, 0..=3) => TerrainType::Plains,
            (3..=5, 4..=6) => TerrainType::Volcanic,
            (3..=5, 7..=9) => TerrainType::Desert,
            (6..=8, 0..=4) => TerrainType::Swamp,
            (6..=8, 5..=9) => TerrainType::Tundra,
            (9, 9) => TerrainType::SpiritualVein, // 稀有灵脉
            _ => TerrainType::Plains,
        }
    }

    /// 根据位置确定材料属性
    fn determine_attribute_by_position(&self, position: Position) -> MaterialAttribute {
        let terrain_type = self.determine_terrain_type(position);

        match terrain_type {
            TerrainType::Forest => MaterialAttribute::Elemental(ElementalAttribute::Wood),
            TerrainType::Mountains => MaterialAttribute::Elemental(ElementalAttribute::Earth),
            TerrainType::Water => MaterialAttribute::Elemental(ElementalAttribute::Water),
            TerrainType::Volcanic => MaterialAttribute::Elemental(ElementalAttribute::Fire),
            TerrainType::Plains => MaterialAttribute::Elemental(ElementalAttribute::Wood),
            TerrainType::Desert => MaterialAttribute::Elemental(ElementalAttribute::Earth),
            TerrainType::Swamp => MaterialAttribute::Elemental(ElementalAttribute::Water),
            TerrainType::Tundra => MaterialAttribute::Elemental(ElementalAttribute::Metal),
            TerrainType::SpiritualVein => MaterialAttribute::Chaos,
            TerrainType::Void => MaterialAttribute::Chaos,
        }
    }
}

// 需要添加这些依赖到外部
use crate::material::MaterialType;
use crate::shared::{MaterialGrade, Position};
use crate::world_map::domain::TerrainType;
use rand::seq::SliceRandom;
