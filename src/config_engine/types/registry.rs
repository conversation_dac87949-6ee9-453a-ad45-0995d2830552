//! # 类型注册中心模块
//!
//! 管理所有类型定义和注册，提供运行时类型查询和验证
//!
//! ## 核心功能
//!
//! - 类型注册和管理
//! - 运行时类型查询
//! - 类型关系管理
//! - 动态类型验证
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::types::{TypeRegistry, TypeRegistration};
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let mut registry = TypeRegistry::new();
//!
//! // 注册新类型
//! let registration = TypeRegistration::new()
//!     .with_type_name("Material")
//!     .with_description("游戏材料类型")
//!     .with_allowed_values(vec!["iron_ore", "gold_ore", "rare_herb"])
//!     .with_validation_rule("length > 0");
//!
//! registry.register_type("material", registration).await?;
//!
//! // 验证类型
//! let is_valid = registry.validate_type_value("material", "iron_ore").await?;
//! assert!(is_valid);
//! # Ok(())
//! # }
//! ```

use crate::config_engine::types::{TypeMetadata, TypeOperationResult, TypeQuery};
use crate::config_engine::{error::ConfigEngineError, Result};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 类型注册信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeRegistration {
    /// 类型名称
    pub type_name: String,
    /// 类型描述
    pub description: Option<String>,
    /// 基础类型（继承关系）
    pub base_type: Option<String>,
    /// 允许的值列表
    pub allowed_values: Vec<String>,
    /// 验证规则表达式
    pub validation_rules: Vec<String>,
    /// 转换规则
    pub conversion_rules: Vec<String>,
    /// 类型约束
    pub constraints: TypeConstraints,
    /// 类型标签
    pub tags: HashMap<String, String>,
    /// 是否启用
    pub enabled: bool,
    /// 优先级
    pub priority: i32,
}

impl TypeRegistration {
    /// 创建新的类型注册
    pub fn new() -> Self {
        Self {
            type_name: String::new(),
            description: None,
            base_type: None,
            allowed_values: Vec::new(),
            validation_rules: Vec::new(),
            conversion_rules: Vec::new(),
            constraints: TypeConstraints::default(),
            tags: HashMap::new(),
            enabled: true,
            priority: 0,
        }
    }

    /// 设置类型名称
    pub fn with_type_name(mut self, name: impl Into<String>) -> Self {
        self.type_name = name.into();
        self
    }

    /// 设置描述
    pub fn with_description(mut self, description: impl Into<String>) -> Self {
        self.description = Some(description.into());
        self
    }

    /// 设置基础类型
    pub fn with_base_type(mut self, base_type: impl Into<String>) -> Self {
        self.base_type = Some(base_type.into());
        self
    }

    /// 添加允许的值
    pub fn with_allowed_values<I, S>(mut self, values: I) -> Self
    where
        I: IntoIterator<Item = S>,
        S: Into<String>,
    {
        self.allowed_values
            .extend(values.into_iter().map(|s| s.into()));
        self
    }

    /// 添加验证规则
    pub fn with_validation_rule(mut self, rule: impl Into<String>) -> Self {
        self.validation_rules.push(rule.into());
        self
    }

    /// 添加转换规则
    pub fn with_conversion_rule(mut self, rule: impl Into<String>) -> Self {
        self.conversion_rules.push(rule.into());
        self
    }

    /// 设置约束
    pub fn with_constraints(mut self, constraints: TypeConstraints) -> Self {
        self.constraints = constraints;
        self
    }

    /// 添加标签
    pub fn with_tag(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.tags.insert(key.into(), value.into());
        self
    }

    /// 设置优先级
    pub fn with_priority(mut self, priority: i32) -> Self {
        self.priority = priority;
        self
    }

    /// 禁用类型
    pub fn disabled(mut self) -> Self {
        self.enabled = false;
        self
    }

    /// 验证注册信息的完整性
    pub fn validate(&self) -> Result<()> {
        if self.type_name.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: "Type name cannot be empty".to_string(),
                details: None,
            });
        }

        // 验证基础类型循环依赖（这里简化实现）
        if let Some(base) = &self.base_type {
            if base == &self.type_name {
                return Err(ConfigEngineError::ValidationError {
                    message: "Type cannot inherit from itself".to_string(),
                    details: Some(format!("Type: {}", self.type_name)),
                });
            }
        }

        // 验证约束
        self.constraints.validate()?;

        Ok(())
    }
}

impl Default for TypeRegistration {
    fn default() -> Self {
        Self::new()
    }
}

/// 类型约束
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeConstraints {
    /// 最小值数量
    pub min_values: Option<usize>,
    /// 最大值数量
    pub max_values: Option<usize>,
    /// 值的最小长度
    pub min_length: Option<usize>,
    /// 值的最大长度
    pub max_length: Option<usize>,
    /// 正则表达式模式
    pub pattern: Option<String>,
    /// 是否允许空值
    pub allow_empty: bool,
    /// 是否允许重复
    pub allow_duplicates: bool,
    /// 自定义约束表达式
    pub custom_constraints: Vec<String>,
}

impl TypeConstraints {
    pub fn new() -> Self {
        Self {
            min_values: None,
            max_values: None,
            min_length: None,
            max_length: None,
            pattern: None,
            allow_empty: true,
            allow_duplicates: true,
            custom_constraints: Vec::new(),
        }
    }

    /// 验证约束的有效性
    pub fn validate(&self) -> Result<()> {
        if let (Some(min), Some(max)) = (self.min_values, self.max_values) {
            if min > max {
                return Err(ConfigEngineError::ValidationError {
                    message: "min_values cannot be greater than max_values".to_string(),
                    details: Some(format!("min: {}, max: {}", min, max)),
                });
            }
        }

        if let (Some(min), Some(max)) = (self.min_length, self.max_length) {
            if min > max {
                return Err(ConfigEngineError::ValidationError {
                    message: "min_length cannot be greater than max_length".to_string(),
                    details: Some(format!("min: {}, max: {}", min, max)),
                });
            }
        }

        // 验证正则表达式
        if let Some(pattern) = &self.pattern {
            regex::Regex::new(pattern).map_err(|e| ConfigEngineError::ValidationError {
                message: format!("Invalid regex pattern: {}", e),
                details: Some(pattern.clone()),
            })?;
        }

        Ok(())
    }

    /// 验证值是否满足约束
    pub fn validate_value(&self, value: &str) -> Result<()> {
        // 检查空值
        if !self.allow_empty && value.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: "Empty value not allowed".to_string(),
                details: None,
            });
        }

        // 检查长度
        if let Some(min_len) = self.min_length {
            if value.len() < min_len {
                return Err(ConfigEngineError::ValidationError {
                    message: format!(
                        "Value length {} is less than minimum {}",
                        value.len(),
                        min_len
                    ),
                    details: Some(value.to_string()),
                });
            }
        }

        if let Some(max_len) = self.max_length {
            if value.len() > max_len {
                return Err(ConfigEngineError::ValidationError {
                    message: format!("Value length {} exceeds maximum {}", value.len(), max_len),
                    details: Some(value.to_string()),
                });
            }
        }

        // 检查正则表达式
        if let Some(pattern) = &self.pattern {
            let regex =
                regex::Regex::new(pattern).map_err(|e| ConfigEngineError::ValidationError {
                    message: format!("Invalid regex pattern: {}", e),
                    details: Some(pattern.clone()),
                })?;

            if !regex.is_match(value) {
                return Err(ConfigEngineError::ValidationError {
                    message: format!("Value '{}' does not match pattern '{}'", value, pattern),
                    details: None,
                });
            }
        }

        Ok(())
    }

    /// 验证值列表是否满足约束
    pub fn validate_values(&self, values: &[String]) -> Result<()> {
        // 检查值数量
        if let Some(min_values) = self.min_values {
            if values.len() < min_values {
                return Err(ConfigEngineError::ValidationError {
                    message: format!(
                        "Number of values {} is less than minimum {}",
                        values.len(),
                        min_values
                    ),
                    details: None,
                });
            }
        }

        if let Some(max_values) = self.max_values {
            if values.len() > max_values {
                return Err(ConfigEngineError::ValidationError {
                    message: format!(
                        "Number of values {} exceeds maximum {}",
                        values.len(),
                        max_values
                    ),
                    details: None,
                });
            }
        }

        // 检查重复
        if !self.allow_duplicates {
            let mut unique_values = std::collections::HashSet::new();
            for value in values {
                if !unique_values.insert(value) {
                    return Err(ConfigEngineError::ValidationError {
                        message: format!("Duplicate value not allowed: {}", value),
                        details: None,
                    });
                }
            }
        }

        // 验证每个值
        for value in values {
            self.validate_value(value)?;
        }

        Ok(())
    }
}

impl Default for TypeConstraints {
    fn default() -> Self {
        Self::new()
    }
}

/// 类型注册中心
pub struct TypeRegistry {
    /// 注册的类型信息
    types: Arc<RwLock<HashMap<String, TypeRegistration>>>,
    /// 类型依赖关系
    dependencies: Arc<RwLock<HashMap<String, Vec<String>>>>,
    /// 类型继承关系
    inheritance: Arc<RwLock<HashMap<String, String>>>,
    /// 事件监听器
    listeners: Arc<RwLock<Vec<Box<dyn TypeRegistryListener>>>>,
}

impl TypeRegistry {
    /// 创建新的类型注册中心
    pub fn new() -> Self {
        Self {
            types: Arc::new(RwLock::new(HashMap::new())),
            dependencies: Arc::new(RwLock::new(HashMap::new())),
            inheritance: Arc::new(RwLock::new(HashMap::new())),
            listeners: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 注册类型
    pub async fn register_type(
        &self,
        type_id: impl Into<String>,
        registration: TypeRegistration,
    ) -> Result<()> {
        let type_id = type_id.into();

        // 验证注册信息
        registration.validate()?;

        // 检查类型是否已存在
        {
            let types = self.types.read().await;
            if types.contains_key(&type_id) {
                return Err(ConfigEngineError::ValidationError {
                    message: format!("Type '{}' already registered", type_id),
                    details: None,
                });
            }
        }

        // 验证基础类型存在
        if let Some(base_type) = &registration.base_type {
            let types = self.types.read().await;
            if !types.contains_key(base_type) {
                return Err(ConfigEngineError::ValidationError {
                    message: format!("Base type '{}' not found", base_type),
                    details: Some(type_id.clone()),
                });
            }
        }

        // 注册类型
        {
            let mut types = self.types.write().await;
            types.insert(type_id.clone(), registration.clone());
        }

        // 更新继承关系
        if let Some(base_type) = &registration.base_type {
            let mut inheritance = self.inheritance.write().await;
            inheritance.insert(type_id.clone(), base_type.clone());
        }

        // 通知监听器
        self.notify_listeners(TypeRegistryEvent::TypeRegistered {
            type_id: type_id.clone(),
            registration: registration.clone(),
        })
        .await;

        Ok(())
    }

    /// 注销类型
    pub async fn unregister_type(&self, type_id: &str) -> Result<()> {
        // 检查依赖关系
        let dependents = self.get_dependent_types(type_id).await?;
        if !dependents.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: format!(
                    "Cannot unregister type '{}' as it has dependents: {:?}",
                    type_id, dependents
                ),
                details: None,
            });
        }

        // 移除类型
        let registration = {
            let mut types = self.types.write().await;
            types.remove(type_id)
        };

        if registration.is_none() {
            return Err(ConfigEngineError::ValidationError {
                message: format!("Type '{}' not found", type_id),
                details: None,
            });
        }

        // 移除继承关系
        {
            let mut inheritance = self.inheritance.write().await;
            inheritance.remove(type_id);
        }

        // 通知监听器
        self.notify_listeners(TypeRegistryEvent::TypeUnregistered {
            type_id: type_id.to_string(),
        })
        .await;

        Ok(())
    }

    /// 获取类型注册信息
    pub async fn get_type(&self, type_id: &str) -> Option<TypeRegistration> {
        let types = self.types.read().await;
        types.get(type_id).cloned()
    }

    /// 检查类型是否存在
    pub async fn type_exists(&self, type_id: &str) -> bool {
        let types = self.types.read().await;
        types.contains_key(type_id)
    }

    /// 验证类型值
    pub async fn validate_type_value(&self, type_id: &str, value: &str) -> Result<bool> {
        let registration =
            self.get_type(type_id)
                .await
                .ok_or_else(|| ConfigEngineError::ValidationError {
                    message: format!("Type '{}' not found", type_id),
                    details: None,
                })?;

        if !registration.enabled {
            return Err(ConfigEngineError::ValidationError {
                message: format!("Type '{}' is disabled", type_id),
                details: None,
            });
        }

        // 验证约束
        registration.constraints.validate_value(value)?;

        // 检查允许的值
        if !registration.allowed_values.is_empty() {
            if !registration.allowed_values.contains(&value.to_string()) {
                return Ok(false);
            }
        }

        // 执行验证规则（这里简化，实际应该使用规则引擎）
        for rule in &registration.validation_rules {
            if !self.evaluate_validation_rule(rule, value).await? {
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// 获取所有类型ID
    pub async fn list_types(&self) -> Vec<String> {
        let types = self.types.read().await;
        types.keys().cloned().collect()
    }

    /// 查询类型
    pub async fn query_types(&self, query: &TypeQuery) -> Vec<TypeRegistration> {
        let types = self.types.read().await;
        let mut results = Vec::new();

        for registration in types.values() {
            // 转换为TypeMetadata进行查询（简化实现）
            let metadata = TypeMetadata::new(
                registration.type_name.clone(),
                registration.type_name.clone(),
            );

            if query.matches(&metadata) {
                results.push(registration.clone());
            }
        }

        // 按优先级排序
        results.sort_by(|a, b| b.priority.cmp(&a.priority));
        results
    }

    /// 获取类型层次结构
    pub async fn get_type_hierarchy(&self) -> HashMap<String, Vec<String>> {
        let inheritance = self.inheritance.read().await;
        let mut hierarchy = HashMap::new();

        for (child, parent) in inheritance.iter() {
            hierarchy
                .entry(parent.clone())
                .or_insert_with(Vec::new)
                .push(child.clone());
        }

        hierarchy
    }

    /// 检查类型继承关系
    pub async fn is_subtype_of(&self, child_type: &str, parent_type: &str) -> bool {
        let inheritance = self.inheritance.read().await;

        let mut current = child_type;
        while let Some(parent) = inheritance.get(current) {
            if parent == parent_type {
                return true;
            }
            current = parent;
        }

        false
    }

    /// 获取依赖此类型的其他类型
    pub async fn get_dependent_types(&self, type_id: &str) -> Result<Vec<String>> {
        let inheritance = self.inheritance.read().await;
        let dependents: Vec<String> = inheritance
            .iter()
            .filter(|(_, parent)| *parent == type_id)
            .map(|(child, _)| child.clone())
            .collect();

        Ok(dependents)
    }

    /// 添加事件监听器
    pub async fn add_listener(&self, listener: Box<dyn TypeRegistryListener>) {
        let mut listeners = self.listeners.write().await;
        listeners.push(listener);
    }

    /// 通知所有监听器
    async fn notify_listeners(&self, event: TypeRegistryEvent) {
        let listeners = self.listeners.read().await;
        for listener in listeners.iter() {
            listener.on_event(&event).await;
        }
    }

    /// 简化的验证规则评估（实际应该集成规则引擎）
    async fn evaluate_validation_rule(&self, rule: &str, value: &str) -> Result<bool> {
        // 这里是简化实现，实际应该使用规则引擎
        match rule {
            "length > 0" => Ok(!value.is_empty()),
            "numeric" => Ok(value.parse::<f64>().is_ok()),
            "alphanumeric" => Ok(value.chars().all(|c| c.is_alphanumeric())),
            _ => {
                // 对于复杂规则，应该调用规则引擎
                log::warn!("Complex validation rule not implemented: {}", rule);
                Ok(true)
            }
        }
    }

    /// 获取统计信息
    pub async fn get_statistics(&self) -> TypeRegistryStatistics {
        let types = self.types.read().await;
        let inheritance = self.inheritance.read().await;

        let total_types = types.len();
        let enabled_types = types.values().filter(|t| t.enabled).count();
        let disabled_types = total_types - enabled_types;
        let types_with_inheritance = inheritance.len();

        TypeRegistryStatistics {
            total_types,
            enabled_types,
            disabled_types,
            types_with_inheritance,
            total_validation_rules: types.values().map(|t| t.validation_rules.len()).sum(),
            total_allowed_values: types.values().map(|t| t.allowed_values.len()).sum(),
        }
    }

    /// 导出类型定义
    pub async fn export_types(&self) -> Result<String> {
        let types = self.types.read().await;
        serde_json::to_string_pretty(&*types).map_err(|e| ConfigEngineError::SerializationError {
            message: format!("Failed to export types: {}", e),
            details: None,
        })
    }

    /// 导入类型定义
    pub async fn import_types(&self, json_data: &str) -> Result<usize> {
        let imported_types: HashMap<String, TypeRegistration> = serde_json::from_str(json_data)
            .map_err(|e| ConfigEngineError::SerializationError {
                message: format!("Failed to parse types JSON: {}", e),
                details: None,
            })?;

        let mut imported_count = 0;
        for (type_id, registration) in imported_types {
            if let Err(e) = self.register_type(type_id.clone(), registration).await {
                log::warn!("Failed to import type '{}': {}", type_id, e);
            } else {
                imported_count += 1;
            }
        }

        Ok(imported_count)
    }
}

impl Default for TypeRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// 类型注册表事件
#[derive(Debug, Clone)]
pub enum TypeRegistryEvent {
    TypeRegistered {
        type_id: String,
        registration: TypeRegistration,
    },
    TypeUnregistered {
        type_id: String,
    },
    TypeUpdated {
        type_id: String,
        old_registration: TypeRegistration,
        new_registration: TypeRegistration,
    },
}

/// 类型注册表事件监听器
#[async_trait]
pub trait TypeRegistryListener: Send + Sync {
    async fn on_event(&self, event: &TypeRegistryEvent);
}

/// 类型注册表统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeRegistryStatistics {
    pub total_types: usize,
    pub enabled_types: usize,
    pub disabled_types: usize,
    pub types_with_inheritance: usize,
    pub total_validation_rules: usize,
    pub total_allowed_values: usize,
}

/// 类型注册构建器
pub struct TypeRegistryBuilder {
    initial_types: Vec<(String, TypeRegistration)>,
    listeners: Vec<Box<dyn TypeRegistryListener>>,
}

impl TypeRegistryBuilder {
    pub fn new() -> Self {
        Self {
            initial_types: Vec::new(),
            listeners: Vec::new(),
        }
    }

    pub fn with_type(mut self, type_id: impl Into<String>, registration: TypeRegistration) -> Self {
        self.initial_types.push((type_id.into(), registration));
        self
    }

    pub fn with_listener(mut self, listener: Box<dyn TypeRegistryListener>) -> Self {
        self.listeners.push(listener);
        self
    }

    pub async fn build(self) -> Result<TypeRegistry> {
        let registry = TypeRegistry::new();

        // 添加监听器
        for listener in self.listeners {
            registry.add_listener(listener).await;
        }

        // 注册初始类型
        for (type_id, registration) in self.initial_types {
            registry.register_type(type_id, registration).await?;
        }

        Ok(registry)
    }
}

impl Default for TypeRegistryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_type_registration() {
        let registry = TypeRegistry::new();

        let registration = TypeRegistration::new()
            .with_type_name("Material")
            .with_description("Game material type")
            .with_allowed_values(vec!["iron_ore", "gold_ore", "rare_herb"])
            .with_validation_rule("length > 0");

        assert!(registry
            .register_type("material", registration)
            .await
            .is_ok());
        assert!(registry.type_exists("material").await);
    }

    #[tokio::test]
    async fn test_type_validation() {
        let registry = TypeRegistry::new();

        let registration = TypeRegistration::new()
            .with_type_name("Material")
            .with_allowed_values(vec!["iron_ore", "gold_ore"])
            .with_validation_rule("length > 0");

        registry
            .register_type("material", registration)
            .await
            .unwrap();

        // 有效值
        assert!(registry
            .validate_type_value("material", "iron_ore")
            .await
            .unwrap());

        // 无效值（不在允许列表中）
        assert!(!registry
            .validate_type_value("material", "invalid_material")
            .await
            .unwrap());
    }

    #[tokio::test]
    async fn test_type_inheritance() {
        let registry = TypeRegistry::new();

        // 注册基础类型
        let base_registration = TypeRegistration::new().with_type_name("BaseType");
        registry
            .register_type("base", base_registration)
            .await
            .unwrap();

        // 注册派生类型
        let derived_registration = TypeRegistration::new()
            .with_type_name("DerivedType")
            .with_base_type("base");
        registry
            .register_type("derived", derived_registration)
            .await
            .unwrap();

        assert!(registry.is_subtype_of("derived", "base").await);
        assert!(!registry.is_subtype_of("base", "derived").await);
    }

    #[tokio::test]
    async fn test_type_constraints() {
        let constraints = TypeConstraints {
            min_length: Some(3),
            max_length: Some(10),
            pattern: Some(r"^[a-z_]+$".to_string()),
            allow_empty: false,
            ..Default::default()
        };

        // 有效值
        assert!(constraints.validate_value("valid_name").is_ok());

        // 太短
        assert!(constraints.validate_value("ab").is_err());

        // 太长
        assert!(constraints
            .validate_value("very_long_name_that_exceeds_limit")
            .is_err());

        // 不匹配模式
        assert!(constraints.validate_value("Invalid-Name").is_err());

        // 空值
        assert!(constraints.validate_value("").is_err());
    }

    #[tokio::test]
    async fn test_type_query() {
        let registry = TypeRegistry::new();

        let material_reg = TypeRegistration::new()
            .with_type_name("Material")
            .with_tag("category", "resource");
        registry
            .register_type("material", material_reg)
            .await
            .unwrap();

        let tool_reg = TypeRegistration::new()
            .with_type_name("Tool")
            .with_tag("category", "equipment");
        registry.register_type("tool", tool_reg).await.unwrap();

        let query = TypeQuery::new().with_tag("category".to_string(), "resource".to_string());

        let results = registry.query_types(&query).await;
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].type_name, "Material");
    }

    #[tokio::test]
    async fn test_registry_statistics() {
        let registry = TypeRegistry::new();

        let reg1 = TypeRegistration::new()
            .with_type_name("Type1")
            .with_validation_rule("rule1")
            .with_allowed_values(vec!["val1", "val2"]);
        registry.register_type("type1", reg1).await.unwrap();

        let reg2 = TypeRegistration::new().with_type_name("Type2").disabled();
        registry.register_type("type2", reg2).await.unwrap();

        let stats = registry.get_statistics().await;
        assert_eq!(stats.total_types, 2);
        assert_eq!(stats.enabled_types, 1);
        assert_eq!(stats.disabled_types, 1);
        assert_eq!(stats.total_validation_rules, 1);
        assert_eq!(stats.total_allowed_values, 2);
    }

    #[tokio::test]
    async fn test_registry_builder() {
        let material_reg = TypeRegistration::new().with_type_name("Material");

        let registry = TypeRegistryBuilder::new()
            .with_type("material", material_reg)
            .build()
            .await
            .unwrap();

        assert!(registry.type_exists("material").await);
    }

    #[tokio::test]
    async fn test_export_import() {
        let registry = TypeRegistry::new();

        let registration = TypeRegistration::new()
            .with_type_name("TestType")
            .with_description("Test type for import/export");
        registry.register_type("test", registration).await.unwrap();

        // 导出
        let exported = registry.export_types().await.unwrap();
        assert!(exported.contains("TestType"));

        // 创建新注册表并导入
        let new_registry = TypeRegistry::new();
        let imported_count = new_registry.import_types(&exported).await.unwrap();
        assert_eq!(imported_count, 1);
        assert!(new_registry.type_exists("test").await);
    }
}
