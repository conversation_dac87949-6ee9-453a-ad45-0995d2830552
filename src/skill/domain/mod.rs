pub mod entities;
pub mod services;
/// 技能系统领域层
///
/// 基于DDD原则重构的技能系统，包含：
/// - 技能聚合（Skill Aggregate）
/// - 技能树聚合（SkillTree Aggregate）
/// - 效果值对象（Effect Value Objects）
/// - 领域服务（Domain Services）
pub mod skill_aggregate;
pub mod skill_tree_aggregate;
pub mod value_objects;

// 导出核心聚合根
pub use skill_aggregate::Skill;
pub use skill_tree_aggregate::SkillTree;

// 导出实体
pub use entities::{EffectInstance, SkillInstance, SkillNode};

// 导出领域服务
pub use services::{
    EffectCalculationService, SkillCooldownService, SkillExecutionService, SkillLevelingService,
};
