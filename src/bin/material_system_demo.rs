/// 材料系统演示程序
/// 展示材料发现、采集、存储和合成的完整流程
use game::{
    basic_definition::MaterialGrade,
    material::*,
    world_map::{
        domain::spatial::WorldLayer,
        services::{create_basic_resource_nodes, WorldMapMaterialService},
        Position,
    },
    WorldLayer,
};

fn main() {
    println!("🧪 材料系统演示程序启动");
    println!("===================================");

    // 1. 创建材料发现管理器
    println!("\n📍 1. 初始化材料发现系统");
    let mut discovery_manager = MaterialDiscoveryManager::new();

    // 添加一些基础资源点
    let herb_garden = WorldResourceNode {
        id: "herb_garden_demo".to_string(),
        name: "演示药草园".to_string(),
        position: Position::new_grid(100, 100, WorldLayer::Mortal),
        node_type: ResourceNodeType::HerbGarden,
        material_types: vec!["healing_herb".to_string(), "mana_grass".to_string()],
        refresh_cycle: 6,
        last_refresh: chrono::Utc::now(),
        status: ResourceNodeStatus::Abundant,
        environmental_modifiers: Vec::new(),
        access_requirements: Vec::new(),
    };

    discovery_manager.add_resource_node(herb_garden);
    println!("✅ 已添加演示药草园资源点");

    // 2. 进行材料搜索
    println!("\n🔍 2. 开始材料搜索");
    let search_position = Position::new_grid(100, 100, WorldLayer::Mortal);
    let discoveries = discovery_manager.search_for_materials(
        "demo_explorer".to_string(),
        search_position,
        DiscoveryMethod::ActiveSearch,
        Some(CollectionSkill::Herbalism),
    );

    println!("🎯 发现了 {} 个材料！", discoveries.len());
    for discovery in &discoveries {
        println!(
            "  - {}: {} (品质: {})",
            discovery.discovered_material.material.name,
            discovery.discovered_material.material.description,
            discovery
                .discovered_material
                .quality_condition
                .chinese_name()
        );
    }

    // 3. 创建材料存储系统
    println!("\n📦 3. 初始化材料存储系统");
    let mut warehouse = MaterialWarehouse::new(
        "demo_warehouse".to_string(),
        "演示仓库".to_string(),
        "demo_explorer".to_string(),
    );

    // 创建基础存储容器
    let basic_container = StorageContainer {
        id: "demo_container".to_string(),
        name: "演示容器".to_string(),
        container_type: ContainerType::BasicWarehouse,
        max_capacity: 1000,
        used_capacity: 0,
        status: ContainerStatus::Operational,
        maintenance_requirements: Vec::new(),
        last_maintenance: chrono::Utc::now(),
    };

    warehouse.add_container(basic_container);
    println!("✅ 已创建演示仓库容器 (1000容量)");

    // 4. 存储发现的材料
    println!("\n📥 4. 存储发现的材料");
    for discovery in &discoveries {
        let result = warehouse.store_material(
            discovery.discovered_material.material.clone(),
            discovery.discovered_material.estimated_quantity,
            Some(ContainerType::BasicWarehouse),
        );

        match result {
            Ok(instance_id) => println!(
                "✅ 已存储: {} (实例ID: {})",
                discovery.discovered_material.material.name, instance_id
            ),
            Err(e) => println!("❌ 存储失败: {}", e),
        }
    }

    // 5. 创建合成系统
    println!("\n⚗️ 5. 初始化材料合成系统");
    let mut synthesis_manager = SynthesisManager::new();

    // 创建一个简单的治疗药水配方
    let healing_potion_recipe = MaterialRecipe {
        id: "healing_potion_basic".to_string(),
        name: "基础治疗药水".to_string(),
        recipe_type: RecipeType::Alchemy,
        level: 1,
        primary_materials: vec![RecipeIngredient {
            material_id: "healing_herb".to_string(),
            quantity: 2,
            min_grade: Some(MaterialGrade::Mortal),
            required_attributes: vec![MaterialAttribute::Elemental(ElementalAttribute::Wood)],
            substitutable: false,
            substitutes: Vec::new(),
        }],
        auxiliary_materials: vec![RecipeIngredient {
            material_id: "pure_water".to_string(),
            quantity: 1,
            min_grade: None,
            required_attributes: vec![MaterialAttribute::Elemental(ElementalAttribute::Water)],
            substitutable: true,
            substitutes: vec!["spring_water".to_string()],
        }],
        catalysts: Vec::new(),
        outputs: vec![RecipeOutput {
            material_id: "healing_potion".to_string(),
            base_quantity: 1,
            quantity_variation: (0, 1),
            probability: 0.8,
            quality_factors: Vec::new(),
        }],
        base_success_rate: 0.7,
        required_skills: std::collections::HashMap::from([("alchemy".to_string(), 5)]),
        required_tools: vec!["alchemy_cauldron".to_string()],
        crafting_time: 30, // 30分钟
        special_conditions: Vec::new(),
        description: "制作基础治疗药水的配方".to_string(),
    };

    synthesis_manager.add_recipe(healing_potion_recipe);
    println!("✅ 已添加基础治疗药水配方");

    // 6. 展示材料品阶系统
    println!("\n🏆 6. 材料品阶系统演示");
    let detailed_grades = vec![
        DetailedMaterialGrade::Mortal(MortalGrade::First),
        DetailedMaterialGrade::Mortal(MortalGrade::Ninth),
        DetailedMaterialGrade::Spirit(SpiritGrade::Fifth),
        DetailedMaterialGrade::Immortal(ImmOrtalGrade::Lower),
        DetailedMaterialGrade::Divine(DivineGrade::Upper),
    ];

    for grade in detailed_grades {
        println!(
            "  - {}: 数值{}, 价值倍数{:.1}x",
            grade.chinese_name(),
            grade.numeric_value(),
            grade.value_multiplier()
        );
    }

    // 7. 材料采集技能演示
    println!("\n🎯 7. 材料采集技能演示");
    let collection_skills = vec![
        CollectionSkill::Herbalism,
        CollectionSkill::Mining,
        CollectionSkill::TreasureHunting,
        CollectionSkill::BeastHunting,
    ];

    for skill in collection_skills {
        println!("  - {}: {}", skill.chinese_name(), skill.description());
    }

    // 8. 世界地图材料集成
    println!("\n🗺️ 8. 世界地图材料集成演示");
    let mut world_service = WorldMapMaterialService::new();

    // 添加基础资源点
    for node in create_basic_resource_nodes() {
        world_service.add_resource_node(node);
    }

    let test_position = Position::new_grid(150, 120, WorldLayer::Mortal);
    let regional_info = world_service.get_regional_material_info(test_position);

    println!(
        "📍 位置 ({}, {}) 材料信息:",
        test_position.x, test_position.y
    );
    println!(
        "  - 主导属性: {:?}",
        regional_info.dominant_attributes.len()
    );
    println!("  - 丰富度: {:.2}", regional_info.estimated_abundance);
    println!("  - 危险等级: {}", regional_info.danger_level);
    println!("  - 特殊特征: {:?}", regional_info.special_features);
    println!("  - 推荐技能: {:?}", regional_info.recommended_skills.len());

    // 9. 开始探索会话
    println!("\n🚶 9. 开始材料探索会话");
    let exploration_result = world_service.start_material_exploration(
        "demo_explorer".to_string(),
        test_position,
        50,                         // 探索半径
        chrono::Duration::hours(2), // 探索时长
        vec![CollectionSkill::Herbalism, CollectionSkill::Mining],
    );

    match exploration_result {
        Ok(session_id) => {
            println!("✅ 探索会话已开始，会话ID: {}", session_id);

            if let Some(session) = world_service.get_exploration_session(&session_id) {
                println!("  - 探索区域半径: {} 单位", session.exploration_area.radius);
                println!("  - 使用技能: {:?}", session.active_skills.len());
                println!("  - 已发现材料: {} 个", session.discoveries.len());
            }
        }
        Err(e) => println!("❌ 探索会话启动失败: {}", e),
    }

    // 10. 统计信息
    println!("\n📊 10. 系统统计信息");
    println!(
        "  - 发现管理器资源点数量: {}",
        discovery_manager.resource_nodes.len()
    );
    println!(
        "  - 发现事件数量: {}",
        discovery_manager.discovery_events.len()
    );
    println!("  - 仓库容器数量: {}", warehouse.containers.len());
    println!(
        "  - 仓库容量使用率: {:.1}%",
        warehouse.get_capacity_usage() * 100.0
    );
    println!("  - 合成配方数量: {}", synthesis_manager.recipes.len());
    println!(
        "  - 世界服务活跃会话: {}",
        world_service.active_discoveries.len()
    );

    println!("\n🎉 材料系统演示完成！");
    println!("材料系统各个模块运行正常，包括:");
    println!("✅ 材料发现与资源点管理");
    println!("✅ 材料存储与容器系统");
    println!("✅ 材料合成与配方系统");
    println!("✅ 材料品阶与品质系统");
    println!("✅ 世界地图集成服务");
    println!("✅ 采集技能与工具系统");
}
