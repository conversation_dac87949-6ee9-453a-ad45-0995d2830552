/// 材料采集系统
/// 实现材料采集技能、工具需求和采集机制

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use crate::material::material_core::{Material, MaterialAttribute, ElementalAttribute};
use crate::basic_definition::MaterialGrade;

// ============================================================================
// 采集技能系统
// ============================================================================

/// 采集技能类型
#[derive(Debug, Clone, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CollectionSkill {
    /// 采药术 - 采集草药和灵植
    Herbalism,
    /// 采矿术 - 开采矿物和晶石
    Mining,
    /// 寻宝术 - 发现和采集珍稀材料
    TreasureHunting,
    /// 狩猎术 - 从灵兽获取材料
    BeastHunting,
    /// 炼制术 - 基础的材料加工
    BasicRefinement,
    /// 鉴定术 - 识别材料品质和属性
    Appraisal,
}

impl CollectionSkill {
    /// 获取技能的中文名称
    pub fn chinese_name(&self) -> &'static str {
        match self {
            CollectionSkill::Herbalism => "采药术",
            CollectionSkill::Mining => "采矿术",
            CollectionSkill::TreasureHunting => "寻宝术",
            CollectionSkill::BeastHunting => "狩猎术",
            CollectionSkill::BasicRefinement => "炼制术",
            CollectionSkill::Appraisal => "鉴定术",
        }
    }
    
    /// 获取技能描述
    pub fn description(&self) -> &'static str {
        match self {
            CollectionSkill::Herbalism => "采集天地灵草，药材品质与技能等级相关",
            CollectionSkill::Mining => "开采矿脉晶石，挖掘深度与技能等级相关",
            CollectionSkill::TreasureHunting => "寻找隐藏珍宝，发现概率与技能等级相关",
            CollectionSkill::BeastHunting => "猎取灵兽材料，成功率与技能等级相关",
            CollectionSkill::BasicRefinement => "初步加工材料，提升材料品质",
            CollectionSkill::Appraisal => "鉴定材料真伪，识别隐藏属性",
        }
    }
    
    /// 获取对应的主要材料属性
    pub fn primary_attributes(&self) -> Vec<MaterialAttribute> {
        match self {
            CollectionSkill::Herbalism => vec![
                MaterialAttribute::Elemental(ElementalAttribute::Wood),
                MaterialAttribute::Elemental(ElementalAttribute::Water),
            ],
            CollectionSkill::Mining => vec![
                MaterialAttribute::Elemental(ElementalAttribute::Earth),
                MaterialAttribute::Elemental(ElementalAttribute::Metal),
            ],
            CollectionSkill::TreasureHunting => vec![
                // 寻宝术可以发现各种属性的材料
                MaterialAttribute::Chaos,
            ],
            CollectionSkill::BeastHunting => vec![
                MaterialAttribute::Elemental(ElementalAttribute::Fire),
                MaterialAttribute::Elemental(ElementalAttribute::Earth),
            ],
            CollectionSkill::BasicRefinement => vec![
                // 炼制术可以处理各种材料
                MaterialAttribute::Chaos,
            ],
            CollectionSkill::Appraisal => vec![
                // 鉴定术可以识别所有材料
                MaterialAttribute::Chaos,
            ],
        }
    }
}

/// 采集技能等级
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SkillLevel {
    /// 技能类型
    pub skill: CollectionSkill,
    /// 当前等级 (1-100)
    pub level: u8,
    /// 当前经验值
    pub experience: u32,
    /// 升级所需经验值
    pub required_experience: u32,
}

impl SkillLevel {
    /// 创建新的技能等级
    pub fn new(skill: CollectionSkill) -> Self {
        Self {
            skill,
            level: 1,
            experience: 0,
            required_experience: 100,
        }
    }
    
    /// 增加经验值
    pub fn add_experience(&mut self, exp: u32) -> bool {
        self.experience += exp;
        
        while self.experience >= self.required_experience && self.level < 100 {
            self.experience -= self.required_experience;
            self.level += 1;
            self.required_experience = self.calculate_required_exp();
        }
        
        self.level < 100
    }
    
    /// 计算升级所需经验值
    fn calculate_required_exp(&self) -> u32 {
        // 指数增长的经验需求
        (100.0 * (1.05_f64).powi(self.level as i32 - 1)) as u32
    }
    
    /// 获取技能效能倍数
    pub fn efficiency_multiplier(&self) -> f64 {
        1.0 + (self.level as f64 - 1.0) * 0.02 // 每级提升2%效能
    }
    
    /// 获取成功率奖励
    pub fn success_rate_bonus(&self) -> f64 {
        (self.level as f64 - 1.0) * 0.005 // 每级提升0.5%成功率
    }
}

// ============================================================================
// 采集工具系统
// ============================================================================

/// 采集工具类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ToolType {
    /// 药铲 - 采集草药专用
    HerbSpade,
    /// 矿镐 - 开采矿物专用
    PickAxe,
    /// 探宝罗盘 - 寻找宝物
    TreasureCompass,
    /// 捕兽网 - 捕获灵兽
    BeastNet,
    /// 炼制炉 - 基础材料加工
    RefinementFurnace,
    /// 鉴定镜 - 识别材料
    AppraisalMirror,
}

impl ToolType {
    /// 获取工具的中文名称
    pub fn chinese_name(&self) -> &'static str {
        match self {
            ToolType::HerbSpade => "药铲",
            ToolType::PickAxe => "矿镐",
            ToolType::TreasureCompass => "探宝罗盘",
            ToolType::BeastNet => "捕兽网",
            ToolType::RefinementFurnace => "炼制炉",
            ToolType::AppraisalMirror => "鉴定镜",
        }
    }
    
    /// 获取对应的技能
    pub fn required_skill(&self) -> CollectionSkill {
        match self {
            ToolType::HerbSpade => CollectionSkill::Herbalism,
            ToolType::PickAxe => CollectionSkill::Mining,
            ToolType::TreasureCompass => CollectionSkill::TreasureHunting,
            ToolType::BeastNet => CollectionSkill::BeastHunting,
            ToolType::RefinementFurnace => CollectionSkill::BasicRefinement,
            ToolType::AppraisalMirror => CollectionSkill::Appraisal,
        }
    }
}

/// 采集工具
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct CollectionTool {
    /// 工具ID
    pub id: String,
    /// 工具名称
    pub name: String,
    /// 工具类型
    pub tool_type: ToolType,
    /// 工具品阶
    pub grade: MaterialGrade,
    /// 当前耐久度
    pub current_durability: u32,
    /// 最大耐久度
    pub max_durability: u32,
    /// 效率加成
    pub efficiency_bonus: f64,
    /// 成功率加成
    pub success_rate_bonus: f64,
    /// 特殊效果
    pub special_effects: Vec<ToolEffect>,
}

/// 工具特殊效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ToolEffect {
    /// 属性亲和 - 对特定属性材料有加成
    AttributeAffinity {
        attribute: MaterialAttribute,
        bonus: f64,
    },
    /// 品质提升 - 有概率提升获得材料的品质
    QualityEnhancement {
        chance: f64,
        grade_boost: u8,
    },
    /// 额外产出 - 有概率获得额外材料
    ExtraYield {
        chance: f64,
        multiplier: f64,
    },
    /// 耐久节约 - 减少耐久度消耗
    DurabilityConservation {
        reduction: f64,
    },
    /// 隐藏发现 - 能发现隐藏的采集点
    HiddenDiscovery {
        detection_range: f64,
    },
}

impl CollectionTool {
    /// 创建新工具
    pub fn new(
        id: String,
        name: String,
        tool_type: ToolType,
        grade: MaterialGrade,
        durability: u32,
    ) -> Self {
        let (efficiency_bonus, success_rate_bonus) = match grade {
            MaterialGrade::Mortal => (0.0, 0.0),
            MaterialGrade::Spiritual => (0.1, 0.05),
            MaterialGrade::Immortal => (0.25, 0.1),
            MaterialGrade::Divine => (0.5, 0.2),
            MaterialGrade::Sacred => (1.0, 0.3),
            MaterialGrade::Chaos => (2.0, 0.5),
        };
        
        Self {
            id,
            name,
            tool_type,
            grade,
            current_durability: durability,
            max_durability: durability,
            efficiency_bonus,
            success_rate_bonus,
            special_effects: Vec::new(),
        }
    }
    
    /// 使用工具（消耗耐久度）
    pub fn use_tool(&mut self, durability_cost: u32) -> bool {
        if self.current_durability >= durability_cost {
            self.current_durability -= durability_cost;
            true
        } else {
            false
        }
    }
    
    /// 修复工具
    pub fn repair(&mut self, amount: u32) {
        self.current_durability = (self.current_durability + amount).min(self.max_durability);
    }
    
    /// 检查工具是否可用
    pub fn is_usable(&self) -> bool {
        self.current_durability > 0
    }
    
    /// 获取工具当前状况
    pub fn condition(&self) -> ToolCondition {
        let ratio = self.current_durability as f64 / self.max_durability as f64;
        match ratio {
            r if r > 0.8 => ToolCondition::Excellent,
            r if r > 0.6 => ToolCondition::Good,
            r if r > 0.4 => ToolCondition::Fair,
            r if r > 0.2 => ToolCondition::Poor,
            r if r > 0.0 => ToolCondition::Damaged,
            _ => ToolCondition::Broken,
        }
    }
}

/// 工具状况
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ToolCondition {
    Excellent, // 完好
    Good,      // 良好
    Fair,      // 一般
    Poor,      // 差
    Damaged,   // 破损
    Broken,    // 损坏
}

impl ToolCondition {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            ToolCondition::Excellent => "完好",
            ToolCondition::Good => "良好",
            ToolCondition::Fair => "一般",
            ToolCondition::Poor => "较差",
            ToolCondition::Damaged => "破损",
            ToolCondition::Broken => "损坏",
        }
    }
    
    pub fn efficiency_modifier(&self) -> f64 {
        match self {
            ToolCondition::Excellent => 1.0,
            ToolCondition::Good => 0.9,
            ToolCondition::Fair => 0.75,
            ToolCondition::Poor => 0.6,
            ToolCondition::Damaged => 0.4,
            ToolCondition::Broken => 0.0,
        }
    }
}

// ============================================================================
// 采集系统
// ============================================================================

/// 采集者 - 进行采集活动的角色
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Collector {
    /// 角色ID
    pub id: String,
    /// 角色名称
    pub name: String,
    /// 技能等级
    pub skills: HashMap<CollectionSkill, SkillLevel>,
    /// 拥有的工具
    pub tools: HashMap<String, CollectionTool>,
    /// 当前装备的工具
    pub equipped_tools: HashMap<ToolType, String>,
    /// 采集统计
    pub statistics: CollectionStatistics,
}

/// 采集统计
#[derive(Debug, Clone, Default, PartialEq, Serialize, Deserialize)]
pub struct CollectionStatistics {
    /// 总采集次数
    pub total_attempts: u32,
    /// 成功次数
    pub successful_attempts: u32,
    /// 获得的材料总数
    pub materials_collected: u32,
    /// 稀有材料获得次数
    pub rare_materials_found: u32,
    /// 各技能使用次数
    pub skill_usage: HashMap<CollectionSkill, u32>,
}

impl Collector {
    /// 创建新采集者
    pub fn new(id: String, name: String) -> Self {
        let mut skills = HashMap::new();
        for skill in [
            CollectionSkill::Herbalism,
            CollectionSkill::Mining,
            CollectionSkill::TreasureHunting,
            CollectionSkill::BeastHunting,
            CollectionSkill::BasicRefinement,
            CollectionSkill::Appraisal,
        ] {
            skills.insert(skill, SkillLevel::new(skill));
        }
        
        Self {
            id,
            name,
            skills,
            tools: HashMap::new(),
            equipped_tools: HashMap::new(),
            statistics: CollectionStatistics::default(),
        }
    }
    
    /// 装备工具
    pub fn equip_tool(&mut self, tool_id: String) -> Result<(), String> {
        if let Some(tool) = self.tools.get(&tool_id) {
            if tool.is_usable() {
                self.equipped_tools.insert(tool.tool_type.clone(), tool_id);
                Ok(())
            } else {
                Err("工具已损坏，无法装备".to_string())
            }
        } else {
            Err("未找到指定工具".to_string())
        }
    }
    
    /// 获取装备的工具
    pub fn get_equipped_tool(&self, tool_type: ToolType) -> Option<&CollectionTool> {
        self.equipped_tools
            .get(&tool_type)
            .and_then(|tool_id| self.tools.get(tool_id))
    }
    
    /// 获取技能等级
    pub fn get_skill_level(&self, skill: CollectionSkill) -> Option<&SkillLevel> {
        self.skills.get(&skill)
    }
    
    /// 增加技能经验
    pub fn add_skill_experience(&mut self, skill: CollectionSkill, exp: u32) {
        if let Some(skill_level) = self.skills.get_mut(&skill) {
            skill_level.add_experience(exp);
        }
    }
    
    /// 计算采集成功率
    pub fn calculate_success_rate(
        &self,
        skill: CollectionSkill,
        material_grade: MaterialGrade,
        tool_type: Option<ToolType>,
    ) -> f64 {
        let mut base_rate = 0.5; // 基础成功率50%
        
        // 技能加成
        if let Some(skill_level) = self.skills.get(&skill) {
            base_rate += skill_level.success_rate_bonus();
        }
        
        // 工具加成
        if let Some(tool_type) = tool_type {
            if let Some(tool) = self.get_equipped_tool(tool_type) {
                base_rate += tool.success_rate_bonus;
                base_rate *= tool.condition().efficiency_modifier();
            }
        }
        
        // 材料品阶难度修正
        let grade_modifier = match material_grade {
            MaterialGrade::Mortal => 1.0,
            MaterialGrade::Spiritual => 0.8,
            MaterialGrade::Immortal => 0.6,
            MaterialGrade::Divine => 0.4,
            MaterialGrade::Sacred => 0.2,
            MaterialGrade::Chaos => 0.1,
        };
        
        (base_rate * grade_modifier).min(0.95).max(0.05) // 限制在5%-95%之间
    }
}

// ============================================================================
// 采集活动
// ============================================================================

/// 采集活动 - 描述一次采集行为
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct CollectionActivity {
    /// 活动ID
    pub id: String,
    /// 采集者ID
    pub collector_id: String,
    /// 使用的技能
    pub skill_used: CollectionSkill,
    /// 使用的工具类型
    pub tool_used: Option<ToolType>,
    /// 目标材料类型
    pub target_material: Option<String>,
    /// 开始时间
    pub start_time: DateTime<Utc>,
    /// 预计结束时间
    pub expected_end_time: DateTime<Utc>,
    /// 活动状态
    pub status: ActivityStatus,
}

/// 活动状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ActivityStatus {
    /// 准备中
    Preparing,
    /// 进行中
    InProgress,
    /// 已完成
    Completed,
    /// 已失败
    Failed,
    /// 已取消
    Cancelled,
}

impl ActivityStatus {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            ActivityStatus::Preparing => "准备中",
            ActivityStatus::InProgress => "进行中",
            ActivityStatus::Completed => "已完成",
            ActivityStatus::Failed => "已失败",
            ActivityStatus::Cancelled => "已取消",
        }
    }
}

/// 采集结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct CollectionResult {
    /// 是否成功
    pub success: bool,
    /// 获得的材料
    pub materials_obtained: Vec<(Material, u32)>, // (材料, 数量)
    /// 获得的经验值
    pub experience_gained: u32,
    /// 工具耐久度消耗
    pub durability_consumed: u32,
    /// 特殊事件
    pub special_events: Vec<String>,
    /// 结果描述
    pub description: String,
}

impl CollectionResult {
    /// 创建成功结果
    pub fn success(
        materials: Vec<(Material, u32)>,
        experience: u32,
        durability_cost: u32,
        description: String,
    ) -> Self {
        Self {
            success: true,
            materials_obtained: materials,
            experience_gained: experience,
            durability_consumed: durability_cost,
            special_events: Vec::new(),
            description,
        }
    }
    
    /// 创建失败结果
    pub fn failure(description: String, durability_cost: u32) -> Self {
        Self {
            success: false,
            materials_obtained: Vec::new(),
            experience_gained: 0,
            durability_consumed: durability_cost,
            special_events: Vec::new(),
            description,
        }
    }
}