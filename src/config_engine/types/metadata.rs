//! # 类型元数据模块
//!
//! 定义类型的元数据信息

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 类型元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeMetadata {
    /// 类型名称
    pub type_name: String,
    /// 类型ID
    pub type_id: String,
    /// 类型描述
    pub description: Option<String>,
    /// 类型版本
    pub version: Option<String>,
    /// 类型标签
    pub tags: HashMap<String, String>,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

impl TypeMetadata {
    pub fn new(type_name: String, type_id: String) -> Self {
        let now = chrono::Utc::now();
        Self {
            type_name,
            type_id,
            description: None,
            version: None,
            tags: HashMap::new(),
            created_at: now,
            updated_at: now,
        }
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn with_version(mut self, version: String) -> Self {
        self.version = Some(version);
        self
    }

    pub fn with_tag(mut self, key: String, value: String) -> Self {
        self.tags.insert(key, value);
        self
    }
}

/// 类型操作结果
#[derive(Debug, Clone)]
pub enum TypeOperationResult {
    Success(String),
    Error(String),
    Warning(String),
}

/// 类型查询
#[derive(Debug, Clone)]
pub struct TypeQuery {
    /// 查询名称
    pub name_pattern: Option<String>,
    /// 查询标签
    pub tags: HashMap<String, String>,
    /// 版本要求
    pub version_requirement: Option<String>,
    /// 限制结果数量
    pub limit: Option<usize>,
}

impl TypeQuery {
    pub fn new() -> Self {
        Self {
            name_pattern: None,
            tags: HashMap::new(),
            version_requirement: None,
            limit: None,
        }
    }

    pub fn with_name_pattern(mut self, pattern: String) -> Self {
        self.name_pattern = Some(pattern);
        self
    }

    pub fn with_tag(mut self, key: String, value: String) -> Self {
        self.tags.insert(key, value);
        self
    }

    pub fn with_version(mut self, version: String) -> Self {
        self.version_requirement = Some(version);
        self
    }

    pub fn with_limit(mut self, limit: usize) -> Self {
        self.limit = Some(limit);
        self
    }

    /// 检查元数据是否匹配查询条件
    pub fn matches(&self, metadata: &TypeMetadata) -> bool {
        // 检查名称模式
        if let Some(pattern) = &self.name_pattern {
            if !metadata.type_name.contains(pattern) {
                return false;
            }
        }

        // 检查标签
        for (key, value) in &self.tags {
            if metadata.tags.get(key) != Some(value) {
                return false;
            }
        }

        // 检查版本
        if let Some(required_version) = &self.version_requirement {
            if metadata.version.as_ref() != Some(required_version) {
                return false;
            }
        }

        true
    }
}

impl Default for TypeQuery {
    fn default() -> Self {
        Self::new()
    }
}
