/// 战斗相关类型定义
///
/// 此模块定义了游戏中所有与战斗、伤害、状态效果等相关的枚举和类型
use super::{identifiers::BattleUnitId, primitives::*};
use crate::battle_unit::ElementType;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::ops::Sub;
use std::time::Duration;

// ============================================================================
// 战斗枚举类型
// ============================================================================

/// 伤害类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DamageType {
    Physical,
    Fire,
    Ice,
    Lightning,
    Poison,
    Holy,
    Dark,
    Chaos,
    /// 魔法伤害（统一名称）
    Magic,
    /// 真实伤害
    True,
    /// 最大生命值百分比伤害
    PercentMaxHealth,
    /// 当前生命值百分比伤害
    PercentCurrentHealth,
    Magical {
        element: Option<String>,
    },
    Healing,
    /// 元素伤害
    Elemental(ElementType),
    /// 持续伤害
    OverTime,
    /// 反射伤害
    Reflected,
    /// 吸血伤害
    Vampiric,
    Bleed, // 流血伤害
    Burn,  // 燃烧伤害
}
impl DamageType {
    pub fn all_types() -> &'static [DamageType] {
        &[
            DamageType::Physical,
            DamageType::Fire,
            DamageType::Ice,
            DamageType::Lightning,
            DamageType::Poison,
            DamageType::Holy,
            DamageType::Dark,
            DamageType::Chaos,
            DamageType::Magic,
            DamageType::True,
            DamageType::PercentMaxHealth,
            DamageType::PercentCurrentHealth,
        ]
    }

    /// 获取伤害类型的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            DamageType::Physical => "物理",
            DamageType::Fire => "火焰",
            DamageType::Ice => "寒冰",
            DamageType::Lightning => "雷电",
            DamageType::Poison => "毒素",
            DamageType::Holy => "神圣",
            DamageType::Dark => "暗黑",
            DamageType::Chaos => "混沌",
            DamageType::Magic => "魔法",
            DamageType::True => "真实",
            DamageType::PercentMaxHealth => "最大生命值百分比",
            DamageType::PercentCurrentHealth => "当前生命值百分比",
            DamageType::Magical { element: _ } => "魔法",
            DamageType::Healing => "治疗",
            DamageType::Elemental(element) => element.display_name(),
            DamageType::OverTime => "持续伤害",
            DamageType::Reflected => "反射伤害",
            DamageType::Vampiric => "吸血伤害",
            DamageType::Bleed => "流血伤害",
            DamageType::Burn => "燃烧伤害",
        }
    }
    /// 检查是否可以被防御减免
    pub fn can_be_mitigated(&self) -> bool {
        !matches!(self, DamageType::True)
    }

    /// 检查是否为元素伤害
    pub fn is_elemental(&self) -> bool {
        matches!(self, DamageType::Elemental(_))
    }

    /// 获取元素类型（如果是元素伤害）
    pub fn element_type(&self) -> Option<ElementType> {
        match self {
            DamageType::Elemental(element) => Some(*element),
            _ => None,
        }
    }
}

/// 状态类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum StatusType {
    /// 眩晕：完全阻止行动，持续期间无法执行任何动作
    Stun,
    /// 睡眠：阻止行动直到受到伤害或持续时间结束
    Sleep,
    /// 沉默：禁止使用技能和法术，但允许普通攻击和移动
    Silence,
    /// 定身：禁止移动但允许攻击和施法
    Root,
    /// 减速：降低移动速度和攻击速度
    Slow,
    /// 加速：提高移动速度和攻击速度
    Haste,
    /// 中毒：周期性造成生命值损失
    Poison,
    /// 燃烧：周期性造成火焰伤害
    Burn,
    /// 冰冻：完全限制行动并增加冰霜伤害易伤
    Freeze,
    /// 麻痹：概率性中断动作执行
    Paralysis,
    /// 混乱：攻击可能随机选择目标
    Confusion,
    /// 魅惑：强制为施法者阵营战斗
    Charm,
    /// 恐惧：不受控制地逃离威胁源
    Fear,
    /// 致盲：大幅降低命中和侦查能力
    Blind,
    /// 缴械：无法使用武器进行攻击
    Disarm,
    /// 无敌：免疫所有伤害和负面效果
    Invulnerable,
    /// 隐形：无法被常规手段侦测
    Invisible,
    /// 护盾：吸收一定量伤害的临时保护层
    Shield,
    /// 再生：持续恢复生命值
    Regeneration,
    /// 狂暴：提高攻击力但失去敌我识别能力
    Berserk,
    // 正面效果
    /// 生命恢复
    HealthRegeneration,
    /// 法力恢复
    ManaRegeneration,
    /// 力量增强
    StrengthBoost,
    /// 敏捷增强
    AgilityBoost,
    /// 智力增强
    IntelligenceBoost,
    /// 护甲增强
    ArmorBoost,
    /// 魔法抗性增强
    MagicResistanceBoost,
    /// 速度增强
    SpeedBoost,
    /// 暴击率增强
    CriticalBoost,
    /// 闪避增强
    DodgeBoost,
    /// 无敌
    Invincible,

    // 负面效果
    /// 虚弱
    Weakness,
    /// 诅咒
    Curse,
    /// 石化
    Petrify,
    /// 嘲讽
    Taunt,
    // 特殊效果
    /// 反射
    Reflect,
    /// 吸血
    Vampiric,
    /// 专注
    Focus,
}

impl StatusType {
    pub fn is_debuff(&self) -> bool {
        matches!(
            self,
            StatusType::Stun
                | StatusType::Sleep
                | StatusType::Silence
                | StatusType::Root
                | StatusType::Slow
                | StatusType::Poison
                | StatusType::Burn
                | StatusType::Freeze
                | StatusType::Paralysis
                | StatusType::Confusion
                | StatusType::Charm
                | StatusType::Fear
                | StatusType::Blind
                | StatusType::Disarm
                | StatusType::Weakness
                | StatusType::Curse
                | StatusType::Petrify
                | StatusType::Taunt
        )
    }

    pub fn is_buff(&self) -> bool {
        matches!(
            self,
            StatusType::Haste
                | StatusType::Invulnerable
                | StatusType::Invisible
                | StatusType::Shield
                | StatusType::Regeneration
                | StatusType::HealthRegeneration
                | StatusType::ManaRegeneration
                | StatusType::StrengthBoost
                | StatusType::AgilityBoost
                | StatusType::IntelligenceBoost
                | StatusType::ArmorBoost
                | StatusType::MagicResistanceBoost
                | StatusType::SpeedBoost
                | StatusType::CriticalBoost
                | StatusType::DodgeBoost
                | StatusType::Invincible
                | StatusType::Reflect
                | StatusType::Vampiric
                | StatusType::Focus
                | StatusType::Berserk
        )
    }
    /// 检查是否为控制效果
    pub fn is_control(&self) -> bool {
        matches!(
            self,
            StatusType::Stun
                | StatusType::Root
                | StatusType::Charm
                | StatusType::Taunt
                | StatusType::Fear
                | StatusType::Sleep
                | StatusType::Paralysis
                | StatusType::Petrify
                | StatusType::Freeze
        )
    }

    /// 检查是否阻止行动
    pub fn prevents_action(&self) -> bool {
        matches!(
            self,
            StatusType::Stun
                | StatusType::Sleep
                | StatusType::Petrify
                | StatusType::Paralysis
                | StatusType::Freeze
        )
    }

    /// 检查是否阻止移动
    pub fn prevents_movement(&self) -> bool {
        matches!(
            self,
            StatusType::Root
                | StatusType::Stun
                | StatusType::Sleep
                | StatusType::Petrify
                | StatusType::Paralysis
                | StatusType::Freeze
        )
    }

    /// 获取效果优先级（数值越高优先级越高）
    pub fn priority(&self) -> u8 {
        match self {
            StatusType::Invincible => 100,
            StatusType::Petrify => 90,
            StatusType::Sleep => 85,
            StatusType::Stun => 80,
            StatusType::Freeze => 75,
            StatusType::Paralysis => 70,
            StatusType::Root => 60,
            StatusType::Silence => 50,
            StatusType::Charm => 45,
            StatusType::Fear => 40,
            StatusType::Confusion => 35,
            StatusType::Taunt => 30,
            _ => 10,
        }
    }

    /// 获取状态类型的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            StatusType::Stun => "眩晕",
            StatusType::Sleep => "睡眠",
            StatusType::Silence => "沉默",
            StatusType::Root => "定身",
            StatusType::Slow => "减速",
            StatusType::Haste => "加速",
            StatusType::Poison => "中毒",
            StatusType::Burn => "燃烧",
            StatusType::Freeze => "冰冻",
            StatusType::Paralysis => "麻痹",
            StatusType::Confusion => "混乱",
            StatusType::Charm => "魅惑",
            StatusType::Fear => "恐惧",
            StatusType::Blind => "致盲",
            StatusType::Disarm => "缴械",
            StatusType::Invulnerable => "无敌",
            StatusType::Invisible => "隐身",
            StatusType::Shield => "护盾",
            StatusType::Regeneration => "再生",
            StatusType::Berserk => "狂暴",
            StatusType::HealthRegeneration => "生命恢复",
            StatusType::ManaRegeneration => "法力恢复",
            StatusType::StrengthBoost => "力量增强",
            StatusType::AgilityBoost => "敏捷增强",
            StatusType::IntelligenceBoost => "智力增强",
            StatusType::ArmorBoost => "护甲增强",
            StatusType::MagicResistanceBoost => "魔法抗性增强",
            StatusType::SpeedBoost => "速度增强",
            StatusType::CriticalBoost => "暴击率增强",
            StatusType::DodgeBoost => "闪避增强",
            StatusType::Invincible => "无敌",
            StatusType::Weakness => "虚弱",
            StatusType::Curse => "诅咒",
            StatusType::Petrify => "石化",
            StatusType::Taunt => "嘲讽",
            StatusType::Reflect => "反射",
            StatusType::Vampiric => "吸血",
            StatusType::Focus => "专注",
        }
    }
}

/// 攻击类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AttackType {
    Melee,
    Ranged,
    Magic,
    Hybrid,
}

impl AttackType {
    /// 获取攻击类型的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            AttackType::Melee => "近战",
            AttackType::Ranged => "远程",
            AttackType::Magic => "法术",
            AttackType::Hybrid => "混合",
        }
    }
}

/// 移动类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MovementType {
    Walking,
    Flying,
    Swimming,
    Teleportation,
    Phase,
}

impl MovementType {
    /// 获取移动类型的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            MovementType::Walking => "步行",
            MovementType::Flying => "飞行",
            MovementType::Swimming => "游泳",
            MovementType::Teleportation => "瞬移",
            MovementType::Phase => "相位",
        }
    }
}

/// 行动类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ActionType {
    Move,
    Attack,
    CastSpell,
    UseItem,
    Defend,
    Wait,
}

impl ActionType {
    /// 获取行动类型的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            ActionType::Move => "移动",
            ActionType::Attack => "攻击",
            ActionType::CastSpell => "施法",
            ActionType::UseItem => "使用物品",
            ActionType::Defend => "防御",
            ActionType::Wait => "等待",
        }
    }
}

/// 行动限制类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ActionRestrictionType {
    Stun,
    Silence,
    Disarm,
    Root,
    Sleep,
}

impl ActionRestrictionType {
    /// 获取行动限制类型的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            ActionRestrictionType::Stun => "眩晕",
            ActionRestrictionType::Silence => "沉默",
            ActionRestrictionType::Disarm => "缴械",
            ActionRestrictionType::Root => "定身",
            ActionRestrictionType::Sleep => "睡眠",
        }
    }
}

/// 阵营类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Faction {
    Player,
    Enemy,
    Neutral,
    Ally,
}

impl Faction {
    /// 检查是否为敌对阵营
    pub fn is_hostile_to(&self, other: &Faction) -> bool {
        match (self, other) {
            (Faction::Player, Faction::Enemy) => true,
            (Faction::Enemy, Faction::Player) => true,
            (Faction::Ally, Faction::Enemy) => true,
            (Faction::Enemy, Faction::Ally) => true,
            _ => false,
        }
    }

    /// 检查是否为友方阵营
    pub fn is_friendly_to(&self, other: &Faction) -> bool {
        match (self, other) {
            (Faction::Player, Faction::Player) => true,
            (Faction::Player, Faction::Ally) => true,
            (Faction::Ally, Faction::Player) => true,
            (Faction::Ally, Faction::Ally) => true,
            _ => false,
        }
    }

    /// 获取阵营的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            Faction::Player => "玩家",
            Faction::Enemy => "敌人",
            Faction::Neutral => "中立",
            Faction::Ally => "盟友",
        }
    }
}

/// 实体类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EntityType {
    PlayerCharacter,
    NPC,
    Monster,
    Boss,
    Summon,
    Trap,
    Environmental,
}

impl EntityType {
    /// 获取实体类型的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            EntityType::PlayerCharacter => "玩家角色",
            EntityType::NPC => "NPC",
            EntityType::Monster => "怪物",
            EntityType::Boss => "首领",
            EntityType::Summon => "召唤物",
            EntityType::Trap => "陷阱",
            EntityType::Environmental => "环境物",
        }
    }
}

/// 疲劳等级
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum FatigueLevel {
    Fresh,
    Fatigued,
    Tired,
    Exhausted,
}

impl FatigueLevel {
    /// 获取疲劳等级对应的数值
    pub fn to_numeric(&self) -> u8 {
        match self {
            FatigueLevel::Fresh => 0,
            FatigueLevel::Fatigued => 1,
            FatigueLevel::Tired => 2,
            FatigueLevel::Exhausted => 3,
        }
    }

    /// 获取疲劳等级的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            FatigueLevel::Fresh => "精神饱满",
            FatigueLevel::Fatigued => "疲倦",
            FatigueLevel::Tired => "疲惫",
            FatigueLevel::Exhausted => "精疲力竭",
        }
    }
}

// ============================================================================
// 复杂战斗数据结构
// ============================================================================
/// 状态效果结构体
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StatusEffect {
    /// 状态类型
    pub status_type: StatusType,
    /// 效果强度
    pub intensity: f32,
    /// 剩余持续时间（秒）
    pub remaining_duration: f32,
    /// 总持续时间
    pub total_duration: f32,
    /// 效果来源ID
    pub source_id: Option<BattleUnitId>,
    /// 是否可叠加
    pub stackable: bool,
    /// 叠加次数
    pub stack_count: u32,
    /// 最大叠加次数
    pub max_stacks: u32,
    /// 每层效果值
    pub effect_per_stack: f32,
    /// 附加属性
    pub properties: HashMap<String, f32>,
    pub id: StatusEffectId,
    pub duration: Duration,
    pub source: Option<BattleUnitId>,
    pub is_permanent: bool,
}

impl StatusEffect {
    pub fn new(id: StatusEffectId, status_type: StatusType, duration: Duration) -> Self {
        let duration_secs = duration.as_secs_f64() as f32;
        Self {
            id,
            status_type,
            duration,
            intensity: 1.0,
            source: None,
            is_permanent: false,
            properties: HashMap::new(),
            source_id: None,
            stackable: false,
            stack_count: 1, // 强制保持为1，与stackable=false一致
            total_duration: duration_secs,
            max_stacks: 1,
            effect_per_stack: 0.0,
            remaining_duration: duration_secs, // 初始化为总持续时间
        }
    }

    pub fn permanent(id: StatusEffectId, status_type: StatusType) -> Self {
        Self::new(id, status_type, Duration::from_secs(0))
    }

    pub fn with_intensity(mut self, intensity: f32) -> Self {
        self.intensity = intensity;
        self
    }

    pub fn with_source(mut self, source: BattleUnitId) -> Self {
        self.source = Some(source);
        self
    }

    pub fn is_expired(&self) -> bool {
        !self.is_permanent && self.duration <= Duration::from_secs(0)
    }

    pub fn update(&mut self, delta_time: f32) {
        if !self.is_permanent {
            self.duration = self.duration.sub(Duration::from_secs_f32(delta_time));
        }
    }

    /// 创建可叠加的状态效果
    pub fn new_stackable(
        status_type: StatusType,
        effect_per_stack: f32,
        duration: f32,
        max_stacks: u32,
    ) -> Self {
        Self {
            status_type,
            intensity: effect_per_stack,
            remaining_duration: duration,
            total_duration: duration,
            source_id: None,
            stackable: true,
            stack_count: 1,
            max_stacks,
            effect_per_stack,
            properties: HashMap::new(),
            id: 0,
            duration: Duration::from_secs_f32(duration),
            source: None,
            is_permanent: false,
        }
    }

    /// 增加叠加层数
    pub fn add_stack(&mut self, count: u32) -> bool {
        if self.stackable && self.stack_count < self.max_stacks {
            self.stack_count = (self.stack_count + count).min(self.max_stacks);
            self.intensity = self.effect_per_stack * self.stack_count as f32;
            true
        } else {
            false
        }
    }

    /// 减少叠加层数
    pub fn remove_stack(&mut self, count: u32) -> bool {
        if self.stack_count > count {
            self.stack_count -= count;
            self.intensity = self.effect_per_stack * self.stack_count as f32;
            false // 还有层数剩余
        } else {
            true // 应该移除整个效果
        }
    }

    /// 更新持续时间
    pub fn update_duration(&mut self, delta_time: f32) -> bool {
        self.remaining_duration -= delta_time;
        self.remaining_duration <= 0.0
    }

    /// 获取当前效果强度
    pub fn current_intensity(&self) -> f32 {
        self.intensity
    }

    /// 获取进度百分比
    pub fn progress_percentage(&self) -> f32 {
        if self.total_duration > 0.0 {
            (self.total_duration - self.remaining_duration) / self.total_duration
        } else {
            1.0
        }
    }

    /// 设置属性
    pub fn set_property(&mut self, key: String, value: f32) {
        self.properties.insert(key, value);
    }

    /// 获取属性
    pub fn get_property(&self, key: &str) -> Option<f32> {
        self.properties.get(key).copied()
    }
}

/// 行动限制
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ActionRestriction {
    pub restriction_type: ActionRestrictionType,
    pub duration: Duration,
    pub source: Option<BattleUnitId>,
}

impl ActionRestriction {
    pub fn new(restriction_type: ActionRestrictionType, duration: Duration) -> Self {
        Self {
            restriction_type,
            duration,
            source: None,
        }
    }

    pub fn with_source(mut self, source: BattleUnitId) -> Self {
        self.source = Some(source);
        self
    }

    pub fn blocks_action(&self, action_type: ActionType) -> bool {
        match self.restriction_type {
            ActionRestrictionType::Stun => true, // 眩晕阻止所有行动
            ActionRestrictionType::Silence => matches!(action_type, ActionType::CastSpell),
            ActionRestrictionType::Disarm => {
                matches!(action_type, ActionType::Attack | ActionType::UseItem)
            }
            ActionRestrictionType::Root => matches!(action_type, ActionType::Move),
            ActionRestrictionType::Sleep => true, // 睡眠阻止所有行动
        }
    }

    pub fn update(&mut self, delta_time: f32) {
        self.duration = self.duration.sub(Duration::from_secs_f32(delta_time));
    }

    pub fn is_expired(&self) -> bool {
        self.duration <= Duration::from_secs(0)
    }
}

/// 伤害信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DamageInfo {
    pub base_damage: Health,
    pub damage_type: DamageType,
    pub source: Option<BattleUnitId>,
    pub is_critical: bool,
    /// 基础伤害值
    /// 暴击倍数（如果暴击）
    pub critical_multiplier: Option<f32>,
    /// 技能ID（如果来自技能）
    pub skill_id: Option<String>,
    /// 穿透值（忽略防御的百分比）
    pub penetration: Resistance,
    /// 额外效果
    pub additional_effects: Vec<StatusEffect>,
    /// 伤害修正器
    pub modifiers: HashMap<String, f32>,
}

impl DamageInfo {
    /// 创建基础物理伤害
    pub fn physical(damage: i32) -> Self {
        Self {
            base_damage: damage,
            damage_type: DamageType::Physical,
            critical_multiplier: None,
            skill_id: None,
            penetration: 0,
            additional_effects: Vec::new(),
            modifiers: HashMap::new(),
            source: None,
            is_critical: false,
        }
    }

    /// 创建魔法伤害
    pub fn magical(damage: i32) -> Self {
        Self {
            base_damage: damage,
            damage_type: DamageType::Magical { element: None },
            critical_multiplier: None,
            skill_id: None,
            penetration: 0.0 as Resistance,
            additional_effects: Vec::new(),
            modifiers: HashMap::new(),
            source: None,
            is_critical: false,
        }
    }

    /// 创建元素伤害
    pub fn elemental(damage: i32, element: ElementType) -> Self {
        Self {
            base_damage: damage,
            damage_type: DamageType::Elemental(element),
            critical_multiplier: None,
            skill_id: None,
            penetration: 0.0 as Resistance,
            additional_effects: Vec::new(),
            modifiers: HashMap::new(),
            source: None,
            is_critical: false,
        }
    }

    /// 创建真实伤害
    pub fn true_damage(damage: i32) -> Self {
        Self {
            base_damage: damage,
            damage_type: DamageType::True,
            critical_multiplier: None,
            skill_id: None,
            penetration: 1.0 as Resistance, // 100%穿透
            additional_effects: Vec::new(),
            modifiers: HashMap::new(),
            source: None,
            is_critical: false,
        }
    }

    /// 设置暴击
    pub fn with_critical(mut self, multiplier: f32) -> Self {
        self.critical_multiplier = Some(multiplier);
        self
    }

    /// 设置来源
    pub fn with_source(mut self, source: BattleUnitId) -> Self {
        self.source = Some(source);
        self
    }

    /// 设置技能
    pub fn with_skill(mut self, skill_id: String) -> Self {
        self.skill_id = Some(skill_id);
        self
    }

    /// 设置穿透
    pub fn with_penetration(mut self, penetration: f32) -> Self {
        self.penetration = penetration.clamp(0.0, 1.0) as Resistance;
        self
    }

    /// 添加状态效果
    pub fn with_effect(mut self, effect: StatusEffect) -> Self {
        self.additional_effects.push(effect);
        self
    }

    /// 添加修正器
    pub fn with_modifier(mut self, key: String, value: f32) -> Self {
        self.modifiers.insert(key, value);
        self
    }

    /// 计算最终伤害
    pub fn calculate_final_damage(&self) -> i32 {
        let mut damage = self.base_damage as f32;

        // 应用暴击
        if let Some(crit_multiplier) = self.critical_multiplier {
            damage *= crit_multiplier;
        }

        // 应用修正器
        for (_, modifier) in &self.modifiers {
            damage *= modifier;
        }

        damage.round() as i32
    }

    /// 检查是否暴击
    pub fn is_critical(&self) -> bool {
        self.critical_multiplier.is_some()
    }
}
