//! 技能的释放范围

/// 技能作用范围（空间化预留，文字版可忽略）
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize, strum_macros::Display)]
pub enum SkillArea {
    #[serde(rename = "单体")]
    #[strum(serialize = "单体")]
    Single { target_id: u32 }, // 单体，立即可实现

    #[serde(rename = "圆形")]
    #[strum(serialize = "圆形")]
    Circle { radius: u32 }, // 圆形AOE，需空间/坐标支持

    #[serde(rename = "扇形")]
    #[strum(serialize = "扇形")]
    Sector { radius: u32, angle: u32 }, // 扇形AOE，需空间/坐标支持

    #[serde(rename = "直线")]
    #[strum(serialize = "直线")]
    Line { length: u32, width: u32 }, // 直线，需空间/坐标支持

    #[serde(rename = "连锁")]
    #[strum(serialize = "连锁")]
    Chain { max_targets: u32, range: u32 }, // 连锁效果，可指定最大目标数和连锁范围

    #[serde(rename = "锥形")]
    #[strum(serialize = "锥形")]
    Cone { radius: u32, angle: u32 }, // 锥形范围，类似扇形但更灵活

    #[serde(rename = "十字")]
    #[strum(serialize = "十字")]
    Cross { size: u32 }, // 十字形范围

    #[serde(rename = "菱形")]
    #[strum(serialize = "菱形")]
    Diamond { size: u32 }, // 菱形范围

    #[serde(rename = "随机")]
    #[strum(serialize = "随机")]
    Random { count: u32, range: u32 }, // 随机目标，指定数量和范围

    #[serde(rename = "范围")]
    #[strum(serialize = "范围")]
    AllInRange { range: u32 }, // 范围内所有目标

    #[serde(rename = "自身及相邻")]
    #[strum(serialize = "自身及相邻")]
    SelfAndAdjacent { max_targets: u32, range: u32 }, // 自身及相邻单位，可指定最大目标数和范围
}
