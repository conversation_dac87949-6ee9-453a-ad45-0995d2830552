//! # 工具模块
//!
//! 提供配置管理、开发和调试相关的工具
//!
//! ## 核心组件
//!
//! - `ConfigGenerator`: 配置文件生成器，从简化格式生成完整配置
//! - `DebugInterface`: Web调试界面，可视化规则执行和配置状态
//! - `MigrationTool`: 配置迁移工具，处理版本升级
//! - `ValidatorTools`: 验证工具集合
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::tools::{ConfigGenerator, DebugInterface};
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! // 生成配置文件
//! let generator = ConfigGenerator::new();
//! generator.generate_from_yaml("simple_config.yaml", "output/").await?;
//!
//! // 启动调试界面
//! let debug_interface = DebugInterface::new("localhost:8080").await?;
//! debug_interface.start().await?;
//! # Ok(())
//! # }
//! ```

// 这些模块将在后续阶段实现
pub mod analyzer;
pub mod config_loader;
pub mod debug;
pub mod generator;
pub mod migration;
pub mod validator_tools;

// 重新导出主要类型
pub use analyzer::{AnalysisReport, ConfigAnalyzer};
pub use config_loader::{ConfigLoader, FormatHandler};
pub use debug::{DebugInterface, DebugSession};
pub use generator::{ConfigGenerator, GenerationTemplate};
pub use migration::{MigrationPlan, MigrationTool};
pub use validator_tools::{ValidationSuite, ValidatorTools};

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 工具配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolsConfig {
    /// 调试接口配置
    pub debug_interface: DebugInterfaceConfig,
    /// 配置生成器配置
    pub generator: GeneratorConfig,
    /// 迁移工具配置
    pub migration: MigrationConfig,
    /// 验证工具配置
    pub validation: ValidationToolsConfig,
}

impl Default for ToolsConfig {
    fn default() -> Self {
        Self {
            debug_interface: DebugInterfaceConfig::default(),
            generator: GeneratorConfig::default(),
            migration: MigrationConfig::default(),
            validation: ValidationToolsConfig::default(),
        }
    }
}

/// 调试接口配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugInterfaceConfig {
    /// 启用调试接口
    pub enabled: bool,
    /// 绑定地址
    pub bind_address: String,
    /// 端口
    pub port: u16,
    /// 启用CORS
    pub enable_cors: bool,
    /// 最大会话数
    pub max_sessions: usize,
    /// 会话超时时间（秒）
    pub session_timeout_secs: u64,
    /// 启用WebSocket
    pub enable_websocket: bool,
    /// 静态文件目录
    pub static_files_dir: Option<String>,
}

impl Default for DebugInterfaceConfig {
    fn default() -> Self {
        Self {
            enabled: false, // 默认关闭
            bind_address: "127.0.0.1".to_string(),
            port: 8080,
            enable_cors: true,
            max_sessions: 10,
            session_timeout_secs: 1800, // 30分钟
            enable_websocket: true,
            static_files_dir: None,
        }
    }
}

/// 配置生成器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneratorConfig {
    /// 默认输出目录
    pub default_output_dir: String,
    /// 模板目录
    pub template_dir: String,
    /// 支持的输入格式
    pub supported_input_formats: Vec<String>,
    /// 默认输出格式
    pub default_output_format: String,
    /// 是否覆盖现有文件
    pub overwrite_existing: bool,
    /// 是否生成备份
    pub create_backup: bool,
}

impl Default for GeneratorConfig {
    fn default() -> Self {
        Self {
            default_output_dir: "./generated_configs".to_string(),
            template_dir: "./templates".to_string(),
            supported_input_formats: vec![
                "yaml".to_string(),
                "json".to_string(),
                "toml".to_string(),
            ],
            default_output_format: "json".to_string(),
            overwrite_existing: false,
            create_backup: true,
        }
    }
}

/// 迁移工具配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MigrationConfig {
    /// 迁移脚本目录
    pub migrations_dir: String,
    /// 备份目录
    pub backup_dir: String,
    /// 是否自动备份
    pub auto_backup: bool,
    /// 是否验证迁移结果
    pub validate_after_migration: bool,
    /// 迁移超时时间（秒）
    pub migration_timeout_secs: u64,
    /// 支持的版本格式
    pub version_pattern: String,
}

impl Default for MigrationConfig {
    fn default() -> Self {
        Self {
            migrations_dir: "./migrations".to_string(),
            backup_dir: "./backups".to_string(),
            auto_backup: true,
            validate_after_migration: true,
            migration_timeout_secs: 300, // 5分钟
            version_pattern: r"^\d+\.\d+\.\d+$".to_string(),
        }
    }
}

/// 验证工具配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationToolsConfig {
    /// 默认验证规则文件
    pub default_rules_file: String,
    /// 报告输出目录
    pub report_output_dir: String,
    /// 报告格式
    pub report_formats: Vec<String>,
    /// 并行验证
    pub parallel_validation: bool,
    /// 最大并行度
    pub max_parallelism: usize,
}

impl Default for ValidationToolsConfig {
    fn default() -> Self {
        Self {
            default_rules_file: "./validation_rules.json".to_string(),
            report_output_dir: "./validation_reports".to_string(),
            report_formats: vec!["json".to_string(), "html".to_string()],
            parallel_validation: true,
            max_parallelism: 4,
        }
    }
}

/// 工具执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolExecutionResult {
    /// 是否成功
    pub success: bool,
    /// 执行消息
    pub message: String,
    /// 输出文件列表
    pub output_files: Vec<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 错误信息
    pub errors: Vec<String>,
    /// 执行统计
    pub stats: ExecutionStats,
    /// 执行时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl ToolExecutionResult {
    pub fn success(message: String) -> Self {
        Self {
            success: true,
            message,
            output_files: Vec::new(),
            warnings: Vec::new(),
            errors: Vec::new(),
            stats: ExecutionStats::new(),
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn failure(message: String, errors: Vec<String>) -> Self {
        Self {
            success: false,
            message,
            output_files: Vec::new(),
            warnings: Vec::new(),
            errors,
            stats: ExecutionStats::new(),
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn with_output_file(mut self, file: String) -> Self {
        self.output_files.push(file);
        self
    }

    pub fn with_warning(mut self, warning: String) -> Self {
        self.warnings.push(warning);
        self
    }

    pub fn with_stats(mut self, stats: ExecutionStats) -> Self {
        self.stats = stats;
        self
    }
}

/// 执行统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionStats {
    /// 处理的文件数量
    pub files_processed: usize,
    /// 生成的文件数量
    pub files_generated: usize,
    /// 处理的规则数量
    pub rules_processed: usize,
    /// 处理的类型数量
    pub types_processed: usize,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    /// 内存使用峰值（字节）
    pub peak_memory_usage: Option<usize>,
}

impl ExecutionStats {
    pub fn new() -> Self {
        Self {
            files_processed: 0,
            files_generated: 0,
            rules_processed: 0,
            types_processed: 0,
            execution_time_ms: 0,
            peak_memory_usage: None,
        }
    }
}

impl Default for ExecutionStats {
    fn default() -> Self {
        Self::new()
    }
}

/// 工具接口trait
pub trait Tool: Send + Sync {
    /// 工具名称
    fn name(&self) -> &str;

    /// 工具版本
    fn version(&self) -> &str;

    /// 工具描述
    fn description(&self) -> &str;

    /// 执行工具
    fn execute(&self, args: &ToolArguments) -> crate::config_engine::Result<ToolExecutionResult>;

    /// 验证参数
    fn validate_arguments(&self, args: &ToolArguments) -> crate::config_engine::Result<()>;

    /// 获取帮助信息
    fn help(&self) -> String;
}

/// 工具参数
#[derive(Debug, Clone)]
pub struct ToolArguments {
    /// 位置参数
    pub positional: Vec<String>,
    /// 命名参数
    pub named: HashMap<String, String>,
    /// 标志参数
    pub flags: HashMap<String, bool>,
}

impl ToolArguments {
    pub fn new() -> Self {
        Self {
            positional: Vec::new(),
            named: HashMap::new(),
            flags: HashMap::new(),
        }
    }

    pub fn with_positional(mut self, arg: String) -> Self {
        self.positional.push(arg);
        self
    }

    pub fn with_named(mut self, key: String, value: String) -> Self {
        self.named.insert(key, value);
        self
    }

    pub fn with_flag(mut self, flag: String, value: bool) -> Self {
        self.flags.insert(flag, value);
        self
    }

    pub fn get_positional(&self, index: usize) -> Option<&String> {
        self.positional.get(index)
    }

    pub fn get_named(&self, key: &str) -> Option<&String> {
        self.named.get(key)
    }

    pub fn get_flag(&self, flag: &str) -> bool {
        self.flags.get(flag).copied().unwrap_or(false)
    }

    pub fn require_positional(&self, index: usize) -> crate::config_engine::Result<&String> {
        self.get_positional(index).ok_or_else(|| {
            crate::config_engine::error::ConfigEngineError::InvalidConfiguration(format!(
                "缺少位置参数 {}",
                index
            ))
        })
    }

    pub fn require_named(&self, key: &str) -> crate::config_engine::Result<&String> {
        self.get_named(key).ok_or_else(|| {
            crate::config_engine::error::ConfigEngineError::InvalidConfiguration(format!(
                "缺少参数: {}",
                key
            ))
        })
    }
}

impl Default for ToolArguments {
    fn default() -> Self {
        Self::new()
    }
}

/// 工具管理器
pub struct ToolManager {
    /// 注册的工具
    tools: HashMap<String, Box<dyn Tool>>,
    /// 工具配置
    config: ToolsConfig,
}

impl ToolManager {
    pub fn new(config: ToolsConfig) -> Self {
        Self {
            tools: HashMap::new(),
            config,
        }
    }

    /// 注册工具
    pub fn register_tool(&mut self, tool: Box<dyn Tool>) {
        let name = tool.name().to_string();
        self.tools.insert(name, tool);
    }

    /// 执行工具
    pub fn execute_tool(
        &self,
        tool_name: &str,
        args: ToolArguments,
    ) -> crate::config_engine::Result<ToolExecutionResult> {
        let tool = self.tools.get(tool_name).ok_or_else(|| {
            crate::config_engine::error::ConfigEngineError::InvalidConfiguration(format!(
                "未找到工具: {}",
                tool_name
            ))
        })?;

        tool.validate_arguments(&args)?;
        tool.execute(&args)
    }

    /// 获取工具列表
    pub fn list_tools(&self) -> Vec<&str> {
        self.tools.keys().map(|s| s.as_str()).collect()
    }

    /// 获取工具帮助
    pub fn get_help(&self, tool_name: &str) -> Option<String> {
        self.tools.get(tool_name).map(|tool| tool.help())
    }

    /// 获取所有工具的帮助信息
    pub fn get_all_help(&self) -> String {
        let mut help = String::new();
        help.push_str("可用工具:\n\n");

        for (name, tool) in &self.tools {
            help.push_str(&format!(
                "{} (v{}): {}\n",
                name,
                tool.version(),
                tool.description()
            ));
        }

        help.push_str("\n使用 'help <tool_name>' 获取具体工具的详细帮助信息。\n");
        help
    }
}

/// 文件操作工具函数
pub mod file_utils {
    use std::path::Path;
    use tokio::fs;

    /// 确保目录存在
    pub async fn ensure_dir_exists<P: AsRef<Path>>(path: P) -> crate::config_engine::Result<()> {
        let path = path.as_ref();
        if !path.exists() {
            fs::create_dir_all(path).await.map_err(|e| {
                crate::config_engine::error::ConfigEngineError::IO {
                    operation: "create_directory".to_string(),
                    path: path.to_string_lossy().to_string(),
                    source: e,
                }
            })?;
        }
        Ok(())
    }

    /// 创建备份文件
    pub async fn create_backup<P: AsRef<Path>>(
        original_path: P,
    ) -> crate::config_engine::Result<String> {
        let original = original_path.as_ref();
        let backup_path = format!(
            "{}.backup.{}",
            original.to_string_lossy(),
            chrono::Utc::now().format("%Y%m%d_%H%M%S")
        );

        fs::copy(original, &backup_path).await.map_err(|e| {
            crate::config_engine::error::ConfigEngineError::IO {
                operation: "create_backup".to_string(),
                path: original.to_string_lossy().to_string(),
                source: e,
            }
        })?;

        Ok(backup_path)
    }

    /// 验证文件扩展名
    pub fn validate_file_extension<P: AsRef<Path>>(path: P, expected_extensions: &[&str]) -> bool {
        if let Some(extension) = path.as_ref().extension().and_then(|ext| ext.to_str()) {
            expected_extensions.contains(&extension.to_lowercase().as_str())
        } else {
            false
        }
    }

    /// 获取相对路径
    pub fn get_relative_path<P: AsRef<Path>, B: AsRef<Path>>(path: P, base: B) -> Option<String> {
        path.as_ref()
            .strip_prefix(base.as_ref())
            .ok()
            .map(|p| p.to_string_lossy().to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_tools_config_default() {
        let config = ToolsConfig::default();
        assert!(!config.debug_interface.enabled);
        assert_eq!(config.debug_interface.port, 8080);
        assert_eq!(config.generator.default_output_format, "json");
        assert!(config.migration.auto_backup);
    }

    #[test]
    fn test_tool_execution_result() {
        let result = ToolExecutionResult::success("完成".to_string())
            .with_output_file("output.json".to_string())
            .with_warning("警告信息".to_string());

        assert!(result.success);
        assert_eq!(result.output_files.len(), 1);
        assert_eq!(result.warnings.len(), 1);
        assert_eq!(result.errors.len(), 0);
    }

    #[test]
    fn test_tool_arguments() {
        let args = ToolArguments::new()
            .with_positional("input.yaml".to_string())
            .with_named("output".to_string(), "output.json".to_string())
            .with_flag("verbose".to_string(), true);

        assert_eq!(args.get_positional(0), Some(&"input.yaml".to_string()));
        assert_eq!(args.get_named("output"), Some(&"output.json".to_string()));
        assert!(args.get_flag("verbose"));
        assert!(!args.get_flag("quiet"));
    }

    #[test]
    fn test_execution_stats() {
        let stats = ExecutionStats {
            files_processed: 5,
            files_generated: 3,
            rules_processed: 10,
            types_processed: 8,
            execution_time_ms: 1500,
            peak_memory_usage: Some(1024 * 1024),
        };

        assert_eq!(stats.files_processed, 5);
        assert_eq!(stats.execution_time_ms, 1500);
        assert_eq!(stats.peak_memory_usage, Some(1024 * 1024));
    }

    struct MockTool;

    impl Tool for MockTool {
        fn name(&self) -> &str {
            "mock"
        }
        fn version(&self) -> &str {
            "1.0.0"
        }
        fn description(&self) -> &str {
            "A mock tool for testing"
        }

        fn execute(
            &self,
            _args: &ToolArguments,
        ) -> crate::config_engine::Result<ToolExecutionResult> {
            Ok(ToolExecutionResult::success("Mock execution".to_string()))
        }

        fn validate_arguments(&self, _args: &ToolArguments) -> crate::config_engine::Result<()> {
            Ok(())
        }

        fn help(&self) -> String {
            "Mock tool help".to_string()
        }
    }

    #[test]
    fn test_tool_manager() {
        let config = ToolsConfig::default();
        let mut manager = ToolManager::new(config);

        manager.register_tool(Box::new(MockTool));

        let tools = manager.list_tools();
        assert!(tools.contains(&"mock"));

        let help = manager.get_help("mock");
        assert!(help.is_some());
        assert_eq!(help.unwrap(), "Mock tool help");

        let args = ToolArguments::new();
        let result = manager.execute_tool("mock", args).unwrap();
        assert!(result.success);
    }

    #[test]
    fn test_file_utils_validate_extension() {
        use super::file_utils::*;

        assert!(validate_file_extension("test.json", &["json", "yaml"]));
        assert!(validate_file_extension("test.YAML", &["json", "yaml"]));
        assert!(!validate_file_extension("test.txt", &["json", "yaml"]));
        assert!(!validate_file_extension("test", &["json", "yaml"]));
    }
}
