/// 战斗结果类型定义
/// 
/// 定义各种战斗操作的结果类型

use serde::{Serialize, Deserialize};
use crate::shared::types::*;
use super::battle_types::*;
use std::time::SystemTime;

// ============================================================================
// 伤害相关结果
// ============================================================================

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DamageInfo {
    pub base_damage: Health,
    pub damage_type: DamageType,
    pub source: Option<BattleUnitId>,
    pub is_critical: bool,
    pub penetration: Resistance,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DetailedDamageResult {
    pub original_damage: Health,
    pub final_damage: Health,
    pub damage_type: DamageType,
    pub resistance_applied: Resistance,
    pub is_critical: bool,
    pub is_fatal: bool,
    pub source: Option<BattleUnitId>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DamageRecord {
    pub amount: Health,
    pub damage_type: DamageType,
    pub source: Option<BattleUnitId>,
    pub timestamp: SystemTime,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct HealRecord {
    pub amount: Health,
    pub source: Option<BattleUnitId>,
    pub timestamp: SystemTime,
}

// ============================================================================
// 攻击相关结果
// ============================================================================

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AttackResult {
    pub hit: bool,
    pub damage_dealt: Health,
    pub is_critical: bool,
    pub target_defeated: bool,
    pub special_effects: Vec<String>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct DamageCalculation {
    pub base_damage: Health,
    pub final_damage: Health,
    pub damage_type: DamageType,
    pub critical_multiplier: f32,
    pub resistance_reduction: Resistance,
    pub penetration_applied: Resistance,
}

// ============================================================================
// 移动相关结果
// ============================================================================

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MovementResult {
    pub success: bool,
    pub distance_moved: f32,
    pub stamina_consumed: Stamina,
    pub action_points_consumed: ActionPoints,
    pub time_taken: f32,
}

// ============================================================================
// 技能相关结果
// ============================================================================

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SkillResult {
    pub success: bool,
    pub targets_affected: Vec<BattleUnitId>,
    pub damage_dealt: HashMap<BattleUnitId, Health>,
    pub healing_done: HashMap<BattleUnitId, Health>,
    pub status_effects_applied: Vec<(BattleUnitId, StatusType)>,
    pub resource_consumed: ResourceCost,
    pub cooldown_applied: Duration,
}

// ============================================================================
// 状态相关数据结构
// ============================================================================

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct VitalStatus {
    pub health: (Health, Health), // (current, max)
    pub mana: (Mana, Mana),       // (current, max)
    pub stamina: (Stamina, Stamina), // (current, max)
    pub is_alive: bool,
    pub fatigue_level: FatigueLevel,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BattleIdentity {
    pub entity_id: BattleUnitId,
    pub name: String,
    pub entity_type: EntityType,
    pub faction: Faction,
    pub level: Level,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ActionCapabilityFlags {
    pub can_move: bool,
    pub can_attack: bool,
    pub can_cast_spells: bool,
    pub can_use_items: bool,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct CompleteBattleStatus {
    pub identity: BattleIdentity,
    pub vital_status: VitalStatus,
    pub position: Position,
    pub attributes: HashMap<crate::attribute::AttributeType, AttributeValue>,
    pub capabilities: ActionCapabilityFlags,
    pub active_effects: Vec<StatusEffect>,
    pub equipment_level: u32,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct CombatPowerRating {
    pub total: u32,
    pub base_power: u32,
    pub level_multiplier: u32,
    pub equipment_bonus: u32,
    pub skill_bonus: u32,
}

// ============================================================================
// 实用工具类型和转换
// ============================================================================

use std::collections::HashMap;

impl Default for VitalStatus {
    fn default() -> Self {
        Self {
            health: (100, 100),
            mana: (50, 50),
            stamina: (100, 100),
            is_alive: true,
            fatigue_level: FatigueLevel::Fresh,
        }
    }
}

impl Default for ActionCapabilityFlags {
    fn default() -> Self {
        Self {
            can_move: true,
            can_attack: true,
            can_cast_spells: true,
            can_use_items: true,
        }
    }
}

impl Default for ResourceCost {
    fn default() -> Self {
        Self {
            mana: 0,
            stamina: 0,
            action_points: 0,
        }
    }
}

impl Default for MovementResult {
    fn default() -> Self {
        Self {
            success: false,
            distance_moved: 0.0,
            stamina_consumed: 0,
            action_points_consumed: 0,
            time_taken: 0.0,
        }
    }
}

impl Default for AttackResult {
    fn default() -> Self {
        Self {
            hit: false,
            damage_dealt: 0,
            is_critical: false,
            target_defeated: false,
            special_effects: Vec::new(),
        }
    }
}

impl Default for SkillResult {
    fn default() -> Self {
        Self {
            success: false,
            targets_affected: Vec::new(),
            damage_dealt: HashMap::new(),
            healing_done: HashMap::new(),
            status_effects_applied: Vec::new(),
            resource_consumed: ResourceCost::default(),
            cooldown_applied: 0.0,
        }
    }
}