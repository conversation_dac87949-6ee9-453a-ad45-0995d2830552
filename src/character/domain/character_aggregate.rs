/// Character聚合根
/// 
/// 重构后的Character遵循DDD原则，使用值对象和实体分离职责，
/// 确保聚合的一致性和业务规则的封装

use crate::battle_system::simplified_battle_traits::TimeAware;
use crate::character::domain::{entities::*, value_objects::*};
use crate::shared::*;
use crate::skill::buff::buff::{Buff, TriggerEffect};
use crate::attribute::attribute::{AttributeType, CoreAttribute};

use serde::{Deserialize, Serialize};

// ============================================================================
// Character聚合根
// ============================================================================

/// Character - 角色聚合根
/// 
/// 负责管理角色的完整生命周期，确保业务规则的一致性
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Character {
    // 聚合根标识
    id: CharacterId,
    
    // 基础信息 - 值对象
    name: String,
    position: Position,
    
    // 生命和法力 - 值对象
    health: HealthValue,
    mana: ManaValue,
    
    // 经验和等级 - 值对象
    experience: ExperienceValue,
    
    // 基础属性 - 值对象
    basic_attributes: BasicAttributes,
    
    // 五行灵气 - 值对象
    spiritual_energy: SpiritualEnergy,
    
    // 聚合内实体
    skill_cooldowns: SkillCooldownManager,
    buff_manager: BuffManager,
    status_manager: StatusManager,
    equipment_ref: EquipmentReference,
    skill_ref: SkillReference,
    
    // 聚合版本（用于并发控制）
    version: u64,
}

impl Character {
    /// 创建新角色
    pub fn create(
        id: CharacterId,
        name: String,
        initial_position: Position,
    ) -> GameResult<Self> {
        // 验证输入
        if name.trim().is_empty() {
            return Err(GameError::validation_error("name", "角色名称不能为空"));
        }
        
        if name.len() > 50 {
            return Err(GameError::validation_error("name", "角色名称不能超过50个字符"));
        }
        
        // 创建默认值对象
        let basic_attributes = BasicAttributes::default();
        let health = HealthValue::new(100 + basic_attributes.health_bonus())?;
        let mana = ManaValue::new(50 + basic_attributes.mana_bonus())?;
        let experience = ExperienceValue::default();
        let spiritual_energy = SpiritualEnergy::default();
        
        Ok(Self {
            id,
            name,
            position: initial_position,
            health,
            mana,
            experience,
            basic_attributes,
            spiritual_energy,
            skill_cooldowns: SkillCooldownManager::default(),
            buff_manager: BuffManager::default(),
            status_manager: StatusManager::default(),
            equipment_ref: EquipmentReference::default(),
            skill_ref: SkillReference::default(),
            version: 1,
        })
    }
    
    // ============================================================================
    // 基础信息访问
    // ============================================================================
    
    /// 获取角色ID
    pub fn id(&self) -> CharacterId {
        self.id
    }
    
    /// 获取角色名称
    pub fn name(&self) -> &str {
        &self.name
    }
    
    /// 获取当前位置
    pub fn position(&self) -> Position {
        self.position
    }
    
    /// 获取等级
    pub fn level(&self) -> Level {
        self.experience.level()
    }
    
    /// 获取当前经验
    pub fn experience(&self) -> Exp {
        self.experience.current_exp()
    }
    
    /// 获取聚合版本
    pub fn version(&self) -> u64 {
        self.version
    }

    /// 获取装备引用
    pub fn equipment(&self) -> &EquipmentReference {
        &self.equipment_ref
    }
    
    // ============================================================================
    // 生命值和法力值操作
    // ============================================================================
    
    /// 获取当前生命值
    pub fn current_health(&self) -> Health {
        self.health.current()
    }
    
    /// 获取最大生命值
    pub fn max_health(&self) -> Health {
        let base_max = self.health.maximum();
        let bonus = self.basic_attributes.health_bonus();
        let buff_modifier = self.buff_manager.calculate_attribute_modifiers();
        buff_modifier.apply_to_attack(base_max + bonus) // 复用应用逻辑
    }
    
    /// 获取当前法力值
    pub fn current_mana(&self) -> Mana {
        self.mana.current()
    }
    
    /// 获取最大法力值
    pub fn max_mana(&self) -> Mana {
        let base_max = self.mana.maximum();
        let bonus = self.basic_attributes.mana_bonus();
        let buff_modifier = self.buff_manager.calculate_attribute_modifiers();
        base_max + bonus + buff_modifier.mana_modifier
    }
    
    /// 是否存活
    pub fn is_alive(&self) -> bool {
        self.health.is_alive()
    }
    
    /// 受到伤害
    pub fn take_damage(&mut self, damage: Health) -> GameResult<DamageResult> {
        if damage <= 0 {
            return Ok(DamageResult::new(0, self.health.current()));
        }
        
        // 检查是否能受到伤害
        if self.status_manager.has_status("invulnerable") {
            return Ok(DamageResult::new(0, self.health.current()));
        }
        
        let old_health = self.health.current();
        self.health = self.health.take_damage(damage)?;
        let actual_damage = old_health - self.health.current();
        
        // 增加版本号
        self.version += 1;
        
        // 如果死亡，触发死亡逻辑
        if !self.is_alive() {
            self.on_death()?;
        }
        
        Ok(DamageResult::new(actual_damage, self.health.current()))
    }
    
    /// 恢复生命值
    pub fn heal(&mut self, amount: Health) -> GameResult<HealResult> {
        if amount <= 0 {
            return Ok(HealResult::new(0, self.health.current()));
        }
        
        let old_health = self.health.current();
        self.health = self.health.heal(amount)?;
        let actual_heal = self.health.current() - old_health;
        
        // 增加版本号
        self.version += 1;
        
        Ok(HealResult::new(actual_heal, self.health.current()))
    }
    
    /// 消耗法力
    pub fn consume_mana(&mut self, amount: Mana) -> GameResult<()> {
        self.mana = self.mana.consume(amount)?;
        self.version += 1;
        Ok(())
    }
    
    /// 恢复法力
    pub fn restore_mana(&mut self, amount: Mana) -> GameResult<()> {
        self.mana = self.mana.restore(amount)?;
        self.version += 1;
        Ok(())
    }
    
    // ============================================================================
    // 经验和升级
    // ============================================================================
    
    /// 获得经验
    pub fn gain_experience(&mut self, amount: Exp) -> GameResult<ExperienceGainResult> {
        let (new_experience, level_ups) = self.experience.add_exp(amount)?;
        self.experience = new_experience;
        
        // 处理升级
        let mut total_attribute_points = 0;
        for level_up in &level_ups {
            total_attribute_points += level_up.gained_points;
            // 升级时恢复生命值和法力值
            self.health = self.health.set_maximum(self.calculate_max_health())?;
            self.mana = self.mana.set_maximum(self.calculate_max_mana())?;
        }
        
        // 增加版本号
        if !level_ups.is_empty() {
            self.version += 1;
        }
        
        Ok(ExperienceGainResult {
            gained_exp: amount,
            level_ups,
            total_attribute_points,
        })
    }
    
    /// 分配属性点
    pub fn allocate_attribute_point(&mut self, attribute: AttributeType, amount: Attr) -> GameResult<()> {
        match attribute {
            AttributeType::Base(CoreAttribute::Earth) => {
                // 土对应体质
                self.basic_attributes = self.basic_attributes.add_constitution(amount);
            },
            AttributeType::Base(CoreAttribute::Metal) => {
                // 金对应力量
                self.basic_attributes = self.basic_attributes.add_strength(amount);
            },
            AttributeType::Base(CoreAttribute::Water) => {
                // 水对应精神
                self.basic_attributes = self.basic_attributes.add_spirit(amount);
            },
            _ => {
                return Err(GameError::validation_error("attribute", "无效的属性类型"));
            }
        }
        
        // 更新最大生命值和法力值
        self.health = self.health.set_maximum(self.calculate_max_health())?;
        self.mana = self.mana.set_maximum(self.calculate_max_mana())?;
        
        self.version += 1;
        Ok(())
    }
    
    // ============================================================================
    // 位置操作
    // ============================================================================
    
    /// 移动到新位置
    pub fn move_to(&mut self, new_position: Position) -> GameResult<()> {
        // 检查是否能移动
        if !self.status_manager.can_move() {
            return Err(GameError::validation_error("movement", "当前状态无法移动"));
        }
        
        // 计算移动距离
        let distance = self.position.distance_to(&new_position);
        let max_distance = self.calculate_movement_range();
        
        if distance > max_distance {
            return Err(GameError::MovementTooFar {
                max_distance,
                attempted_distance: distance,
            });
        }
        
        self.position = new_position;
        self.version += 1;
        Ok(())
    }
    
    // ============================================================================
    // 五行灵气操作
    // ============================================================================
    
    /// 修炼获得灵气
    pub fn cultivate_essence(&mut self, attribute: AttributeType, amount: f64) -> GameResult<()> {
        self.spiritual_energy = self.spiritual_energy.add_essence(attribute, amount)?;
        self.version += 1;
        Ok(())
    }
    
    /// 获取灵气值
    pub fn get_essence(&self, attribute: &AttributeType) -> f64 {
        self.spiritual_energy.get_essence(attribute)
    }
    
    /// 获取主要属性
    pub fn primary_spiritual_attribute(&self) -> Option<AttributeType> {
        self.spiritual_energy.primary_attribute()
    }
    
    // ============================================================================
    // 技能和状态操作
    // ============================================================================
    
    /// 学习技能
    pub fn learn_skill(&mut self, skill_id: SkillId) -> GameResult<()> {
        self.skill_ref.learn_skill(skill_id)?;
        self.version += 1;
        Ok(())
    }
    
    /// 使用技能
    pub fn use_skill(&mut self, skill_id: SkillId, mana_cost: Mana, cooldown: CooldownTime) -> GameResult<()> {
        // 检查技能是否学会
        if !self.skill_ref.has_learned(&skill_id) {
            return Err(GameError::skill_not_found_error(skill_id.0.to_string()));
        }
        
        // 检查是否能施法
        if !self.status_manager.can_cast() {
            return Err(GameError::validation_error("cast", "当前状态无法施法"));
        }
        
        // 检查冷却
        if !self.skill_cooldowns.is_skill_ready(&skill_id) {
            let remaining = self.skill_cooldowns.get_cooldown(&skill_id);
            return Err(GameError::skill_on_cooldown_error(skill_id.0.to_string(), remaining));
        }
        
        // 检查法力
        if !self.mana.has_enough(mana_cost) {
            return Err(GameError::insufficient_mana_error(mana_cost, self.mana.current()));
        }
        
        // 消耗法力
        self.mana = self.mana.consume(mana_cost)?;
        
        // 设置冷却
        self.skill_cooldowns.set_cooldown(skill_id, cooldown)?;
        
        self.version += 1;
        Ok(())
    }
    
    /// 添加Buff
    pub fn add_buff(&mut self, buff: Buff) -> GameResult<()> {
        self.buff_manager.add_buff(buff)?;
        self.version += 1;
        Ok(())
    }
    
    /// 添加状态
    pub fn add_status(&mut self, status: String) -> GameResult<()> {
        self.status_manager.add_status(status)?;
        self.version += 1;
        Ok(())
    }

    /// 移除状态
    pub fn remove_status(&mut self, status: &str) -> GameResult<bool> {
        let removed = self.status_manager.remove_status(status);
        if removed {
            self.version += 1;
        }
        Ok(removed)
    }

    // ============================================================================
    // 状态查询
    // ============================================================================
    
    /// 是否能移动
    pub fn can_move(&self) -> bool {
        self.status_manager.can_move()
    }

    /// 是否能攻击
    pub fn can_attack(&self) -> bool {
        self.status_manager.can_attack()
    }

    /// 是否能施法
    pub fn can_cast(&self) -> bool {
        self.status_manager.can_cast()
    }

    // ============================================================================
    // 内部管理器访问（用于兼容旧trait，应逐步移除）
    // ============================================================================

    pub fn skill_cooldowns(&self) -> &SkillCooldownManager {
        &self.skill_cooldowns
    }

    pub fn skill_cooldowns_mut(&mut self) -> &mut SkillCooldownManager {
        &mut self.skill_cooldowns
    }

    pub fn buff_manager(&self) -> &BuffManager {
        &self.buff_manager
    }

    pub fn buff_manager_mut(&mut self) -> &mut BuffManager {
        &mut self.buff_manager
    }

    pub fn skill_ref(&self) -> &SkillReference {
        &self.skill_ref
    }


    pub fn status_manager(&self) -> &StatusManager {
        &self.status_manager
    }
    
    // ============================================================================
    // 战斗属性计算
    // ============================================================================
    
    /// 计算攻击力
    pub fn calculate_attack(&self) -> Attack {
        let base_attack = self.basic_attributes.physical_attack();
        let modifiers = self.buff_manager.calculate_attribute_modifiers();
        modifiers.apply_to_attack(base_attack)
    }
    
    /// 计算防御力
    pub fn calculate_defense(&self) -> Defense {
        let base_defense = self.basic_attributes.constitution() as Defense;
        let modifiers = self.buff_manager.calculate_attribute_modifiers();
        modifiers.apply_to_defense(base_defense)
    }
    
    /// 计算移动速度
    pub fn calculate_speed(&self) -> Speed {
        let base_speed = 5.0; // 基础速度
        let modifiers = self.buff_manager.calculate_attribute_modifiers();
        modifiers.apply_to_speed(base_speed)
    }
    
    // ============================================================================
    // 游戏循环更新
    // ============================================================================
    
    /// 更新角色状态（每帧调用）
    pub fn update(&mut self, delta_time: f32) -> GameResult<()> {
        // 更新技能冷却
        self.skill_cooldowns.update_cooldowns(delta_time);

        // 更新Buff持续时间
        self.buff_manager.update_buffs(delta_time);

        // 应用Buff的持续效果 (DOT/HOT)
        let (total_damage, total_heal) = self.calculate_continuous_effects(delta_time);

        if total_damage > 0 {
            // 注意：这里应用的是无来源的直接伤害，不触发on-hit等效果
            self.take_damage(total_damage)?;
        }

        if total_heal > 0 {
            self.heal(total_heal)?;
        }

        // 移除已经过期的Buff
        self.buff_manager.remove_expired_buffs();

        Ok(())
    }
    
    // ============================================================================
    // 私有辅助方法
    // ============================================================================
    
    /// 计算最大生命值
    fn calculate_max_health(&self) -> Health {
        100 + self.basic_attributes.health_bonus()
    }
    
    /// 计算最大法力值
    fn calculate_max_mana(&self) -> Mana {
        50 + self.basic_attributes.mana_bonus()
    }
    
    /// 计算移动范围
    fn calculate_movement_range(&self) -> f32 {
        let base_range = 10.0;
        let speed_modifier = self.calculate_speed() / 5.0; // 基础速度为5
        base_range * speed_modifier
    }
    
    /// 计算持续效果
    fn calculate_continuous_effects(&self, delta_time: f32) -> (Health, Health) {
        let mut total_damage = 0.0;
        let mut total_heal = 0.0;

        for buff in self.buff_manager.get_buffs() {
            if let Some(ref effect) = buff.trigger_effect {
                match effect {
                    TriggerEffect::Damage(dps) => {
                        total_damage += dps * delta_time as f64;
                    }
                    TriggerEffect::Heal(hps) => {
                        total_heal += hps * delta_time as f64;
                    }
                    // 其他持续效果可以在这里添加
                    _ => {}
                }
            }
        }

        (total_damage as Health, total_heal as Health)
    }

    /// 死亡处理
    fn on_death(&mut self) -> GameResult<()> {
        // 清除所有buff
        self.buff_manager.clear_all_buffs();
        
        // 添加死亡状态
        self.status_manager.add_status("dead".to_string())?;
        
        Ok(())
    }
}

// ============================================================================
// 操作结果值对象
// ============================================================================

/// 伤害结果
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct DamageResult {
    pub actual_damage: Health,
    pub remaining_health: Health,
}

impl DamageResult {
    pub fn new(actual_damage: Health, remaining_health: Health) -> Self {
        Self { actual_damage, remaining_health }
    }
}

/// 治疗结果
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct HealResult {
    pub actual_heal: Health,
    pub current_health: Health,
}

impl HealResult {
    pub fn new(actual_heal: Health, current_health: Health) -> Self {
        Self { actual_heal, current_health }
    }
}

/// 经验获得结果
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct ExperienceGainResult {
    pub gained_exp: Exp,
    pub level_ups: Vec<LevelUpEvent>,
    pub total_attribute_points: u32,
}

impl TimeAware for Character {
    fn update(&mut self, delta_seconds: f32) {
        let _ = self.update(delta_seconds);
    }
}

// ============================================================================
// 构建器模式（可选）
// ============================================================================

/// Character构建器 - 提供便捷的角色创建方式
pub struct CharacterBuilder {
    id: Option<CharacterId>,
    name: Option<String>,
    position: Option<Position>,
    initial_attributes: Option<BasicAttributes>,
}

impl CharacterBuilder {
    pub fn new() -> Self {
        Self {
            id: None,
            name: None,
            position: None,
            initial_attributes: None,
        }
    }
    
    pub fn with_id(mut self, id: CharacterId) -> Self {
        self.id = Some(id);
        self
    }
    
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }
    
    pub fn with_position(mut self, position: Position) -> Self {
        self.position = Some(position);
        self
    }
    
    pub fn with_attributes(mut self, attributes: BasicAttributes) -> Self {
        self.initial_attributes = Some(attributes);
        self
    }
    
    pub fn build(self) -> GameResult<Character> {
        let id = self.id.ok_or_else(|| GameError::validation_error("id", "角色ID不能为空"))?;
        let name = self.name.ok_or_else(|| GameError::validation_error("name", "角色名称不能为空"))?;
        let position = self.position.unwrap_or_default();
        
        let mut character = Character::create(id, name, position)?;
        
        if let Some(attributes) = self.initial_attributes {
            character.basic_attributes = attributes;
            character.health = HealthValue::new(character.calculate_max_health())?;
            character.mana = ManaValue::new(character.calculate_max_mana())?;
        }
        
        Ok(character)
    }
}

impl Default for CharacterBuilder {
    fn default() -> Self {
        Self::new()
    }
}