/// 经验值系统演示程序
/// 展示经验值计算、等级提升、成长系统和成就系统的完整功能
use game::experience::*;
use std::collections::HashMap;

fn main() {
    println!("🎯 经验值和成长系统演示");
    println!("=====================================");

    // 1. 创建系统实例
    let exp_calculator = ExperienceCalculator::new();
    let level_system = LevelSystem::new();
    let growth_system = GrowthSystem::new();
    let mut achievement_system = AchievementSystem::new();

    // 2. 创建测试角色
    let character_id = 1001;
    let mut character_level = 1;
    let mut character_exp = 0;

    println!("🧙‍♂️ 创建角色 ID: {}", character_id);

    // 初始化角色成长数据
    let mut character_growth = growth_system.create_initial_character_growth(character_id);
    achievement_system.initialize_character(character_id);

    println!("📊 初始状态:");
    println!("  等级: {}", character_level);
    println!("  经验值: {}", character_exp);
    println!(
        "  灵根: {} ({}星)",
        character_growth.spiritual_root.root_type.chinese_name(),
        character_growth.spiritual_root.root_type.star_rating()
    );
    println!("  天赋: {}", character_growth.talents[0].chinese_name());
    println!("  悟性: {}", character_growth.comprehension);
    println!("  可分配属性点: {}", character_growth.free_attribute_points);

    println!("\n⚔️ 战斗经验值计算演示");
    println!("=====================================");

    // 3. 模拟战斗并计算经验值
    let character_levels = {
        let mut levels = HashMap::new();
        levels.insert(character_id, character_level);
        levels
    };

    // 普通战斗
    let simple_battle = create_simple_battle_result(character_id, 3, MonsterType::CommonBeast);
    let exp_rewards = exp_calculator.calculate_battle_exp(&simple_battle, &character_levels);

    for reward in &exp_rewards {
        println!("🏆 战斗奖励 (普通野兽 Lv.3):");
        println!("  基础经验: {}", reward.base_exp);
        println!("  等级差异奖励: {}", reward.level_diff_bonus);
        println!("  怪物类型奖励: {}", reward.monster_type_bonus);
        println!("  表现奖励: {}", reward.performance_bonus);
        println!("  团队奖励: {}", reward.team_bonus);
        println!("  🎁 总经验值: {}", reward.total_exp);

        if !reward.bonus_descriptions.is_empty() {
            println!("  奖励说明:");
            for desc in &reward.bonus_descriptions {
                println!("    • {}", desc);
            }
        }

        character_exp += reward.total_exp;
    }

    // 更新成就进度
    let completed_achievements =
        update_combat_achievement(&mut achievement_system, character_id, "普通野兽", 3);

    if !completed_achievements.is_empty() {
        println!("🏅 完成成就:");
        for achievement_id in &completed_achievements {
            if let Some(achievement) = achievement_system.achievements.get(achievement_id) {
                println!("  ⭐ {}: {}", achievement.name, achievement.description);
            }
        }
    }

    println!("\n📈 等级提升演示");
    println!("=====================================");

    // 4. 检查等级提升
    if let Some(level_up_result) =
        level_system.calculate_level_up(character_level, character_exp, 0)
    {
        character_level = level_up_result.new_level;
        character_exp = 0; // 简化处理，假设剩余经验为0

        println!("🎉 等级提升！");
        println!("  新等级: {}", level_up_result.new_level);
        println!("  新境界: {}", level_up_result.new_realm.chinese_name());
        println!(
            "  境界突破: {}",
            if level_up_result.realm_breakthrough {
                "是"
            } else {
                "否"
            }
        );
        println!("  属性成长:");
        println!("    生命值: +{}", level_up_result.attribute_growth.hp_bonus);
        println!(
            "    法力值: +{}",
            level_up_result.attribute_growth.mana_bonus
        );
        println!(
            "    攻击力: +{}",
            level_up_result.attribute_growth.attack_bonus
        );
        println!(
            "    防御力: +{}",
            level_up_result.attribute_growth.defense_bonus
        );
        println!("  获得技能点: {}", level_up_result.skill_points_gained);

        if level_up_result.realm_breakthrough {
            println!("  境界突破奖励:");
            println!(
                "    生命值: +{}",
                level_up_result.breakthrough_bonus.hp_bonus
            );
            println!(
                "    法力值: +{}",
                level_up_result.breakthrough_bonus.mana_bonus
            );
            println!(
                "    攻击力: +{}",
                level_up_result.breakthrough_bonus.attack_bonus
            );
        }

        // 处理成长事件
        let growth_result = growth_system.process_level_up_growth(
            &mut character_growth,
            level_up_result.new_level,
            level_up_result.realm_breakthrough,
        );

        if !growth_result.new_talents.is_empty() {
            println!("✨ 天赋觉醒:");
            for talent in &growth_result.new_talents {
                println!("    🌟 {}: {}", talent.chinese_name(), talent.description());
            }
        }

        if !growth_result.growth_messages.is_empty() {
            println!("  成长消息:");
            for message in &growth_result.growth_messages {
                println!("    • {}", message);
            }
        }
    }

    println!("\n🔮 高级战斗演示 (团队Boss战)");
    println!("=====================================");

    // 5. 模拟团队Boss战
    let team_battle = create_team_battle_result(vec![character_id, 1002, 1003], 15);
    let mut team_levels = HashMap::new();
    team_levels.insert(character_id, character_level);
    team_levels.insert(1002, character_level + 1);
    team_levels.insert(1003, character_level - 1);

    let boss_rewards = exp_calculator.calculate_battle_exp(&team_battle, &team_levels);

    for reward in &boss_rewards {
        if reward.character_id == character_id {
            println!("🐉 Boss战奖励 ({}级Boss):", 15);
            println!("  基础经验: {}", reward.base_exp);
            println!("  等级差异奖励: {}", reward.level_diff_bonus);
            println!("  怪物类型奖励: {}", reward.monster_type_bonus);
            println!("  表现奖励: {}", reward.performance_bonus);
            println!("  团队奖励: {}", reward.team_bonus);
            println!("  🎁 总经验值: {}", reward.total_exp);

            if !reward.bonus_descriptions.is_empty() {
                println!("  特殊奖励:");
                for desc in &reward.bonus_descriptions {
                    println!("    • {}", desc);
                }
            }
        }
    }

    println!("\n🗺️ 探索经验演示");
    println!("=====================================");

    // 6. 探索经验计算
    let exploration_reward = exp_calculator.calculate_exploration_exp(
        character_level,
        10, // 探索了10个区域
        3,  // 发现了3个秘密
        1,  // 1个稀有发现
    );

    println!("🧭 探索奖励:");
    println!("  基础经验: {}", exploration_reward.base_exp);
    println!("  探索奖励: {}", exploration_reward.performance_bonus);
    println!("  特殊发现奖励: {}", exploration_reward.team_bonus);
    println!("  🎁 总经验值: {}", exploration_reward.total_exp);

    if !exploration_reward.bonus_descriptions.is_empty() {
        println!("  详细说明:");
        for desc in &exploration_reward.bonus_descriptions {
            println!("    • {}", desc);
        }
    }

    println!("\n🏆 成就系统演示");
    println!("=====================================");

    // 7. 查看成就进度
    if let Some(achievements) = achievement_system.get_character_achievements(character_id) {
        println!("📊 成就统计:");
        println!("  总完成数: {}", achievements.statistics.total_completed);
        println!("  获得称号: {}", achievements.statistics.titles_earned);

        println!("\n📋 进行中的成就:");
        for (_, progress) in &achievements.progress {
            if let Some(achievement) = achievement_system
                .achievements
                .get(&progress.achievement_id)
            {
                println!(
                    "  • {} ({:.1}%)",
                    achievement.name,
                    progress.progress_percentage * 100.0
                );
                println!(
                    "    进度: {:.0}/{:.0}",
                    progress.current_value, progress.target_value
                );
            }
        }

        println!("\n🎖️ 已完成的成就:");
        for (achievement_id, completed) in &achievements.completed_achievements {
            if let Some(achievement) = achievement_system.achievements.get(achievement_id) {
                println!(
                    "  ⭐ {} ({})",
                    achievement.name,
                    achievement.rarity.chinese_name()
                );
                println!(
                    "    完成时间: {}",
                    completed.completed_at.format("%Y-%m-%d %H:%M")
                );
                if let Some(title) = &achievement.rewards.title {
                    println!("    获得称号: {}", title);
                }
            }
        }
    }

    println!("\n🎨 属性分配演示");
    println!("=====================================");

    // 8. 属性分配
    println!(
        "📊 当前可分配属性点: {}",
        character_growth.free_attribute_points
    );

    if character_growth.free_attribute_points >= 10 {
        match growth_system.allocate_attribute_points(&mut character_growth, 3, 3, 2, 2) {
            Ok(bonus) => {
                println!("✅ 属性分配成功!");
                println!("  生命值增加: {}", bonus.hp_bonus);
                println!("  法力值增加: {}", bonus.mana_bonus);
                println!("  攻击力增加: {}", bonus.attack_bonus);
                println!("  防御力增加: {}", bonus.defense_bonus);
                println!("  剩余属性点: {}", character_growth.free_attribute_points);
            }
            Err(error) => {
                println!("❌ 属性分配失败: {}", error);
            }
        }
    }

    println!("\n📚 等级系统信息查询");
    println!("=====================================");

    // 9. 等级系统信息查询
    let (_current_realm, realm_description) = level_system.get_realm_info(character_level);
    println!("🏮 当前境界: {}", realm_description);

    if let Some(exp_to_next) =
        level_system.calculate_exp_to_next_level(character_level, character_exp)
    {
        println!("📈 升级所需经验: {}", exp_to_next);
    }

    if let Some((next_realm, exp_to_realm)) =
        level_system.calculate_exp_to_next_realm(character_level, character_exp)
    {
        println!("🚀 下个境界: {}", next_realm.chinese_name());
        println!("🎯 境界突破所需经验: {}", exp_to_realm);
    }

    println!("\n🌟 天赋系统演示");
    println!("=====================================");

    // 10. 天赋系统展示
    println!("🎭 当前拥有的天赋:");
    for (i, talent) in character_growth.talents.iter().enumerate() {
        println!(
            "  {}. {} ({}星)",
            i + 1,
            talent.chinese_name(),
            talent.rarity()
        );
        println!("     {}", talent.description());
    }

    // 生成随机天赋演示
    println!("\n🎲 随机天赋生成演示:");
    for i in 1..=5 {
        let random_talent = growth_system.generate_random_talent();
        println!(
            "  {}. {} ({}星)",
            i,
            random_talent.chinese_name(),
            random_talent.rarity()
        );
    }

    println!("\n🌱 灵根系统演示");
    println!("=====================================");

    // 11. 灵根系统展示
    println!(
        "🌿 当前灵根: {} ({}星)",
        character_growth.spiritual_root.root_type.chinese_name(),
        character_growth.spiritual_root.root_type.star_rating()
    );
    println!(
        "⚡ 修炼速度倍数: {:.1}x",
        character_growth
            .spiritual_root
            .root_type
            .cultivation_speed_multiplier()
    );

    println!("🔥 元素亲和度:");
    for (element, affinity) in &character_growth.spiritual_root.elemental_affinities {
        println!("  {}: {:.2}", element.chinese_name(), affinity);
    }

    // 生成随机灵根演示
    println!("\n🎲 随机灵根生成演示:");
    for i in 1..=3 {
        let random_root = growth_system.generate_random_spiritual_root();
        println!(
            "  {}. {} ({}星)",
            i,
            random_root.root_type.chinese_name(),
            random_root.root_type.star_rating()
        );
    }

    println!("\n🎊 系统演示完成!");
    println!("=====================================");
    println!("经验值和成长系统已成功运行，展示了以下功能:");
    println!("✅ 经验值计算 (战斗、探索、奖励机制)");
    println!("✅ 等级提升系统 (境界突破、属性成长)");
    println!("✅ 成长系统 (天赋觉醒、灵根系统、属性分配)");
    println!("✅ 成就系统 (进度跟踪、奖励发放、称号系统)");
    println!("✅ 修仙世界观 (境界、灵根、天赋、五行元素)");
    println!("\n系统设计完整，功能丰富，符合修仙游戏的世界观设定！");
}
