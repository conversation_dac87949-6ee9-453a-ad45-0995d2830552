//! # 执行上下文
//!
//! 规则执行过程中的上下文管理，提供变量存储、作用域管理和状态跟踪

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use crate::ContextValue;

/// 上下文值类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ContextValue {
    /// 字符串值
    String(String),
    /// 整数值
    Int(i64),
    /// 浮点数值
    Float(f64),
    /// 布尔值
    Bool(bool),
    /// 数组值
    Array(Vec<ContextValue>),
    /// 对象值
    Object(HashMap<String, ContextValue>),
    /// 空值
    Null,
}

impl ContextValue {
    /// 反序列化为指定类型
    pub fn deserialize<T>(&self) -> crate::config_engine::Result<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        let json_value = serde_json::to_value(self)?;
        Ok(serde_json::from_value(json_value)?)
    }

    /// 获取字符串值
    pub fn as_string(&self) -> Option<&str> {
        match self {
            ContextValue::String(s) => Some(s),
            _ => None,
        }
    }

    /// 获取数组
    pub fn as_array(&self) -> Option<&Vec<ContextValue>> {
        match self {
            ContextValue::Array(arr) => Some(arr),
            _ => None,
        }
    }

    /// 获取对象
    pub fn as_object(&self) -> Option<&HashMap<String, ContextValue>> {
        match self {
            ContextValue::Object(obj) => Some(obj),
            _ => None,
        }
    }

    /// 检查是否为空值
    pub fn is_null(&self) -> bool {
        matches!(self, ContextValue::Null)
    }

    /// 转换为字符串
    pub fn as_str(&self) -> &str {
        match self {
            ContextValue::String(s) => s,
            _ => "",
        }
    }

    /// 转换为整数
    pub fn as_int(&self) -> Option<i64> {
        match self {
            ContextValue::Int(i) => Some(*i),
            ContextValue::Float(f) => Some(*f as i64),
            ContextValue::String(s) => s.parse().ok(),
            _ => None,
        }
    }

    /// 转换为浮点数
    pub fn as_float(&self) -> Option<f64> {
        match self {
            ContextValue::Float(f) => Some(*f),
            ContextValue::Int(i) => Some(*i as f64),
            ContextValue::String(s) => s.parse().ok(),
            _ => None,
        }
    }

    /// 转换为布尔值
    pub fn as_bool(&self) -> bool {
        match self {
            ContextValue::Bool(b) => *b,
            ContextValue::String(s) => !s.is_empty(),
            ContextValue::Int(i) => *i != 0,
            ContextValue::Float(f) => *f != 0.0,
            ContextValue::Array(arr) => !arr.is_empty(),
            ContextValue::Object(obj) => !obj.is_empty(),
            ContextValue::Null => false,
        }
    }

    /// 获取类型名称
    pub fn type_name(&self) -> &'static str {
        match self {
            ContextValue::String(_) => "string",
            ContextValue::Int(_) => "int",
            ContextValue::Float(_) => "float",
            ContextValue::Bool(_) => "bool",
            ContextValue::Array(_) => "array",
            ContextValue::Object(_) => "object",
            ContextValue::Null => "null",
        }
    }

    /// 检查是否为数值类型
    pub fn is_numeric(&self) -> bool {
        matches!(self, ContextValue::Int(_) | ContextValue::Float(_))
    }

    /// 获取数组长度
    pub fn array_len(&self) -> Option<usize> {
        match self {
            ContextValue::Array(arr) => Some(arr.len()),
            _ => None,
        }
    }

    /// 获取对象键数量
    pub fn object_len(&self) -> Option<usize> {
        match self {
            ContextValue::Object(obj) => Some(obj.len()),
            _ => None,
        }
    }
}

impl std::fmt::Display for ContextValue {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ContextValue::String(s) => write!(f, "{}", s),
            ContextValue::Int(i) => write!(f, "{}", i),
            ContextValue::Float(fl) => write!(f, "{}", fl),
            ContextValue::Bool(b) => write!(f, "{}", b),
            ContextValue::Array(arr) => {
                write!(f, "[")?;
                for (i, item) in arr.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", item)?;
                }
                write!(f, "]")
            }
            ContextValue::Object(obj) => {
                write!(f, "{{")?;
                for (i, (key, value)) in obj.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "\"{}\": {}", key, value)?;
                }
                write!(f, "}}")
            }
            ContextValue::Null => write!(f, "null"),
        }
    }
}

// 实现从常见类型到ContextValue的转换
impl From<bool> for ContextValue {
    fn from(value: bool) -> Self {
        ContextValue::Bool(value)
    }
}

impl From<i32> for ContextValue {
    fn from(value: i32) -> Self {
        ContextValue::Int(value as i64)
    }
}

impl From<i64> for ContextValue {
    fn from(value: i64) -> Self {
        ContextValue::Int(value)
    }
}

impl From<f32> for ContextValue {
    fn from(value: f32) -> Self {
        ContextValue::Float(value as f64)
    }
}

impl From<f64> for ContextValue {
    fn from(value: f64) -> Self {
        ContextValue::Float(value)
    }
}

impl From<String> for ContextValue {
    fn from(value: String) -> Self {
        ContextValue::String(value)
    }
}

impl From<&str> for ContextValue {
    fn from(value: &str) -> Self {
        ContextValue::String(value.to_string())
    }
}

impl<T> From<Vec<T>> for ContextValue
where
    T: Into<ContextValue>,
{
    fn from(value: Vec<T>) -> Self {
        ContextValue::Array(value.into_iter().map(|v| v.into()).collect())
    }
}

impl<T> From<HashMap<String, T>> for ContextValue
where
    T: Into<ContextValue>,
{
    fn from(value: HashMap<String, T>) -> Self {
        ContextValue::Object(value.into_iter().map(|(k, v)| (k, v.into())).collect())
    }
}
/// 执行上下文
///
/// 维护规则执行过程中的变量状态、执行选项和追踪信息
#[derive(Debug, Clone)]
pub struct ExecutionContext {
    /// 上下文变量
    variables: HashMap<String, ContextValue>,
    /// 作用域栈
    scope_stack: Vec<HashMap<String, ContextValue>>,
    /// 执行选项
    options: ExecutionOptions,
    /// 执行追踪
    trace: Option<ExecutionTrace>,
    /// 执行开始时间
    start_time: Option<Instant>,
    /// 超时设置
    timeout: Option<Duration>,
    /// 调试信息
    debug_info: DebugInfo,
}

impl ExecutionContext {
    /// 创建新的执行上下文
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
            scope_stack: Vec::new(),
            options: ExecutionOptions::default(),
            trace: None,
            start_time: Some(Instant::now()),
            timeout: None,
            debug_info: DebugInfo::new(),
        }
    }

    /// 从规则执行请求创建上下文
    pub fn from_request(request: RuleExecutionRequest) -> Self {
        let mut context = Self::new();
        context.variables = request.input_data;
        context.options = request.options;

        if context.options.trace_execution {
            context.enable_tracing();
        }

        context
    }

    /// 设置变量
    pub fn set(&mut self, key: String, value: ContextValue) {
        // 记录变量设置操作
        self.debug_info.variable_operations.push(VariableOperation {
            operation: "set".to_string(),
            key: key.clone(),
            old_value: self.variables.get(&key).cloned(),
            new_value: Some(value.clone()),
            timestamp: Instant::now(),
        });

        self.variables.insert(key, value);
    }

    /// 获取变量值（字符串形式）
    pub fn get(&self, key: &str) -> Option<&str> {
        self.variables.get(key).map(|v| v.as_str())
    }

    /// 获取原始变量值
    pub fn get_raw(&self, key: &str) -> Option<&ContextValue> {
        // 首先检查当前作用域
        if let Some(current_scope) = self.scope_stack.last() {
            if let Some(value) = current_scope.get(key) {
                return Some(value);
            }
        }

        // 然后检查全局变量
        self.variables.get(key)
    }

    /// 获取所有变量
    pub fn get_all_values(&self) -> HashMap<String, ContextValue> {
        let mut result = self.variables.clone();

        // 添加作用域变量
        for scope in &self.scope_stack {
            result.extend(scope.clone());
        }

        result
    }

    /// 删除变量
    pub fn remove(&mut self, key: &str) -> Option<ContextValue> {
        // 记录变量删除操作
        let old_value = self.variables.get(key).cloned();
        self.debug_info.variable_operations.push(VariableOperation {
            operation: "remove".to_string(),
            key: key.to_string(),
            old_value: old_value.clone(),
            new_value: None,
            timestamp: Instant::now(),
        });

        self.variables.remove(key)
    }

    /// 检查变量是否存在
    pub fn contains_key(&self, key: &str) -> bool {
        // 检查作用域栈
        for scope in self.scope_stack.iter().rev() {
            if scope.contains_key(key) {
                return true;
            }
        }

        // 检查全局变量
        self.variables.contains_key(key)
    }

    /// 进入新作用域
    pub fn push_scope(&mut self) {
        self.scope_stack.push(HashMap::new());
        self.debug_info.scope_operations.push(ScopeOperation {
            operation: "push".to_string(),
            scope_depth: self.scope_stack.len(),
            timestamp: Instant::now(),
        });
    }

    /// 退出当前作用域
    pub fn pop_scope(&mut self) -> Option<HashMap<String, ContextValue>> {
        let result = self.scope_stack.pop();
        self.debug_info.scope_operations.push(ScopeOperation {
            operation: "pop".to_string(),
            scope_depth: self.scope_stack.len(),
            timestamp: Instant::now(),
        });
        result
    }

    /// 在当前作用域设置变量
    pub fn set_in_scope(&mut self, key: String, value: ContextValue) {
        if let Some(current_scope) = self.scope_stack.last_mut() {
            current_scope.insert(key.clone(), value.clone());
        } else {
            // 如果没有作用域，设置为全局变量
            self.set(key, value);
        }
    }

    /// 获取当前作用域深度
    pub fn scope_depth(&self) -> usize {
        self.scope_stack.len()
    }

    /// 设置超时
    pub fn set_timeout(&mut self, timeout: Duration) {
        self.timeout = Some(timeout);
    }

    /// 检查是否超时
    pub fn is_timeout(&self) -> bool {
        if let (Some(start_time), Some(timeout)) = (self.start_time, self.timeout) {
            start_time.elapsed() > timeout
        } else {
            false
        }
    }

    /// 获取剩余时间
    pub fn remaining_time(&self) -> Option<Duration> {
        if let (Some(start_time), Some(timeout)) = (self.start_time, self.timeout) {
            let elapsed = start_time.elapsed();
            if elapsed < timeout {
                Some(timeout - elapsed)
            } else {
                Some(Duration::from_secs(0))
            }
        } else {
            None
        }
    }

    /// 启用追踪
    pub fn enable_tracing(&mut self) {
        self.trace = Some(ExecutionTrace {
            steps: Vec::new(),
            node_visits: HashMap::new(),
            condition_evaluations: Vec::new(),
        });
        self.options.trace_execution = true;
    }

    /// 禁用追踪
    pub fn disable_tracing(&mut self) {
        self.trace = None;
        self.options.trace_execution = false;
    }

    /// 检查是否启用了追踪
    pub fn is_tracing_enabled(&self) -> bool {
        self.trace.is_some()
    }

    /// 获取追踪信息
    pub fn get_trace(&self) -> Option<&ExecutionTrace> {
        self.trace.as_ref()
    }

    /// 添加追踪步骤
    pub fn add_trace_step(&mut self, step: ExecutionStep) {
        if let Some(ref mut trace) = self.trace {
            trace.steps.push(step);
        }
    }

    /// 记录节点访问
    pub fn record_node_visit(&mut self, node_id: String) {
        if let Some(ref mut trace) = self.trace {
            *trace.node_visits.entry(node_id).or_insert(0) += 1;
        }
    }

    /// 记录条件评估
    pub fn record_condition_evaluation(&mut self, evaluation: ConditionEvaluation) {
        if let Some(ref mut trace) = self.trace {
            trace.condition_evaluations.push(evaluation);
        }
    }

    /// 获取执行选项
    pub fn get_options(&self) -> &ExecutionOptions {
        &self.options
    }

    /// 设置执行选项
    pub fn set_options(&mut self, options: ExecutionOptions) {
        self.options = options;
    }

    /// 获取调试信息
    pub fn get_debug_info(&self) -> &DebugInfo {
        &self.debug_info
    }

    /// 清除调试信息
    pub fn clear_debug_info(&mut self) {
        self.debug_info = DebugInfo::new();
    }

    /// 获取执行时间
    pub fn execution_time(&self) -> Option<Duration> {
        self.start_time.map(|start| start.elapsed())
    }

    /// 重置执行时间
    pub fn reset_timer(&mut self) {
        self.start_time = Some(Instant::now());
    }

    /// 创建子上下文
    ///
    /// 子上下文继承父上下文的变量，但有独立的作用域
    pub fn create_child(&self) -> ExecutionContext {
        let mut child = ExecutionContext::new();
        child.variables = self.variables.clone();
        child.options = self.options.clone();
        child.timeout = self.timeout;
        child
    }

    /// 合并其他上下文
    ///
    /// 将另一个上下文的变量合并到当前上下文
    pub fn merge_from(&mut self, other: &ExecutionContext) {
        for (key, value) in &other.variables {
            self.set(key.clone(), value.clone());
        }
    }

    /// 导出快照
    ///
    /// 创建当前上下文状态的快照
    pub fn snapshot(&self) -> ContextSnapshot {
        ContextSnapshot {
            variables: self.variables.clone(),
            scope_stack: self.scope_stack.clone(),
            options: self.options.clone(),
            execution_time: self.execution_time(),
            scope_depth: self.scope_depth(),
        }
    }

    /// 从快照恢复
    ///
    /// 从快照恢复上下文状态
    pub fn restore_from_snapshot(&mut self, snapshot: &ContextSnapshot) {
        self.variables = snapshot.variables.clone();
        self.scope_stack = snapshot.scope_stack.clone();
        self.options = snapshot.options.clone();
    }

    /// 验证上下文完整性
    pub fn validate(&self) -> Result<(), String> {
        // 检查作用域栈一致性
        for (i, scope) in self.scope_stack.iter().enumerate() {
            if scope.is_empty() {
                return Err(format!("作用域 {} 为空", i));
            }
        }

        // 检查必需变量
        let required_vars = ["rule_id", "execution_id"];
        for var in &required_vars {
            if !self.contains_key(var) {
                log::warn!("缺少必需变量: {}", var);
            }
        }

        Ok(())
    }
}

impl Default for ExecutionContext {
    fn default() -> Self {
        Self::new()
    }
}

/// 执行选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionOptions {
    /// 启用缓存
    pub cache_enabled: bool,
    /// 启用追踪
    pub trace_execution: bool,
    /// 超时时间（毫秒）
    pub timeout_ms: Option<u64>,
    /// 最大递归深度
    pub max_recursion_depth: Option<u32>,
    /// 调试模式
    pub debug_mode: bool,
    /// 性能分析
    pub profile_execution: bool,
    /// 验证输入
    pub validate_input: bool,
    /// 验证输出
    pub validate_output: bool,
}

impl Default for ExecutionOptions {
    fn default() -> Self {
        Self {
            cache_enabled: true,
            trace_execution: false,
            timeout_ms: Some(5000), // 5秒
            max_recursion_depth: Some(100),
            debug_mode: false,
            profile_execution: false,
            validate_input: true,
            validate_output: true,
        }
    }
}

/// 执行追踪
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionTrace {
    /// 执行步骤
    pub steps: Vec<ExecutionStep>,
    /// 节点访问计数
    pub node_visits: HashMap<String, u32>,
    /// 条件评估
    pub condition_evaluations: Vec<ConditionEvaluation>,
}

impl ExecutionTrace {
    /// 获取总步骤数
    pub fn total_steps(&self) -> usize {
        self.steps.len()
    }

    /// 获取总节点访问数
    pub fn total_node_visits(&self) -> u32 {
        self.node_visits.values().sum()
    }

    /// 获取总条件评估数
    pub fn total_condition_evaluations(&self) -> usize {
        self.condition_evaluations.len()
    }

    /// 获取执行路径
    pub fn execution_path(&self) -> Vec<String> {
        self.steps.iter().map(|step| step.node_id.clone()).collect()
    }

    /// 查找最频繁访问的节点
    pub fn most_visited_node(&self) -> Option<(String, u32)> {
        self.node_visits
            .iter()
            .max_by_key(|(_, count)| *count)
            .map(|(node, count)| (node.clone(), *count))
    }

    /// 生成执行摘要
    pub fn generate_summary(&self) -> ExecutionSummary {
        ExecutionSummary {
            total_steps: self.total_steps(),
            total_node_visits: self.total_node_visits(),
            total_condition_evaluations: self.total_condition_evaluations(),
            execution_path: self.execution_path(),
            most_visited_node: self.most_visited_node(),
        }
    }
}

/// 执行步骤
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionStep {
    /// 步骤ID
    pub step_id: String,
    /// 节点ID
    pub node_id: String,
    /// 步骤类型
    pub step_type: String,
    /// 输入数据
    pub input: HashMap<String, ContextValue>,
    /// 输出数据
    pub output: HashMap<String, ContextValue>,
    /// 执行时间（微秒）
    pub duration_us: u64,
}

impl ExecutionStep {
    /// 创建新的执行步骤
    pub fn new(
        node_id: String,
        step_type: String,
        input: HashMap<String, ContextValue>,
        output: HashMap<String, ContextValue>,
        duration_us: u64,
    ) -> Self {
        Self {
            step_id: uuid::Uuid::new_v4().to_string(),
            node_id,
            step_type,
            input,
            output,
            duration_us,
        }
    }

    /// 获取步骤持续时间（毫秒）
    pub fn duration_ms(&self) -> f64 {
        self.duration_us as f64 / 1000.0
    }
}

/// 条件评估
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConditionEvaluation {
    /// 条件表达式
    pub condition: String,
    /// 评估结果
    pub result: bool,
    /// 评估时的上下文
    pub context: HashMap<String, ContextValue>,
}

/// 执行摘要
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionSummary {
    /// 总步骤数
    pub total_steps: usize,
    /// 总节点访问数
    pub total_node_visits: u32,
    /// 总条件评估数
    pub total_condition_evaluations: usize,
    /// 执行路径
    pub execution_path: Vec<String>,
    /// 最频繁访问的节点
    pub most_visited_node: Option<(String, u32)>,
}

/// 上下文快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextSnapshot {
    /// 变量状态
    pub variables: HashMap<String, ContextValue>,
    /// 作用域栈状态
    pub scope_stack: Vec<HashMap<String, ContextValue>>,
    /// 执行选项
    pub options: ExecutionOptions,
    /// 执行时间
    pub execution_time: Option<Duration>,
    /// 作用域深度
    pub scope_depth: usize,
}

/// 调试信息
#[derive(Debug, Clone)]
pub struct DebugInfo {
    /// 变量操作历史
    pub variable_operations: Vec<VariableOperation>,
    /// 作用域操作历史
    pub scope_operations: Vec<ScopeOperation>,
    /// 性能标记
    pub performance_markers: Vec<PerformanceMarker>,
}

impl DebugInfo {
    pub fn new() -> Self {
        Self {
            variable_operations: Vec::new(),
            scope_operations: Vec::new(),
            performance_markers: Vec::new(),
        }
    }

    /// 添加性能标记
    pub fn add_performance_marker(&mut self, name: String, timestamp: Instant) {
        self.performance_markers
            .push(PerformanceMarker { name, timestamp });
    }

    /// 获取操作统计
    pub fn get_operation_stats(&self) -> OperationStats {
        OperationStats {
            total_variable_operations: self.variable_operations.len(),
            total_scope_operations: self.scope_operations.len(),
            total_performance_markers: self.performance_markers.len(),
        }
    }
}

impl Default for DebugInfo {
    fn default() -> Self {
        Self::new()
    }
}

/// 变量操作记录
#[derive(Debug, Clone)]
pub struct VariableOperation {
    /// 操作类型（set, remove, get）
    pub operation: String,
    /// 变量键
    pub key: String,
    /// 旧值
    pub old_value: Option<ContextValue>,
    /// 新值
    pub new_value: Option<ContextValue>,
    /// 操作时间
    pub timestamp: Instant,
}

/// 作用域操作记录
#[derive(Debug, Clone)]
pub struct ScopeOperation {
    /// 操作类型（push, pop）
    pub operation: String,
    /// 作用域深度
    pub scope_depth: usize,
    /// 操作时间
    pub timestamp: Instant,
}

/// 性能标记
#[derive(Debug, Clone)]
pub struct PerformanceMarker {
    /// 标记名称
    pub name: String,
    /// 时间戳
    pub timestamp: Instant,
}

/// 操作统计
#[derive(Debug, Clone)]
pub struct OperationStats {
    /// 变量操作总数
    pub total_variable_operations: usize,
    /// 作用域操作总数
    pub total_scope_operations: usize,
    /// 性能标记总数
    pub total_performance_markers: usize,
}

/// 规则执行请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleExecutionRequest {
    /// 规则ID
    pub rule_id: String,
    /// 输入数据
    pub input_data: HashMap<String, ContextValue>,
    /// 执行选项
    pub options: ExecutionOptions,
}

impl RuleExecutionRequest {
    /// 创建新的执行请求
    pub fn new(rule_id: String, input_data: HashMap<String, ContextValue>) -> Self {
        Self {
            rule_id,
            input_data,
            options: ExecutionOptions::default(),
        }
    }

    /// 设置执行选项
    pub fn with_options(mut self, options: ExecutionOptions) -> Self {
        self.options = options;
        self
    }

    /// 启用追踪
    pub fn with_tracing(mut self) -> Self {
        self.options.trace_execution = true;
        self
    }

    /// 设置超时
    pub fn with_timeout(mut self, timeout_ms: u64) -> Self {
        self.options.timeout_ms = Some(timeout_ms);
        self
    }

    /// 禁用缓存
    pub fn without_cache(mut self) -> Self {
        self.options.cache_enabled = false;
        self
    }
}

/// 规则执行响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleExecutionResponse {
    /// 请求ID
    pub request_id: String,
    /// 规则ID
    pub rule_id: String,
    /// 执行结果
    pub result: Option<HashMap<String, ContextValue>>,
    /// 执行状态
    pub status: ExecutionStatus,
    /// 错误信息
    pub error: Option<String>,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    /// 是否来自缓存
    pub from_cache: bool,
    /// 执行追踪
    pub trace: Option<ExecutionTrace>,
}

/// 执行状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionStatus {
    /// 执行成功
    Success,
    /// 执行失败
    Error,
    /// 执行超时
    Timeout,
    /// 规则未找到
    RuleNotFound,
    /// 输入无效
    InvalidInput,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_context_value_conversions() {
        let str_val = ContextValue::String("test".to_string());
        assert_eq!(str_val.as_str(), "test");
        assert_eq!(str_val.type_name(), "string");

        let int_val = ContextValue::Int(42);
        assert_eq!(int_val.as_int(), Some(42));
        assert_eq!(int_val.as_float(), Some(42.0));
        assert_eq!(int_val.type_name(), "int");

        let bool_val = ContextValue::Bool(true);
        assert!(bool_val.as_bool());
        assert_eq!(bool_val.type_name(), "bool");
    }

    #[test]
    fn test_execution_context_basic_operations() {
        let mut context = ExecutionContext::new();

        // 测试设置和获取变量
        context.set(
            "test_key".to_string(),
            ContextValue::String("test_value".to_string()),
        );
        assert_eq!(context.get("test_key"), Some("test_value"));
        assert!(context.contains_key("test_key"));

        // 测试删除变量
        let removed = context.remove("test_key");
        assert_eq!(
            removed,
            Some(ContextValue::String("test_value".to_string()))
        );
        assert!(!context.contains_key("test_key"));
    }

    #[test]
    fn test_execution_context_scopes() {
        let mut context = ExecutionContext::new();

        // 设置全局变量
        context.set(
            "global_var".to_string(),
            ContextValue::String("global".to_string()),
        );

        // 进入新作用域
        context.push_scope();
        assert_eq!(context.scope_depth(), 1);

        // 在作用域中设置变量
        context.set_in_scope(
            "scope_var".to_string(),
            ContextValue::String("scope".to_string()),
        );

        // 检查变量访问
        assert_eq!(context.get("global_var"), Some("global"));
        assert_eq!(context.get("scope_var"), Some("scope"));

        // 退出作用域
        let popped_scope = context.pop_scope();
        assert!(popped_scope.is_some());
        assert_eq!(context.scope_depth(), 0);
    }

    #[test]
    fn test_execution_context_timeout() {
        let mut context = ExecutionContext::new();
        context.set_timeout(Duration::from_millis(100));

        // 不应该立即超时
        assert!(!context.is_timeout());

        // 应该有剩余时间
        assert!(context.remaining_time().is_some());
    }

    #[test]
    fn test_execution_context_tracing() {
        let mut context = ExecutionContext::new();

        // 启用追踪
        context.enable_tracing();
        assert!(context.is_tracing_enabled());

        // 记录节点访问
        context.record_node_visit("node1".to_string());
        context.record_node_visit("node1".to_string());
        context.record_node_visit("node2".to_string());

        // 检查追踪信息
        if let Some(trace) = context.get_trace() {
            assert_eq!(trace.node_visits.get("node1"), Some(&2));
            assert_eq!(trace.node_visits.get("node2"), Some(&1));
        }

        // 禁用追踪
        context.disable_tracing();
        assert!(!context.is_tracing_enabled());
    }

    #[test]
    fn test_execution_context_snapshot() {
        let mut context = ExecutionContext::new();
        context.set("test".to_string(), ContextValue::Int(42));
        context.push_scope();

        // 创建快照
        let snapshot = context.snapshot();
        assert_eq!(snapshot.scope_depth, 1);
        assert!(snapshot.variables.contains_key("test"));

        // 修改上下文
        context.set(
            "new_var".to_string(),
            ContextValue::String("new".to_string()),
        );
        context.pop_scope();

        // 从快照恢复
        context.restore_from_snapshot(&snapshot);
        assert_eq!(context.scope_depth(), 1);
        assert!(context.contains_key("test"));
        assert!(!context.contains_key("new_var"));
    }

    #[test]
    fn test_execution_context_child() {
        let mut parent = ExecutionContext::new();
        parent.set(
            "parent_var".to_string(),
            ContextValue::String("parent".to_string()),
        );

        // 创建子上下文
        let child = parent.create_child();
        assert_eq!(child.get("parent_var"), Some("parent"));

        // 子上下文的修改不影响父上下文
        let mut child_mut = child;
        child_mut.set(
            "child_var".to_string(),
            ContextValue::String("child".to_string()),
        );
        assert!(!parent.contains_key("child_var"));
    }

    #[test]
    fn test_execution_options() {
        let options = ExecutionOptions::default();
        assert!(options.cache_enabled);
        assert!(!options.trace_execution);
        assert_eq!(options.timeout_ms, Some(5000));
        assert!(options.validate_input);
    }

    #[test]
    fn test_rule_execution_request() {
        let mut input_data = HashMap::new();
        input_data.insert("key".to_string(), ContextValue::String("value".to_string()));

        let request = RuleExecutionRequest::new("test_rule".to_string(), input_data)
            .with_tracing()
            .with_timeout(3000)
            .without_cache();

        assert_eq!(request.rule_id, "test_rule");
        assert!(request.options.trace_execution);
        assert_eq!(request.options.timeout_ms, Some(3000));
        assert!(!request.options.cache_enabled);
    }

    #[test]
    fn test_context_value_display() {
        let str_val = ContextValue::String("test".to_string());
        assert_eq!(format!("{}", str_val), "test");

        let int_val = ContextValue::Int(42);
        assert_eq!(format!("{}", int_val), "42");

        let array_val = ContextValue::Array(vec![
            ContextValue::String("a".to_string()),
            ContextValue::Int(1),
        ]);
        assert_eq!(format!("{}", array_val), "[a, 1]");
    }

    #[test]
    fn test_debug_info() {
        let mut context = ExecutionContext::new();

        // 执行一些操作
        context.set(
            "var1".to_string(),
            ContextValue::String("value1".to_string()),
        );
        context.push_scope();
        context.set("var2".to_string(), ContextValue::Int(42));
        context.pop_scope();

        // 检查调试信息
        let debug_info = context.get_debug_info();
        let stats = debug_info.get_operation_stats();

        assert_eq!(stats.total_variable_operations, 2);
        assert_eq!(stats.total_scope_operations, 2);
    }
}
