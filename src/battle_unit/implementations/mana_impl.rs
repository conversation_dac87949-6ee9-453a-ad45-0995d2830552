/// 法力系统trait实现
/// 
/// 实现ManaSystem trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;
use crate::battle_unit::types::*;
use crate::battle_unit::traits::ManaSystem;
use crate::attribute::{AttributeType, CoreAttribute};

impl ManaSystem for BattleUnitAdapter {
    fn current_mana(&self) -> <PERSON><PERSON> {
        self.character.current_mana()
    }
    
    fn max_mana(&self) -> <PERSON><PERSON> {
        self.character.max_mana()
    }
    
    fn base_mana(&self) -> <PERSON><PERSON> {
        // 基于智力计算基础法力值
        let intelligence = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Water)
        );
        intelligence * 5 + 20 // 基础20 + 智力*5
    }
    
    fn mana_bonus(&self) -> Mana {
        self.max_mana() - self.base_mana()
    }
    
    fn mana_regeneration_rate(&self) -> <PERSON><PERSON> {
        let intelligence_mod = BattleUnitAdapter::calculate_modifier(
            self.calculate_total_attribute(
                AttributeType::Base(CoreAttribute::Water)
            )
        );
        (1 + intelligence_mod).max(0) as Mana
    }
    
    fn mana_percentage(&self) -> f32 {
        if self.max_mana() > 0 {
            self.current_mana() as f32 / self.max_mana() as f32
        } else {
            0.0
        }
    }
    
    fn consume_mana(&mut self, amount: Mana) -> GameResult<()> {
        let reduced_cost = (amount as f32 * (1.0 - self.mana_cost_reduction())) as Mana;
        if self.current_mana() >= reduced_cost {
            // TODO: 使用Character的consume_mana方法
            // self.character.consume_mana(reduced_cost)?;
            Ok(())
        } else {
            Err("法力不足".into())
        }
    }
    
    fn restore_mana(&mut self, amount: Mana) -> GameResult<()> {
        // TODO: 使用Character的restore_mana方法
        // self.character.restore_mana(amount)?;
        Ok(())
    }
    
    fn set_mana(&mut self, mana: Mana) -> GameResult<()> {
        // TODO: 实现Character的set_mana方法
        // self.character.set_mana(mana)?;
        Ok(())
    }
    
    fn has_enough_mana(&self, required: Mana) -> bool {
        let reduced_cost = (required as f32 * (1.0 - self.mana_cost_reduction())) as Mana;
        self.current_mana() >= reduced_cost
    }
    
    fn mana_cost_reduction(&self) -> f32 {
        // 基于装备和buff计算法力消耗减免
        let mut reduction = 0.0;
        
        // 从装备获取减免
        let equipment_attrs = self.equipment_bar.get_total_equipment_attributes();
        // TODO: 添加法力消耗减免属性到装备系统
        
        // 从buff获取减免
        for buff in &self.buff_manager.active_buffs {
            // TODO: 检查buff是否提供法力消耗减免
        }
        
        reduction.min(0.8) // 最大80%减免
    }
}