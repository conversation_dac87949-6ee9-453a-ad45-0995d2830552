/// 属性系统trait实现
/// 
/// 实现AttributeSystem trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;
use crate::battle_unit::types::*;
use crate::battle_unit::traits::AttributeSystem;
use crate::attribute::{AttributeType, CoreAttribute, DerivedAttribute, AttributePoints};

impl AttributeSystem for BattleUnitAdapter {
    fn base_attribute(&self, attribute: AttributeType) -> AttributePoints {
        self.character.attributes().get_attribute_value(attribute)
    }
    
    fn equipment_attribute_bonus(&self, attribute: AttributeType) -> AttributePoints {
        self.equipment_bar.get_total_equipment_attributes()
            .get_attribute_value(attribute)
    }
    
    fn temporary_attribute_bonus(&self, attribute: AttributeType) -> AttributePoints {
        let mut bonus = 0;
        
        // 从buff获取临时属性加成
        for buff in &self.buff_manager.active_buffs {
            // TODO: 实现buff的属性加成计算
            // bonus += buff.get_attribute_bonus(attribute);
        }
        
        bonus
    }
    
    fn calculate_total_attribute(&self, attribute: AttributeType) -> AttributePoints {
        let base = self.base_attribute(attribute);
        let equipment = self.equipment_attribute_bonus(attribute);
        let temporary = self.temporary_attribute_bonus(attribute);
        
        base + equipment + temporary
    }
    
    fn attribute_modifier(&self, attribute: AttributeType) -> i32 {
        let total = self.calculate_total_attribute(attribute);
        BattleUnitAdapter::calculate_modifier(total)
    }
    
    fn get_all_attributes(&self) -> AttributeSnapshot {
        let mut snapshot = AttributeSnapshot::new();
        
        // 五行属性
        let core_attributes = [
            CoreAttribute::Metal,
            CoreAttribute::Wood,
            CoreAttribute::Water,
            CoreAttribute::Fire,
            CoreAttribute::Earth,
        ];
        
        for attr in core_attributes.iter() {
            let attr_type = AttributeType::Base(*attr);
            snapshot.base_attributes.insert(
                *attr,
                self.calculate_total_attribute(attr_type)
            );
        }
        
        // 衍生属性
        snapshot.attack = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Attack)
        );
        snapshot.defense = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Defense)
        );
        snapshot.speed = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Speed)
        );
        
        snapshot
    }
    
    fn primary_attribute(&self) -> AttributeType {
        // 找到最高的五行属性
        let core_attributes = [
            CoreAttribute::Metal,
            CoreAttribute::Wood,
            CoreAttribute::Water,
            CoreAttribute::Fire,
            CoreAttribute::Earth,
        ];
        
        let mut highest_attr = CoreAttribute::Metal;
        let mut highest_value = 0;
        
        for attr in core_attributes.iter() {
            let value = self.calculate_total_attribute(AttributeType::Base(*attr));
            if value > highest_value {
                highest_value = value;
                highest_attr = *attr;
            }
        }
        
        AttributeType::Base(highest_attr)
    }
    
    fn attribute_growth_potential(&self, attribute: AttributeType) -> f32 {
        // 基于角色等级和经验计算属性成长潜力
        let current_value = self.calculate_total_attribute(attribute);
        let level = self.character.level() as f32;
        
        // 计算成长潜力（高属性值和低等级有更高潜力）
        let value_factor = (current_value as f32 / 100.0).min(1.0);
        let level_factor = (1.0 - (level / 100.0)).max(0.1);
        
        value_factor * level_factor
    }
}

impl BattleUnitAdapter {
    /// 根据属性值计算调整值
    pub fn calculate_modifier(attribute_value: AttributePoints) -> i32 {
        ((attribute_value as i32) - 10) / 2
    }
}