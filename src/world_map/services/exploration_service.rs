/// 世界探索领域服务
/// 处理玩家的探索行为和发现逻辑

use crate::world_map::{Position, Direction};
use crate::world_map::domain::{
    RegionId, NodeId, BuildingId, TerrainType, DangerLevel,
    ResourceNodeType,
};
use crate::world_map::domain::WorldLayer;
use crate::world_map::infrastructure::{WorldMapConfig, SpatialIndex};

/// 世界探索服务
pub struct WorldExplorationService {
    config: WorldMapConfig,
    spatial_index: SpatialIndex,
}

/// 探索结果
#[derive(Debug, Clone)]
pub struct ExplorationResult {
    /// 探索是否成功
    pub success: bool,
    /// 发现的内容
    pub discoveries: Vec<Discovery>,
    /// 获得的经验值
    pub experience_gained: u32,
    /// 消耗的体力
    pub stamina_consumed: u32,
    /// 遭遇的事件
    pub encounters: Vec<Encounter>,
    /// 结果描述
    pub message: String,
}

/// 发现内容
#[derive(Debug, Clone)]
pub enum Discovery {
    /// 发现新区域
    NewRegion {
        region_id: RegionId,
        region_name: String,
        terrain_type: TerrainType,
    },
    /// 发现资源节点
    ResourceNode {
        node_id: NodeId,
        node_type: ResourceNodeType,
        position: Position,
    },
    /// 发现建筑
    Building {
        building_id: BuildingId,
        building_name: String,
        position: Position,
    },
    /// 发现隐藏路径
    HiddenPath {
        from: Position,
        to: Position,
        path_type: PathType,
    },
    /// 发现特殊地点
    SpecialLocation {
        name: String,
        position: Position,
        special_type: SpecialLocationType,
    },
}

/// 路径类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PathType {
    /// 普通道路
    Road,
    /// 隐秘小径
    HiddenTrail,
    /// 传送门
    Portal,
    /// 灵气通道
    SpiritualPassage,
}

/// 特殊地点类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SpecialLocationType {
    /// 古代遗迹
    AncientRuin,
    /// 灵气汇聚点
    SpiritualNode,
    /// 天然洞府
    NaturalCave,
    /// 奇异现象
    Anomaly,
}

/// 遭遇事件
#[derive(Debug, Clone)]
pub enum Encounter {
    /// 野生灵兽
    WildBeast {
        beast_name: String,
        danger_level: DangerLevel,
        is_hostile: bool,
    },
    /// 其他修士
    OtherCultivator {
        cultivator_name: String,
        faction: Option<String>,
        attitude: CultivatorAttitude,
    },
    /// 自然现象
    NaturalPhenomenon {
        phenomenon_type: String,
        effect: PhenomenonEffect,
    },
    /// 宝物发现
    TreasureFind {
        treasure_name: String,
        rarity: u8,
    },
}

/// 修士态度
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CultivatorAttitude {
    Friendly,
    Neutral,
    Suspicious,
    Hostile,
}

/// 自然现象效果
#[derive(Debug, Clone)]
pub enum PhenomenonEffect {
    /// 正面效果
    Beneficial { description: String },
    /// 负面效果
    Harmful { description: String },
    /// 中性效果
    Neutral { description: String },
}

/// 探索上下文
#[derive(Debug, Clone)]
pub struct ExplorationContext {
    /// 探索者信息
    pub explorer: ExplorerInfo,
    /// 当前位置
    pub current_position: Position,
    /// 目标位置
    pub target_position: Position,
    /// 探索类型
    pub exploration_type: ExplorationType,
    /// 探索时间
    pub exploration_time: u32, // 分钟
}

/// 探索者信息
#[derive(Debug, Clone)]
pub struct ExplorerInfo {
    /// 探索者ID
    pub id: String,
    /// 等级
    pub level: u8,
    /// 探索技能等级
    pub exploration_skill: u8,
    /// 感知能力
    pub perception: u8,
    /// 当前体力
    pub current_stamina: u32,
    /// 最大体力
    pub max_stamina: u32,
    /// 已知区域
    pub known_regions: Vec<RegionId>,
    /// 特殊能力
    pub special_abilities: Vec<String>,
}

/// 探索类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ExplorationType {
    /// 快速探索
    Quick,
    /// 仔细探索
    Thorough,
    /// 隐秘探索
    Stealth,
    /// 灵识探索
    Spiritual,
}

impl WorldExplorationService {
    /// 创建新的探索服务
    pub fn new(config: WorldMapConfig) -> Self {
        Self {
            config,
            spatial_index: SpatialIndex::default(),
        }
    }
    
    /// 探索新区域
    pub fn explore_region(&self, context: ExplorationContext) -> ExplorationResult {
        let mut result = ExplorationResult {
            success: false,
            discoveries: Vec::new(),
            experience_gained: 0,
            stamina_consumed: 0,
            encounters: Vec::new(),
            message: String::new(),
        };
        
        // 检查探索者状态
        if !self.can_explore(&context) {
            result.message = "探索者状态不适合进行探索".to_string();
            return result;
        }
        
        // 计算探索成功率
        let success_chance = self.calculate_exploration_success_chance(&context);
        let random_value = self.generate_random(); // 简化的随机数生成
        
        if random_value < success_chance {
            result.success = true;
            
            // 生成发现内容
            result.discoveries = self.generate_discoveries(&context);
            
            // 生成遭遇事件
            result.encounters = self.generate_encounters(&context);
            
            // 计算奖励
            result.experience_gained = self.calculate_experience_reward(&context, &result.discoveries);
            result.stamina_consumed = self.calculate_stamina_cost(&context);
            
            result.message = format!("探索成功！发现了 {} 个新地点", result.discoveries.len());
        } else {
            result.message = "探索失败，没有发现新的地点".to_string();
            result.stamina_consumed = self.calculate_stamina_cost(&context) / 2; // 失败时消耗一半体力
        }
        
        result
    }
    
    /// 尝试发现隐藏内容
    pub fn attempt_discovery(&self, context: ExplorationContext) -> DiscoveryResult {
        let mut discoveries = Vec::new();
        
        // 查询当前位置周围的对象
        let _nearby_objects = self.spatial_index.get_nearby_objects(
            context.current_position,
            self.get_discovery_radius(&context)
        );
        
        // 尝试发现隐藏的资源节点
        if self.should_discover_resource_node(&context) {
            if let Some(node_discovery) = self.generate_resource_node_discovery(&context) {
                discoveries.push(node_discovery);
            }
        }
        
        // 尝试发现特殊地点
        if self.should_discover_special_location(&context) {
            if let Some(location_discovery) = self.generate_special_location_discovery(&context) {
                discoveries.push(location_discovery);
            }
        }
        
        let success = !discoveries.is_empty();
        DiscoveryResult {
            discoveries,
            success,
        }
    }
    
    /// 计算遭遇概率
    pub fn calculate_encounter_chance(&self, position: Position, explorer: &ExplorerInfo) -> f32 {
        let base_chance = 0.1; // 基础遭遇概率
        
        // 根据地形调整
        let terrain_modifier = self.get_terrain_encounter_modifier(position);
        
        // 根据探索者等级调整
        let level_modifier = 1.0 + (explorer.level as f32 * 0.02);
        
        // 根据探索技能调整
        let skill_modifier = 1.0 - (explorer.exploration_skill as f32 * 0.01);
        
        base_chance * terrain_modifier * level_modifier * skill_modifier
    }
    
    /// 获取可探索的方向
    pub fn get_explorable_directions(&self, position: Position) -> Vec<Direction> {
        let mut directions = Vec::new();
        
        for direction in Direction::horizontal_directions() {
            let target_position = position.move_in_direction(direction, 1);
            if self.can_explore_position(target_position) {
                directions.push(direction);
            }
        }
        
        directions
    }
    
    /// 根据世界层级调整探索难度
    pub fn get_layer_exploration_modifier(&self, layer: WorldLayer) -> f32 {
        match layer {
            WorldLayer::Mortal => 1.0,      // 凡人界基础难度
            WorldLayer::Spirit => 1.5,      // 灵界增加50%难度
            WorldLayer::Immortal => 2.0,    // 仙界增加100%难度
            WorldLayer::Chaos => 3.0,       // 混沌界增加200%难度
            WorldLayer::SecretRealm(level) => {
                // 秘境难度根据层数动态调整
                1.0 + (level as f32 * 0.3)
            }
        }
    }
    
    /// 检查世界层级是否允许探索
    pub fn can_explore_layer(&self, explorer: &ExplorerInfo, layer: WorldLayer) -> bool {
        match layer {
            WorldLayer::Mortal => true,                    // 所有人都能探索凡人界
            WorldLayer::Spirit => explorer.level >= 10,     // 需要10级以上
            WorldLayer::Immortal => explorer.level >= 30,   // 需要30级以上
            WorldLayer::Chaos => explorer.level >= 50,      // 需要50级以上
            WorldLayer::SecretRealm(level) => {
                // 秘境需要等级 >= 20 + 秘境层数 * 10
                u32::from(explorer.level) >= (20 + level * 10)
            }
        }
    }
    
    /// 获取探索建议
    pub fn get_exploration_suggestions(&self, position: Position, explorer: &ExplorerInfo) -> Vec<ExplorationSuggestion> {
        let mut suggestions = Vec::new();
        
        // 基于探索者技能和位置生成建议
        let nearby_objects = self.spatial_index.get_nearby_objects(position, 100.0);
        
        if !nearby_objects.resource_nodes.is_empty() {
            suggestions.push(ExplorationSuggestion {
                suggestion_type: SuggestionType::ResourceGathering,
                description: "附近发现资源节点，建议进行采集".to_string(),
                priority: 3,
            });
        }
        
        if explorer.exploration_skill >= 5 {
            suggestions.push(ExplorationSuggestion {
                suggestion_type: SuggestionType::HiddenPathSearch,
                description: "你的探索技能足够高，可以尝试寻找隐藏路径".to_string(),
                priority: 2,
            });
        }
        
        suggestions
    }
    
    // 私有辅助方法
    
    fn can_explore(&self, context: &ExplorationContext) -> bool {
        context.explorer.current_stamina >= self.calculate_stamina_cost(context)
    }
    
    fn calculate_exploration_success_chance(&self, context: &ExplorationContext) -> f32 {
        let base_chance = self.config.exploration.base_discovery_chance;
        let skill_bonus = context.explorer.exploration_skill as f32 * self.config.exploration.skill_bonus_multiplier;
        let fatigue_penalty = if context.explorer.current_stamina < context.explorer.max_stamina / 2 {
            self.config.exploration.fatigue_penalty
        } else {
            0.0
        };
        
        // 根据世界层级调整成功率
        let layer_modifier = self.get_layer_exploration_modifier(context.current_position.layer);
        let layer_penalty = (layer_modifier - 1.0) * 0.2; // 每增加0.5难度，减少10%成功率
        
        (base_chance + skill_bonus - fatigue_penalty - layer_penalty).clamp(0.0, 1.0)
    }
    
    fn generate_discoveries(&self, context: &ExplorationContext) -> Vec<Discovery> {
        let mut discoveries = Vec::new();
        
        // 简化的发现生成逻辑
        match context.exploration_type {
            ExplorationType::Thorough => {
                // 仔细探索更容易发现资源节点
                if self.generate_random() < 0.6 {
                    discoveries.push(self.generate_resource_discovery(context));
                }
            }
            ExplorationType::Spiritual => {
                // 灵识探索更容易发现特殊地点
                if self.generate_random() < 0.4 {
                    discoveries.push(self.generate_special_location(context));
                }
            }
            _ => {
                // 其他类型的探索
                if self.generate_random() < 0.3 {
                    discoveries.push(self.generate_resource_discovery(context));
                }
            }
        }
        
        discoveries
    }
    
    fn generate_encounters(&self, context: &ExplorationContext) -> Vec<Encounter> {
        let mut encounters = Vec::new();
        
        let encounter_chance = self.calculate_encounter_chance(context.target_position, &context.explorer);
        
        if self.generate_random() < encounter_chance {
            encounters.push(self.generate_random_encounter(context));
        }
        
        encounters
    }
    
    fn calculate_experience_reward(&self, context: &ExplorationContext, discoveries: &[Discovery]) -> u32 {
        let base_exp = 10;
        let discovery_bonus = discoveries.len() as u32 * 5;
        let exploration_type_bonus = match context.exploration_type {
            ExplorationType::Thorough => 5,
            ExplorationType::Spiritual => 8,
            _ => 0,
        };
        
        base_exp + discovery_bonus + exploration_type_bonus
    }
    
    fn calculate_stamina_cost(&self, context: &ExplorationContext) -> u32 {
        let base_cost = 10;
        let distance_cost = context.current_position.distance_to(&context.target_position) as u32;
        let exploration_type_cost = match context.exploration_type {
            ExplorationType::Quick => 0,
            ExplorationType::Thorough => 5,
            ExplorationType::Stealth => 3,
            ExplorationType::Spiritual => 8,
        };
        
        base_cost + distance_cost + exploration_type_cost
    }
    
    fn generate_random(&self) -> f32 {
        // 简化的随机数生成，实际应用中应使用真正的随机数生成器
        0.5
    }
    
    fn get_discovery_radius(&self, context: &ExplorationContext) -> f32 {
        let base_radius = 50.0;
        let skill_bonus = context.explorer.exploration_skill as f32 * 5.0;
        base_radius + skill_bonus
    }
    
    fn should_discover_resource_node(&self, context: &ExplorationContext) -> bool {
        context.explorer.exploration_skill >= 3 && self.generate_random() < 0.3
    }
    
    fn should_discover_special_location(&self, context: &ExplorationContext) -> bool {
        context.explorer.exploration_skill >= 5 && self.generate_random() < 0.1
    }
    
    fn generate_resource_node_discovery(&self, context: &ExplorationContext) -> Option<Discovery> {
        Some(Discovery::ResourceNode {
            node_id: crate::world_map::domain::ids::NodeId::new(1),
            node_type: ResourceNodeType::SpiritualHerbs,
            position: context.target_position,
        })
    }
    
    fn generate_special_location_discovery(&self, context: &ExplorationContext) -> Option<Discovery> {
        Some(Discovery::SpecialLocation {
            name: "神秘洞穴".to_string(),
            position: context.target_position,
            special_type: SpecialLocationType::NaturalCave,
        })
    }
    
    fn get_terrain_encounter_modifier(&self, _position: Position) -> f32 {
        // 简化实现，实际应根据地形类型返回不同的修正值
        1.0
    }
    
    fn can_explore_position(&self, _position: Position) -> bool {
        // 简化实现，实际应检查位置是否可达、是否有障碍等
        true
    }
    
    fn generate_resource_discovery(&self, context: &ExplorationContext) -> Discovery {
        Discovery::ResourceNode {
            node_id: crate::world_map::domain::ids::NodeId::new(2),
            node_type: ResourceNodeType::SpiritualHerbs,
            position: context.target_position,
        }
    }
    
    fn generate_special_location(&self, context: &ExplorationContext) -> Discovery {
        Discovery::SpecialLocation {
            name: "灵气汇聚点".to_string(),
            position: context.target_position,
            special_type: SpecialLocationType::SpiritualNode,
        }
    }
    
    fn generate_random_encounter(&self, _context: &ExplorationContext) -> Encounter {
        Encounter::WildBeast {
            beast_name: "灵狐".to_string(),
            danger_level: DangerLevel::Low,
            is_hostile: false,
        }
    }
}

/// 发现结果
#[derive(Debug, Clone)]
pub struct DiscoveryResult {
    pub discoveries: Vec<Discovery>,
    pub success: bool,
}

/// 探索建议
#[derive(Debug, Clone)]
pub struct ExplorationSuggestion {
    pub suggestion_type: SuggestionType,
    pub description: String,
    pub priority: u8, // 1-5，5为最高优先级
}

/// 建议类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SuggestionType {
    ResourceGathering,
    HiddenPathSearch,
    DangerAvoidance,
    SkillTraining,
    RestRecommendation,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::world_map::infrastructure::WorldMapConfig;
    
    #[test]
    fn test_exploration_service_creation() {
        let config = WorldMapConfig::default();
        let _service = WorldExplorationService::new(config);
        
        // 基本的服务创建测试
        assert!(true); // 如果能创建服务就算成功
    }
    
    #[test]
    fn test_exploration_context() {
        let explorer = ExplorerInfo {
            id: "test_explorer".to_string(),
            level: 5,
            exploration_skill: 3,
            perception: 4,
            current_stamina: 100,
            max_stamina: 100,
            known_regions: Vec::new(),
            special_abilities: Vec::new(),
        };
        
        let context = ExplorationContext {
            explorer,
            current_position: Position::new_2d(0, 0, WorldLayer::Mortal),
            target_position: Position::new_2d(10, 10, WorldLayer::Mortal),
            exploration_type: ExplorationType::Thorough,
            exploration_time: 60,
        };
        
        assert_eq!(context.exploration_type, ExplorationType::Thorough);
    }
}