use crate::shared::{
    BattleUnitId, CharacterId, CooldownState, GameError, GameResult, Level, Position, ResourceCost,
    SkillId,
};
use crate::skill::domain::value_objects::{SkillEffect, SkillEffectComposition};
/// 技能聚合实体
///
/// 包含技能聚合内的实体对象：
/// - 技能实例（SkillInstance）- 技能执行时的状态
/// - 技能节点（SkillNode）- 技能树中的节点
/// - 效果实例（EffectInstance）- 效果执行时的状态
use serde::{Deserialize, Serialize};
// ============================================================================
// 技能实例实体
// ============================================================================

/// 技能实例
///
/// 表示技能执行时的运行时状态，包含施法进度、冷却状态等
///
/// 技能实例代表了一个已激活或准备施放的技能的完整状态。
/// 它包含了所有必要的信息来计算和应用技能效果。
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SkillInstance {
    /// 实例唯一标识
    pub instance_id: SkillInstanceId,
    /// 技能ID
    pub skill_id: SkillId,
    /// 施法者ID
    pub caster_id: BattleUnitId,
    /// 目标位置（可选）
    pub target_position: Option<Position>,
    /// 技能等级
    pub level: Level,
    /// 冷却状态
    pub cooldown_state: CooldownState,
    /// 资源消耗
    pub resource_cost: ResourceCost,
    /// 效果组合（运行时副本）
    pub effects: Vec<SkillEffectComposition>,
    /// 实例状态
    pub state: SkillInstanceState,
    /// 施法进度（0.0 - 1.0）
    pub cast_progress: f64,
    /// 施法持续时间
    pub cast_duration: f64,
    /// 创建时间
    pub created_at: f64,
    /// 版本控制
    pub version: u64,
}

impl SkillInstance {
    pub fn new(
        skill_id: SkillId,
        caster_id: BattleUnitId,
        target_position: Option<Position>,
        level: Level,
        cooldown: f64,
        resource_cost: ResourceCost,
        effects: Vec<SkillEffectComposition>,
    ) -> Self {
        Self {
            instance_id: SkillInstanceId::new(),
            skill_id,
            caster_id,
            target_position,
            level,
            cooldown_state: CooldownState::new(cooldown),
            resource_cost,
            effects,
            state: SkillInstanceState::Ready,
            cast_progress: 0.0,
            cast_duration: 1.0, // 默认1秒施法时间
            created_at: 0.0,    // TODO: 使用实际时间
            version: 1,
        }
    }

    /// 重置实例以供对象池复用
    pub fn reset_for_pooling(&mut self) {
        self.instance_id = SkillInstanceId(0);
        self.skill_id = SkillId(0);
        self.caster_id = BattleUnitId::Character(CharacterId(0));
        self.target_position = None;
        self.level = 1;
        self.cooldown_state = CooldownState::ready();
        self.resource_cost = ResourceCost::none();
        self.effects = Vec::new();
        self.state = SkillInstanceState::Ready;
        self.cast_progress = 0.0;
        self.cast_duration = 0.0;
        self.created_at = 0.0;
        self.version = 0;
    }

    // ============================================================================
    // 基本信息访问
    // ============================================================================

    pub fn instance_id(&self) -> SkillInstanceId {
        self.instance_id
    }

    pub fn skill_id(&self) -> SkillId {
        self.skill_id
    }

    pub fn caster_id(&self) -> BattleUnitId {
        self.caster_id
    }

    pub fn target_position(&self) -> Option<Position> {
        self.target_position
    }

    pub fn level(&self) -> Level {
        self.level
    }

    pub fn state(&self) -> SkillInstanceState {
        self.state
    }

    pub fn version(&self) -> u64 {
        self.version
    }

    // ============================================================================
    // 施法状态管理
    // ============================================================================

    /// 开始施法
    pub fn start_casting(&mut self, cast_duration: f64) -> GameResult<()> {
        if !matches!(self.state, SkillInstanceState::Ready) {
            return Err(GameError::validation_error(
                "skill_state",
                "只有准备状态的技能才能开始施法",
            ));
        }

        self.state = SkillInstanceState::Casting;
        self.cast_duration = cast_duration;
        self.cast_progress = 0.0;
        self.version += 1;

        Ok(())
    }

    /// 更新施法进度
    pub fn update_casting(&mut self, delta_time: f32) -> GameResult<bool> {
        if !matches!(
            self.state,
            SkillInstanceState::Casting | SkillInstanceState::Channeling
        ) {
            return Ok(false);
        }

        self.cast_progress += (delta_time as f64) / self.cast_duration;

        if self.cast_progress >= 1.0 {
            self.cast_progress = 1.0;
            self.complete_casting()?;
            Ok(true) // 施法完成
        } else {
            self.version += 1;
            Ok(false) // 施法继续
        }
    }

    /// 完成施法
    pub fn complete_casting(&mut self) -> GameResult<()> {
        if !matches!(
            self.state,
            SkillInstanceState::Casting | SkillInstanceState::Channeling
        ) {
            return Err(GameError::validation_error(
                "skill_state",
                "只有施法中的技能才能完成施法",
            ));
        }

        self.state = SkillInstanceState::Executing;
        self.cast_progress = 1.0;
        self.version += 1;

        Ok(())
    }

    /// 中断施法
    pub fn interrupt_casting(&mut self) -> GameResult<()> {
        match self.state {
            SkillInstanceState::Casting | SkillInstanceState::Channeling => {
                self.state = SkillInstanceState::Interrupted;
                self.version += 1;
                Ok(())
            }
            _ => Err(GameError::validation_error(
                "skill_state",
                "只能中断正在施法的技能",
            )),
        }
    }

    /// 开始引导
    pub fn start_channeling(&mut self, channel_duration: f64) -> GameResult<()> {
        if !matches!(self.state, SkillInstanceState::Executing) {
            return Err(GameError::validation_error(
                "skill_state",
                "只有执行状态的技能才能开始引导",
            ));
        }

        self.state = SkillInstanceState::Channeling;
        self.cast_duration = channel_duration;
        self.cast_progress = 0.0;
        self.version += 1;

        Ok(())
    }

    /// 完成技能执行
    pub fn complete_execution(&mut self) -> GameResult<()> {
        if !matches!(self.state, SkillInstanceState::Executing) {
            return Err(GameError::validation_error(
                "skill_state",
                "只有执行状态的技能才能完成执行",
            ));
        }

        self.state = SkillInstanceState::Completed;
        // 开始冷却
        self.cooldown_state
            .reset(self.cooldown_state.total_cooldown());
        self.version += 1;

        Ok(())
    }

    // ============================================================================
    // 冷却管理
    // ============================================================================

    pub fn cooldown_state(&self) -> &CooldownState {
        &self.cooldown_state
    }

    pub fn is_ready(&self) -> bool {
        matches!(self.state, SkillInstanceState::Ready) && self.cooldown_state.is_ready()
    }

    /// 更新冷却时间
    pub fn update_cooldown(&mut self, delta_time: f32) {
        if matches!(self.state, SkillInstanceState::Completed) {
            self.cooldown_state.update(delta_time);

            if self.cooldown_state.is_ready() {
                self.state = SkillInstanceState::Ready;
                self.version += 1;
            }
        }
    }

    /// 重置冷却
    pub fn reset_cooldown(&mut self) {
        self.cooldown_state.complete();
        if matches!(self.state, SkillInstanceState::Completed) {
            self.state = SkillInstanceState::Ready;
        }
        self.version += 1;
    }

    // ============================================================================
    // 效果管理
    // ============================================================================

    pub fn effects(&self) -> &Vec<SkillEffectComposition> {
        &self.effects
    }

    /// 获取当前等级的效果威力
    pub fn get_effect_power(&self, effect_id: &str) -> f64 {
        // self.effects.get_effect_power(effect_id, self.level)
        0.0
    }

    /// 创建效果实例列表
    pub fn create_effect_instances(&self) -> Vec<EffectInstance> {
        let mut instances = Vec::new();

        // for effect in self.effects.get_all_effects() {
        //     let instance = EffectInstance::new(
        //         effect.id().to_string(),
        //         self.instance_id,
        //         self.caster_id,
        //         self.target_position,
        //         effect.clone(),
        //         self.level,
        //     );
        //     instances.push(instance);
        // }

        instances
    }

    // ============================================================================
    // 资源管理
    // ============================================================================

    pub fn resource_cost(&self) -> &ResourceCost {
        &self.resource_cost
    }

    pub fn get_current_cost(&self) -> ResourceCost {
        self.resource_cost.scaled_by_level(self.level)
    }

    // ============================================================================
    // 状态查询
    // ============================================================================

    pub fn cast_progress(&self) -> f64 {
        self.cast_progress
    }

    pub fn cast_duration(&self) -> f64 {
        self.cast_duration
    }

    pub fn is_casting(&self) -> bool {
        matches!(
            self.state,
            SkillInstanceState::Casting | SkillInstanceState::Channeling
        )
    }

    pub fn is_completed(&self) -> bool {
        matches!(
            self.state,
            SkillInstanceState::Completed | SkillInstanceState::Ready
        )
    }

    pub fn is_interrupted(&self) -> bool {
        matches!(self.state, SkillInstanceState::Interrupted)
    }

    /// 获取完整状态信息
    pub fn get_status(&self) -> SkillInstanceStatus {
        SkillInstanceStatus {
            instance_id: self.instance_id,
            skill_id: self.skill_id,
            caster_id: self.caster_id,
            level: self.level,
            state: self.state,
            cast_progress: self.cast_progress,
            cooldown_progress: self.cooldown_state.progress(),
            is_ready: self.is_ready(),
            version: self.version,
        }
    }
}

/// 技能实例状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SkillInstanceState {
    /// 准备中（刚创建）
    Ready,
    /// 施法中
    Casting,
    /// 执行中（即时技能）
    Executing,
    /// 引导中（持续施法）
    Channeling,
    /// 已完成（开始冷却）
    Completed,
    /// 被中断
    Interrupted,
    /// 失败
    Failed,
}

/// 技能实例状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillInstanceStatus {
    pub instance_id: SkillInstanceId,
    pub skill_id: SkillId,
    pub caster_id: BattleUnitId,
    pub level: Level,
    pub state: SkillInstanceState,
    pub cast_progress: f64,
    pub cooldown_progress: f64,
    pub is_ready: bool,
    pub version: u64,
}

// ============================================================================
// 技能节点实体
// ============================================================================

/// 技能节点
///
/// 技能树中的节点，表示技能在技能树中的位置和关系
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillNode {
    /// 节点唯一标识
    pub node_id: SkillNodeId,
    /// 技能ID
    pub skill_id: SkillId,
    /// 节点位置
    pub position: SkillTreePosition,
    /// 父节点（前置技能）
    pub parent_nodes: Vec<SkillNodeId>,
    /// 子节点（后续技能）
    pub child_nodes: Vec<SkillNodeId>,
    /// 学习状态
    pub learn_state: SkillLearnState,
    /// 投入的技能点
    pub invested_points: i32,
    /// 最大技能点
    pub max_points: i32,
    /// 节点类型
    pub node_type: SkillNodeType,
    /// 是否可见
    pub visible: bool,
    /// 版本控制
    pub version: u64,
}

impl SkillNode {
    pub fn new(skill_id: SkillId, position: SkillTreePosition, node_type: SkillNodeType) -> Self {
        Self {
            node_id: SkillNodeId::new(),
            skill_id,
            position,
            parent_nodes: Vec::new(),
            child_nodes: Vec::new(),
            learn_state: SkillLearnState::Locked,
            invested_points: 0,
            max_points: 5, // 默认最大5点
            node_type,
            visible: true,
            version: 1,
        }
    }

    // ============================================================================
    // 基本信息访问
    // ============================================================================

    pub fn node_id(&self) -> SkillNodeId {
        self.node_id
    }

    pub fn skill_id(&self) -> SkillId {
        self.skill_id
    }

    pub fn position(&self) -> &SkillTreePosition {
        &self.position
    }

    pub fn learn_state(&self) -> SkillLearnState {
        self.learn_state
    }

    pub fn node_type(&self) -> SkillNodeType {
        self.node_type
    }

    pub fn version(&self) -> u64 {
        self.version
    }

    // ============================================================================
    // 技能点管理
    // ============================================================================

    pub fn invested_points(&self) -> i32 {
        self.invested_points
    }

    pub fn max_points(&self) -> i32 {
        self.max_points
    }

    pub fn available_points(&self) -> i32 {
        self.max_points - self.invested_points
    }

    /// 投入技能点
    pub fn invest_point(&mut self) -> GameResult<()> {
        if self.invested_points >= self.max_points {
            return Err(GameError::validation_error(
                "skill_points",
                "技能点已达到最大值",
            ));
        }

        if !matches!(
            self.learn_state,
            SkillLearnState::Available | SkillLearnState::Learned
        ) {
            return Err(GameError::validation_error(
                "skill_state",
                "技能未解锁，无法投入技能点",
            ));
        }

        self.invested_points += 1;
        if self.invested_points == 1 && matches!(self.learn_state, SkillLearnState::Available) {
            self.learn_state = SkillLearnState::Learned;
        }
        self.version += 1;

        Ok(())
    }

    /// 重置技能点
    pub fn reset_points(&mut self) -> GameResult<i32> {
        let refunded_points = self.invested_points;
        self.invested_points = 0;

        if matches!(self.learn_state, SkillLearnState::Learned) {
            self.learn_state = SkillLearnState::Available;
        }

        self.version += 1;
        Ok(refunded_points)
    }

    // ============================================================================
    // 关系管理
    // ============================================================================

    pub fn parent_nodes(&self) -> &[SkillNodeId] {
        &self.parent_nodes
    }

    pub fn child_nodes(&self) -> &[SkillNodeId] {
        &self.child_nodes
    }

    /// 添加父节点
    pub fn add_parent(&mut self, parent_id: SkillNodeId) {
        if !self.parent_nodes.contains(&parent_id) {
            self.parent_nodes.push(parent_id);
            self.version += 1;
        }
    }

    /// 添加子节点
    pub fn add_child(&mut self, child_id: SkillNodeId) {
        if !self.child_nodes.contains(&child_id) {
            self.child_nodes.push(child_id);
            self.version += 1;
        }
    }

    /// 检查是否为根节点
    pub fn is_root(&self) -> bool {
        self.parent_nodes.is_empty()
    }

    /// 检查是否为叶节点
    pub fn is_leaf(&self) -> bool {
        self.child_nodes.is_empty()
    }

    // ============================================================================
    // 状态管理
    // ============================================================================

    /// 解锁技能
    pub fn unlock(&mut self) -> GameResult<()> {
        if !matches!(self.learn_state, SkillLearnState::Locked) {
            return Err(GameError::validation_error("skill_state", "技能已经解锁"));
        }

        self.learn_state = SkillLearnState::Available;
        self.version += 1;
        Ok(())
    }

    /// 学习技能
    pub fn learn(&mut self) -> GameResult<()> {
        if !matches!(self.learn_state, SkillLearnState::Available) {
            return Err(GameError::validation_error("skill_state", "技能不可学习"));
        }

        self.learn_state = SkillLearnState::Learned;
        self.invested_points = 1; // 学习技能至少需要1点
        self.version += 1;
        Ok(())
    }

    /// 设置可见性
    pub fn set_visibility(&mut self, visible: bool) {
        if self.visible != visible {
            self.visible = visible;
            self.version += 1;
        }
    }

    pub fn is_visible(&self) -> bool {
        self.visible
    }

    /// 检查是否可以学习
    pub fn can_learn(&self) -> bool {
        matches!(self.learn_state, SkillLearnState::Available) && self.visible
    }

    /// 检查是否已学习
    pub fn is_learned(&self) -> bool {
        matches!(self.learn_state, SkillLearnState::Learned)
    }

    /// 获取节点状态
    pub fn get_status(&self) -> SkillNodeStatus {
        SkillNodeStatus {
            node_id: self.node_id,
            skill_id: self.skill_id,
            position: self.position,
            learn_state: self.learn_state,
            invested_points: self.invested_points,
            max_points: self.max_points,
            node_type: self.node_type,
            visible: self.visible,
            can_learn: self.can_learn(),
            is_learned: self.is_learned(),
            version: self.version,
        }
    }
}

/// 技能学习状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SkillLearnState {
    /// 锁定（前置条件未满足）
    Locked,
    /// 可学习（前置条件已满足）
    Available,
    /// 已学习
    Learned,
    /// 大师级（最大等级）
    Mastered,
}

/// 技能节点类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SkillNodeType {
    /// 普通技能
    Normal,
    /// 关键技能（重要节点）
    Keystone,
    /// 小技能（被动加成）
    Minor,
    /// 专精技能（分支特化）
    Mastery,
    /// 究极技能（终极能力）
    Ultimate,
}

/// 技能树位置
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct SkillTreePosition {
    /// 层级（从根节点开始）
    pub tier: u32,
    /// 在该层级中的位置
    pub index: u32,
    /// 分支ID（用于多分支技能树）
    pub branch: u32,
}

impl SkillTreePosition {
    pub fn new(tier: u32, index: u32, branch: u32) -> Self {
        Self {
            tier,
            index,
            branch,
        }
    }

    /// 计算到另一个位置的距离
    pub fn distance_to(&self, other: &SkillTreePosition) -> f64 {
        let tier_diff = (self.tier as i32 - other.tier as i32) as f64;
        let index_diff = (self.index as i32 - other.index as i32) as f64;
        let branch_diff = if self.branch == other.branch {
            0.0
        } else {
            10.0
        }; // 不同分支距离更远

        (tier_diff * tier_diff + index_diff * index_diff + branch_diff * branch_diff).sqrt()
    }
}

/// 技能节点状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillNodeStatus {
    pub node_id: SkillNodeId,
    pub skill_id: SkillId,
    pub position: SkillTreePosition,
    pub learn_state: SkillLearnState,
    pub invested_points: i32,
    pub max_points: i32,
    pub node_type: SkillNodeType,
    pub visible: bool,
    pub can_learn: bool,
    pub is_learned: bool,
    pub version: u64,
}

// ============================================================================
// 效果实例实体
// ============================================================================

/// 效果实例
///
/// 表示技能效果执行时的运行时状态
///
/// 效果实例代表了应用在战斗单位身上的具体效果（如持续伤害、Buff、Debuff等）。
/// 它由技能实例创建，并在战斗中被跟踪和管理。
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct EffectInstance {
    /// 实例唯一标识
    pub instance_id: EffectInstanceId,
    /// 效果ID
    pub effect_id: String,
    /// 所属技能实例
    pub skill_instance_id: SkillInstanceId,
    /// 施法者ID
    pub caster_id: BattleUnitId,
    /// 目标位置
    pub target_position: Option<Position>,
    /// 效果定义
    pub effect: SkillEffect,
    /// 技能等级
    pub skill_level: Level,
    /// 实例状态
    pub state: EffectInstanceState,
    /// 持续时间（对于持续效果）
    pub duration: Option<f64>,
    /// 剩余时间
    pub remaining_time: f64,
    /// 累积数值（对于叠加效果）
    pub accumulated_value: f64,
    /// 触发次数
    pub trigger_count: u32,
    /// 创建时间
    pub created_at: f64,
    /// 版本控制
    pub version: u64,
}

impl EffectInstance {
    pub fn new(
        effect_id: String,
        skill_instance_id: SkillInstanceId,
        caster_id: BattleUnitId,
        target_position: Option<Position>,
        effect: SkillEffect,
        skill_level: Level,
    ) -> Self {
        // TODO : effect.duration()
        // let duration = effect.duration();

        Self {
            instance_id: EffectInstanceId::new(),
            effect_id,
            skill_instance_id,
            caster_id,
            target_position,
            effect,
            skill_level,
            state: EffectInstanceState::Pending,
            duration: Some(0.0),
            remaining_time: 0.0,
            accumulated_value: 0.0,
            trigger_count: 0,
            created_at: 0.0, // TODO: 使用实际时间
            version: 1,
        }
    }

    /// 重置实例以供对象池复用
    pub fn reset_for_pooling(&mut self) {
        self.instance_id = EffectInstanceId(0);
        self.effect_id = String::new();
        self.skill_instance_id = SkillInstanceId(0);
        self.caster_id = BattleUnitId::Character(CharacterId(0));
        self.target_position = None;
        self.effect = SkillEffect::default();
        self.skill_level = 1;
        self.state = EffectInstanceState::Pending;
        self.duration = None;
        self.remaining_time = 0.0;
        self.accumulated_value = 0.0;
        self.trigger_count = 0;
        self.created_at = 0.0;
        self.version = 0;
    }

    // ============================================================================
    // 基本信息访问
    // ============================================================================

    pub fn instance_id(&self) -> EffectInstanceId {
        self.instance_id
    }

    pub fn effect_id(&self) -> &str {
        &self.effect_id
    }

    pub fn skill_instance_id(&self) -> SkillInstanceId {
        self.skill_instance_id
    }

    pub fn caster_id(&self) -> BattleUnitId {
        self.caster_id
    }

    pub fn target_position(&self) -> Option<Position> {
        self.target_position
    }

    pub fn effect(&self) -> &SkillEffect {
        &self.effect
    }

    pub fn state(&self) -> EffectInstanceState {
        self.state
    }

    pub fn version(&self) -> u64 {
        self.version
    }

    // ============================================================================
    // 状态管理
    // ============================================================================

    /// 激活效果
    pub fn activate(&mut self) -> GameResult<()> {
        if !matches!(self.state, EffectInstanceState::Pending) {
            return Err(GameError::validation_error(
                "effect_state",
                "只有待处理的效果才能激活",
            ));
        }

        self.state = EffectInstanceState::Active;
        self.version += 1;
        Ok(())
    }

    /// 更新效果（用于持续效果）
    pub fn update(&mut self, delta_time: f32) -> GameResult<bool> {
        if !matches!(self.state, EffectInstanceState::Active) {
            return Ok(false);
        }

        if let Some(duration) = self.duration {
            self.remaining_time -= delta_time as f64;

            if self.remaining_time <= 0.0 {
                self.state = EffectInstanceState::Expired;
                self.version += 1;
                return Ok(true); // 效果过期
            }
        }

        Ok(false) // 效果继续
    }

    /// 触发效果
    pub fn trigger(&mut self) -> GameResult<f64> {
        if !matches!(
            self.state,
            EffectInstanceState::Active | EffectInstanceState::Pending
        ) {
            return Err(GameError::validation_error(
                "effect_state",
                "效果状态不允许触发",
            ));
        }

        // let value = self.effect.calculate_value(self.skill_level);
        //TODO: calculate_value
        let value = 0.0;
        self.accumulated_value += value;
        self.trigger_count += 1;

        // 如果是即时效果，触发后立即完成
        if self.duration.is_none() {
            self.state = EffectInstanceState::Completed;
        } else if matches!(self.state, EffectInstanceState::Pending) {
            self.state = EffectInstanceState::Active;
        }

        self.version += 1;
        Ok(value)
    }

    /// 停止效果
    pub fn stop(&mut self) -> GameResult<()> {
        match self.state {
            EffectInstanceState::Active | EffectInstanceState::Pending => {
                self.state = EffectInstanceState::Stopped;
                self.version += 1;
                Ok(())
            }
            _ => Err(GameError::validation_error(
                "effect_state",
                "效果状态不允许停止",
            )),
        }
    }

    // ============================================================================
    // 状态查询
    // ============================================================================

    pub fn is_active(&self) -> bool {
        matches!(self.state, EffectInstanceState::Active)
    }

    pub fn is_completed(&self) -> bool {
        matches!(
            self.state,
            EffectInstanceState::Completed
                | EffectInstanceState::Expired
                | EffectInstanceState::Stopped
        )
    }

    pub fn remaining_time(&self) -> f64 {
        self.remaining_time
    }

    pub fn progress(&self) -> f64 {
        if let Some(duration) = self.duration {
            if duration > 0.0 {
                1.0 - (self.remaining_time / duration).max(0.0).min(1.0)
            } else {
                1.0
            }
        } else {
            if matches!(self.state, EffectInstanceState::Completed) {
                1.0
            } else {
                0.0
            }
        }
    }

    pub fn accumulated_value(&self) -> f64 {
        self.accumulated_value
    }

    pub fn trigger_count(&self) -> u32 {
        self.trigger_count
    }

    /// 获取效果状态
    pub fn get_status(&self) -> EffectInstanceStatus {
        EffectInstanceStatus {
            instance_id: self.instance_id,
            effect_id: self.effect_id.clone(),
            skill_instance_id: self.skill_instance_id,
            state: self.state,
            remaining_time: self.remaining_time,
            progress: self.progress(),
            accumulated_value: self.accumulated_value,
            trigger_count: self.trigger_count,
            version: self.version,
        }
    }
}

/// 效果实例状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum EffectInstanceState {
    /// 待处理
    Pending,
    /// 激活中
    Active,
    /// 已完成
    Completed,
    /// 已过期
    Expired,
    /// 已停止
    Stopped,
    /// 失败
    Failed,
}

/// 效果实例状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EffectInstanceStatus {
    pub instance_id: EffectInstanceId,
    pub effect_id: String,
    pub skill_instance_id: SkillInstanceId,
    pub state: EffectInstanceState,
    pub remaining_time: f64,
    pub progress: f64,
    pub accumulated_value: f64,
    pub trigger_count: u32,
    pub version: u64,
}

// ============================================================================
// 唯一标识符类型
// ============================================================================

/// 技能实例ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct SkillInstanceId(pub u64);

impl SkillInstanceId {
    pub fn new() -> Self {
        use std::sync::atomic::{AtomicU64, Ordering};
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        Self(COUNTER.fetch_add(1, Ordering::SeqCst))
    }
}

/// 技能节点ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct SkillNodeId(pub u64);

impl SkillNodeId {
    pub fn new() -> Self {
        use std::sync::atomic::{AtomicU64, Ordering};
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        Self(COUNTER.fetch_add(1, Ordering::SeqCst))
    }
}

/// 效果实例ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct EffectInstanceId(pub u64);

impl EffectInstanceId {
    pub fn new() -> Self {
        use std::sync::atomic::{AtomicU64, Ordering};
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        Self(COUNTER.fetch_add(1, Ordering::SeqCst))
    }
}
