# 🚀 RPG战斗系统 - 快速开始指南

## 📚 目录
- [安装和运行](#installation)
- [基础用法](#basic-usage)
- [战斗模式](#battle-modes)
- [高级特性](#advanced-features)
- [演示程序](#demos)

---

## 🛠️ 安装和运行 {#installation}

### 前置要求
- **Rust 1.70+** (推荐使用最新稳定版)
- **Cargo** (Rust包管理器)

### 克隆和构建
```bash
# 克隆项目
git clone <项目地址>
cd game

# 构建项目
cargo build

# 运行测试
cargo test

# 运行基础演示
cargo run --bin battle_demo
```

---

## 🎮 基础用法 {#basic-usage}

### 1. 创建战斗单位

首先，你需要创建实现了`BattleUnit` trait的角色：

```rust
use game::character::Character;
use game::monster::{Monster, MonsterKind};

// 创建玩家角色
let mut player = Character::new(
    "勇敢的战士".to_string(),
    5,    // 等级
    100,  // 生命值
    50,   // 法力值
    25,   // 攻击力
    20,   // 防御力
    15,   // 速度
);

// 创建敌人
let mut enemy = Monster::new(
    MonsterKind::Goblin,
    "哥布林战士".to_string(),
    4,    // 等级
    80,   // 生命值
    30,   // 法力值
    12,   // 攻击力
    8,    // 防御力
    11,   // 速度
);
```

### 2. 开始战斗

```rust
use game::battle_system::battle_manager::BattleManager;

// 创建战斗管理器
let mut battle_manager = BattleManager::new();

// 开始1vs1战斗
let result = battle_manager.start_1v1_battle(&mut player, &mut enemy);

// 查看结果
match result.winner {
    Some(winner) => println!("🎉 {} 获得了胜利！", winner),
    None => println!("⚔️ 战斗平局！"),
}

// 查看战斗统计
let stats = battle_manager.get_battle_stats();
println!("📊 战斗持续了 {} 回合", stats.total_turns);
println!("💥 总伤害: {}", stats.total_damage_dealt);
```

---

## ⚔️ 战斗模式 {#battle-modes}

### 🎲 传统回合制战斗

最经典的RPG战斗模式，严格按照回合顺序执行：

```rust
use game::battle_system::battle_manager::BattleManager;

let mut battle_manager = BattleManager::new();

// 1vs1 决斗
let result = battle_manager.start_1v1_battle(&mut hero, &mut villain);

// 1vs多 英雄挑战
let enemies = vec![&mut goblin1, &mut goblin2, &mut goblin3];
let result = battle_manager.start_1vn_battle(&mut hero, enemies);

// 团队 vs 团队
let team_a = vec![&mut hero1, &mut hero2];
let team_b = vec![&mut monster1, &mut monster2];
let result = battle_manager.start_nvn_battle(team_a, team_b);
```

### ⚡ 高性能战斗引擎

采用先进的优化技术，提供高性能的回合制战斗体验。

```rust
use game::battle_system::performance::battle_engine::{
    OptimizedBattleEngine, BattleEngineConfig, EngineOptimization, TurnResult, BattleEndStatus, ActionResult,
};
use game::battle_system::simplified_battle_traits::FullBattleUnit;

// 使用标准优化配置来创建引擎
let config = EngineOptimization::standard().to_config();
let mut engine = OptimizedBattleEngine::new(config);

// 创建战斗单位
let mut player = create_player_character(); // 假设这些函数已定义
let mut enemy = create_enemy_monster();   // 假设这些函数已定义

// 初始化战斗
let participants: Vec<Box<dyn FullBattleUnit>> = vec![Box::new(player), Box::new(enemy)];
let battle_id = engine.initialize_battle(participants).unwrap();
println!("⚔️ === 战斗开始 (ID: {:?}) ===", battle_id);

// 战斗循环
loop {
    let turn_result = engine.execute_turn(battle_id).unwrap();
    
    // ... 在此处处理和打印回合结果 ...

    if !matches!(turn_result.battle_status, BattleEndStatus::Ongoing) {
        println!("\n🏆 === 战斗结束 ===");
        break;
    }
}
```

---

## 🔧 高级特性 {#advanced-features}

### ⚙️ 自定义战斗配置

```rust
use game::battle_system::battle_config::{BattleConfig, DamageConfig, SkillConfig};

let config = BattleConfig {
    damage: DamageConfig {
        base_critical_rate: 0.15,      // 15% 基础暴击率
        critical_multiplier: 2.5,      // 2.5倍暴击伤害
        base_block_rate: 0.1,          // 10% 基础格挡率
        block_reduction: 0.5,          // 格挡减少50%伤害
        level_critical_bonus: 0.01,    // 每级增加1%暴击率
        defense_block_bonus: 0.001,    // 每点防御增加0.1%格挡
        max_critical_rate: 0.75,       // 最大75%暴击率
        max_block_rate: 0.5,           // 最大50%格挡率
    },
    skill: SkillConfig {
        global_cooldown: 1.0,          // 1秒全局冷却
        mana_regen_per_turn: 5,        // 每回合恢复5法力
        skill_power_scaling: 1.5,      // 1.5倍技能威力缩放
    },
    turn: TurnConfig {
        max_turns: 100,                // 最大100回合
        turn_timeout: 30.0,            // 30秒回合超时
    },
};

// 使用自定义配置创建战斗管理器
let mut battle_manager = BattleManager::with_config(config);
```

### 💥 伤害修饰符系统

```rust
use game::battle_system::damage_calculator::{DamageCalculator, DamageModifier, DamageModifierType};

let mut damage_calc = DamageCalculator::new();

// 添加全局伤害增强
damage_calc.add_global_modifier(DamageModifier {
    modifier_type: DamageModifierType::DamageIncrease(0.2), // 20% 伤害增加
    source: "战斗狂热".to_string(),
    duration: Some(5), // 持续5回合
    priority: 1,
});

// 添加单位特定修饰符
damage_calc.add_unit_modifier("火焰法师", DamageModifier {
    modifier_type: DamageModifierType::ElementalAmplification(DamageType::Burn, 0.5),
    source: "火焰专精".to_string(),
    duration: None, // 永久效果
    priority: 2,
});
```

### 🔮 状态效果管理

```rust
use game::battle_system::status_effect_processor::StatusEffectProcessor;

let mut status_processor = StatusEffectProcessor::new();

// 应用Buff到角色
status_processor.apply_buff_to_unit(&mut hero, enhanced_buff, 3); // 持续3回合

// 处理回合结束的状态效果
status_processor.process_turn_end_effects(&mut all_units);

// 移除过期效果
status_processor.cleanup_expired_effects(&mut all_units);
```

### ✨ 技能释放系统

```rust
use game::battle_system::skill_caster::SkillCaster;

let mut skill_caster = SkillCaster::new();

// 释放单体技能
let targets = vec![&mut enemy];
let result = skill_caster.cast_skill(
    &mut caster,
    &skill,
    targets,
    &mut damage_calculator,
    &mut battle_logger
);

// 释放群体技能
let all_enemies = vec![&mut enemy1, &mut enemy2, &mut enemy3];
let result = skill_caster.cast_aoe_skill(
    &mut caster,
    &aoe_skill,
    all_enemies,
    &mut damage_calculator,
    &mut battle_logger
);
```

---

## 🧪 演示程序 {#demos}

### 📋 可用的演示程序

```bash
# 基础功能演示
cargo run --bin battle_demo

# 实际战斗演示（包含1vs1和1vsN）
cargo run --bin battle_demo_real

# 平衡战斗演示
cargo run --bin battle_demo_balanced

# 多战斗系统演示（所有5种战斗模式）
cargo run --bin battle_demo_systems

# 状态效果系统测试
cargo run --bin status_effect_test

# 技能系统测试
cargo run --bin skill_system_test

# AI战斗演示
cargo run --bin ai_battle_demo

# 配置系统演示
cargo run --bin config_demo
```

### 🎯 推荐学习顺序

1. **`battle_demo`** - 了解基础概念和系统结构
2. **`battle_demo_real`** - 观看实际战斗过程
3. **`battle_demo_systems`** - 体验不同战斗模式
4. **`status_effect_test`** - 学习状态效果系统
5. **`skill_system_test`** - 理解技能机制
6. **`config_demo`** - 掌握配置和定制

---

## 🎮 实际应用示例

### 简单的战斗游戏循环

```rust
use game::battle_system::battle_manager::BattleManager;
use game::character::Character;
use game::monster::{Monster, MonsterKind};

fn main() {
    // 创建角色
    let mut hero = Character::new("冒险者".to_string(), 1, 100, 50, 20, 15, 12);
    
    // 战斗管理器
    let mut battle_manager = BattleManager::new();
    
    // 游戏主循环
    for level in 1..=5 {
        println!("🎮 进入第{}关！", level);
        
        // 根据关卡创建敌人
        let mut enemy = create_enemy_for_level(level);
        
        // 开始战斗
        let result = battle_manager.start_1v1_battle(&mut hero, &mut enemy);
        
        match result.winner {
            Some(winner) if winner == hero.get_name() => {
                println!("🎉 {}胜利！获得经验值和奖励！", winner);
                hero.level_up(); // 升级
            },
            Some(winner) => {
                println!("💀 {}败北！游戏结束！", hero.get_name());
                break;
            },
            None => {
                println!("⚔️ 平局！重新开始本关！");
                continue;
            }
        }
        
        // 显示战斗统计
        let stats = battle_manager.get_battle_stats();
        println!("📊 本次战斗统计: {:?}", stats);
    }
}

fn create_enemy_for_level(level: u32) -> Monster {
    let (kind, hp, atk) = match level {
        1 => (MonsterKind::Goblin, 60, 10),
        2 => (MonsterKind::Goblin, 80, 15),
        3 => (MonsterKind::Orc, 120, 20),
        4 => (MonsterKind::Orc, 150, 25),
        5 => (MonsterKind::Dragon, 200, 35),
        _ => (MonsterKind::Goblin, 60, 10),
    };
    
    Monster::new(kind, format!("{}级敌人", level), level, hp, 30, atk, 8, 10)
}
```

---

## 🔍 故障排除

### 常见问题

**Q: 编译错误 "trait `BattleUnit` is not implemented"**
```rust
// 确保你的结构体实现了所有必要的trait
impl BattleUnit for MyCharacter {}
// 这需要先实现 BasicAttributes, Vitality, Skillable, CombatAttributes, StatusEffect, BattleState
```

**Q: 战斗无法开始，提示"单位已死亡"**
```rust
// 确保战斗单位的生命值大于0
if hero.get_current_health() <= 0 {
    hero.set_current_health(hero.get_max_health()); // 恢复满血
}
```

**Q: 技能无法释放**
```rust
// 检查法力值和冷却时间
if hero.get_current_mana() < skill.mana_cost {
    hero.restore_mana(50); // 恢复法力
}
```

---

## 📚 进一步学习

- 📖 查看 `BATTLE_SYSTEM_COMPLETION_REPORT.md` 了解完整功能
- 🔧 查看源代码中的详细注释和文档
- 🧪 运行所有演示程序体验不同特性
- 💡 参考 `src/bin/` 中的示例代码

**祝你在RPG冒险中取得胜利！** 