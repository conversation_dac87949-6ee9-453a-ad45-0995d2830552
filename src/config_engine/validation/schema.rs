//! # Schema验证器模块
//!
//! 提供JSON Schema验证功能，确保数据结构的正确性
//!
//! ## 核心功能
//!
//! - JSON Schema验证
//! - 自定义Schema规则
//! - Schema注册和管理
//! - 验证结果详细报告

use crate::config_engine::validation::{
    ErrorSeverity, ValidatableData, ValidationContext, ValidationError, ValidationResult,
    ValidationSource, Validator,
};
use crate::config_engine::Result;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Schema规则定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchemaRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// JSON Schema内容
    pub schema: serde_json::Value,
    /// 适用的数据类型
    pub target_types: Vec<String>,
    /// 是否启用
    pub enabled: bool,
    /// 严重性级别
    pub severity: ErrorSeverity,
}

impl SchemaRule {
    pub fn new(id: String, name: String, schema: serde_json::Value) -> Self {
        Self {
            id,
            name,
            schema,
            target_types: Vec::new(),
            enabled: true,
            severity: ErrorSeverity::Error,
        }
    }

    pub fn with_target_type(mut self, target_type: String) -> Self {
        self.target_types.push(target_type);
        self
    }

    pub fn with_severity(mut self, severity: ErrorSeverity) -> Self {
        self.severity = severity;
        self
    }

    pub fn applies_to(&self, data_type: &str) -> bool {
        self.enabled
            && (self.target_types.is_empty() || self.target_types.contains(&data_type.to_string()))
    }
}

/// Schema验证器
pub struct SchemaValidator {
    /// Schema规则集合
    rules: HashMap<String, SchemaRule>,
}

impl SchemaValidator {
    pub fn new() -> Self {
        Self {
            rules: HashMap::new(),
        }
    }

    /// 添加Schema规则
    pub fn add_rule(&mut self, rule: SchemaRule) {
        self.rules.insert(rule.id.clone(), rule);
    }

    /// 移除Schema规则
    pub fn remove_rule(&mut self, rule_id: &str) -> bool {
        self.rules.remove(rule_id).is_some()
    }

    /// 获取Schema规则
    pub fn get_rule(&self, rule_id: &str) -> Option<&SchemaRule> {
        self.rules.get(rule_id)
    }

    /// 验证JSON数据
    pub fn validate_json(&self, data: &serde_json::Value, data_type: &str) -> ValidationResult {
        let mut result = ValidationResult::new();

        for rule in self.rules.values() {
            if rule.applies_to(data_type) {
                let rule_result = self.validate_against_schema(data, rule);
                result.merge(rule_result);
            }
        }

        result
    }

    /// 针对特定Schema验证
    fn validate_against_schema(
        &self,
        data: &serde_json::Value,
        rule: &SchemaRule,
    ) -> ValidationResult {
        let mut result = ValidationResult::new();

        // 简化的Schema验证实现
        // 实际应该使用jsonschema库进行完整的JSON Schema验证
        if let Err(error_msg) = self.basic_schema_validation(data, &rule.schema) {
            result.add_error(
                ValidationError::new(
                    rule.id.clone(),
                    format!(
                        "Schema validation failed for rule '{}': {}",
                        rule.name, error_msg
                    ),
                    ValidationSource::Schema,
                )
                .with_severity(rule.severity.clone()),
            );
        }

        result
    }

    /// 基础Schema验证
    fn basic_schema_validation(
        &self,
        data: &serde_json::Value,
        schema: &serde_json::Value,
    ) -> Result<(), String> {
        // 简化实现：检查基本类型匹配
        if let Some(schema_type) = schema.get("type") {
            if let Some(expected_type) = schema_type.as_str() {
                let actual_type = match data {
                    serde_json::Value::Object(_) => "object",
                    serde_json::Value::Array(_) => "array",
                    serde_json::Value::String(_) => "string",
                    serde_json::Value::Number(_) => "number",
                    serde_json::Value::Bool(_) => "boolean",
                    serde_json::Value::Null => "null",
                };

                if actual_type != expected_type {
                    return Err(format!(
                        "Type mismatch: expected {}, got {}",
                        expected_type, actual_type
                    ));
                }
            }
        }

        // 检查必需字段
        if let Some(required) = schema.get("required") {
            if let Some(required_array) = required.as_array() {
                if let Some(data_obj) = data.as_object() {
                    for required_field in required_array {
                        if let Some(field_name) = required_field.as_str() {
                            if !data_obj.contains_key(field_name) {
                                return Err(format!("Required field '{}' is missing", field_name));
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }
}

impl Default for SchemaValidator {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Validator for SchemaValidator {
    fn validator_name(&self) -> &str {
        "SchemaValidator"
    }

    fn validate(&self, context: &mut ValidationContext, data: &dyn ValidatableData) -> Result<()> {
        let json_data = data.to_json_value();
        let data_type = data.data_type();

        let validation_result = self.validate_json(&json_data, &data_type);
        context.result.merge(validation_result);

        Ok(())
    }

    fn supports(&self, data_type: &str) -> bool {
        // Schema验证器支持所有数据类型
        true
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_schema_rule_creation() {
        let schema = serde_json::json!({
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "name": {"type": "string"}
            },
            "required": ["id", "name"]
        });

        let rule = SchemaRule::new("test_rule".to_string(), "Test Rule".to_string(), schema)
            .with_target_type("test_object".to_string());

        assert_eq!(rule.id, "test_rule");
        assert_eq!(rule.name, "Test Rule");
        assert!(rule.applies_to("test_object"));
        assert!(!rule.applies_to("other_object"));
    }

    #[test]
    fn test_schema_validator() {
        let mut validator = SchemaValidator::new();

        let schema = serde_json::json!({
            "type": "object",
            "required": ["id", "name"]
        });

        let rule = SchemaRule::new("object_rule".to_string(), "Object Rule".to_string(), schema)
            .with_target_type("object".to_string());

        validator.add_rule(rule);

        // 有效数据
        let valid_data = serde_json::json!({
            "id": "123",
            "name": "test"
        });

        let result = validator.validate_json(&valid_data, "object");
        assert!(result.is_valid);

        // 无效数据（缺少必需字段）
        let invalid_data = serde_json::json!({
            "id": "123"
        });

        let result = validator.validate_json(&invalid_data, "object");
        assert!(!result.is_valid);
        assert!(result.errors.len() > 0);
    }

    #[test]
    fn test_basic_schema_validation() {
        let validator = SchemaValidator::new();

        // 类型匹配测试
        let string_schema = serde_json::json!({"type": "string"});
        let string_data = serde_json::Value::String("test".to_string());
        assert!(validator
            .basic_schema_validation(&string_data, &string_schema)
            .is_ok());

        let number_data = serde_json::Value::Number(serde_json::Number::from(123));
        assert!(validator
            .basic_schema_validation(&number_data, &string_schema)
            .is_err());

        // 必需字段测试
        let object_schema = serde_json::json!({
            "type": "object",
            "required": ["id", "name"]
        });

        let valid_object = serde_json::json!({
            "id": "123",
            "name": "test",
            "optional": "value"
        });
        assert!(validator
            .basic_schema_validation(&valid_object, &object_schema)
            .is_ok());

        let invalid_object = serde_json::json!({
            "id": "123"
        });
        assert!(validator
            .basic_schema_validation(&invalid_object, &object_schema)
            .is_err());
    }
}
