//! # 配置引擎顶层API
//!
//! 提供用户使用的主要接口

use std::sync::Arc;
use std::time::Duration;

use crate::config_engine::{
    cache::CacheManager,
    config::ConfigManager,
    core::{ExecutionOptions, RuleContext, RuleResult},
    error::ConfigEngineError,
    rules::RuleExecutor,
    types::TypeRegistry,
    validation::Validator,
    CacheConfig, ConfigSourceType, Result,
};

/// 配置引擎的主要入口点
///
/// 这是用户与配置引擎交互的主要接口，提供了所有核心功能：
/// - 规则执行
/// - 配置管理
/// - 类型注册
/// - 缓存控制
/// - 热重载
///
/// # 示例
///
/// ```rust
/// use game::config_engine::{ConfigEngine, RuleContext};
///
/// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
/// let engine = ConfigEngine::builder()
///     .add_config_source("./game_configs")
///     .enable_cache(true)
///     .cache_size(10000)
///     .build()
///     .await?;
///
/// let context = RuleContext::new()
///     .set("player_level", 50)
///     .set("terrain", "forest");
///
/// let materials = engine.execute("material_discovery", &context).await?;
/// # Ok(())
/// # }
/// ```
pub struct ConfigEngine {
    config_manager: Arc<ConfigManager>,
    rule_executor: Arc<dyn RuleExecutor>,
    type_registry: Arc<TypeRegistry>,
    cache_manager: Arc<CacheManager>,
    validator: Arc<dyn Validator>,
}

impl ConfigEngine {
    /// 创建配置引擎构建器
    pub fn builder() -> ConfigEngineBuilder {
        ConfigEngineBuilder::new()
    }

    /// 执行指定的规则
    ///
    /// # 参数
    /// - `rule_name`: 要执行的规则名称
    /// - `context`: 规则执行上下文，包含输入参数
    ///
    /// # 返回
    /// 规则执行结果，包含输出数据和执行元信息
    ///
    /// # 示例
    ///
    /// ```rust
    /// # use game::config_engine::{ConfigEngine, RuleContext};
    /// # async fn example(engine: ConfigEngine) -> Result<(), Box<dyn std::error::Error>> {
    /// let context = RuleContext::new()
    ///     .set("terrain_type", "mountain")
    ///     .set("discovery_method", "careful_search");
    ///
    /// let result = engine.execute("material_discovery", &context).await?;
    ///
    /// // 获取发现的材料列表
    /// let materials: Vec<String> = result.get_array("discovered_materials")?;
    /// println!("发现的材料: {:?}", materials);
    /// # Ok(())
    /// # }
    /// ```
    pub async fn execute(&self, rule_name: &str, context: &RuleContext) -> Result<RuleResult> {
        self.execute_with_options(rule_name, context, &ExecutionOptions::default())
            .await
    }

    /// 使用指定选项执行规则
    ///
    /// 提供更细粒度的执行控制，包括缓存策略、超时设置等
    pub async fn execute_with_options(
        &self,
        rule_name: &str,
        context: &RuleContext,
        options: &ExecutionOptions,
    ) -> Result<RuleResult> {
        // 输入验证
        self.validator.validate_rule_input(rule_name, context)?;

        // 检查缓存
        if options.use_cache {
            if let Some(cached_result) = self.cache_manager.get(rule_name, context).await? {
                return Ok(cached_result);
            }
        }

        // 执行规则
        let result = self
            .rule_executor
            .execute(rule_name, context, options)
            .await?;

        // 缓存结果
        if options.use_cache {
            self.cache_manager.put(rule_name, context, &result).await?;
        }

        Ok(result)
    }

    /// 批量执行多个规则
    ///
    /// 优化的批量执行，可以并行处理多个规则，提高吞吐量
    pub async fn execute_batch(
        &self,
        requests: Vec<(String, RuleContext)>,
    ) -> Result<Vec<Result<RuleResult>>> {
        self.rule_executor.execute_batch(requests).await
    }

    /// 获取类型注册表的引用
    ///
    /// 用于动态注册新类型或查询类型信息
    pub fn type_registry(&self) -> &TypeRegistry {
        &self.type_registry
    }

    /// 重新加载所有配置
    ///
    /// 从配置源重新加载配置，支持热更新
    ///
    /// # 示例
    ///
    /// ```rust
    /// # use game::config_engine::ConfigEngine;
    /// # async fn example(engine: ConfigEngine) -> Result<(), Box<dyn std::error::Error>> {
    /// // 配置文件被修改后，重新加载
    /// engine.reload_config().await?;
    ///
    /// // 新的规则立即生效，无需重启应用
    /// # Ok(())
    /// # }
    /// ```
    pub async fn reload_config(&self) -> Result<()> {
        self.config_manager.reload_all().await?;
        self.cache_manager.invalidate_all().await?;
        Ok(())
    }

    /// 获取规则列表
    ///
    /// 返回当前已加载的所有规则名称
    pub async fn list_rules(&self) -> Result<Vec<String>> {
        self.rule_executor.list_rules().await
    }

    /// 验证指定规则的配置
    ///
    /// 检查规则定义是否正确，输入/输出Schema是否匹配
    pub async fn validate_rule(&self, rule_name: &str) -> Result<()> {
        self.validator.validate_rule_definition(rule_name).await
    }

    /// 获取缓存统计信息
    ///
    /// 用于监控和调优缓存性能
    pub async fn cache_stats(&self) -> Result<CacheStats> {
        let moka_stats = self.cache_manager.get_stats().await;
        Ok(CacheStats {
            total_entries: moka_stats.total_cache_size() as usize,
            hit_count: moka_stats.total_hits(),
            miss_count: moka_stats.total_misses(),
            hit_rate: moka_stats.hit_rate(),
            memory_usage_bytes: moka_stats.total_cache_weight() as usize,
        })
    }

    /// 清空指定规则的缓存
    pub async fn clear_cache(&self, rule_name: Option<&str>) -> Result<()> {
        match rule_name {
            Some(name) => self.cache_manager.invalidate_rule(name).await,
            None => self.cache_manager.invalidate_all().await,
        }
    }

    /// 启用调试模式
    ///
    /// 开启后会记录详细的执行追踪信息，便于调试
    pub async fn enable_debug_mode(&self, enabled: bool) -> Result<()> {
        self.rule_executor.set_debug_mode(enabled).await
    }

    /// 获取规则执行历史
    ///
    /// 返回最近的规则执行记录，用于调试和分析
    pub async fn execution_history(&self, limit: usize) -> Result<Vec<ExecutionRecord>> {
        self.rule_executor.execution_history(limit).await
    }
}

/// 配置引擎构建器
///
/// 使用构建者模式创建配置引擎实例，提供灵活的配置选项
pub struct ConfigEngineBuilder {
    config_sources: Vec<ConfigSourceType>,
    cache_enabled: bool,
    cache_size: usize,
    cache_ttl: Option<Duration>,
    hot_reload_enabled: bool,
    debug_mode: bool,
    execution_timeout: Option<Duration>,
    max_concurrent_executions: usize,
}

impl ConfigEngineBuilder {
    pub fn new() -> Self {
        Self {
            config_sources: Vec::new(),
            cache_enabled: true,
            cache_size: 10000,
            cache_ttl: Some(Duration::from_secs(3600)), // 1小时默认TTL
            hot_reload_enabled: false,
            debug_mode: false,
            execution_timeout: Some(Duration::from_secs(30)),
            max_concurrent_executions: 100,
        }
    }

    /// 添加配置源
    ///
    /// # 示例
    ///
    /// ```rust
    /// use game::config_engine::{ConfigEngine, ConfigSourceType};
    ///
    /// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let engine = ConfigEngine::builder()
    ///     .add_config_source("./configs")  // 文件系统
    ///     .add_config_source_typed(ConfigSourceType::Http {
    ///         url: "https://config-api.example.com".to_string(),
    ///         auth: Some("Bearer token123".to_string()),
    ///     })  // HTTP配置源
    ///     .build()
    ///     .await?;
    /// # Ok(())
    /// # }
    /// ```
    pub fn add_config_source(mut self, source: impl Into<ConfigSourceType>) -> Self {
        self.config_sources.push(source.into());
        self
    }

    pub fn add_config_source_typed(mut self, source: ConfigSourceType) -> Self {
        self.config_sources.push(source);
        self
    }

    /// 启用/禁用缓存
    pub fn enable_cache(mut self, enabled: bool) -> Self {
        self.cache_enabled = enabled;
        self
    }

    /// 设置缓存大小（条目数量）
    pub fn cache_size(mut self, size: usize) -> Self {
        self.cache_size = size;
        self
    }

    /// 设置缓存TTL（生存时间）
    pub fn cache_ttl(mut self, ttl: Duration) -> Self {
        self.cache_ttl = Some(ttl);
        self
    }

    /// 启用热重载
    ///
    /// 开启后会监听配置文件变化，自动重新加载
    pub fn enable_hot_reload(mut self, enabled: bool) -> Self {
        self.hot_reload_enabled = enabled;
        self
    }

    /// 启用调试模式
    pub fn enable_debug(mut self, enabled: bool) -> Self {
        self.debug_mode = enabled;
        self
    }

    /// 设置规则执行超时
    pub fn execution_timeout(mut self, timeout: Duration) -> Self {
        self.execution_timeout = Some(timeout);
        self
    }

    /// 设置最大并发执行数
    pub fn max_concurrent_executions(mut self, max: usize) -> Self {
        self.max_concurrent_executions = max;
        self
    }

    /// 构建配置引擎实例
    ///
    /// 根据设置的选项创建并初始化配置引擎
    pub async fn build(self) -> Result<ConfigEngine> {
        // 验证配置
        if self.config_sources.is_empty() {
            return Err(ConfigEngineError::InvalidConfiguration(
                "至少需要一个配置源".to_string(),
            ));
        }

        // 创建各个组件
        let config_manager =
            Arc::new(ConfigManager::new(self.config_sources, self.hot_reload_enabled).await?);

        let type_registry = Arc::new(TypeRegistry::new());

        let cache_manager = Arc::new(CacheManager::new(CacheConfig::default()).await?);

        let validator = Arc::new(Validator::new(type_registry.clone()));

        let rule_executor = Arc::new(
            RuleExecutor::new(
                config_manager.clone(),
                type_registry.clone(),
                self.debug_mode,
                self.execution_timeout,
                self.max_concurrent_executions,
            )
            .await?,
        );

        // 初始化配置
        config_manager.load_initial_config().await?;

        Ok(ConfigEngine {
            config_manager,
            rule_executor,
            type_registry,
            cache_manager,
            validator,
        })
    }
}

impl Default for ConfigEngineBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 缓存统计信息
#[derive(Debug, Clone)]
pub struct CacheStats {
    pub total_entries: usize,
    pub hit_count: u64,
    pub miss_count: u64,
    pub hit_rate: f64,
    pub memory_usage_bytes: usize,
}

/// 规则执行记录
#[derive(Debug, Clone)]
pub struct ExecutionRecord {
    pub rule_name: String,
    pub input_context: RuleContext,
    pub result: Option<RuleResult>,
    pub execution_time: Duration,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub cache_hit: bool,
    pub error: Option<String>,
}

// 便利的类型转换实现
impl From<&str> for ConfigSourceType {
    fn from(path: &str) -> Self {
        ConfigSourceType::FileSystem {
            path: path.to_string(),
        }
    }
}

impl From<String> for ConfigSourceType {
    fn from(path: String) -> Self {
        ConfigSourceType::FileSystem { path }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_builder_default() {
        let builder = ConfigEngineBuilder::new();
        assert!(builder.cache_enabled);
        assert_eq!(builder.cache_size, 10000);
        assert!(!builder.hot_reload_enabled);
    }

    #[test]
    fn test_config_source_conversion() {
        let source: ConfigSourceType = "./configs".into();
        match source {
            ConfigSourceType::FileSystem { path } => {
                assert_eq!(path, "./configs");
            }
            _ => panic!("Expected FileSystem source"),
        }
    }
}
