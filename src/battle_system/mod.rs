pub mod battle_unit;
pub mod battle_event;
pub mod damage_calculator;
pub mod skill_caster;
pub mod battle_types;
pub mod impls;
pub mod adapters;
pub mod battle_config;
pub mod battle_errors;

// 新的简化trait系统
pub mod simplified_battle_traits;


// 战斗视图模块（方案二：分离战斗视图和聚合根）
pub mod battle_view;

// 战斗计算器模块
pub mod combat_calculator;

// 智能AI系统
pub mod intelligent_ai;

// Buff适配器
pub mod buff_adapter;

pub mod performance;  // 第五阶段新增：性能优化模块

pub use battle_unit::*;
pub use battle_event::*;
pub use damage_calculator::*;
pub use skill_caster::*;
pub use battle_types::*;
pub use impls::*;
pub use adapters::*;
pub use battle_config::*;


// 导出新的简化trait（推荐用于新代码）
pub use simplified_battle_traits::{
    BattleEntity, LifeForce, CombatCapable, SkillUser, TimeAware,
    BasicBattleUnit, FullBattleUnit,
    DamageResult, HealResult, BattleStatus, BasicBattleStatus,
};


// 导出战斗视图（方案二架构）
pub use battle_view::BattleView;

// 导出性能优化组件（核心功能）
pub use performance::{
    PerformanceConfig, PerformanceStats, PerformanceManager
};

/// 战斗系统版本信息
pub const BATTLE_SYSTEM_VERSION: &str = "2.3.0-performance-optimized";

