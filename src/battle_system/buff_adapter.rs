/// Buff适配器
/// 
/// 用于在新旧Buff系统之间进行转换

use crate::skill::buff::Buff as OfficialBuff;

/// 简化的Buff结构体，用于战斗系统
#[derive(Debug, Clone)]
pub struct SimplifiedBuff {
    pub skill_type: String,
    pub buff_name: String,
    pub effect_value: f32,
    pub duration: f32,
    pub is_permanent: bool,
}

impl SimplifiedBuff {
    pub fn new(skill_type: String, buff_name: String, effect_value: f32, duration: f32) -> Self {
        Self {
            skill_type,
            buff_name,
            effect_value,
            duration,
            is_permanent: false,
        }
    }
    
    /// 从官方Buff结构转换
    pub fn from_official_buff(buff: &OfficialBuff) -> Self {
        SimplifiedBuff::from(buff)
    }
    
    /// 转换为官方Buff结构
    pub fn to_official_buff(&self) -> OfficialBuff {
        OfficialBuff::from(self.clone())
    }
}

impl From<&OfficialBuff> for SimplifiedBuff {
    fn from(buff: &OfficialBuff) -> Self {
        // 根据Buff名称推断技能类型
        let skill_type = match buff.name.as_str() {
            "暴击强化" | "暴击伤害提升" => "crit_bonus",
            "暴击率提升" => "crit_damage",
            "闪避强化" | "敏捷提升" => "evasion",
            "闪避提升" => "dodge",
            "治疗暴击" => "heal_crit",
            "治疗强化" => "heal_crit_damage",
            "攻击范围提升" => "range_bonus",
            "武器延伸" => "weapon_reach",
            "技能冷却减少" => "cooldown_reduction",
            "法力效率" => "mana_efficiency",
            "中毒" => "poison",
            "燃烧" => "burn",
            "冰冻" => "freeze",
            "眩晕" => "stun",
            "护盾" => "shield",
            "恢复" | "再生" => "regeneration",
            "加速" => "haste",
            "减速" => "slow",
            "防御姿态" => "defense_boost",
            "法力恢复" => "mana_regen",
            _ => "unknown",
        }.to_string();
        
        // 根据Buff类型估算效果值
        let effect_value = match skill_type.as_str() {
            "crit_bonus" | "crit_damage" => 10.0, // 10%暴击提升
            "evasion" | "dodge" => 15.0, // 15%闪避提升
            "heal_crit" | "heal_crit_damage" => 8.0, // 8%治疗暴击
            "range_bonus" => 1.0, // 1.0范围提升
            "cooldown_reduction" => 20.0, // 20%冷却减少
            "mana_efficiency" => 25.0, // 25%法力效率
            "poison" | "burn" => 5.0, // 每秒5点伤害
            "regeneration" | "mana_regen" => 3.0, // 每秒3点恢复
            "haste" => 30.0, // 30%加速
            "slow" => -20.0, // 20%减速
            "defense_boost" => 50.0, // 50点防御提升
            _ => 1.0,
        };
        
        Self {
            skill_type,
            buff_name: buff.name.clone(),
            effect_value,
            duration: buff.duration as f32,
            is_permanent: buff.is_permanent,
        }
    }
}

impl From<SimplifiedBuff> for OfficialBuff {
    fn from(simple_buff: SimplifiedBuff) -> Self {
        use crate::skill::buff::buff::BuffType;
        
        OfficialBuff::new(
            1, // 临时ID
            simple_buff.buff_name,
            simple_buff.duration,
            BuffType::Buff, // 简化为增益类型
        )
    }
}

/// Buff转换工具
pub struct BuffConverter;

impl BuffConverter {
    /// 将官方Buff转换为简化Buff
    pub fn to_simplified(buffs: &[OfficialBuff]) -> Vec<SimplifiedBuff> {
        buffs.iter().map(SimplifiedBuff::from).collect()
    }
    
    /// 将简化Buff转换为官方Buff
    pub fn to_official(buffs: &[SimplifiedBuff]) -> Vec<OfficialBuff> {
        buffs.iter().cloned().map(OfficialBuff::from).collect()
    }
    
    /// 创建特定类型的简化Buff
    pub fn create_effect_buff(effect_type: &str, value: f32, duration: f32) -> SimplifiedBuff {
        let (skill_type, buff_name) = match effect_type {
            "poison" => ("poison", "中毒"),
            "burn" => ("burn", "燃烧"),
            "regeneration" => ("regeneration", "恢复"),
            "haste" => ("haste", "加速"),
            "slow" => ("slow", "减速"),
            "defense_boost" => ("defense_boost", "防御姿态"),
            "mana_regen" => ("mana_regen", "法力恢复"),
            _ => ("unknown", "未知效果"),
        };
        
        SimplifiedBuff::new(
            skill_type.to_string(),
            buff_name.to_string(),
            value,
            duration,
        )
    }
}