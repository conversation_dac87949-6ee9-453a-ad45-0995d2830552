use game::battle_system::BattleConfig;
use game::battle_unit::BattleConfig;
use game::world_map::domain::{BuildingId, DangerLevel, NodeId, RegionId, TerrainType, WorldLayer};
use game::world_map::infrastructure::{QueryRange, SpatialIndex, WorldMapConfig};
use game::world_map::services::{
    BattleLocationInfo, EnvironmentalEffect, ExplorationContext, ExplorationType, ExplorerInfo,
    WorldExplorationService, WorldMapBattleAdapter,
};
/// 世界地图模块演示程序
/// 展示世界地图的各种功能，包括配置加载、空间查询、探索服务等
use game::world_map::Position;
use game::{Position, WorldLayer};

fn main() {
    println!("🗺️ 世界地图模块演示程序");
    println!("{}", "=".repeat(50));

    // 1. 配置系统演示
    demo_config_system();

    // 2. 位置系统演示
    demo_position_system();

    // 3. 空间索引演示
    demo_spatial_index();

    // 4. 探索服务演示
    demo_exploration_service();

    // 5. 战斗集成演示
    demo_battle_integration();

    println!("\n🎉 演示完成！世界地图模块功能正常运行。");
}

/// 演示配置系统
fn demo_config_system() {
    println!("\n📋 1. 配置系统演示");
    println!("{}", "-".repeat(30));

    // 创建默认配置
    let config = WorldMapConfig::default();
    println!("✅ 创建默认配置成功");

    // 验证配置
    match config.validate() {
        Ok(()) => println!("✅ 配置验证通过"),
        Err(e) => println!("❌ 配置验证失败: {}", e),
    }

    // 显示一些配置信息
    println!("📊 配置信息:");
    println!(
        "   - 默认灵气密度: {}",
        config.world_settings.default_spiritual_density
    );
    println!("   - 时间流速: {}", config.world_settings.time_scale);
    println!(
        "   - 基础发现概率: {}",
        config.exploration.base_discovery_chance
    );
    println!("   - 区域数量: {}", config.regions.len());

    // 尝试加载配置文件
    match WorldMapConfig::load_from_file("world_map_config.toml") {
        Ok(loaded_config) => {
            println!("✅ 从文件加载配置成功");
            println!("   - 加载的区域数量: {}", loaded_config.regions.len());
        }
        Err(e) => println!("⚠️ 从文件加载配置失败: {}", e),
    }
}

/// 演示位置系统
fn demo_position_system() {
    println!("\n📍 2. 位置系统演示");
    println!("{}", "-".repeat(30));

    // 创建不同类型的位置
    let mortal_pos = Position::new_grid(100, 200, WorldLayer::Mortal);
    let spirit_pos = Position::new_3d(50, 75, 10, WorldLayer::Spirit);
    let secret_realm_pos = Position::new_grid(0, 0, WorldLayer::SecretRealm(42));

    println!("📍 位置信息:");
    println!("   - 凡间位置: {}", mortal_pos);
    println!("   - 灵界位置: {}", spirit_pos);
    println!("   - 秘境位置: {}", secret_realm_pos);

    // 距离计算
    let distance = mortal_pos.distance_to(&Position::new_grid(150, 250, WorldLayer::Mortal));
    println!("📏 距离计算: {:.2}", distance);

    // 方向计算
    let target = Position::new_grid(120, 220, WorldLayer::Mortal);
    if let Some(direction) = mortal_pos.direction_to(&target) {
        println!("🧭 方向: {}", direction);
    }

    // 周围位置获取
    let adjacent = mortal_pos.get_adjacent_positions();
    println!("🔄 周围位置数量: {}", adjacent.len());

    // 半径内位置
    let nearby = mortal_pos.get_positions_within_radius(5);
    println!("📍 半径5内位置数量: {}", nearby.len());

    // 传送能力检查
    println!("🌀 传送能力检查:");
    println!(
        "   - 凡间→灵界: {}",
        mortal_pos.can_teleport_to(&spirit_pos)
    );
    println!(
        "   - 灵界→凡间: {}",
        spirit_pos.can_teleport_to(&mortal_pos)
    );
    println!(
        "   - 凡间→秘境: {}",
        mortal_pos.can_teleport_to(&secret_realm_pos)
    );
}

/// 演示空间索引
fn demo_spatial_index() {
    println!("\n🗂️ 3. 空间索引演示");
    println!("{}", "-".repeat(30));

    let mut spatial_index = SpatialIndex::new(50);

    // 添加一些对象到索引
    let region_id = RegionId::new(1);
    let node_id = NodeId::new(1);
    let building_id = BuildingId::new(1);

    let world_layer = WorldLayer::new(
        "1234".to_string(),
        "1234".to_string(),
        vec![vec![0; 100]; 100],
    );

    let center_pos = Position::new_grid(100, 100, world_layer);
    spatial_index.add_region(region_id, center_pos);
    spatial_index.add_resource_node(node_id, Position::new_grid(110, 110, WorldLayer::Mortal));
    spatial_index.add_building(building_id, Position::new_grid(90, 90, WorldLayer::Mortal));

    println!("✅ 添加对象到空间索引:");
    println!("   - 区域ID: {:?}", region_id);
    println!("   - 资源节点ID: {:?}", node_id);
    println!("   - 建筑ID: {:?}", building_id);

    // 点查询
    let point_result = spatial_index.query(QueryRange::Point(center_pos));
    println!("🔍 点查询结果:");
    println!("   - 区域数量: {}", point_result.regions.len());
    println!("   - 资源节点数量: {}", point_result.resource_nodes.len());
    println!("   - 建筑数量: {}", point_result.buildings.len());

    // 圆形范围查询
    let circle_result = spatial_index.query(QueryRange::Circle {
        center: center_pos,
        radius: 50.0,
    });
    println!("🔍 圆形范围查询结果:");
    println!("   - 区域数量: {}", circle_result.regions.len());
    println!("   - 资源节点数量: {}", circle_result.resource_nodes.len());
    println!("   - 建筑数量: {}", circle_result.buildings.len());

    // 获取统计信息
    let stats = spatial_index.get_statistics();
    println!("📊 空间索引统计:");
    println!("   - 总对象数量: {}", stats.total_objects());
    println!("   - 总网格数量: {}", stats.total_grids());
    println!("   - 层级数量: {}", stats.layers.len());
}

/// 演示探索服务
fn demo_exploration_service() {
    println!("\n🔍 4. 探索服务演示");
    println!("{}", "-".repeat(30));

    let config = WorldMapConfig::default();
    let exploration_service = WorldExplorationService::new(config);

    // 创建探索者信息
    let explorer = ExplorerInfo {
        id: "demo_explorer".to_string(),
        level: 5,
        exploration_skill: 3,
        perception: 4,
        current_stamina: 100,
        max_stamina: 100,
        known_regions: Vec::new(),
        special_abilities: vec!["灵识探测".to_string()],
    };

    println!("👤 探索者信息:");
    println!("   - ID: {}", explorer.id);
    println!("   - 等级: {}", explorer.level);
    println!("   - 探索技能: {}", explorer.exploration_skill);
    println!(
        "   - 当前体力: {}/{}",
        explorer.current_stamina, explorer.max_stamina
    );

    // 创建探索上下文
    let context = ExplorationContext {
        explorer,
        current_position: Position::new_grid(0, 0, WorldLayer::Mortal),
        target_position: Position::new_grid(50, 50, WorldLayer::Mortal),
        exploration_type: ExplorationType::Thorough,
        exploration_time: 60,
    };

    println!("🎯 探索任务:");
    println!("   - 起始位置: {}", context.current_position);
    println!("   - 目标位置: {}", context.target_position);
    println!("   - 探索类型: {:?}", context.exploration_type);
    println!("   - 预计时间: {} 分钟", context.exploration_time);

    // 执行探索
    let result = exploration_service.explore_region(context.clone());
    println!("📋 探索结果:");
    println!("   - 成功: {}", result.success);
    println!("   - 发现数量: {}", result.discoveries.len());
    println!("   - 获得经验: {}", result.experience_gained);
    println!("   - 消耗体力: {}", result.stamina_consumed);
    println!("   - 遭遇数量: {}", result.encounters.len());
    println!("   - 结果描述: {}", result.message);

    // 显示发现的内容
    for (i, discovery) in result.discoveries.iter().enumerate() {
        println!("   🔍 发现 {}: {:?}", i + 1, discovery);
    }

    // 显示遭遇事件
    for (i, encounter) in result.encounters.iter().enumerate() {
        println!("   ⚔️ 遭遇 {}: {:?}", i + 1, encounter);
    }

    // 获取可探索方向
    let directions = exploration_service.get_explorable_directions(context.current_position);
    println!("🧭 可探索方向: {:?}", directions);

    // 获取探索建议
    let suggestions = exploration_service
        .get_exploration_suggestions(context.current_position, &context.explorer);
    println!("💡 探索建议数量: {}", suggestions.len());
    for suggestion in suggestions {
        println!(
            "   - {}: {}",
            suggestion.suggestion_type as u8, suggestion.description
        );
    }
}

/// 演示战斗集成
fn demo_battle_integration() {
    println!("\n⚔️ 5. 战斗集成演示");
    println!("{}", "-".repeat(30));

    let battle_adapter = WorldMapBattleAdapter::new();

    // 创建战斗位置信息
    let battle_location = BattleLocationInfo {
        position: Position::new_grid(100, 100, WorldLayer::Mortal),
        terrain_type: TerrainType::Mountains,
        danger_level: DangerLevel::Medium,
        region_id: Some(RegionId::new(1)),
        spiritual_density: 1.5,
        environmental_effects: vec![
            EnvironmentalEffect::HighSpiritualDensity { multiplier: 1.2 },
            EnvironmentalEffect::AncientBattlefield { exp_bonus: 0.3 },
        ],
    };

    println!("🏔️ 战斗地点信息:");
    println!("   - 位置: {}", battle_location.position);
    println!("   - 地形: {:?}", battle_location.terrain_type);
    println!("   - 危险等级: {:?}", battle_location.danger_level);
    println!("   - 灵气密度: {}", battle_location.spiritual_density);
    println!(
        "   - 环境效果数量: {}",
        battle_location.environmental_effects.len()
    );

    // 获取地形描述
    let terrain_desc = battle_adapter.get_terrain_description(battle_location.terrain_type);
    println!("📝 地形描述: {}", terrain_desc);

    // 应用地形效果到战斗配置
    let mut battle_config = BattleConfig::default();
    battle_adapter.apply_terrain_effects(&mut battle_config, &battle_location);

    // 触发地形战斗事件
    let mut battle_events = Vec::new();
    battle_adapter.trigger_terrain_battle_events(&mut battle_events, &battle_location);

    println!("🎭 触发的战斗事件:");
    for (i, event) in battle_events.iter().enumerate() {
        println!("   {}. {:?}", i + 1, event);
    }

    // 计算地形战斗加成
    let attacker_pos = Position::new_3d(100, 100, 15, WorldLayer::Mortal);
    let defender_pos = Position::new_3d(100, 100, 10, WorldLayer::Mortal);

    let combat_bonus = battle_adapter.calculate_terrain_combat_bonus(
        attacker_pos,
        defender_pos,
        battle_location.terrain_type,
    );

    println!("⚡ 地形战斗加成:");
    println!("   - 高度优势: {}", combat_bonus.height_advantage);
    println!("   - 攻击加成: {:.1}%", combat_bonus.attack_bonus * 100.0);
    println!("   - 防御加成: {:.1}%", combat_bonus.defense_bonus * 100.0);
    println!("   - 命中加成: {:.1}%", combat_bonus.accuracy_bonus * 100.0);
    println!("   - 闪避加成: {:.1}%", combat_bonus.evasion_bonus * 100.0);

    // 获取推荐战斗位置
    let recommended_positions =
        battle_adapter.get_recommended_battle_positions(battle_location.position, 10, 4);

    println!("📍 推荐战斗位置:");
    for (i, pos) in recommended_positions.iter().enumerate() {
        println!("   {}. {}", i + 1, pos);
    }

    // 检查位置适宜性
    let is_suitable = battle_adapter.is_suitable_for_battle(battle_location.position);
    println!("✅ 位置适合战斗: {}", is_suitable);
}

/// 辅助函数：打印分隔线
#[allow(dead_code)]
fn print_separator(title: &str) {
    println!("\n{}", "=".repeat(50));
    println!("  {}", title);
    println!("{}", "=".repeat(50));
}
