//! # 完整性验证器模块
//!
//! 验证数据的完整性和一致性
//!
//! ## 核心功能
//!
//! - 引用完整性检查
//! - 数据一致性验证
//! - 约束违反检测
//! - 孤立数据检测

use crate::config_engine::validation::{
    ErrorSeverity, ValidatableData, ValidationContext, ValidationError, ValidationResult,
    ValidationSource, ValidationWarning, Validator,
};
use crate::config_engine::{error::ConfigEngineError, Result};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

/// 引用检查类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReferenceCheck {
    /// 检查实体引用是否存在
    EntityReference {
        /// 引用字段名
        field_name: String,
        /// 引用的实体类型
        target_entity_type: String,
        /// 是否必需
        required: bool,
    },
    /// 检查外键约束
    ForeignKeyConstraint {
        /// 外键字段
        foreign_key_field: String,
        /// 目标表
        target_table: String,
        /// 目标字段
        target_field: String,
        /// 级联删除规则
        cascade_delete: bool,
    },
    /// 检查循环引用
    CircularReference {
        /// 检查路径
        check_path: Vec<String>,
        /// 最大深度
        max_depth: usize,
    },
    /// 检查孤儿记录
    OrphanRecord {
        /// 父字段
        parent_field: String,
        /// 父实体类型
        parent_entity_type: String,
    },
    /// 检查数据一致性
    DataConsistency {
        /// 检查的字段组
        field_group: Vec<String>,
        /// 一致性规则
        consistency_rule: ConsistencyRule,
    },
}

/// 一致性规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConsistencyRule {
    /// 字段值必须相等
    FieldsEqual,
    /// 字段值必须不相等
    FieldsNotEqual,
    /// 数值关系验证
    NumericRelation {
        /// 关系类型
        relation: NumericRelationType,
        /// 基准字段索引
        base_field_index: usize,
        /// 比较字段索引
        compare_field_index: usize,
    },
    /// 时间关系验证
    TemporalRelation {
        /// 时间关系类型
        relation: TemporalRelationType,
        /// 时间字段索引
        time_fields: Vec<usize>,
    },
    /// 自定义验证规则
    Custom {
        /// 规则名称
        rule_name: String,
        /// 规则参数
        parameters: HashMap<String, serde_json::Value>,
    },
}

/// 数值关系类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NumericRelationType {
    /// 大于
    GreaterThan,
    /// 大于等于
    GreaterThanOrEqual,
    /// 小于
    LessThan,
    /// 小于等于
    LessThanOrEqual,
    /// 等于
    Equal,
    /// 不等于
    NotEqual,
}

/// 时间关系类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TemporalRelationType {
    /// 之前
    Before,
    /// 之后
    After,
    /// 同时
    Simultaneous,
    /// 在范围内
    Within {
        /// 时间窗口（秒）
        window_seconds: i64,
    },
}

/// 完整性检查器接口
#[async_trait::async_trait]
pub trait IntegrityChecker: Send + Sync {
    /// 检查引用完整性
    async fn check_reference_integrity(
        &self,
        data: &serde_json::Value,
        checks: &[ReferenceCheck],
    ) -> Result<ValidationResult>;

    /// 检查数据一致性
    async fn check_data_consistency(
        &self,
        data: &serde_json::Value,
        rules: &[ConsistencyRule],
    ) -> Result<ValidationResult>;

    /// 检查循环引用
    async fn check_circular_references(
        &self,
        data: &serde_json::Value,
        max_depth: usize,
    ) -> Result<ValidationResult>;

    /// 检查孤儿记录
    async fn check_orphan_records(
        &self,
        data: &serde_json::Value,
        parent_mapping: &HashMap<String, String>,
    ) -> Result<ValidationResult>;

    /// 获取检查器名称
    fn checker_name(&self) -> &str;

    /// 获取支持的检查类型
    fn supported_checks(&self) -> &[ReferenceCheck];
}

/// 完整性检查器实现
pub struct IntegrityValidator {
    /// 引用映射
    reference_map: HashMap<String, HashSet<String>>,
    /// 实体类型映射
    entity_types: HashMap<String, String>,
    /// 支持的检查类型
    supported_checks: Vec<ReferenceCheck>,
    /// 缓存的实体数据
    entity_cache: HashMap<String, serde_json::Value>,
    /// 循环引用检查的访问路径
    visited_paths: HashSet<String>,
}

impl IntegrityValidator {
    pub fn new() -> Self {
        Self {
            reference_map: HashMap::new(),
            entity_types: HashMap::new(),
            supported_checks: Self::default_checks(),
            entity_cache: HashMap::new(),
            visited_paths: HashSet::new(),
        }
    }

    /// 默认支持的检查类型
    fn default_checks() -> Vec<ReferenceCheck> {
        vec![
            ReferenceCheck::EntityReference {
                field_name: "id".to_string(),
                target_entity_type: "any".to_string(),
                required: true,
            },
            ReferenceCheck::CircularReference {
                check_path: vec!["parent_id".to_string(), "children".to_string()],
                max_depth: 10,
            },
            ReferenceCheck::DataConsistency {
                field_group: vec!["start_time".to_string(), "end_time".to_string()],
                consistency_rule: ConsistencyRule::TemporalRelation {
                    relation: TemporalRelationType::Before,
                    time_fields: vec![0, 1],
                },
            },
        ]
    }

    /// 验证引用完整性
    pub fn validate_reference_integrity(&self, data: &serde_json::Value) -> ValidationResult {
        let mut result = ValidationResult::new();

        // 检查引用的实体是否存在
        if let Some(obj) = data.as_object() {
            for (key, value) in obj {
                if key.ends_with("_id") || key.ends_with("_ref") {
                    if let Some(ref_id) = value.as_str() {
                        if !self.entity_exists(ref_id) {
                            result.add_error(ValidationError::new(
                                "reference_integrity".to_string(),
                                format!("Referenced entity '{}' does not exist", ref_id),
                                ValidationSource::Integrity,
                            ));
                        }
                    }
                }
            }
        }

        result
    }

    /// 验证数据一致性
    pub fn validate_data_consistency(&self, data: &serde_json::Value) -> ValidationResult {
        let mut result = ValidationResult::new();

        // 简化的一致性检查
        if let Some(obj) = data.as_object() {
            // 检查ID和名称的一致性
            if let (Some(id), Some(name)) = (obj.get("id"), obj.get("name")) {
                if let (Some(id_str), Some(name_str)) = (id.as_str(), name.as_str()) {
                    if id_str.is_empty() || name_str.is_empty() {
                        result.add_warning(ValidationWarning::new(
                            "empty_identifier".to_string(),
                            "ID or name is empty".to_string(),
                            ValidationSource::Integrity,
                        ));
                    }
                }
            }
        }

        result
    }

    /// 检查实体是否存在
    fn entity_exists(&self, entity_id: &str) -> bool {
        self.entity_types.contains_key(entity_id)
    }

    /// 注册实体
    pub fn register_entity(&mut self, entity_id: String, entity_type: String) {
        self.entity_types.insert(entity_id, entity_type);
    }

    /// 注册实体数据
    pub fn register_entity_data(&mut self, entity_id: String, data: serde_json::Value) {
        self.entity_cache.insert(entity_id, data);
    }

    /// 添加支持的检查类型
    pub fn add_supported_check(&mut self, check: ReferenceCheck) {
        self.supported_checks.push(check);
    }

    /// 清理访问路径缓存
    pub fn clear_visited_paths(&mut self) {
        self.visited_paths.clear();
    }

    /// 检查数值关系
    fn check_numeric_relation(
        &self,
        values: &[f64],
        relation: &NumericRelationType,
        base_idx: usize,
        compare_idx: usize,
    ) -> bool {
        if base_idx >= values.len() || compare_idx >= values.len() {
            return false;
        }

        let base_val = values[base_idx];
        let compare_val = values[compare_idx];

        match relation {
            NumericRelationType::GreaterThan => base_val > compare_val,
            NumericRelationType::GreaterThanOrEqual => base_val >= compare_val,
            NumericRelationType::LessThan => base_val < compare_val,
            NumericRelationType::LessThanOrEqual => base_val <= compare_val,
            NumericRelationType::Equal => (base_val - compare_val).abs() < f64::EPSILON,
            NumericRelationType::NotEqual => (base_val - compare_val).abs() >= f64::EPSILON,
        }
    }

    /// 检查时间关系
    fn check_temporal_relation(&self, timestamps: &[i64], relation: &TemporalRelationType) -> bool {
        if timestamps.len() < 2 {
            return false;
        }

        match relation {
            TemporalRelationType::Before => timestamps[0] < timestamps[1],
            TemporalRelationType::After => timestamps[0] > timestamps[1],
            TemporalRelationType::Simultaneous => timestamps[0] == timestamps[1],
            TemporalRelationType::Within { window_seconds } => {
                let diff = (timestamps[0] - timestamps[1]).abs();
                diff <= *window_seconds
            }
        }
    }

    /// 执行自定义规则
    fn execute_custom_rule(
        &self,
        data: &serde_json::Value,
        rule_name: &str,
        parameters: &HashMap<String, serde_json::Value>,
    ) -> Result<bool> {
        match rule_name {
            "unique_field" => {
                if let Some(field_name) = parameters.get("field").and_then(|v| v.as_str()) {
                    if let Some(field_value) = data.get(field_name) {
                        // 检查字段值的唯一性
                        let field_str = field_value.to_string();
                        return Ok(!self.entity_cache.values().any(|cached_data| {
                            cached_data
                                .get(field_name)
                                .map_or(false, |v| v.to_string() == field_str)
                        }));
                    }
                }
                Ok(false)
            }
            "required_when" => {
                // 条件性必填验证
                if let (Some(condition_field), Some(condition_value), Some(required_field)) = (
                    parameters.get("condition_field").and_then(|v| v.as_str()),
                    parameters.get("condition_value"),
                    parameters.get("required_field").and_then(|v| v.as_str()),
                ) {
                    if let Some(current_value) = data.get(condition_field) {
                        if current_value == condition_value {
                            return Ok(data.get(required_field).is_some());
                        }
                    }
                }
                Ok(true)
            }
            _ => {
                log::warn!("不支持的自定义规则: {}", rule_name);
                Ok(true)
            }
        }
    }
}

impl Default for IntegrityValidator {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Validator for IntegrityValidator {
    fn validator_name(&self) -> &str {
        "IntegrityValidator"
    }

    fn validate(&self, context: &mut ValidationContext, data: &dyn ValidatableData) -> Result<()> {
        let json_data = data.to_json_value();

        let integrity_result = self.validate_reference_integrity(&json_data);
        context.result.merge(integrity_result);

        let consistency_result = self.validate_data_consistency(&json_data);
        context.result.merge(consistency_result);

        Ok(())
    }

    fn supports(&self, _data_type: &str) -> bool {
        true // 支持所有数据类型
    }
}

/// 为 IntegrityValidator 实现 IntegrityChecker trait
#[async_trait::async_trait]
impl IntegrityChecker for IntegrityValidator {
    async fn check_reference_integrity(
        &self,
        data: &serde_json::Value,
        checks: &[ReferenceCheck],
    ) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();

        for check in checks {
            match check {
                ReferenceCheck::EntityReference {
                    field_name,
                    target_entity_type,
                    required,
                } => {
                    if let Some(field_value) = data.get(field_name) {
                        if let Some(ref_id) = field_value.as_str() {
                            if !self.entity_exists(ref_id) {
                                let error = ValidationError::new(
                                    "entity_reference".to_string(),
                                    format!("引用的实体 '{}' 不存在", ref_id),
                                    ValidationSource::Integrity,
                                );
                                result.add_error(error);
                            } else if target_entity_type != "any" {
                                // 检查实体类型是否匹配
                                if let Some(actual_type) = self.entity_types.get(ref_id) {
                                    if actual_type != target_entity_type {
                                        let warning = ValidationWarning::new(
                                            "entity_type_mismatch".to_string(),
                                            format!(
                                                "实体 '{}' 类型不匹配，期望: {}, 实际: {}",
                                                ref_id, target_entity_type, actual_type
                                            ),
                                            ValidationSource::Integrity,
                                        );
                                        result.add_warning(warning);
                                    }
                                }
                            }
                        }
                    } else if *required {
                        let error = ValidationError::new(
                            "missing_required_reference".to_string(),
                            format!("缺少必需的引用字段 '{}'", field_name),
                            ValidationSource::Integrity,
                        );
                        result.add_error(error);
                    }
                }
                ReferenceCheck::ForeignKeyConstraint {
                    foreign_key_field,
                    target_table,
                    target_field,
                    ..
                } => {
                    if let Some(fk_value) = data.get(foreign_key_field) {
                        if let Some(fk_str) = fk_value.as_str() {
                            // 检查外键约束
                            let target_key = format!("{}:{}", target_table, fk_str);
                            if !self.entity_cache.contains_key(&target_key) {
                                let error = ValidationError::new(
                                    "foreign_key_violation".to_string(),
                                    format!(
                                        "外键约束违反: {}={} 在 {} 中不存在",
                                        foreign_key_field, fk_str, target_table
                                    ),
                                    ValidationSource::Integrity,
                                );
                                result.add_error(error);
                            }
                        }
                    }
                }
                _ => {
                    // 其他检查类型由其他方法处理
                }
            }
        }

        Ok(result)
    }

    async fn check_data_consistency(
        &self,
        data: &serde_json::Value,
        rules: &[ConsistencyRule],
    ) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();

        for rule in rules {
            match rule {
                ConsistencyRule::FieldsEqual => {
                    // 检查字段值是否相等
                    // 需要在上下文中提供具体的字段名
                }
                ConsistencyRule::NumericRelation {
                    relation,
                    base_field_index,
                    compare_field_index,
                } => {
                    // 需要从上下文中获取字段值
                    // 这里简化处理
                }
                ConsistencyRule::TemporalRelation {
                    relation,
                    time_fields,
                } => {
                    // 时间关系验证
                    if time_fields.len() >= 2 {
                        // 简化处理，实际应该从数据中提取时间字段
                    }
                }
                ConsistencyRule::Custom {
                    rule_name,
                    parameters,
                } => match self.execute_custom_rule(data, rule_name, parameters) {
                    Ok(is_valid) => {
                        if !is_valid {
                            let error = ValidationError::new(
                                "custom_rule_violation".to_string(),
                                format!("自定义规则 '{}' 验证失败", rule_name),
                                ValidationSource::Integrity,
                            );
                            result.add_error(error);
                        }
                    }
                    Err(e) => {
                        let error = ValidationError::new(
                            "custom_rule_error".to_string(),
                            format!("自定义规则 '{}' 执行错误: {}", rule_name, e),
                            ValidationSource::Integrity,
                        );
                        result.add_error(error);
                    }
                },
                _ => {
                    // 其他规则类型的处理
                }
            }
        }

        Ok(result)
    }

    async fn check_circular_references(
        &self,
        data: &serde_json::Value,
        max_depth: usize,
    ) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();

        // 检查循环引用
        if let Some(id) = data.get("id").and_then(|v| v.as_str()) {
            let mut visited = HashSet::new();
            if self.has_circular_reference(id, &mut visited, 0, max_depth) {
                let error = ValidationError::new(
                    "circular_reference".to_string(),
                    format!("检测到循环引用: {}", id),
                    ValidationSource::Integrity,
                );
                result.add_error(error);
            }
        }

        Ok(result)
    }

    async fn check_orphan_records(
        &self,
        data: &serde_json::Value,
        parent_mapping: &HashMap<String, String>,
    ) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();

        // 检查孤儿记录
        for (child_field, parent_field) in parent_mapping {
            if let Some(parent_id) = data.get(parent_field).and_then(|v| v.as_str()) {
                if !self.entity_exists(parent_id) {
                    let warning = ValidationWarning::new(
                        "orphan_record".to_string(),
                        format!("孤儿记录: 父记录 '{}' 不存在", parent_id),
                        ValidationSource::Integrity,
                    );
                    result.add_warning(warning);
                }
            }
        }

        Ok(result)
    }

    fn checker_name(&self) -> &str {
        "IntegrityValidator"
    }

    fn supported_checks(&self) -> &[ReferenceCheck] {
        &self.supported_checks
    }
}

impl IntegrityValidator {
    /// 检查是否存在循环引用
    fn has_circular_reference(
        &self,
        entity_id: &str,
        visited: &mut HashSet<String>,
        depth: usize,
        max_depth: usize,
    ) -> bool {
        if depth >= max_depth {
            return false;
        }

        if visited.contains(entity_id) {
            return true;
        }

        visited.insert(entity_id.to_string());

        // 检查该实体的所有引用
        if let Some(references) = self.reference_map.get(entity_id) {
            for ref_id in references {
                if self.has_circular_reference(ref_id, visited, depth + 1, max_depth) {
                    return true;
                }
            }
        }

        visited.remove(entity_id);
        false
    }
}
