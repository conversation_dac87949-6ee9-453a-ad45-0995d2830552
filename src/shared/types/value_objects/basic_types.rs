//! # 基础类型定义

use serde::{Deserialize, Serialize};

/// 基础属性
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct BasicAttributes {
    pub strength: i32,
    pub agility: i32,
    pub intelligence: i32,
    pub vitality: i32,
}

/// 战斗单位基础信息
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct BattleUnit {
    pub id: String,
    pub name: String,
    pub level: u32,
    pub attributes: BasicAttributes,
}

/// 工具类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ToolType {
    Pickaxe,
    Shovel,
    Axe,
    Sword,
    Staff,
    Bow,
}

impl Default for BasicAttributes {
    fn default() -> Self {
        Self {
            strength: 10,
            agility: 10,
            intelligence: 10,
            vitality: 10,
        }
    }
}

impl Default for BattleUnit {
    fn default() -> Self {
        Self {
            id: "default".to_string(),
            name: "Default Unit".to_string(),
            level: 1,
            attributes: BasicAttributes::default(),
        }
    }
}
