use std::{
    any::{Any, TypeId},
    collections::HashMap,
    sync::{Arc, Weak, RwLock},
};
use crossbeam_queue::SegQueue;
use parking_lot::Mutex;
use futures::future::BoxFuture;
use std::pin::Pin;
use once_cell::sync::Lazy;

pub static EVENT_BUS: Lazy<Arc<EventBus>> = Lazy::new(|| EventBus::new());

/// 事件 trait，所有事件需实现
pub trait Event: Send + Sync + 'static {}

impl<T: Send + Sync + 'static> Event for T {}

type Callback = Arc<dyn Fn(&dyn Any) + Send + Sync>;
type AsyncCallback = Arc<dyn Fn(&dyn Any) -> BoxFuture<'static, ()> + Send + Sync>;

enum SubscriptionKind {
    Sync(Callback),
    Async(AsyncCallback),
}

#[derive(Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Debug)]
pub enum Priority {
    High = 2,
    Normal = 1,
    Low = 0,
}

struct Subscriber {
    kind: SubscriptionKind,
    filter: Option<Arc<dyn Fn(&dyn Any) -> bool + Send + Sync>>,
    once: bool,
    id: usize,
    priority: Priority,
}

type SubscriberList = Arc<RwLock<Vec<Weak<Subscriber>>>>;

/// 事件持久化 trait（预留，默认不实现）
pub trait EventPersistence: Send + Sync {
    fn persist(&self, _type_id: TypeId, _event: &Arc<dyn Any + Send + Sync>) {}
}

/// 分布式事件总线 trait（预留，默认不实现）
pub trait DistributedBus: Send + Sync {
    fn publish_distributed(&self, _type_id: TypeId, _event: &Arc<dyn Any + Send + Sync>) {}
}

pub struct EventBus {
    subscribers: Mutex<HashMap<TypeId, SubscriberList>>,
    id_gen: Mutex<usize>,
    queue: SegQueue<(TypeId, Arc<dyn Any + Send + Sync>)>,
    // 持久化与分布式接口（可选）
    persistence: Option<Arc<dyn EventPersistence>>,
    distributed: Option<Arc<dyn DistributedBus>>,
}

impl EventBus {
    pub fn new() -> Arc<Self> {
        Arc::new(EventBus {
            subscribers: Mutex::new(HashMap::new()),
            id_gen: Mutex::new(0),
            queue: SegQueue::new(),
            persistence: None,
            distributed: None,
        })
    }

    /// 设置事件持久化实现
    pub fn set_persistence(&mut self, persistence: Arc<dyn EventPersistence>) {
        self.persistence = Some(persistence);
    }

    /// 设置分布式总线实现
    pub fn set_distributed(&mut self, distributed: Arc<dyn DistributedBus>) {
        self.distributed = Some(distributed);
    }

    /// 订阅同步事件（可指定优先级）
    pub fn subscribe<E, F>(&self, callback: F, priority: Priority) -> usize
    where
        E: Event,
        F: Fn(&E) + Send + Sync + 'static,
    {
        self.subscribe_inner::<E>(
            SubscriptionKind::Sync(Arc::new(move |e| {
                if let Some(ev) = e.downcast_ref::<E>() {
                    callback(ev);
                }
            })),
            None,
            false,
            priority,
        )
    }

    /// 订阅异步事件（可指定优先级）
    pub fn subscribe_async<E, F, Fut>(&self, callback: F, priority: Priority) -> usize
    where
        E: Event,
        F: Fn(&E) -> Fut + Send + Sync + 'static,
        Fut: std::future::Future<Output = ()> + Send + 'static,
    {
        self.subscribe_inner::<E>(
            SubscriptionKind::Async(Arc::new(move |e| {
                let fut: Pin<Box<dyn std::future::Future<Output = ()> + Send>> = if let Some(ev) = e.downcast_ref::<E>() {
                    Box::pin(callback(ev))
                } else {
                    Box::pin(async {})
                };
                fut
            })),
            None,
            false,
            priority,
        )
    }

    /// 支持过滤器和一次性订阅
    pub fn subscribe_with<E, F, Filter>(
        &self,
        callback: F,
        filter: Filter,
        once: bool,
        priority: Priority,
    ) -> usize
    where
        E: Event,
        F: Fn(&E) + Send + Sync + 'static,
        Filter: Fn(&E) -> bool + Send + Sync + 'static,
    {
        self.subscribe_inner::<E>(
            SubscriptionKind::Sync(Arc::new(move |e| {
                if let Some(ev) = e.downcast_ref::<E>() {
                    callback(ev);
                }
            })),
            Some(Arc::new(move |e| {
                if let Some(ev) = e.downcast_ref::<E>() {
                    filter(ev)
                } else {
                    false
                }
            })),
            once,
            priority,
        )
    }

    fn subscribe_inner<E: Event>(
        &self,
        kind: SubscriptionKind,
        filter: Option<Arc<dyn Fn(&dyn Any) -> bool + Send + Sync>>,
        once: bool,
        priority: Priority,
    ) -> usize {
        let mut id_gen = self.id_gen.lock();
        *id_gen += 1;
        let id = *id_gen;

        let sub = Arc::new(Subscriber {
            kind,
            filter,
            once,
            id,
            priority,
        });

        let mut subs = self.subscribers.lock();
        let entry = subs.entry(TypeId::of::<E>()).or_insert_with(|| Arc::new(RwLock::new(Vec::new())));
        entry.write().expect("RwLock poisoned").push(Arc::downgrade(&sub));
        id
    }

    /// 取消订阅
    pub fn unsubscribe<E: Event>(&self, id: usize) {
        let mut subs = self.subscribers.lock();
        if let Some(list) = subs.get_mut(&TypeId::of::<E>()) {
            let mut vec = list.write().expect("RwLock poisoned");
            vec.retain(|w| w.upgrade().map_or(false, |s| s.id != id));
        }
    }

    /// 同步发布（立即分发，阻塞所有订阅者，按优先级高到低）
    pub fn publish<E: Event>(&self, event: E)
    where
        E: Event,
    {
        let type_id = TypeId::of::<E>();
        let event = Arc::new(event);
        let event_any: Arc<dyn Any + Send + Sync> = event.clone();
        // 事件持久化（如有）
        if let Some(persist) = &self.persistence {
            persist.persist(type_id, &event_any);
        }
        // 分布式总线（如有）
        if let Some(dist) = &self.distributed {
            dist.publish_distributed(type_id, &event_any);
        }

        if let Some(list) = self.subscribers.lock().get(&type_id) {
            // 收集所有订阅者，按优先级排序
            let guard = list.read().expect("RwLock poisoned");
            let mut subs: Vec<_> = guard.iter().filter_map(|w| w.upgrade()).collect();
            
            subs.sort_by_key(|s| std::cmp::Reverse(s.priority));
            let mut to_remove = Vec::new();
            for (i, sub) in subs.iter().enumerate() {
                if let Some(ref filter) = sub.filter {
                    if !filter(event.as_ref()) {
                        continue;
                    }
                }
                match &sub.kind {
                    SubscriptionKind::Sync(cb) => cb(event.as_ref()),
                    SubscriptionKind::Async(_) => {} // 忽略异步订阅
                }
                if sub.once {
                    to_remove.push(i);
                }
            }
            if !to_remove.is_empty() {
                let mut vec = list.write().expect("RwLock posoned");
                for i in to_remove.into_iter().rev() {
                    vec.remove(i);
                }
            }
        }
    }

    /// 异步发布（tokio/async-std 兼容，所有 async 订阅者并发执行，按优先级高到低）
    pub async fn publish_async<E: Event>(&self, event: E)
    where
        E: Event,
    {
        let type_id = TypeId::of::<E>();
        let event = Arc::new(event);
        let event_any: Arc<dyn Any + Send + Sync> = event.clone();
        // 事件持久化（如有）
        if let Some(persist) = &self.persistence {
            persist.persist(type_id, &event_any);
        }
        // 分布式总线（如有）
        if let Some(dist) = &self.distributed {
            dist.publish_distributed(type_id, &event_any);
        }

        if let Some(list) = self.subscribers.lock().get(&type_id) {
            let mut subs: Vec<_> = list
                .read()
                .expect("RwLock poisoned")
                .iter()
                .filter_map(|w| w.upgrade())
                .collect();
            subs.sort_by_key(|s| std::cmp::Reverse(s.priority));
            let mut futs = Vec::new();
            let mut to_remove = Vec::new();
            for (i, sub) in subs.iter().enumerate() {
                if let Some(ref filter) = sub.filter {
                    if !filter(event.as_ref()) {
                        continue;
                    }
                }
                match &sub.kind {
                    SubscriptionKind::Async(cb) => {
                        futs.push(cb(event.as_ref()));
                    }
                    SubscriptionKind::Sync(_) => {}
                }
                if sub.once {
                    to_remove.push(i);
                }
            }
            if !to_remove.is_empty() {
                let mut vec = list.write().expect("RwLock poisoned");
                for i in to_remove.into_iter().rev() {
                    vec.remove(i);
                }
            }
            futures::future::join_all(futs).await;
        }
    }

    /// 高吞吐批量发布（无锁队列，适合日志/监控/批量事件）
    pub fn enqueue<E: Event>(&self, event: E) {
        self.queue.push((TypeId::of::<E>(), Arc::new(event)));
    }

    /// 批量消费队列（可在独立线程/定时器中调用，按优先级分发）
    pub fn drain(&self) {
        while let Some((type_id, event)) = self.queue.pop() {
            // 事件持久化（如有）
            if let Some(persist) = &self.persistence {
                persist.persist(type_id, &event);
            }
            // 分布式总线（如有）
            if let Some(dist) = &self.distributed {
                dist.publish_distributed(type_id, &event);
            }
            if let Some(list) = self.subscribers.lock().get(&type_id) {
                let mut subs: Vec<_> = list
                    .read()
                    .expect("RwLock poisoned")
                    .iter()
                    .filter_map(|w| w.upgrade())
                    .collect();
                subs.sort_by_key(|s| std::cmp::Reverse(s.priority));
                let mut to_remove = Vec::new();
                for (i, sub) in subs.iter().enumerate() {
                    if let Some(ref filter) = sub.filter {
                        if !filter(event.as_ref()) {
                            continue;
                        }
                    }
                    match &sub.kind {
                        SubscriptionKind::Sync(cb) => cb(event.as_ref()),
                        SubscriptionKind::Async(_) => {}
                    }
                    if sub.once {
                        to_remove.push(i);
                    }
                }
                if !to_remove.is_empty() {
                    let mut vec = list.write().expect("RwLock poisoned");
                    for i in to_remove.into_iter().rev() {
                        vec.remove(i);
                    }
                }
            }
        }
    }
} 