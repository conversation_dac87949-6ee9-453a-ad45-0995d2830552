/// Character领域服务
/// 
/// 包含跨聚合根的领域逻辑和计算服务

use crate::shared::*;
use super::value_objects::*;
use crate::attribute::{AttributeType, CoreAttribute};

// ============================================================================
// 属性计算服务
// ============================================================================

/// 属性计算服务 - 处理复杂的属性计算逻辑
pub struct AttributeCalculationService;

impl AttributeCalculationService {
    /// 计算基于等级的属性加成
    pub fn calculate_level_bonus(level: Level, attribute_type: AttributeType) -> f64 {
        let base_bonus = match attribute_type {
            AttributeType::Base(CoreAttribute::Earth) => level as f64 * 2.0, // 体质加成
            AttributeType::Base(CoreAttribute::Metal) => level as f64 * 1.5, // 力量加成  
            AttributeType::Base(CoreAttribute::Water) => level as f64 * 1.8, // 精神加成
            _ => level as f64 * 1.0,
        };
        
        base_bonus
    }
    
    /// 计算属性相互作用加成
    pub fn calculate_attribute_synergy(primary: &AttributeType, secondary: &AttributeType) -> f64 {
        // 简化实现：移除对旧的AttributeInteractionEngine的依赖
        // 这里可以根据新的领域规则重新实现
        if primary == secondary {
            return 1.2; // 同属性共鸣
        }
        
        match (primary, secondary) {
            // 五行相生
            (AttributeType::Base(CoreAttribute::Wood), AttributeType::Base(CoreAttribute::Fire)) => 1.15,
            (AttributeType::Base(CoreAttribute::Fire), AttributeType::Base(CoreAttribute::Earth)) => 1.15,
            (AttributeType::Base(CoreAttribute::Earth), AttributeType::Base(CoreAttribute::Metal)) => 1.15,
            (AttributeType::Base(CoreAttribute::Metal), AttributeType::Base(CoreAttribute::Water)) => 1.15,
            (AttributeType::Base(CoreAttribute::Water), AttributeType::Base(CoreAttribute::Wood)) => 1.15,
            // 五行相克
            (AttributeType::Base(CoreAttribute::Wood), AttributeType::Base(CoreAttribute::Earth)) => 0.85,
            (AttributeType::Base(CoreAttribute::Earth), AttributeType::Base(CoreAttribute::Water)) => 0.85,
            (AttributeType::Base(CoreAttribute::Water), AttributeType::Base(CoreAttribute::Fire)) => 0.85,
            (AttributeType::Base(CoreAttribute::Fire), AttributeType::Base(CoreAttribute::Metal)) => 0.85,
            (AttributeType::Base(CoreAttribute::Metal), AttributeType::Base(CoreAttribute::Wood)) => 0.85,
            _ => 1.0, // 其他情况为中性
        }
    }
    
    /// 计算修炼效率
    pub fn calculate_cultivation_efficiency(
        spiritual_energy: &SpiritualEnergy,
        target_attribute: &AttributeType
    ) -> f64 {
        let current_value = spiritual_energy.get_essence(target_attribute);
        let total_essence = spiritual_energy.total_essence() as f64;
        
        if total_essence == 0.0 {
            return 1.0; // 初始效率
        }
        
        let balance_score = spiritual_energy.balance_score();
        let saturation = current_value / total_essence;
        
        // 平衡度高且饱和度不高时效率最佳
        let balance_bonus = balance_score;
        let saturation_penalty = if saturation > 0.5 { 0.8 } else { 1.0 };
        
        balance_bonus * saturation_penalty
    }
}

// ============================================================================
// 战斗力评估服务  
// ============================================================================

/// 战斗力评估服务
pub struct CombatPowerService;

impl CombatPowerService {
    /// 计算角色综合战斗力
    pub fn calculate_combat_power(
        basic_attributes: &BasicAttributes,
        spiritual_energy: &SpiritualEnergy,
        level: Level,
    ) -> u32 {
        // 基础属性贡献
        let physical_power = basic_attributes.strength() as u32 * 10;
        let vitality_power = basic_attributes.constitution() as u32 * 8;
        let magical_power = basic_attributes.spirit() as u32 * 12;
        
        // 等级贡献
        let level_power = level * level * 5;
        
        // 灵气贡献
        let spiritual_power = spiritual_energy.total_essence() / 10;
        
        // 平衡度奖励
        let balance_bonus = (spiritual_energy.balance_score() * 100.0) as u32;
        
        physical_power + vitality_power + magical_power + level_power + spiritual_power as u32 + balance_bonus
    }
    
    /// 评估战斗潜力等级
    pub fn evaluate_potential_rank(combat_power: u32) -> CombatRank {
        match combat_power {
            0..=999 => CombatRank::Novice,
            1000..=4999 => CombatRank::Apprentice,
            5000..=14999 => CombatRank::Adept,
            15000..=39999 => CombatRank::Expert,
            40000..=99999 => CombatRank::Master,
            _ => CombatRank::Grandmaster,
        }
    }
}

/// 战斗力等级
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum CombatRank {
    Novice,      // 新手
    Apprentice,  // 学徒  
    Adept,       // 熟练
    Expert,      // 专家
    Master,      // 大师
    Grandmaster, // 宗师
}

impl CombatRank {
    pub fn name(&self) -> &'static str {
        match self {
            CombatRank::Novice => "新手",
            CombatRank::Apprentice => "学徒",
            CombatRank::Adept => "熟练",
            CombatRank::Expert => "专家", 
            CombatRank::Master => "大师",
            CombatRank::Grandmaster => "宗师",
        }
    }
}

// ============================================================================
// 升级规划服务
// ============================================================================

/// 升级规划服务
pub struct LevelingPlanService;

impl LevelingPlanService {
    /// 推荐属性分配方案
    pub fn recommend_attribute_allocation(
        current_attributes: &BasicAttributes,
        target_build: BuildType,
    ) -> Vec<AttributeAllocation> {
        match target_build {
            BuildType::Physical => vec![
                AttributeAllocation::new(AttributeType::Base(CoreAttribute::Metal), 0.4),
                AttributeAllocation::new(AttributeType::Base(CoreAttribute::Earth), 0.6),
            ],
            BuildType::Magical => vec![
                AttributeAllocation::new(AttributeType::Base(CoreAttribute::Water), 0.7),
                AttributeAllocation::new(AttributeType::Base(CoreAttribute::Earth), 0.3),
            ],
            BuildType::Balanced => vec![
                AttributeAllocation::new(AttributeType::Base(CoreAttribute::Metal), 0.3),
                AttributeAllocation::new(AttributeType::Base(CoreAttribute::Water), 0.3),
                AttributeAllocation::new(AttributeType::Base(CoreAttribute::Earth), 0.4),
            ],
        }
    }
    
    /// 计算升级收益
    pub fn calculate_level_up_benefits(
        current_level: Level,
        target_level: Level,
        current_attributes: &BasicAttributes,
    ) -> LevelUpBenefits {
        let level_diff = target_level - current_level;
        let total_points = level_diff * 3; // 每级3点属性
        
        let health_gain = level_diff as Health * 20;
        let mana_gain = level_diff as Mana * 15;
        
        LevelUpBenefits {
            attribute_points: total_points,
            health_gain,
            mana_gain,
            estimated_combat_power_gain: level_diff * level_diff * 5,
        }
    }
}

/// 构建类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BuildType {
    Physical, // 物理型
    Magical,  // 法术型
    Balanced, // 平衡型
}

/// 属性分配建议
#[derive(Debug, Clone)]
pub struct AttributeAllocation {
    pub attribute: AttributeType,
    pub ratio: f32, // 分配比例 0.0-1.0
}

impl AttributeAllocation {
    pub fn new(attribute: AttributeType, ratio: f32) -> Self {
        Self { attribute, ratio }
    }
}

/// 升级收益
#[derive(Debug, Clone, Copy)]
pub struct LevelUpBenefits {
    pub attribute_points: u32,
    pub health_gain: Health,
    pub mana_gain: Mana,
    pub estimated_combat_power_gain: u32,
}
