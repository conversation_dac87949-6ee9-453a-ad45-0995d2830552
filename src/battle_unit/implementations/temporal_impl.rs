/// 时间相关trait实现
/// 
/// 实现TemporalEntity trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;

use crate::battle_unit::traits::TemporalEntity;

impl TemporalEntity for BattleUnitAdapter {
    fn update_by_time(&mut self, delta_seconds: f32) -> GameResult<()> {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs_f64();
        
        // 更新Buff状态
        self.update_buffs(delta_seconds);
        
        // 更新技能冷却
        for cooldown in self.skill_cooldowns.values_mut() {
            *cooldown = (*cooldown - delta_seconds as f64).max(0.0);
        }
        
        // 恢复行动点数
        if self.action_points < 100 {
            self.action_points = (self.action_points + (delta_seconds * 10.0) as ActionPoints)
                .min(100);
        }
        
        // 减少移动疲劳
        if self.movement_fatigue > 0.0 {
            self.movement_fatigue = (self.movement_fatigue - delta_seconds * 0.5).max(0.0);
        }
        
        self.last_update_time = current_time;
        Ok(())
    }
    
    fn creation_time(&self) -> GameTime {
        self.creation_time as GameTime
    }
    
    fn age(&self, current_time: GameTime) -> f32 {
        (current_time - self.creation_time as GameTime) as f32
    }
    
    fn is_temporary(&self) -> bool {
        false // 角色为永久实体
    }
    
    fn lifetime_limit(&self) -> Option<f32> {
        None // 角色无生存时间限制
    }
}