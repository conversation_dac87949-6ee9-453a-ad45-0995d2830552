# 重构迁移计划：从并存到纯净

**目标**: 彻底移除项目中的遗留代码（`_legacy` 文件、旧的 Trait 和数据结构），让新的 DDD 架构完全取代旧实现。

**追踪方式**: 本文件中的任务列表。完成一项后，对应的复选框将被标记为 `[x]`。

---

## 阶段一：`BattleUnit` Trait 迁移

**目标**: 将所有代码中对旧 `BattleUnit` Trait 体系的依赖，全部切换到新的 `simplified_battle_traits`。

- [x] **1.1. 识别依赖点**: 使用 `grep` 全局搜索旧 Trait (`BattleUnit`, `Vitality` 等) 的使用位置。
- [x] **1.2. 核心组件解耦**: 移除 `StatusEffectProcessor`，将其职责（如DOT、HOT、Buff管理）移入 `Character` 聚合根自身。
    - [x] 重构 `realtime_battle_manager` 以移除 `StatusEffectProcessor` 依赖。
    - [x] 扩展 `SkillUser` Trait 以包含 `add_buff` 方法。
    - [x] 重构 `skill_caster` 以移除 `StatusEffectProcessor` 依赖。
    - [x] 删除 `status_effect_processor.rs` 文件。
- [ ] **1.3. 批量修改剩余依赖**: 将依赖旧 Trait 的函数签名和泛型约束，修改为依赖新的核心 Trait (`FullBattleUnit`, `LifeForce` 等)。
    - [x] 重构 `bin/battle_demo_systems.rs`。
- [ ] **1.4. 逐个修复编译错误**: 根据编译器提示，将旧的 Trait 方法调用 (`.get_hp()`) 替换为新的方法调用 (`.current_health()`)。
- [ ] **1.5. 阶段性验证**: 确保所有修改完成后，`cargo test` 能够全部通过。

---

## 阶段二：`Character` 实体迁移

**目标**: 彻底消除对 `character_legacy.rs` 的所有引用，全面采用新的 `Character` 聚合根。

- [ ] **2.1. 识别依赖点**: 使用 `grep` 全局搜索 `character_legacy` 和 `LegacyCharacter` 的使用位置。
- [ ] **2.2. 替换实例创建**: 将所有 `LegacyCharacter { ... }` 的实例化代码，替换为使用 `Character::create(...)` 或 `CharacterBuilder`。
- [ ] **2.3. 替换字段访问为方法调用**: 将所有对 `LegacyCharacter` 公共字段的直接读写，替换为对新 `Character` 聚合方法的调用 (例如 `char.hp -= 10` => `char.take_damage(10)`)。
- [ ] **2.4. 阶段性验证**: 确保所有修改完成后，`cargo test` 能够全部通过。

---

## 阶段三：`Skill` 系统迁移

**目标**: 消除对旧 `skill.rs` 和 `skill_effect.rs` 的依赖，全面采用新的 `Skill` 聚合。

- [ ] **3.1. 识别依赖点**: 使用 `grep` 全局搜索旧 `skill::Skill` 和 `skill::SkillEffect` 的使用位置。
- [ ] **3.2. 迁移技能数据定义**: 使用 `SkillBuilder` 重新定义所有旧的技能数据。
- [ ] **3.3. 重写技能施放逻辑**: 将旧的、基于 `match` 的技能施放逻辑，重构为基于 `SkillInstance` 的新模式。
- [ ] **3.4. 阶段性验证**: 确保所有修改完成后，`cargo test` 能够全部通过。

---

## 阶段四：最终清理

**目标**: 在所有依赖都解除后，删除所有遗留代码和兼容层。

- [ ] **4.1. 确认无依赖**: 确认对所有遗留模块的搜索结果为零。
- [ ] **4.2. 删除遗留文件**:
    - [ ] 删除 `src/character_legacy.rs`
    - [ ] 删除 `src/battle_system/battle_unit.rs`
    - [ ] 删除 `src/skill/skill.rs`
    - [ ] 删除 `src/skill/skill_effect.rs`
    - [ ] 删除其他相关旧文件
- [ ] **4.3. 删除兼容层**:
    - [ ] 删除 `src/battle_system/compatibility_layer.rs`
- [ ] **4.4. 清理模块导入**: 编辑 `lib.rs` 和各模块的 `mod.rs`。
- [ ] **4.5. 最终验证**: `cargo check`, `cargo build`, `cargo test --all-features` 全部通过，无警告。
