/// 资源系统相关的类型定义
/// 处理世界中的资源节点、采集条件和资源类型

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc}; // Duration 暂时未使用
// use std::collections::HashSet; // 暂时未使用
// use crate::world_map::domain::ids::{NodeId, PlayerId}; // 暂时未使用

// ============================================================================
// 资源节点类型
// ============================================================================

/// 资源节点类型 - 定义不同种类的资源点
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ResourceNodeType {
    /// 矿脉 - 金属矿物的主要来源
    MineralVein,
    /// 灵草丛 - 药材和灵草的生长地
    SpiritualHerbs,
    /// 灵兽栖息地 - 可以获得兽类材料
    BeastLair,
    /// 灵泉 - 特殊液体资源的来源
    SpiritualSpring,
    /// 古遗迹 - 古代留下的宝藏点
    AncientRuin,
    /// 天材地宝点 - 稀有材料的自然生成点
    TreasureSpot,
    /// 元素聚集点 - 特定元素能量的汇聚处
    ElementalNode,
    /// 灵石矿 - 修炼用灵石的开采点
    SpiritStoneMine,
}

impl ResourceNodeType {
    /// 获取节点类型的基础采集时间（分钟）
    pub fn base_harvest_time(&self) -> u32 {
        match self {
            ResourceNodeType::MineralVein => 30,
            ResourceNodeType::SpiritualHerbs => 10,
            ResourceNodeType::BeastLair => 60, // 需要时间捕获或获取材料
            ResourceNodeType::SpiritualSpring => 5,
            ResourceNodeType::AncientRuin => 120, // 需要探索和解谜
            ResourceNodeType::TreasureSpot => 15,
            ResourceNodeType::ElementalNode => 45,
            ResourceNodeType::SpiritStoneMine => 40,
        }
    }
    
    /// 获取节点类型的稀有度
    pub fn rarity_level(&self) -> u8 {
        match self {
            ResourceNodeType::SpiritualHerbs => 1,
            ResourceNodeType::MineralVein => 2,
            ResourceNodeType::SpiritualSpring => 2,
            ResourceNodeType::SpiritStoneMine => 3,
            ResourceNodeType::BeastLair => 3,
            ResourceNodeType::ElementalNode => 4,
            ResourceNodeType::TreasureSpot => 4,
            ResourceNodeType::AncientRuin => 5,
        }
    }
    
    /// 获取节点类型可能产出的资源类型
    pub fn possible_resources(&self) -> Vec<ResourceType> {
        match self {
            ResourceNodeType::MineralVein => vec![
                ResourceType::MetalOre,
                ResourceType::CraftingMaterial,
            ],
            ResourceNodeType::SpiritualHerbs => vec![
                ResourceType::Herbs,
                ResourceType::AlchemyMaterial,
            ],
            ResourceNodeType::BeastLair => vec![
                ResourceType::BeastMaterial,
                ResourceType::CraftingMaterial,
            ],
            ResourceNodeType::SpiritualSpring => vec![
                ResourceType::LiquidEssence,
                ResourceType::AlchemyMaterial,
            ],
            ResourceNodeType::AncientRuin => vec![
                ResourceType::AncientArtifact,
                ResourceType::FormationMaterial,
                ResourceType::CraftingMaterial,
            ],
            ResourceNodeType::TreasureSpot => vec![
                ResourceType::SpiritStone,
                ResourceType::RareMaterial,
                ResourceType::FormationMaterial,
            ],
            ResourceNodeType::ElementalNode => vec![
                ResourceType::ElementalEssence,
                ResourceType::FormationMaterial,
            ],
            ResourceNodeType::SpiritStoneMine => vec![
                ResourceType::SpiritStone,
                ResourceType::CraftingMaterial,
            ],
        }
    }
}

// ============================================================================
// 资源类型
// ============================================================================

/// 资源类型 - 与材料系统对接的资源分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ResourceType {
    /// 金属矿物 - 基础的金属材料
    MetalOre,
    /// 灵石 - 修炼和阵法的基础材料
    SpiritStone,
    /// 药草 - 炼丹的基础材料
    Herbs,
    /// 灵兽材料 - 从灵兽获得的各种材料
    BeastMaterial,
    /// 炼器材料 - 炼制法器的专用材料
    CraftingMaterial,
    /// 阵法材料 - 布置阵法的专用材料
    FormationMaterial,
    /// 炼丹材料 - 炼丹的高级材料
    AlchemyMaterial,
    /// 液体精华 - 各种液体形态的珍贵材料
    LiquidEssence,
    /// 元素精华 - 纯粹的元素能量结晶
    ElementalEssence,
    /// 古代神器 - 从遗迹中发现的古老物品
    AncientArtifact,
    /// 稀有材料 - 极其罕见的特殊材料
    RareMaterial,
}

impl ResourceType {
    /// 获取资源类型的基础价值
    pub fn base_value(&self) -> u32 {
        match self {
            ResourceType::MetalOre => 10,
            ResourceType::Herbs => 15,
            ResourceType::CraftingMaterial => 20,
            ResourceType::BeastMaterial => 25,
            ResourceType::SpiritStone => 50,
            ResourceType::AlchemyMaterial => 40,
            ResourceType::FormationMaterial => 60,
            ResourceType::LiquidEssence => 80,
            ResourceType::ElementalEssence => 100,
            ResourceType::RareMaterial => 200,
            ResourceType::AncientArtifact => 500,
        }
    }
    
    /// 获取资源类型的稀有度等级
    pub fn rarity_tier(&self) -> u8 {
        match self {
            ResourceType::MetalOre | ResourceType::Herbs => 1,
            ResourceType::CraftingMaterial | ResourceType::BeastMaterial => 2,
            ResourceType::SpiritStone | ResourceType::AlchemyMaterial => 3,
            ResourceType::FormationMaterial | ResourceType::LiquidEssence => 4,
            ResourceType::ElementalEssence | ResourceType::RareMaterial => 5,
            ResourceType::AncientArtifact => 6,
        }
    }
}

// ============================================================================
// 资源丰度
// ============================================================================

/// 资源丰度 - 表示资源节点的当前资源量
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum ResourceAbundance {
    /// 枯竭 - 资源已经耗尽
    Depleted,
    /// 微量 - 仅剩极少量资源
    Trace,
    /// 少量 - 资源较少
    Low,
    /// 中等 - 正常的资源量
    Medium,
    /// 丰富 - 资源丰富
    High,
    /// 极丰富 - 资源极其丰富
    Abundant,
}

impl ResourceAbundance {
    /// 转换为数值表示 (0.0 - 1.0)
    pub fn to_float(&self) -> f32 {
        match self {
            ResourceAbundance::Depleted => 0.0,
            ResourceAbundance::Trace => 0.1,
            ResourceAbundance::Low => 0.3,
            ResourceAbundance::Medium => 0.5,
            ResourceAbundance::High => 0.8,
            ResourceAbundance::Abundant => 1.0,
        }
    }
    
    /// 从数值创建资源丰度
    pub fn from_float(value: f32) -> Self {
        match value {
            v if v <= 0.0 => ResourceAbundance::Depleted,
            v if v <= 0.2 => ResourceAbundance::Trace,
            v if v <= 0.4 => ResourceAbundance::Low,
            v if v <= 0.6 => ResourceAbundance::Medium,
            v if v <= 0.9 => ResourceAbundance::High,
            _ => ResourceAbundance::Abundant,
        }
    }
    
    /// 获取采集效率修正
    pub fn harvest_efficiency(&self) -> f32 {
        match self {
            ResourceAbundance::Depleted => 0.0,
            ResourceAbundance::Trace => 0.2,
            ResourceAbundance::Low => 0.5,
            ResourceAbundance::Medium => 0.8,
            ResourceAbundance::High => 1.0,
            ResourceAbundance::Abundant => 1.2,
        }
    }
}

// ============================================================================
// 采集条件
// ============================================================================

/// 采集条件 - 定义采集资源所需满足的条件
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum HarvestCondition {
    /// 最低等级要求
    MinimumLevel { level: u8 },
    /// 需要特定技能
    RequiredSkill { skill_name: String, min_level: u8 },
    /// 需要特定工具
    RequiredTool { tool_name: String },
    /// 时间条件（特定时间段才能采集）
    TimeWindow { start_hour: u8, end_hour: u8 },
    /// 季节条件
    SeasonRestriction { seasons: Vec<crate::world_map::domain::time::Season> },
    /// 天气条件
    WeatherCondition { weather_type: String },
    /// 需要团队协作
    TeamRequired { min_members: u8 },
    /// 需要消耗特定物品
    ConsumableRequired { item_name: String, quantity: u32 },
    /// 灵气密度要求
    SpiritualDensityMin { min_density: f32 },
    /// 危险等级限制
    DangerLevelMax { max_danger: crate::world_map::domain::terrain::DangerLevel },
}

impl HarvestCondition {
    /// 检查条件是否满足（简化版本，实际需要传入更多参数）
    pub fn is_satisfied(&self, _context: &HarvestContext) -> bool {
        // 这里是简化实现，实际需要根据具体的游戏状态来判断
        // 暂时返回true，后续在实现具体的采集系统时会完善
        true
    }
}

/// 采集上下文 - 包含采集时的各种状态信息
#[derive(Debug, Clone)]
pub struct HarvestContext {
    pub player_level: u8,
    pub player_skills: std::collections::HashMap<String, u8>,
    pub player_tools: Vec<String>,
    pub current_time: DateTime<Utc>,
    pub current_season: crate::world_map::domain::time::Season,
    pub weather: String,
    pub team_size: u8,
    pub available_items: std::collections::HashMap<String, u32>,
    pub spiritual_density: f32,
    pub danger_level: crate::world_map::domain::terrain::DangerLevel,
}

// ============================================================================
// 访问要求
// ============================================================================

/// 访问要求 - 定义访问资源节点所需的条件
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AccessRequirement {
    /// 最低修为要求
    MinimumCultivation { level: u8 },
    /// 需要特定门派身份
    FactionMembership { faction_id: crate::world_map::domain::ids::FactionId },
    /// 需要特定令牌或钥匙
    RequiredToken { token_name: String },
    /// 需要完成前置任务
    QuestCompletion { quest_id: String },
    /// 需要支付费用
    PaymentRequired { cost: u32, currency: String },
    /// 需要特定称号
    TitleRequired { title: String },
    /// 声望要求
    ReputationRequired { faction: String, min_reputation: i32 },
}

// ============================================================================
// 资源生成规则
// ============================================================================

/// 资源生成规则 - 定义区域内资源节点的生成规律
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ResourceSpawnRule {
    /// 资源节点类型
    pub node_type: ResourceNodeType,
    /// 生成概率 (0.0 - 1.0)
    pub spawn_probability: f32,
    /// 最大数量
    pub max_count: u32,
    /// 最小间距（避免节点过于密集）
    pub min_distance: f32,
    /// 特定地形要求
    pub terrain_requirements: Vec<crate::world_map::domain::terrain::TerrainType>,
    /// 灵气密度要求
    pub spiritual_density_min: f32,
    /// 生成条件
    pub spawn_conditions: Vec<SpawnCondition>,
}

/// 生成条件
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SpawnCondition {
    /// 时间条件
    TimeCondition { 
        earliest_hour: u8, 
        latest_hour: u8 
    },
    /// 月相条件
    MoonPhase { 
        phase: String 
    },
    /// 事件触发
    EventTriggered { 
        event_type: String 
    },
    /// 玩家活动触发
    PlayerActivity { 
        activity_type: String,
        threshold: u32 
    },
}

// ============================================================================
// 采集结果
// ============================================================================

/// 采集收益 - 定义采集操作的结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct HarvestYield {
    /// 获得的资源列表
    pub resources: Vec<HarvestedResource>,
    /// 经验值获得
    pub experience_gained: u32,
    /// 技能经验获得
    pub skill_experience: std::collections::HashMap<String, u32>,
    /// 是否触发特殊事件
    pub special_event: Option<String>,
    /// 节点状态变化
    pub node_depletion: f32,
}

/// 采集到的资源
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct HarvestedResource {
    /// 资源类型
    pub resource_type: ResourceType,
    /// 数量
    pub quantity: u32,
    /// 品质等级
    pub quality: crate::basic_definition::MaterialGrade,
    /// 特殊属性
    pub special_properties: Vec<String>,
}

// ============================================================================
// 采集结果枚举
// ============================================================================

/// 采集结果 - 采集操作的最终结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum HarvestResult {
    /// 成功采集
    Success {
        yield_info: HarvestYield,
        message: String,
    },
    /// 采集失败
    Failure {
        reason: HarvestFailureReason,
        message: String,
    },
    /// 部分成功
    PartialSuccess {
        yield_info: HarvestYield,
        issues: Vec<String>,
    },
}

/// 采集失败原因
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum HarvestFailureReason {
    /// 条件不满足
    ConditionsNotMet,
    /// 访问权限不足
    AccessDenied,
    /// 资源已枯竭
    ResourceDepleted,
    /// 工具损坏
    ToolBroken,
    /// 意外事件
    UnexpectedEvent,
    /// 技能不足
    InsufficientSkill,
    /// 时间不对
    WrongTime,
}

// ============================================================================
// 默认实现
// ============================================================================

impl Default for ResourceAbundance {
    fn default() -> Self {
        ResourceAbundance::Medium
    }
}

impl Default for ResourceSpawnRule {
    fn default() -> Self {
        Self {
            node_type: ResourceNodeType::SpiritualHerbs,
            spawn_probability: 0.1,
            max_count: 5,
            min_distance: 100.0,
            terrain_requirements: vec![],
            spiritual_density_min: 0.5,
            spawn_conditions: vec![],
        }
    }
}