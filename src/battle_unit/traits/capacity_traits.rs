//! # 能力系统 Trait
//!
//! 定义战斗单位的各种能力接口

use super::super::types::*;
use crate::shared::GameResult;
use crate::{
    BattleUnitId, DamageInfo, DetailedDamageResult, HealResult, MovementCost, MovementResult,
    Position, ResourceCost, SkillResult, StatusEffect,
};
use std::collections::HashMap;

/// 行动能力 Trait
///
/// 定义战斗单位的行动相关能力
pub trait ActionCapacity {
    /// 检查是否可以行动
    fn can_act(&self) -> bool;

    /// 获取行动点数
    fn get_action_points(&self) -> i32;

    /// 设置行动点数
    fn set_action_points(&mut self, points: i32);

    /// 消耗行动点数
    fn consume_action_points(&mut self, points: i32) -> GameResult<()>;

    /// 恢复行动点数
    fn restore_action_points(&mut self, points: i32);

    /// 获取最大行动点数
    fn get_max_action_points(&self) -> i32;

    /// 获取行动优先级
    fn get_action_priority(&self) -> i32;

    /// 设置行动优先级
    fn set_action_priority(&mut self, priority: i32);

    /// 检查是否处于行动冷却中
    fn is_on_action_cooldown(&self) -> bool;

    /// 获取行动冷却剩余时间
    fn get_action_cooldown_remaining(&self) -> f32;

    /// 设置行动冷却
    fn set_action_cooldown(&mut self, duration: f32);

    /// 更新行动冷却
    fn update_action_cooldown(&mut self, delta_time: f32);

    /// 获取可执行的行动列表
    fn get_available_actions(&self) -> Vec<String>;

    /// 检查特定行动是否可用
    fn can_perform_action(&self, action_id: &str) -> bool;

    /// 执行行动
    fn perform_action(
        &mut self,
        action_id: &str,
        target: Option<BattleUnitId>,
    ) -> GameResult<ActionResult>;

    /// 获取行动消耗
    fn get_action_cost(&self, action_id: &str) -> Option<i32>;

    /// 中断当前行动
    fn interrupt_action(&mut self) -> GameResult<()>;

    /// 检查是否正在执行行动
    fn is_performing_action(&self) -> bool;

    /// 获取当前行动信息
    fn get_current_action(&self) -> Option<&ActionInfo>;
}

/// 移动能力 Trait
///
/// 定义战斗单位的移动相关能力
pub trait MovementCapacity {
    /// 获取移动速度
    fn get_movement_speed(&self) -> f32;

    /// 设置移动速度
    fn set_movement_speed(&mut self, speed: f32);

    /// 获取基础移动速度
    fn get_base_movement_speed(&self) -> f32;

    /// 计算移动距离
    fn calculate_movement_distance(&self, time: f32, movement_type: MovementType) -> f32;

    /// 计算移动时间
    fn calculate_movement_time(&self, distance: f32, movement_type: MovementType) -> f32;

    /// 计算移动消耗
    fn calculate_movement_cost(&self, distance: f32, movement_type: MovementType) -> MovementCost;

    /// 检查是否可以移动
    fn can_move(&self, movement_type: MovementType) -> bool;

    /// 检查是否可以移动到指定位置
    fn can_move_to(&self, target_position: &Position, movement_type: MovementType) -> bool;

    /// 执行移动
    fn move_to(
        &mut self,
        target_position: Position,
        movement_type: MovementType,
    ) -> GameResult<MovementResult>;

    /// 获取移动范围
    fn get_movement_range(&self, movement_type: MovementType) -> f32;

    /// 获取支持的移动类型
    fn get_supported_movement_types(&self) -> Vec<MovementType>;

    /// 检查是否支持特定移动类型
    fn supports_movement_type(&self, movement_type: MovementType) -> bool;

    /// 获取移动修正器
    fn get_movement_modifiers(&self) -> HashMap<MovementType, f32>;

    /// 添加移动修正器
    fn add_movement_modifier(&mut self, movement_type: MovementType, modifier: f32);

    /// 移除移动修正器
    fn remove_movement_modifier(&mut self, movement_type: MovementType);

    /// 获取当前位置
    fn get_current_position(&self) -> Position;

    /// 设置当前位置
    fn set_current_position(&mut self, position: Position);

    /// 获取移动历史
    fn get_movement_history(&self) -> Vec<MovementRecord>;

    /// 检查是否正在移动
    fn is_moving(&self) -> bool;

    /// 停止移动
    fn stop_movement(&mut self) -> GameResult<()>;
}

/// 攻击能力 Trait
///
/// 定义战斗单位的攻击相关能力
pub trait AttackCapacity {
    /// 获取攻击力
    fn get_attack_power(&self) -> i32;

    /// 设置攻击力
    fn set_attack_power(&mut self, power: i32);

    /// 获取基础攻击力
    fn get_base_attack_power(&self) -> i32;

    /// 计算对目标的伤害
    fn calculate_damage(&self, target: &dyn AttackCapacity, attack_type: AttackType) -> DamageInfo;

    /// 执行攻击
    fn attack(
        &mut self,
        target: &mut dyn AttackCapacity,
        attack_type: AttackType,
    ) -> GameResult<DetailedDamageResult>;

    /// 获取攻击范围
    fn get_attack_range(&self, attack_type: AttackType) -> f32;

    /// 获取攻击速度
    fn get_attack_speed(&self) -> f32;

    /// 设置攻击速度
    fn set_attack_speed(&mut self, speed: f32);

    /// 计算攻击时间
    fn calculate_attack_time(&self, attack_type: AttackType) -> f32;

    /// 检查是否可以攻击
    fn can_attack(&self, attack_type: AttackType) -> bool;

    /// 检查是否可以攻击目标
    fn can_attack_target(&self, target: &dyn AttackCapacity, attack_type: AttackType) -> bool;

    /// 获取暴击率
    fn get_critical_rate(&self) -> f32;

    /// 获取暴击伤害倍数
    fn get_critical_multiplier(&self) -> f32;

    /// 设置暴击率
    fn set_critical_rate(&mut self, rate: f32);

    /// 设置暴击伤害倍数
    fn set_critical_multiplier(&mut self, multiplier: f32);

    /// 检查攻击是否暴击
    fn roll_critical(&self) -> bool;

    /// 获取攻击类型列表
    fn get_attack_types(&self) -> Vec<AttackType>;

    /// 获取默认攻击类型
    fn get_default_attack_type(&self) -> AttackType;

    /// 获取攻击消耗
    fn get_attack_cost(&self, attack_type: AttackType) -> ResourceCost;

    /// 获取攻击冷却时间
    fn get_attack_cooldown(&self, attack_type: AttackType) -> f32;

    /// 检查攻击是否在冷却中
    fn is_attack_on_cooldown(&self, attack_type: AttackType) -> bool;

    /// 应用攻击冷却
    fn apply_attack_cooldown(&mut self, attack_type: AttackType);

    /// 更新攻击冷却
    fn update_attack_cooldowns(&mut self, delta_time: f32);
}

/// 技能系统 Trait
///
/// 定义战斗单位的技能相关能力
pub trait SkillSystem {
    /// 获取技能列表
    fn get_skills(&self) -> Vec<String>;

    /// 检查是否拥有技能
    fn has_skill(&self, skill_id: &str) -> bool;

    /// 学习技能
    fn learn_skill(&mut self, skill_id: String) -> GameResult<()>;

    /// 遗忘技能
    fn forget_skill(&mut self, skill_id: &str) -> GameResult<()>;

    /// 获取技能等级
    fn get_skill_level(&self, skill_id: &str) -> Option<u32>;

    /// 升级技能
    fn upgrade_skill(&mut self, skill_id: &str) -> GameResult<()>;

    /// 检查技能是否可用
    fn is_skill_available(&self, skill_id: &str) -> bool;

    /// 检查技能是否在冷却中
    fn is_skill_on_cooldown(&self, skill_id: &str) -> bool;

    /// 获取技能冷却剩余时间
    fn get_skill_cooldown_remaining(&self, skill_id: &str) -> Option<f32>;

    /// 执行技能
    fn cast_skill(
        &mut self,
        skill_id: &str,
        target: Option<BattleUnitId>,
    ) -> GameResult<SkillResult>;

    /// 中断技能施放
    fn interrupt_skill(&mut self, skill_id: &str) -> GameResult<()>;

    /// 检查是否正在施放技能
    fn is_casting_skill(&self) -> bool;

    /// 获取当前施放的技能
    fn get_current_casting_skill(&self) -> Option<&str>;

    /// 获取技能消耗
    fn get_skill_cost(&self, skill_id: &str) -> Option<ResourceCost>;

    /// 检查是否有足够资源施放技能
    fn can_afford_skill(&self, skill_id: &str) -> bool;

    /// 获取技能范围
    fn get_skill_range(&self, skill_id: &str) -> Option<f32>;

    /// 获取技能施放时间
    fn get_skill_cast_time(&self, skill_id: &str) -> Option<f32>;

    /// 获取技能冷却时间
    fn get_skill_cooldown_time(&self, skill_id: &str) -> Option<f32>;

    /// 更新技能冷却
    fn update_skill_cooldowns(&mut self, delta_time: f32);

    /// 重置所有技能冷却
    fn reset_all_skill_cooldowns(&mut self);

    /// 获取技能经验
    fn get_skill_experience(&self, skill_id: &str) -> Option<u64>;

    /// 增加技能经验
    fn add_skill_experience(&mut self, skill_id: &str, exp: u64) -> GameResult<()>;

    /// 检查技能是否可以升级
    fn can_upgrade_skill(&self, skill_id: &str) -> bool;

    /// 获取技能升级所需经验
    fn get_skill_upgrade_requirement(&self, skill_id: &str) -> Option<u64>;
}

// 辅助类型定义

/// 行动信息
#[derive(Debug, Clone, PartialEq)]
pub struct ActionInfo {
    /// 行动ID
    pub action_id: String,
    /// 开始时间
    pub start_time: f64,
    /// 持续时间
    pub duration: f32,
    /// 目标ID
    pub target_id: Option<BattleUnitId>,
    /// 行动状态
    pub status: ActionStatus,
}

/// 行动状态
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ActionStatus {
    /// 准备中
    Preparing,
    /// 执行中
    Executing,
    /// 完成
    Completed,
    /// 中断
    Interrupted,
    /// 失败
    Failed,
}

/// 行动结果
#[derive(Debug, Clone, PartialEq)]
pub struct ActionResult {
    /// 是否成功
    pub success: bool,
    /// 行动ID
    pub action_id: String,
    /// 消耗的行动点
    pub action_points_consumed: i32,
    /// 造成的效果
    pub effects: Vec<ActionEffect>,
    /// 失败原因
    pub failure_reason: Option<String>,
}

/// 行动效果
#[derive(Debug, Clone, PartialEq)]
pub enum ActionEffect {
    /// 伤害效果
    Damage(DetailedDamageResult),
    /// 治疗效果
    Heal(HealResult),
    /// 状态效果
    StatusEffect(StatusEffect),
    /// 移动效果
    Movement(MovementResult),
    /// 自定义效果
    Custom(String, serde_json::Value),
}

/// 攻击类型
#[derive(Debug, Clone, Copy, PartialEq, Hash)]
pub enum AttackType {
    /// 近战攻击
    Melee,
    /// 远程攻击
    Ranged,
    /// 魔法攻击
    Magic,
    /// 重击
    Heavy,
    /// 快速攻击
    Quick,
    /// 蓄力攻击
    Charged,
    /// 反击
    Counter,
    /// 范围攻击
    Area,
}

/// 移动记录
#[derive(Debug, Clone, PartialEq)]
pub struct MovementRecord {
    /// 时间戳
    pub timestamp: f64,
    /// 起始位置
    pub from_position: Position,
    /// 目标位置
    pub to_position: Position,
    /// 移动类型
    pub movement_type: MovementType,
    /// 移动距离
    pub distance: f32,
    /// 移动时间
    pub duration: f32,
}
