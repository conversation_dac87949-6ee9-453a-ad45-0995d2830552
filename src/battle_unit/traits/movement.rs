use crate::shared::GameResult;
/// 移动能力trait
///
/// 处理各种移动相关的能力，包括寻路和地形遍历
use crate::world_map::TerrainType;
use crate::{MovementCost, MovementResult, MovementType, Position, Speed};

/// 移动能力 - 处理各种移动相关的能力
pub trait MovementCapabilities {
    /// 获取基础移动速度
    fn base_movement_speed(&self) -> Speed;

    /// 获取当前移动速度（考虑状态效果）
    fn current_movement_speed(&self) -> Speed;

    /// 获取移动类型
    fn movement_type(&self) -> MovementType;

    /// 检查是否能通过地形
    fn can_traverse_terrain(&self, terrain: TerrainType) -> bool;

    /// 移动到指定位置
    fn move_to(&mut self, target: Position) -> GameResult<MovementResult>;

    /// 计算移动消耗
    fn calculate_movement_cost(&self, from: Position, to: Position) -> MovementCost;

    /// 获取移动路径
    fn find_path(&self, target: Position) -> Option<Vec<Position>>;

    /// 检查是否被定身
    fn is_immobilized(&self) -> bool;

    /// 检查是否减速
    fn is_slowed(&self) -> bool;

    /// 获取位移技能冷却
    fn displacement_skill_cooldown(&self) -> f32;
}
