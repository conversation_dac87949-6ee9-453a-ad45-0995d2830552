/// 游戏机制相关类型定义
///
/// 此模块定义了游戏中特有的机制类型，如品质、稀有度等
use serde::{Deserialize, Serialize};

// ============================================================================
// 游戏核心概念类型
// ============================================================================

/// 材料品质等级 - 修仙世界的品级体系
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, PartialOrd, Ord, Serialize, Deserialize)]
pub enum MaterialGrade {
    /// 凡品 - 最低级的材料
    Mortal,
    /// 灵品 - 含有灵气的材料
    Spiritual,
    /// 仙品 - 仙级材料
    Immortal,
    /// 神品 - 神级材料
    Divine,
    /// 圣品 - 圣级材料
    Sacred,
    /// 混沌品 - 最高级的混沌材料
    Chaos,
}

impl MaterialGrade {
    /// 获取品质等级的数值表示
    pub fn to_numeric(&self) -> u8 {
        match self {
            MaterialGrade::Mortal => 1,
            MaterialGrade::Spiritual => 2,
            MaterialGrade::Immortal => 3,
            MaterialGrade::Divine => 4,
            MaterialGrade::Sacred => 5,
            MaterialGrade::Chaos => 6,
        }
    }

    /// 从数值创建品质等级
    pub fn from_numeric(value: u8) -> Self {
        match value {
            1 => MaterialGrade::Mortal,
            2 => MaterialGrade::Spiritual,
            3 => MaterialGrade::Immortal,
            4 => MaterialGrade::Divine,
            5 => MaterialGrade::Sacred,
            _ => MaterialGrade::Chaos,
        }
    }

    /// 获取品质等级的颜色代码（用于显示）
    pub fn color_code(&self) -> &'static str {
        match self {
            MaterialGrade::Mortal => "#808080",    // 灰色
            MaterialGrade::Spiritual => "#00FF00", // 绿色
            MaterialGrade::Immortal => "#0080FF",  // 蓝色
            MaterialGrade::Divine => "#8000FF",    // 紫色
            MaterialGrade::Sacred => "#FF8000",    // 橙色
            MaterialGrade::Chaos => "#FF0000",     // 红色
        }
    }

    /// 获取品质等级的倍数修正
    pub fn quality_multiplier(&self) -> f32 {
        match self {
            MaterialGrade::Mortal => 1.0,
            MaterialGrade::Spiritual => 2.0,
            MaterialGrade::Immortal => 4.0,
            MaterialGrade::Divine => 8.0,
            MaterialGrade::Sacred => 16.0,
            MaterialGrade::Chaos => 32.0,
        }
    }
}

impl std::fmt::Display for MaterialGrade {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let name = match self {
            MaterialGrade::Mortal => "凡品",
            MaterialGrade::Spiritual => "灵品",
            MaterialGrade::Immortal => "仙品",
            MaterialGrade::Divine => "神品",
            MaterialGrade::Sacred => "圣品",
            MaterialGrade::Chaos => "混沌品",
        };
        write!(f, "{}", name)
    }
}

impl Default for MaterialGrade {
    fn default() -> Self {
        MaterialGrade::Mortal
    }
}

/// 稀有度等级
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, PartialOrd, Ord, Serialize, Deserialize)]
pub enum Rarity {
    Common,
    Uncommon,
    Rare,
    Epic,
    Legendary,
    Mythic,
}

impl Rarity {
    /// 获取稀有度的数值表示
    pub fn to_numeric(&self) -> u8 {
        match self {
            Rarity::Common => 1,
            Rarity::Uncommon => 2,
            Rarity::Rare => 3,
            Rarity::Epic => 4,
            Rarity::Legendary => 5,
            Rarity::Mythic => 6,
        }
    }

    /// 从数值创建稀有度
    pub fn from_numeric(value: u8) -> Self {
        match value {
            1 => Rarity::Common,
            2 => Rarity::Uncommon,
            3 => Rarity::Rare,
            4 => Rarity::Epic,
            5 => Rarity::Legendary,
            _ => Rarity::Mythic,
        }
    }

    /// 获取稀有度的颜色代码
    pub fn color_code(&self) -> &'static str {
        match self {
            Rarity::Common => "#808080",    // 灰色
            Rarity::Uncommon => "#00FF00",  // 绿色
            Rarity::Rare => "#0080FF",      // 蓝色
            Rarity::Epic => "#8000FF",      // 紫色
            Rarity::Legendary => "#FF8000", // 橙色
            Rarity::Mythic => "#FF0000",    // 红色
        }
    }

    /// 获取稀有度的品质倍数
    pub fn quality_multiplier(&self) -> f32 {
        match self {
            Rarity::Common => 1.0,
            Rarity::Uncommon => 1.5,
            Rarity::Rare => 2.0,
            Rarity::Epic => 3.0,
            Rarity::Legendary => 5.0,
            Rarity::Mythic => 8.0,
        }
    }
}

impl std::fmt::Display for Rarity {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let name = match self {
            Rarity::Common => "普通",
            Rarity::Uncommon => "不凡",
            Rarity::Rare => "稀有",
            Rarity::Epic => "史诗",
            Rarity::Legendary => "传说",
            Rarity::Mythic => "神话",
        };
        write!(f, "{}", name)
    }
}

impl Default for Rarity {
    fn default() -> Self {
        Rarity::Common
    }
}

/// 装备槽位
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EquipmentSlot {
    MainHand,
    OffHand,
    Head,
    Chest,
    Legs,
    Feet,
    Hands,
    Ring1,
    Ring2,
    Necklace,
}

impl EquipmentSlot {
    pub fn all_slots() -> &'static [EquipmentSlot] {
        &[
            EquipmentSlot::MainHand,
            EquipmentSlot::OffHand,
            EquipmentSlot::Head,
            EquipmentSlot::Chest,
            EquipmentSlot::Legs,
            EquipmentSlot::Feet,
            EquipmentSlot::Hands,
            EquipmentSlot::Ring1,
            EquipmentSlot::Ring2,
            EquipmentSlot::Necklace,
        ]
    }

    /// 获取装备槽位的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            EquipmentSlot::MainHand => "主手",
            EquipmentSlot::OffHand => "副手",
            EquipmentSlot::Head => "头部",
            EquipmentSlot::Chest => "胸部",
            EquipmentSlot::Legs => "腿部",
            EquipmentSlot::Feet => "脚部",
            EquipmentSlot::Hands => "手部",
            EquipmentSlot::Ring1 => "戒指1",
            EquipmentSlot::Ring2 => "戒指2",
            EquipmentSlot::Necklace => "项链",
        }
    }
}

/// 元素类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ElementType {
    Fire,
    Water,
    Earth,
    Air,
    Light,
    Dark,
    Lightning,
    Ice,
    Nature,
    Arcane,
    Chaos,
    Order,
}

impl ElementType {
    pub fn all_elements() -> &'static [ElementType] {
        &[
            ElementType::Fire,
            ElementType::Water,
            ElementType::Earth,
            ElementType::Air,
            ElementType::Light,
            ElementType::Dark,
            ElementType::Lightning,
            ElementType::Ice,
            ElementType::Nature,
            ElementType::Arcane,
            ElementType::Chaos,
            ElementType::Order,
        ]
    }

    /// 获取元素的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            ElementType::Fire => "火",
            ElementType::Water => "水",
            ElementType::Earth => "土",
            ElementType::Air => "风",
            ElementType::Light => "光",
            ElementType::Dark => "暗",
            ElementType::Lightning => "雷",
            ElementType::Ice => "冰",
            ElementType::Nature => "自然",
            ElementType::Arcane => "奥术",
            ElementType::Chaos => "混沌",
            ElementType::Order => "秩序",
        }
    }

    /// 获取克制的元素
    pub fn counters(&self) -> Option<ElementType> {
        match self {
            ElementType::Fire => Some(ElementType::Ice),
            ElementType::Ice => Some(ElementType::Fire),
            ElementType::Water => Some(ElementType::Fire),
            ElementType::Earth => Some(ElementType::Air),
            ElementType::Air => Some(ElementType::Earth),
            ElementType::Light => Some(ElementType::Dark),
            ElementType::Dark => Some(ElementType::Light),
            ElementType::Lightning => Some(ElementType::Water),
            ElementType::Nature => Some(ElementType::Dark),
            ElementType::Arcane => Some(ElementType::Chaos),
            ElementType::Chaos => Some(ElementType::Order),
            ElementType::Order => Some(ElementType::Chaos),
        }
    }

    /// 获取被克制的元素
    pub fn weak_to(&self) -> Option<ElementType> {
        ElementType::all_elements()
            .iter()
            .find(|&element| element.counters() == Some(*self))
            .copied()
    }
}

/// 目标类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TargetType {
    /// 自己
    Self_,
    /// 友方单位
    Ally,
    /// 敌方单位
    Enemy,
    /// 任何单位
    Any,
    /// 指定区域
    Area,
    /// 空地
    Ground,
}

impl TargetType {
    /// 获取目标类型的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            TargetType::Self_ => "自己",
            TargetType::Ally => "友方",
            TargetType::Enemy => "敌方",
            TargetType::Any => "任意目标",
            TargetType::Area => "区域",
            TargetType::Ground => "地面",
        }
    }
}
