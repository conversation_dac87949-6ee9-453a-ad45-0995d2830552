/// 战斗单位实现模块
/// 
/// 提供各种trait的具体实现，按原子化原则组织

// 基础结构和工具
pub mod adapter_struct;
pub mod adapter_utils;

// trait实现（按功能域分组）
pub mod identity_impl;
pub mod spatial_impl;
pub mod temporal_impl;
pub mod health_impl;
pub mod mana_impl;
pub mod stamina_impl;
pub mod attributes_impl;
pub mod resistance_impl;
pub mod action_impl;
pub mod movement_impl;

// 其他trait实现
pub mod attack_impl;
pub mod skill_impl;
pub mod status_impl;
pub mod equipment_impl;

// 重导出主要类型
pub use adapter_struct::*;

/// 实现模块版本
pub const IMPLEMENTATIONS_VERSION: &str = "1.0.0";