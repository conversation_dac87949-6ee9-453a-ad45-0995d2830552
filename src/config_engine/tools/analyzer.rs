//! # 分析器模块

use crate::config_engine::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 分析报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisReport {
    pub name: String,
    pub timestamp: std::time::SystemTime,
    pub summary: AnalysisSummary,
    pub structure_analysis: StructureAnalysis,
    pub performance_analysis: PerformanceAnalysis,
    pub recommendations: Vec<Recommendation>,
    pub warnings: Vec<String>,
    pub errors: Vec<String>,
}

/// 分析摘要
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisSummary {
    pub total_configurations: usize,
    pub complexity_score: f64,
    pub health_score: f64,
    pub maintenance_score: f64,
}

/// 结构分析
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StructureAnalysis {
    pub depth_levels: usize,
    pub total_keys: usize,
    pub duplicate_keys: Vec<String>,
    pub unused_configurations: Vec<String>,
    pub circular_references: Vec<String>,
}

/// 性能分析
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PerformanceAnalysis {
    pub memory_usage: u64,
    pub load_time: std::time::Duration,
    pub cache_hit_rate: f64,
    pub bottlenecks: Vec<String>,
}

/// 建议
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Recommendation {
    pub category: RecommendationCategory,
    pub title: String,
    pub description: String,
    pub priority: RecommendationPriority,
    pub action: String,
}

/// 建议类别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationCategory {
    Performance,
    Structure,
    Security,
    Maintenance,
    Best_Practices,
}

/// 建议优先级
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationPriority {
    Critical,
    High,
    Medium,
    Low,
}

impl AnalysisReport {
    pub fn new(name: String) -> Self {
        Self {
            name,
            timestamp: std::time::SystemTime::now(),
            summary: AnalysisSummary {
                total_configurations: 0,
                complexity_score: 0.0,
                health_score: 0.0,
                maintenance_score: 0.0,
            },
            structure_analysis: StructureAnalysis {
                depth_levels: 0,
                total_keys: 0,
                duplicate_keys: Vec::new(),
                unused_configurations: Vec::new(),
                circular_references: Vec::new(),
            },
            performance_analysis: PerformanceAnalysis {
                memory_usage: 0,
                load_time: std::time::Duration::from_millis(0),
                cache_hit_rate: 0.0,
                bottlenecks: Vec::new(),
            },
            recommendations: Vec::new(),
            warnings: Vec::new(),
            errors: Vec::new(),
        }
    }

    pub fn add_recommendation(&mut self, recommendation: Recommendation) {
        self.recommendations.push(recommendation);
    }

    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }

    pub fn add_error(&mut self, error: String) {
        self.errors.push(error);
    }

    pub fn get_critical_recommendations(&self) -> Vec<&Recommendation> {
        self.recommendations
            .iter()
            .filter(|r| matches!(r.priority, RecommendationPriority::Critical))
            .collect()
    }

    pub fn get_health_status(&self) -> HealthStatus {
        if self.summary.health_score >= 90.0 {
            HealthStatus::Excellent
        } else if self.summary.health_score >= 75.0 {
            HealthStatus::Good
        } else if self.summary.health_score >= 60.0 {
            HealthStatus::Fair
        } else if self.summary.health_score >= 40.0 {
            HealthStatus::Poor
        } else {
            HealthStatus::Critical
        }
    }
}

/// 健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HealthStatus {
    Excellent,
    Good,
    Fair,
    Poor,
    Critical,
}

/// 配置分析器
pub struct ConfigAnalyzer {
    analysis_history: HashMap<String, AnalysisReport>,
}

impl ConfigAnalyzer {
    pub fn new() -> Self {
        Self {
            analysis_history: HashMap::new(),
        }
    }

    pub fn analyze_structure(&self, data: &serde_json::Value) -> Result<String> {
        Ok(format!("Structure analysis: {}", data.to_string()))
    }

    pub fn analyze_performance(&self, data: &serde_json::Value) -> Result<String> {
        Ok(format!("Performance analysis: OK"))
    }

    /// 完整分析
    pub fn analyze(&mut self, name: String, data: &serde_json::Value) -> Result<AnalysisReport> {
        let start_time = std::time::Instant::now();
        let mut report = AnalysisReport::new(name.clone());

        // 结构分析
        self.analyze_structure_detailed(data, &mut report)?;

        // 性能分析
        self.analyze_performance_detailed(data, &mut report)?;

        // 生成建议
        self.generate_recommendations(&mut report)?;

        // 计算健康分数
        self.calculate_health_scores(&mut report)?;

        report.performance_analysis.load_time = start_time.elapsed();

        // 存储历史
        self.analysis_history.insert(name, report.clone());

        Ok(report)
    }

    fn analyze_structure_detailed(
        &self,
        data: &serde_json::Value,
        report: &mut AnalysisReport,
    ) -> Result<()> {
        report.structure_analysis.depth_levels = self.calculate_depth(data);
        report.structure_analysis.total_keys = self.count_keys(data);
        report.summary.total_configurations = report.structure_analysis.total_keys;

        // 简化实现，实际应该做更详细的分析
        if report.structure_analysis.depth_levels > 10 {
            report.add_warning("配置结构层次过深，可能影响性能".to_string());
        }

        if report.structure_analysis.total_keys > 1000 {
            report.add_warning("配置项过多，建议考虑分片化".to_string());
        }

        Ok(())
    }

    fn analyze_performance_detailed(
        &self,
        _data: &serde_json::Value,
        report: &mut AnalysisReport,
    ) -> Result<()> {
        // 简化实现
        report.performance_analysis.memory_usage = 1024; // 模拟值
        report.performance_analysis.cache_hit_rate = 85.0; // 模拟值

        if report.performance_analysis.cache_hit_rate < 70.0 {
            report.add_warning("缓存命中率较低，建议优化缓存策略".to_string());
        }

        Ok(())
    }

    fn generate_recommendations(&self, report: &mut AnalysisReport) -> Result<()> {
        // 基于分析结果生成建议
        if report.structure_analysis.depth_levels > 8 {
            report.add_recommendation(Recommendation {
                category: RecommendationCategory::Structure,
                title: "简化配置结构".to_string(),
                description: "配置结构层次过深，建议重新设计".to_string(),
                priority: RecommendationPriority::Medium,
                action: "重构配置文件，减少嵌套层次".to_string(),
            });
        }

        if report.performance_analysis.cache_hit_rate < 70.0 {
            report.add_recommendation(Recommendation {
                category: RecommendationCategory::Performance,
                title: "优化缓存策略".to_string(),
                description: "缓存命中率较低，需要优化".to_string(),
                priority: RecommendationPriority::High,
                action: "调整缓存配置或增加缓存大小".to_string(),
            });
        }

        Ok(())
    }

    fn calculate_health_scores(&self, report: &mut AnalysisReport) -> Result<()> {
        // 简化的健康分数计算
        let mut health_score = 100.0;

        // 基于结构复杂度减分
        if report.structure_analysis.depth_levels > 10 {
            health_score -= 20.0;
        } else if report.structure_analysis.depth_levels > 5 {
            health_score -= 10.0;
        }

        // 基于缓存命中率减分
        if report.performance_analysis.cache_hit_rate < 70.0 {
            health_score -= 15.0;
        }

        // 基于警告和错误减分
        health_score -= report.warnings.len() as f64 * 5.0;
        health_score -= report.errors.len() as f64 * 10.0;

        report.summary.health_score = health_score.max(0.0);
        report.summary.complexity_score =
            (report.structure_analysis.depth_levels as f64 / 10.0 * 100.0).min(100.0);
        report.summary.maintenance_score = (100.0 - report.warnings.len() as f64 * 5.0).max(0.0);

        Ok(())
    }

    fn calculate_depth(&self, value: &serde_json::Value) -> usize {
        match value {
            serde_json::Value::Object(map) => {
                1 + map
                    .values()
                    .map(|v| self.calculate_depth(v))
                    .max()
                    .unwrap_or(0)
            }
            serde_json::Value::Array(arr) => {
                1 + arr
                    .iter()
                    .map(|v| self.calculate_depth(v))
                    .max()
                    .unwrap_or(0)
            }
            _ => 0,
        }
    }

    fn count_keys(&self, value: &serde_json::Value) -> usize {
        match value {
            serde_json::Value::Object(map) => {
                map.len() + map.values().map(|v| self.count_keys(v)).sum::<usize>()
            }
            serde_json::Value::Array(arr) => arr.iter().map(|v| self.count_keys(v)).sum(),
            _ => 0,
        }
    }

    pub fn get_analysis_history(&self, name: &str) -> Option<&AnalysisReport> {
        self.analysis_history.get(name)
    }

    pub fn list_analyses(&self) -> Vec<String> {
        self.analysis_history.keys().cloned().collect()
    }

    pub fn clear_history(&mut self) {
        self.analysis_history.clear();
    }
}

impl Default for ConfigAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}
