# 材料系统功能模块化设计

## 模块一：材料定义与基础设定 (Material Definition & Core Settings)

此模块负责定义材料的固有属性和基本信息。

1.  **子模块：属性体系 (Attribute System)**
    *   功能：管理所有基础五行属性（金木水火土）和衍生/变异属性（冰雷风光暗等）的定义、特性及其元数据。
    *   关联：《材料系统设计备忘录.md》核心设定，《材料命名词库.md》核心属性。
2.  **子模块：命名规范 (Naming Convention System)**
    *   功能：实现基于《材料命名词库.md》的规则化命名生成器，并允许为特殊材料手动指定名称。
    *   关联：《材料系统设计备忘录.md》材料命名体系，《材料命名词库.md》完整内容。
3.  **子模块：品阶与年份体系 (Grade & Age System)**
    *   功能：定义材料的品阶等级（凡、灵、仙、神…混沌）、各品阶的细分规则（九品、三品、极品等），以及年份对材料的影响机制（质变点、曲线增强）。
    *   关联：《材料系统设计备忘录.md》材料品阶/稀有度、品阶与年份关联，《材料命名词库.md》品阶/年份描述词。
4.  **子模块：形态与来源分类 (Form & Source Categorization)**
    *   功能：对材料的物理形态（固体、液体、精华等）和主要来源（怪物、矿物、灵植等）进行分类和管理。
    *   关联：《材料系统设计备忘录.md》各材料获取途径描述，《材料命名词库.md》材料本体/形态后缀。
5.  **子模块：描述文本管理 (Descriptive Text Management)**
    *   功能：存储和管理每种材料的描述文本，确保符合修仙风格和《材料系统设计备忘录.md》中提出的撰写原则。
    *   关联：《材料系统设计备忘录.md》材料的描述文本。

## 模块二：材料获取与产出 (Material Acquisition & Generation)

此模块负责游戏中所有材料的来源和产出逻辑。

1.  **子模块：怪物掉落逻辑 (Monster Drop Logic)**
    *   功能：处理怪物掉落材料的计算，包括基础概率、等级修正、幸运值影响、特定击杀条件、掉落池管理等。
    *   关联：《材料系统设计备忘录.md》怪物掉落。
2.  **子模块：资源点采集逻辑 (Resource Node Gathering Logic)**
    *   功能：管理矿场、灵草仙药等固定或动态资源点的材料产出，包括资源点生成、发现、枯竭、采集工具影响、采集技能判定、特殊产出（地脉精华）等。
    *   关联：《材料系统设计备忘录.md》矿场采集、物体上采集。
3.  **子模块：世界事件与动态产出 (World Events & Dynamic Spawning)**
    *   功能：管理通过特定世界事件（如地龙翻身）、天气、时间等动态条件生成的稀有材料或资源点。
    *   关联：《材料系统设计备忘录.md》提及的动态生成矿藏、特定时间/天气影响材料。

## 模块三：材料属性互动与效果 (Material Property Interaction & Effects)

此模块负责定义材料间的复杂关系及其在游戏行为中的具体体现。

1.  **子模块：五行生克引擎 (Five Elements Engine)**
    *   功能：实现五行相生相克的规则，并应用于炼制、布阵、傀儡部件组合、装备镶嵌等场景，决定成功率、效果增减、词缀产生等。
    *   关联：《材料系统设计备忘录.md》属性间的克制与生发关系 (5.1, 5.2, 5.3, 5.4)。
2.  **子模块：衍生属性互动逻辑 (Derived Attribute Interaction Logic)**
    *   功能：定义冰、雷、风、光、暗等衍生属性与基础五行及彼此之间的相互作用规则。
    *   关联：《材料系统设计备忘录.md》衍生属性的介入 (5.1.C)。
3.  **子模块：年份效果处理器 (Age Effect Processor)**
    *   功能：根据材料的年份，应用其“质变”和“曲线增强”效果，影响材料的基础效用、词缀、用途等。
    *   关联：《材料系统设计备忘录.md》品阶与年份的关联机制。
4.  **子模块：特殊材料特性处理 (Special Material Trait Handling)**
    *   功能：管理具有“灵性”、高能量、不稳定、活体等特殊性质材料的独特行为和影响。
    *   关联：《材料系统设计备忘录.md》材料的“灵性”影响、特殊材料（高能量、不稳定、活体）的堆叠与存储规则。

## 模块四：材料应用与消耗 (Material Utilization & Consumption)

此模块是材料价值的核心体现，涵盖所有消耗材料的系统。

1.  **子模块：通用制作接口 (General Crafting Interface)**
    *   功能：为炼丹、炼器、布阵、符箓绘制等提供一个统一的材料消耗和成品生成的框架。
2.  **子模块：傀儡系统深度集成 (Puppet System Integration)**
    *   功能：完整实现《材料系统设计备忘录.md》中详述的傀儡制作（图纸、主辅材、属性融合、技艺、环境）与驱动材料需求。
    *   关联：《材料系统设计备忘录.md》傀儡制作/驱动 (3.3)。
3.  **子模块：灵宠系统深度集成 (Pet System Integration)**
    *   功能：完整实现《材料系统设计备忘录.md》中详述的灵宠喂养（材料偏好、效果）与宠物卵孵化（元素能量、特殊引子）的材料需求。
    *   关联：《材料系统设计备忘录.md》喂养灵宠与宠物卵孵化 (3.1)。
4.  **子模块：洞府系统深度集成 (Cave Abode System Integration)**
    *   功能：完整实现《材料系统设计备忘录.md》中详述的洞府建设、升级、维护、灵气平衡等方面的材料消耗。
    *   关联：《材料系统设计备忘录.md》洞府建设/升级与维护 (3.2)。
5.  **子模块：任务与贡献系统接口 (Quest & Reputation System Interface)**
    *   功能：允许任务系统和门派贡献系统将特定材料作为需求或奖励。

## 模块五：材料存储与管理 (Material Storage & Management)

此模块负责玩家如何存储、携带和管理获得的材料。

1.  **子模块：堆叠与拆分逻辑 (Stacking & Splitting Logic)**
    *   功能：定义不同材料的堆叠上限规则，并提供统一的物品堆叠和拆分操作。
    *   关联：《材料系统设计备忘录.md》不同材料的堆叠上限设定与通用堆叠/拆分逻辑 (4.1)。
2.  **子模块：玩家背包与储物装备 (Player Inventory & Storage Gear)**
    *   功能：管理玩家的基础背包和通过穿戴/锻造储物装备（储物袋、戒指等）获得的扩展存储空间。
    *   关联：《材料系统设计备忘录.md》储物特性与装备 (4.2 - 一)。
3.  **子模块：固定仓储设施 (Fixed Storage Facilities)**
    *   功能：管理洞府或门派中的基础仓库、专属材料仓库（药柜、材料架等）及其特殊效果和扩容机制。
    *   关联：《材料系统设计备忘录.md》专属仓库/设施 (4.2 - 二)。
4.  **子模块：特殊材料存储规则 (Special Material Storage Rules)**
    *   功能：实现高能量、不稳定、活体材料的特定存储工具/环境需求及其风险管理。
    *   关联：《材料系统设计备忘录.md》特殊材料（高能量、不稳定、活体）的堆叠与存储规则 (4.3)。

## 模块六：系统维护与扩展接口 (System Maintenance & Expansion Interface)

此模块关注系统的可维护性和未来发展。

1.  **子模块：数据配置与平衡工具 (Data Configuration & Balancing Tools)**
    *   功能：提供接口或工具，便于设计者调整材料产出率、消耗量、配方、效果数值等，以进行游戏平衡。
2.  **子模块：扩展性支持 (Extensibility Support)**
    *   功能：设计系统时预留接口，方便未来添加新的材料种类、属性、用途或相关玩法系统。
