/// 移动能力trait实现
/// 
/// 实现MovementCapacity trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;
use crate::battle_unit::types::*;
use crate::battle_unit::traits::MovementCapacity;
use crate::attribute::{AttributeType, CoreAttribute, DerivedAttribute};

impl MovementCapacity for BattleUnitAdapter {
    fn movement_speed(&self) -> MovementSpeed {
        // 基础移动速度 = 敏捷 + 木属性
        let speed = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Speed)
        );
        let wood_attr = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Wood)
        );
        
        let base_speed = (speed + wood_attr) / 4; // 基础速度
        
        // 装备移动速度加成
        let equipment_bonus = self.get_equipment_movement_bonus();
        
        // Buff移动速度加成
        let buff_bonus = self.get_buff_movement_bonus();
        
        // 负重和状态惩罚
        let penalty = self.calculate_movement_penalty();
        
        let total_speed = base_speed + equipment_bonus + buff_bonus - penalty;
        total_speed.max(1) as MovementSpeed // 最小移动速度为1
    }
    
    fn max_movement_range(&self) -> Distance {
        // 每回合最大移动距离
        let movement_speed = self.movement_speed();
        let stamina_factor = self.stamina_percentage();
        
        // 基础移动范围 = 移动速度 * 耐力因子
        let base_range = movement_speed as f32 * stamina_factor;
        
        // 地形修正
        let terrain_modifier = self.get_terrain_movement_modifier();
        
        (base_range * terrain_modifier) as Distance
    }
    
    fn movement_cost(&self, distance: Distance) -> ActionPoints {
        let movement_speed = self.movement_speed();
        
        // 基础消耗：距离 / 移动速度
        let base_cost = (distance as f32 / movement_speed as f32).ceil();
        
        // 地形额外消耗
        let terrain_cost = self.get_terrain_movement_cost(distance);
        
        (base_cost + terrain_cost) as ActionPoints
    }
    
    fn can_move_to(&self, target_position: Position) -> bool {
        // 检查是否可以移动到目标位置
        let distance = self.position().distance_to(target_position);
        
        // 检查移动范围
        if distance > self.max_movement_range() {
            return false;
        }
        
        // 检查行动点数
        let movement_cost = self.movement_cost(distance);
        if self.action_points() < movement_cost {
            return false;
        }
        
        // 检查移动阻止状态
        if self.has_movement_blocking_status() {
            return false;
        }
        
        // 检查地形可通行性
        if !self.is_terrain_passable(target_position) {
            return false;
        }
        
        true
    }
    
    fn move_to(&mut self, target_position: Position) -> GameResult<MovementResult> {
        if !self.can_move_to(target_position) {
            return Err("无法移动到目标位置".into());
        }
        
        let distance = self.position().distance_to(target_position);
        let movement_cost = self.movement_cost(distance);
        
        // 消耗行动点数
        self.consume_action_points(movement_cost)?;
        
        // 消耗耐力
        let stamina_cost = self.calculate_movement_stamina_cost(distance);
        self.consume_stamina(stamina_cost)?;
        
        // 更新位置
        let old_position = self.position();
        self.current_position = target_position;
        
        // 触发移动事件
        let movement_result = MovementResult {
            from: old_position,
            to: target_position,
            distance,
            action_points_consumed: movement_cost,
            stamina_consumed: stamina_cost,
            movement_time: self.calculate_movement_time(distance),
        };
        
        // 触发移动相关效果
        self.trigger_movement_effects(&movement_result)?;
        
        Ok(movement_result)
    }
    
    fn dash(&mut self, target_position: Position) -> GameResult<MovementResult> {
        // 冲刺移动（消耗更多耐力但距离更远）
        let dash_range = self.max_movement_range() * 2;
        let distance = self.position().distance_to(target_position);
        
        if distance > dash_range {
            return Err("冲刺距离超出范围".into());
        }
        
        let stamina_cost = self.calculate_movement_stamina_cost(distance) * 2;
        if !self.has_enough_stamina(stamina_cost) {
            return Err("耐力不足以进行冲刺".into());
        }
        
        // 冲刺消耗所有行动点数
        let action_cost = self.action_points();
        self.consume_action_points(action_cost)?;
        self.consume_stamina(stamina_cost)?;
        
        let old_position = self.position();
        self.current_position = target_position;
        
        Ok(MovementResult {
            from: old_position,
            to: target_position,
            distance,
            action_points_consumed: action_cost,
            stamina_consumed: stamina_cost,
            movement_time: self.calculate_movement_time(distance) / 2, // 冲刺更快
        })
    }
    
    fn teleport(&mut self, target_position: Position) -> GameResult<MovementResult> {
        // 瞬移（需要特殊能力）
        if !self.can_teleport() {
            return Err("没有瞬移能力".into());
        }
        
        let mana_cost = self.calculate_teleport_mana_cost(target_position);
        if !self.has_enough_mana(mana_cost) {
            return Err("法力不足以进行瞬移".into());
        }
        
        self.consume_mana(mana_cost)?;
        self.consume_action_points(2)?; // 瞬移消耗2点行动力
        
        let old_position = self.position();
        let distance = old_position.distance_to(target_position);
        self.current_position = target_position;
        
        Ok(MovementResult {
            from: old_position,
            to: target_position,
            distance,
            action_points_consumed: 2,
            stamina_consumed: 0,
            movement_time: 0, // 瞬移无时间消耗
        })
    }
}

impl BattleUnitAdapter {
    fn get_equipment_movement_bonus(&self) -> AttributePoints {
        // TODO: 实现装备移动速度加成计算
        0
    }
    
    fn get_buff_movement_bonus(&self) -> AttributePoints {
        // TODO: 实现buff移动速度加成计算
        0
    }
    
    fn calculate_movement_penalty(&self) -> AttributePoints {
        let mut penalty = 0;
        
        // 负重惩罚
        penalty += self.calculate_encumbrance_penalty();
        
        // 状态效果惩罚
        if self.has_status(StatusType::Poison) {
            penalty += 2;
        }
        if self.has_status(StatusType::Burn) {
            penalty += 1;
        }
        
        penalty
    }
    
    fn get_terrain_movement_modifier(&self) -> f32 {
        // TODO: 基于当前地形计算移动修正
        1.0
    }
    
    fn get_terrain_movement_cost(&self, distance: Distance) -> f32 {
        // TODO: 基于地形计算额外移动消耗
        0.0
    }
    
    fn has_movement_blocking_status(&self) -> bool {
        self.has_status(StatusType::Paralysis) ||
        self.has_status(StatusType::Freeze) ||
        self.has_status(StatusType::Stun)
    }
    
    fn is_terrain_passable(&self, position: Position) -> bool {
        // TODO: 检查地形可通行性
        true
    }
    
    fn calculate_movement_stamina_cost(&self, distance: Distance) -> Stamina {
        // 基础耐力消耗 = 距离
        let base_cost = distance;
        
        // 体质减少耐力消耗
        let constitution_mod = BattleUnitAdapter::calculate_modifier(
            self.calculate_total_attribute(
                AttributeType::Base(CoreAttribute::Earth)
            )
        );
        
        let adjusted_cost = base_cost - constitution_mod.max(0) as Distance;
        adjusted_cost.max(1) as Stamina
    }
    
    fn calculate_movement_time(&self, distance: Distance) -> GameTime {
        let movement_speed = self.movement_speed();
        (distance as f32 / movement_speed as f32 * 100.0) as GameTime
    }
    
    fn trigger_movement_effects(&mut self, result: &MovementResult) -> GameResult<()> {
        // TODO: 触发移动相关的技能效果、地形效果等
        Ok(())
    }
    
    fn can_teleport(&self) -> bool {
        // TODO: 检查是否有瞬移能力（技能、装备等）
        false
    }
    
    fn calculate_teleport_mana_cost(&self, target_position: Position) -> Mana {
        let distance = self.position().distance_to(target_position);
        (distance * 2) as Mana // 每单位距离消耗2点法力
    }
}