//! # 配置加载器
//!
//! 负责解析各种格式的配置文件，包括JSON、YAML、TOML和JDM格式

use serde::Deserialize;
use std::collections::HashMap;

use crate::config_engine::{
    config::{FieldDefinition, FieldType, RuleDefinition, TypeDefinition},
    core::ContextValue,
    error::{ConfigEngineError, ConfigurationError},
    Result,
};

/// 配置文件格式
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ConfigFormat {
    /// JSON格式
    Json,
    /// YAML格式
    Yaml,
    /// TOML格式
    Toml,
    /// JDM格式（JSON Decision Model）
    Jdm,
}

/// 配置加载器
///
/// 提供统一的配置文件解析接口，支持多种格式
pub struct ConfigLoader;

impl ConfigLoader {
    /// 解析配置内容
    ///
    /// # 参数
    /// - `content`: 配置文件内容
    /// - `format`: 配置文件格式
    ///
    /// # 返回
    /// 解析后的配置结构
    pub fn parse_content(content: &str, format: ConfigFormat) -> Result<ParsedConfig> {
        match format {
            ConfigFormat::Json => Self::parse_json(content),
            ConfigFormat::Yaml => Self::parse_yaml(content),
            ConfigFormat::Toml => Self::parse_toml(content),
            ConfigFormat::Jdm => Self::parse_jdm(content),
        }
    }

    /// 解析JSON格式配置
    fn parse_json(content: &str) -> Result<ParsedConfig> {
        let raw_config: RawConfig = serde_json::from_str(content).map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::ParseError {
                details: format!("JSON解析错误: {}", e),
            })
        })?;

        Self::convert_raw_config(raw_config)
    }

    /// 解析YAML格式配置
    fn parse_yaml(content: &str) -> Result<ParsedConfig> {
        #[cfg(feature = "yaml")]
        {
            let raw_config: RawConfig = serde_yaml::from_str(content).map_err(|e| {
                ConfigEngineError::Configuration(ConfigurationError::ParseError {
                    details: format!("YAML解析错误: {}", e),
                })
            })?;

            Self::convert_raw_config(raw_config)
        }

        #[cfg(not(feature = "yaml"))]
        {
            Err(ConfigEngineError::InvalidConfiguration(
                "YAML support requires 'yaml' feature".to_string(),
            ))
        }
    }

    /// 解析TOML格式配置
    fn parse_toml(content: &str) -> Result<ParsedConfig> {
        let raw_config: RawConfig = toml::from_str(content).map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::ParseError {
                details: format!("TOML解析错误: {}", e),
            })
        })?;

        Self::convert_raw_config(raw_config)
    }

    /// 解析JDM格式（JSON Decision Model）
    fn parse_jdm(content: &str) -> Result<ParsedConfig> {
        // JDM是单个决策模型，将其转换为规则定义
        let jdm_doc: serde_json::Value = serde_json::from_str(content).map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::ParseError {
                details: format!("JDM解析错误: {}", e),
            })
        })?;

        // 验证JDM格式
        if !Self::is_valid_jdm(&jdm_doc) {
            return Err(ConfigEngineError::Configuration(
                ConfigurationError::InvalidFormat {
                    file: "jdm".to_string(),
                    reason: "不是有效的JDM格式".to_string(),
                },
            ));
        }

        // 提取规则信息
        let rule_id = Self::extract_jdm_rule_id(&jdm_doc)?;
        let rule_name = Self::extract_jdm_rule_name(&jdm_doc)?;

        let rule = RuleDefinition::new(rule_id, rule_name, content.to_string());

        let mut rules = Vec::new();
        rules.push(rule);

        Ok(ParsedConfig {
            rules,
            types: Vec::new(),
        })
    }

    /// 验证JDM格式
    fn is_valid_jdm(jdm: &serde_json::Value) -> bool {
        jdm.get("contentType")
            .and_then(|ct| ct.as_str())
            .map(|ct| ct == "application/vnd.gorules.decision")
            .unwrap_or(false)
    }

    /// 从JDM中提取规则ID
    fn extract_jdm_rule_id(jdm: &serde_json::Value) -> Result<String> {
        // 尝试从多个位置提取ID
        if let Some(id) = jdm.get("id").and_then(|v| v.as_str()) {
            return Ok(id.to_string());
        }

        if let Some(name) = jdm.get("name").and_then(|v| v.as_str()) {
            return Ok(name.to_lowercase().replace(' ', "_"));
        }

        // 生成默认ID
        Ok(format!(
            "rule_{}",
            uuid::Uuid::new_v4().to_string()[..8].to_string()
        ))
    }

    /// 从JDM中提取规则名称
    fn extract_jdm_rule_name(jdm: &serde_json::Value) -> Result<String> {
        if let Some(name) = jdm.get("name").and_then(|v| v.as_str()) {
            return Ok(name.to_string());
        }

        if let Some(id) = jdm.get("id").and_then(|v| v.as_str()) {
            return Ok(id.to_string());
        }

        Ok("Unnamed Rule".to_string())
    }

    /// 转换原始配置为结构化配置
    fn convert_raw_config(raw: RawConfig) -> Result<ParsedConfig> {
        let mut rules = Vec::new();
        let mut types = Vec::new();

        // 转换规则定义
        if let Some(raw_rules) = raw.rules {
            for raw_rule in raw_rules {
                let rule = Self::convert_raw_rule(raw_rule)?;
                rules.push(rule);
            }
        }

        // 转换类型定义
        if let Some(raw_types) = raw.types {
            for raw_type in raw_types {
                let type_def = Self::convert_raw_type(raw_type)?;
                types.push(type_def);
            }
        }

        Ok(ParsedConfig { rules, types })
    }

    /// 转换原始规则定义
    fn convert_raw_rule(raw: RawRuleDefinition) -> Result<RuleDefinition> {
        let now = chrono::Utc::now();

        Ok(RuleDefinition {
            id: raw.id,
            name: raw.name,
            version: raw.version.unwrap_or_else(|| "1.0.0".to_string()),
            description: raw.description,
            jdm_content: raw.jdm_content,
            input_schema: raw.input_schema,
            output_schema: raw.output_schema,
            tags: raw.tags.unwrap_or_default(),
            created_at: now,
            updated_at: now,
            enabled: raw.enabled.unwrap_or(true),
        })
    }

    /// 转换原始类型定义
    fn convert_raw_type(raw: RawTypeDefinition) -> Result<TypeDefinition> {
        let now = chrono::Utc::now();

        let mut fields = HashMap::new();
        if let Some(raw_fields) = raw.fields {
            for (field_name, raw_field) in raw_fields {
                let field_def = Self::convert_raw_field(raw_field)?;
                fields.insert(field_name, field_def);
            }
        }

        Ok(TypeDefinition {
            id: raw.id,
            name: raw.name,
            description: raw.description,
            fields,
            validation_rules: raw.validation_rules.unwrap_or_default(),
            tags: raw.tags.unwrap_or_default(),
            created_at: now,
            updated_at: now,
        })
    }

    /// 转换原始字段定义
    fn convert_raw_field(raw: RawFieldDefinition) -> Result<FieldDefinition> {
        let field_type = Self::convert_field_type(&raw.field_type)?;

        Ok(FieldDefinition {
            field_type,
            required: raw.required.unwrap_or(false),
            default_value: raw.default_value,
            description: raw.description,
            validation_rules: raw.validation_rules.unwrap_or_default(),
        })
    }

    /// 转换字段类型
    fn convert_field_type(type_str: &str) -> Result<FieldType> {
        match type_str.to_lowercase().as_str() {
            "string" => Ok(FieldType::String),
            "integer" | "int" => Ok(FieldType::Integer),
            "float" | "number" => Ok(FieldType::Float),
            "boolean" | "bool" => Ok(FieldType::Boolean),
            "object" => Ok(FieldType::Object),
            _ => {
                // 处理数组类型：array[string], array[integer]等
                if type_str.starts_with("array[") && type_str.ends_with(']') {
                    let element_type_str = &type_str[6..type_str.len() - 1];
                    let element_type = Self::convert_field_type(element_type_str)?;
                    return Ok(FieldType::Array(Box::new(element_type)));
                }

                // 处理枚举类型：enum[A,B,C]
                if type_str.starts_with("enum[") && type_str.ends_with(']') {
                    let values_str = &type_str[5..type_str.len() - 1];
                    let values: Vec<String> = values_str
                        .split(',')
                        .map(|s| s.trim().to_string())
                        .collect();
                    return Ok(FieldType::Enum(values));
                }

                // 处理引用类型：ref[TypeName]
                if type_str.starts_with("ref[") && type_str.ends_with(']') {
                    let ref_type = &type_str[4..type_str.len() - 1];
                    return Ok(FieldType::Reference(ref_type.to_string()));
                }

                // 默认当作引用类型
                Ok(FieldType::Reference(type_str.to_string()))
            }
        }
    }

    /// 验证配置完整性
    pub fn validate_config(config: &ParsedConfig) -> Result<ValidationReport> {
        let mut report = ValidationReport::new();

        // 验证规则定义
        for rule in &config.rules {
            Self::validate_rule_definition(rule, &mut report);
        }

        // 验证类型定义
        for type_def in &config.types {
            Self::validate_type_definition(type_def, &mut report);
        }

        // 验证引用完整性
        Self::validate_references(config, &mut report);

        Ok(report)
    }

    /// 验证规则定义
    fn validate_rule_definition(rule: &RuleDefinition, report: &mut ValidationReport) {
        // 验证ID不为空
        if rule.id.is_empty() {
            report.add_error(format!("规则ID不能为空"));
        }

        // 验证名称不为空
        if rule.name.is_empty() {
            report.add_error(format!("规则名称不能为空: {}", rule.id));
        }

        // 验证JDM内容
        if rule.jdm_content.is_empty() {
            report.add_error(format!("规则JDM内容不能为空: {}", rule.id));
        } else {
            // 尝试解析JDM内容
            match serde_json::from_str::<serde_json::Value>(&rule.jdm_content) {
                Ok(jdm) => {
                    if !Self::is_valid_jdm(&jdm) {
                        report.add_warning(format!("规则 {} 的JDM格式可能不正确", rule.id));
                    }
                }
                Err(e) => {
                    report.add_error(format!("规则 {} 的JDM内容解析失败: {}", rule.id, e));
                }
            }
        }

        // 验证版本格式
        if !Self::is_valid_version(&rule.version) {
            report.add_warning(format!(
                "规则 {} 的版本格式不规范: {}",
                rule.id, rule.version
            ));
        }
    }

    /// 验证类型定义
    fn validate_type_definition(type_def: &TypeDefinition, report: &mut ValidationReport) {
        // 验证ID不为空
        if type_def.id.is_empty() {
            report.add_error(format!("类型ID不能为空"));
        }

        // 验证名称不为空
        if type_def.name.is_empty() {
            report.add_error(format!("类型名称不能为空: {}", type_def.id));
        }

        // 验证字段定义
        for (field_name, field_def) in &type_def.fields {
            Self::validate_field_definition(&type_def.id, field_name, field_def, report);
        }
    }

    /// 验证字段定义
    fn validate_field_definition(
        type_id: &str,
        field_name: &str,
        field_def: &FieldDefinition,
        report: &mut ValidationReport,
    ) {
        // 验证字段名不为空
        if field_name.is_empty() {
            report.add_error(format!("类型 {} 的字段名不能为空", type_id));
        }

        // 验证默认值类型匹配
        if let Some(default_value) = &field_def.default_value {
            if !Self::is_value_compatible_with_type(default_value, &field_def.field_type) {
                report.add_warning(format!(
                    "类型 {} 的字段 {} 的默认值类型不匹配",
                    type_id, field_name
                ));
            }
        }
    }

    /// 验证引用完整性
    fn validate_references(config: &ParsedConfig, report: &mut ValidationReport) {
        let type_ids: std::collections::HashSet<String> =
            config.types.iter().map(|t| t.id.clone()).collect();

        // 检查字段类型引用
        for type_def in &config.types {
            for (field_name, field_def) in &type_def.fields {
                Self::check_field_type_references(
                    &type_def.id,
                    field_name,
                    &field_def.field_type,
                    &type_ids,
                    report,
                );
            }
        }
    }

    /// 检查字段类型引用
    fn check_field_type_references(
        type_id: &str,
        field_name: &str,
        field_type: &FieldType,
        available_types: &std::collections::HashSet<String>,
        report: &mut ValidationReport,
    ) {
        match field_type {
            FieldType::Array(element_type) => {
                Self::check_field_type_references(
                    type_id,
                    field_name,
                    element_type,
                    available_types,
                    report,
                );
            }
            FieldType::Reference(ref_type) => {
                if !available_types.contains(ref_type) {
                    report.add_error(format!(
                        "类型 {} 的字段 {} 引用了不存在的类型: {}",
                        type_id, field_name, ref_type
                    ));
                }
            }
            _ => {} // 其他类型不需要检查引用
        }
    }

    /// 验证版本格式（简单的语义版本检查）
    fn is_valid_version(version: &str) -> bool {
        let parts: Vec<&str> = version.split('.').collect();
        if parts.len() != 3 {
            return false;
        }

        parts.iter().all(|part| part.parse::<u32>().is_ok())
    }

    /// 检查值是否与类型兼容
    fn is_value_compatible_with_type(value: &ContextValue, field_type: &FieldType) -> bool {
        match (value, field_type) {
            (ContextValue::String(_), FieldType::String) => true,
            (ContextValue::Int(_), FieldType::Integer) => true,
            (ContextValue::Float(_), FieldType::Float) => true,
            (ContextValue::Bool(_), FieldType::Boolean) => true,
            (ContextValue::Array(_), FieldType::Array(_)) => true,
            (ContextValue::Object(_), FieldType::Object) => true,
            (ContextValue::String(s), FieldType::Enum(values)) => values.contains(s),
            _ => false,
        }
    }
}

/// 解析后的配置结构
#[derive(Debug, Clone)]
pub struct ParsedConfig {
    pub rules: Vec<RuleDefinition>,
    pub types: Vec<TypeDefinition>,
}

impl ParsedConfig {
    pub fn new() -> Self {
        Self {
            rules: Vec::new(),
            types: Vec::new(),
        }
    }

    pub fn merge(mut self, other: ParsedConfig) -> Self {
        self.rules.extend(other.rules);
        self.types.extend(other.types);
        self
    }
}

impl Default for ParsedConfig {
    fn default() -> Self {
        Self::new()
    }
}

/// 验证报告
#[derive(Debug, Clone)]
pub struct ValidationReport {
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

impl ValidationReport {
    pub fn new() -> Self {
        Self {
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }

    pub fn add_error(&mut self, error: String) {
        self.errors.push(error);
    }

    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }

    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }

    pub fn has_warnings(&self) -> bool {
        !self.warnings.is_empty()
    }

    pub fn is_valid(&self) -> bool {
        !self.has_errors()
    }
}

impl Default for ValidationReport {
    fn default() -> Self {
        Self::new()
    }
}

// 原始配置结构（用于反序列化）
#[derive(Debug, Deserialize)]
struct RawConfig {
    rules: Option<Vec<RawRuleDefinition>>,
    types: Option<Vec<RawTypeDefinition>>,
}

#[derive(Debug, Deserialize)]
struct RawRuleDefinition {
    id: String,
    name: String,
    version: Option<String>,
    description: Option<String>,
    jdm_content: String,
    input_schema: Option<serde_json::Value>,
    output_schema: Option<serde_json::Value>,
    tags: Option<HashMap<String, String>>,
    enabled: Option<bool>,
}

#[derive(Debug, Deserialize)]
struct RawTypeDefinition {
    id: String,
    name: String,
    description: Option<String>,
    fields: Option<HashMap<String, RawFieldDefinition>>,
    validation_rules: Option<Vec<String>>,
    tags: Option<HashMap<String, String>>,
}

#[derive(Debug, Deserialize)]
struct RawFieldDefinition {
    #[serde(rename = "type")]
    field_type: String,
    required: Option<bool>,
    default_value: Option<ContextValue>,
    description: Option<String>,
    validation_rules: Option<Vec<String>>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_json_config() {
        let json_content = r#"{
            "rules": [
                {
                    "id": "test_rule",
                    "name": "Test Rule",
                    "version": "1.0.0",
                    "jdm_content": "{\"contentType\": \"application/vnd.gorules.decision\"}",
                    "enabled": true
                }
            ],
            "types": [
                {
                    "id": "Material",
                    "name": "Material Type",
                    "fields": {
                        "id": {
                            "type": "string",
                            "required": true
                        },
                        "rarity": {
                            "type": "enum[Common,Rare,Epic]",
                            "required": true
                        }
                    }
                }
            ]
        }"#;

        let parsed = ConfigLoader::parse_content(json_content, ConfigFormat::Json).unwrap();

        assert_eq!(parsed.rules.len(), 1);
        assert_eq!(parsed.types.len(), 1);

        let rule = &parsed.rules[0];
        assert_eq!(rule.id, "test_rule");
        assert_eq!(rule.name, "Test Rule");
        assert!(rule.enabled);

        let type_def = &parsed.types[0];
        assert_eq!(type_def.id, "Material");
        assert_eq!(type_def.fields.len(), 2);
    }

    #[test]
    fn test_parse_jdm_content() {
        let jdm_content = r#"{
            "contentType": "application/vnd.gorules.decision",
            "name": "Material Discovery",
            "nodes": [],
            "edges": []
        }"#;

        let parsed = ConfigLoader::parse_content(jdm_content, ConfigFormat::Jdm).unwrap();

        assert_eq!(parsed.rules.len(), 1);
        assert_eq!(parsed.types.len(), 0);

        let rule = &parsed.rules[0];
        assert_eq!(rule.name, "Material Discovery");
        assert_eq!(rule.jdm_content, jdm_content);
    }

    #[test]
    fn test_field_type_conversion() {
        assert_eq!(
            ConfigLoader::convert_field_type("string").unwrap(),
            FieldType::String
        );
        assert_eq!(
            ConfigLoader::convert_field_type("array[integer]").unwrap(),
            FieldType::Array(Box::new(FieldType::Integer))
        );
        assert_eq!(
            ConfigLoader::convert_field_type("enum[A,B,C]").unwrap(),
            FieldType::Enum(vec!["A".to_string(), "B".to_string(), "C".to_string()])
        );
        assert_eq!(
            ConfigLoader::convert_field_type("ref[CustomType]").unwrap(),
            FieldType::Reference("CustomType".to_string())
        );
    }

    #[test]
    fn test_config_validation() {
        let rule = RuleDefinition::new(
            "test".to_string(),
            "Test Rule".to_string(),
            r#"{"contentType": "application/vnd.gorules.decision"}"#.to_string(),
        );

        let config = ParsedConfig {
            rules: vec![rule],
            types: vec![],
        };

        let report = ConfigLoader::validate_config(&config).unwrap();
        assert!(report.is_valid());
    }

    #[test]
    fn test_invalid_config_validation() {
        let invalid_rule = RuleDefinition::new(
            "".to_string(),             // 空ID
            "".to_string(),             // 空名称
            "invalid json".to_string(), // 无效JDM
        );

        let config = ParsedConfig {
            rules: vec![invalid_rule],
            types: vec![],
        };

        let report = ConfigLoader::validate_config(&config).unwrap();
        assert!(!report.is_valid());
        assert!(report.errors.len() >= 2); // 至少有ID和名称错误
    }
}
