//! 掉落系统：支持多类型、特殊掉落、动态规则、可扩展
use crate::equipment::Equipment;
use crate::attribute::attribute::AttributeType;
use crate::monster::MonsterKind;
use crate::battle_system::battle_errors::{BattleError, BattleResult};
use rand::Rng;


/// 材料结构体，支持属性、品阶、描述等
/// 游戏材料结构体，代表可掉落的材料类物品。
/// 可用于炼器、炼丹、任务、合成等多种用途。
/// 可扩展字段以支持稀有度、绑定、唯一性等。
#[derive(Debug, Clone)]
pub struct Material {
    pub id: u32,
    pub name: String,
    pub grade: String,
    pub attribute: AttributeType,
    pub description: String,
}

/// 特殊掉落物类型，支持称号、宠物蛋、时装、世界事件等
/// 特殊掉落物类型，涵盖称号、宠物蛋、时装、世界事件道具等非通用物品。
/// 用于丰富玩法和奖励体系，便于后续扩展新类型。
#[derive(Debug, Clone)]
pub enum SpecialLoot {
    Achievement(String),
    Title(String),
    PetEgg(String),
    QuestItem(String),
    Skin(String),
    TreasureChest(String),
    Key(String),
    WorldEventItem(String),
    SkillBook(String),
    Token(String),
    SoulFragment(String),
    BoundItem(String),
    TimeLimitedItem { name: String, expire_at: u64 },
    StoryItem(String),
    // ...可扩展
}

/// 掉落物类型，支持装备、材料、金币、特殊物品等
/// 掉落物类型，统一管理所有可掉落物品。
/// 便于后续扩展和统一掉落逻辑。
#[derive(Debug, Clone)]
pub enum LootItem {
    Equipment(Equipment),
    Material(Material),
    Gold(u32),
    Special(SpecialLoot),
}

/// 掉落规则，支持概率、权重、首杀、玩家等级、怪物类型、自定义等
/// 掉落规则，决定某条目在何种条件下可掉落。
/// 支持概率、权重、首杀、玩家等级、怪物类型、自定义等多种判定方式。
pub enum LootRule {
    Always,
    Probability(f32),
    Weight(u32),
    PlayerLevelAbove(u32),
    MonsterKindOnly(MonsterKind),
    FirstKillOnly,
    #[allow(clippy::type_complexity)]
    Custom(Box<dyn Fn(&DropContext) -> bool + Send + Sync>),
}

/// 安全的克隆方法，返回Result类型
impl LootRule {
    /// 尝试克隆LootRule，对于Custom规则返回错误
    pub fn try_clone(&self) -> BattleResult<Self> {
        match self {
            LootRule::Always => Ok(LootRule::Always),
            LootRule::Probability(p) => Ok(LootRule::Probability(*p)),
            LootRule::Weight(w) => Ok(LootRule::Weight(*w)),
            LootRule::PlayerLevelAbove(lv) => Ok(LootRule::PlayerLevelAbove(*lv)),
            LootRule::MonsterKindOnly(kind) => Ok(LootRule::MonsterKindOnly(kind.clone())),
            LootRule::FirstKillOnly => Ok(LootRule::FirstKillOnly),
            LootRule::Custom(_) => Err(BattleError::LootRuleCloneError),
        }
    }
}

/// 为了向后兼容，保留Clone trait，但会记录错误日志
impl Clone for LootRule {
    fn clone(&self) -> Self {
        match self.try_clone() {
            Ok(cloned) => cloned,
            Err(err) => {
                eprintln!("警告: 无法克隆自定义LootRule: {}", err);
                // 返回一个默认的规则作为备选
                LootRule::Always
            }
        }
    }
}

impl std::fmt::Debug for LootRule {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            LootRule::Always => write!(f, "Always"),
            LootRule::Probability(p) => write!(f, "Probability({})", p),
            LootRule::Weight(w) => write!(f, "Weight({})", w),
            LootRule::PlayerLevelAbove(lv) => write!(f, "PlayerLevelAbove({})", lv),
            LootRule::MonsterKindOnly(kind) => write!(f, "MonsterKindOnly({:?})", kind),
            LootRule::FirstKillOnly => write!(f, "FirstKillOnly"),
            LootRule::Custom(_) => write!(f, "Custom(<closure>)"),
        }
    }
}

/// 掉落条目，包含掉落物、规则、数量范围
/// 掉落条目，描述一种掉落物及其规则和数量范围。
/// 可配置多条规则叠加，支持灵活的掉落判定。
#[derive(Debug, Clone)]
pub struct LootEntry {
    pub item: LootItem,
    pub rules: Vec<LootRule>,
    pub min_count: u32,
    pub max_count: u32,
}

/// 掉落池类型，支持必掉、普通、稀有、世界池等
/// 掉落池类型，支持多层次掉落池设计。
/// 便于实现必掉、普通、稀有、世界级等多样化掉落体系。
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum LootPoolKind {
    Guaranteed,
    Normal,
    Rare,
    World,
}

/// 掉落池，包含一组掉落条目及池类型
/// 掉落池，包含一组掉落条目及池类型。
/// 每个怪物可配置多个掉落池，实现分层掉落。
#[derive(Debug, Clone)]
pub struct LootPool {
    pub kind: LootPoolKind,
    pub entries: Vec<LootEntry>,
}

/// 掉落上下文，支持动态权重/规则判定
/// 掉落上下文，包含判定掉落时所需的所有动态信息。
/// 支持动态权重、条件判定、自定义规则等高级玩法。
pub struct DropContext<'a> {
    pub player_level: u32,
    pub is_first_kill: bool,
    pub monster_kind: MonsterKind,
    pub monster_level: u32,
    pub rng: &'a mut rand::rngs::ThreadRng,
}

impl LootEntry {
    /// 判定该条目在当前上下文下是否掉落。
    /// 依次检查所有规则，任一规则不满足则不掉落。
    /// 权重规则由掉落池统一处理。
    pub fn should_drop(&self, ctx: &mut DropContext) -> bool {
        for rule in &self.rules {
            match rule {
                LootRule::Always => continue,
                LootRule::Probability(p) => if ctx.rng.gen::<f32>() >= *p { return false; },
                LootRule::Weight(_) => continue,
                LootRule::PlayerLevelAbove(lv) => if ctx.player_level <= *lv { return false; },
                LootRule::MonsterKindOnly(kind) => if ctx.monster_kind != *kind { return false; },
                LootRule::FirstKillOnly => if !ctx.is_first_kill { return false; },
                LootRule::Custom(f) => if !(f)(ctx) { return false; },
            }
        }
        true
    }
}
