//! 命名系统接口及占位实现
use crate::attribute::attribute::AttributeType;

pub trait NamingService {
    /// 生成材料名称
    fn material_name(&self, monster_name: &str, attr: &AttributeType, grade: &str) -> String;
    /// 生成材料描述
    fn material_desc(&self, monster_name: &str, attr: &AttributeType, grade: &str) -> String;
    /// 生成装备名称
    fn equipment_name(&self, monster_name: &str, eq_type: &str) -> String;
    /// 生成装备描述
    fn equipment_desc(&self, monster_name: &str, eq_type: &str) -> String;
}

/// 临时占位实现，后续可由命名规范模块替换
pub struct DummyNamingService;
impl NamingService for DummyNamingService {
    fn material_name(&self, monster_name: &str, attr: &AttributeType, grade: &str) -> String {
        format!("{monster_name}之精({:?}{})", attr, grade)
    }
    fn material_desc(&self, monster_name: &str, attr: &AttributeType, grade: &str) -> String {
        format!("由{monster_name}体内凝聚的{:?}属性{grade}精华", attr)
    }
    fn equipment_name(&self, monster_name: &str, eq_type: &str) -> String {
        format!("{monster_name}之{eq_type}")
    }
    fn equipment_desc(&self, monster_name: &str, eq_type: &str) -> String {
        format!("由{monster_name}掉落的{eq_type}")
    }
}
