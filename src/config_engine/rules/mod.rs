//! # 规则引擎模块
//!
//! 提供基于zen-engine的JDM规则执行系统，支持复杂的业务规则配置和执行

pub mod context;
pub mod dependency;
pub mod engine;
pub mod executor;

// 重新导出主要类型
pub use engine::{RuleEngine, RuleEngineConfig, RuleEngineHealth, RuleEngineStats, RuleExecutor};

pub use executor::{
    AdvancedJdmExecutor, ExecutionMetadata, ExecutionResult, ExecutorStats, JdmExecutorConfig,
    PerformanceMetrics,
};

pub use context::{
    ConditionEvaluation, ContextSnapshot, ContextValue, DebugInfo, ExecutionContext,
    ExecutionOptions, ExecutionStatus, ExecutionStep, ExecutionSummary, ExecutionTrace,
    OperationStats, PerformanceMarker, RuleExecutionRequest, RuleExecutionResponse, ScopeOperation,
    VariableOperation,
};

pub use dependency::{
    DependencyComplexityAnalysis, DependencyIssue, DependencyIssueType, DependencyNode,
    DependencyResolver, DependencyResolverConfig, DependencyStatistics, DependencyType,
    DependencyValidationReport, IssueSeverity, RuleDependency,
};

use crate::config_engine::{error::ConfigEngineError, Result};

/// 规则引擎构建器
///
/// 提供便捷的规则引擎创建和配置接口
#[derive(Debug)]
pub struct RuleEngineBuilder {
    /// 引擎配置
    config: RuleEngineConfig,
    /// 自定义执行器
    custom_executors: Vec<(String, Box<dyn RuleExecutor>)>,
}

impl RuleEngineBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: RuleEngineConfig::default(),
            custom_executors: Vec::new(),
        }
    }

    /// 设置默认超时时间
    pub fn with_default_timeout(mut self, timeout_ms: u64) -> Self {
        self.config.default_timeout_ms = timeout_ms;
        self
    }

    /// 设置最大并发执行数
    pub fn with_max_concurrent_executions(mut self, max_concurrent: usize) -> Self {
        self.config.max_concurrent_executions = max_concurrent;
        self
    }

    /// 启用执行追踪
    pub fn with_tracing_enabled(mut self, enabled: bool) -> Self {
        self.config.enable_tracing = enabled;
        self
    }

    /// 设置预热规则列表
    pub fn with_warmup_rules(mut self, rules: Vec<String>) -> Self {
        self.config.warmup_rules = rules;
        self
    }

    /// 注册自定义执行器
    pub fn with_custom_executor<E>(mut self, name: String, executor: E) -> Self
    where
        E: RuleExecutor + 'static,
    {
        self.custom_executors.push((name, Box::new(executor)));
        self
    }

    /// 构建规则引擎
    pub async fn build(
        self,
        config_manager: std::sync::Arc<crate::config_engine::config::ConfigManager>,
        cache_manager: std::sync::Arc<crate::config_engine::cache::CacheManager>,
    ) -> Result<RuleEngine> {
        let mut engine = RuleEngine::new(config_manager, cache_manager, self.config).await?;

        // 注册自定义执行器
        for (name, executor) in self.custom_executors {
            engine.register_executor(name, *executor).await?;
        }

        Ok(engine)
    }
}

impl Default for RuleEngineBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 规则执行工厂
///
/// 提供便捷的规则执行接口
pub struct RuleExecutionFactory {
    engine: std::sync::Arc<RuleEngine>,
}

impl RuleExecutionFactory {
    /// 创建新的执行工厂
    pub fn new(engine: std::sync::Arc<RuleEngine>) -> Self {
        Self { engine }
    }

    /// 快速执行规则
    ///
    /// # 参数
    /// - `rule_id`: 规则ID
    /// - `input_data`: 输入数据（键值对）
    ///
    /// # 示例
    ///
    /// ```rust
    /// use std::collections::HashMap;
    /// use game::config_engine::rules::{RuleExecutionFactory, ContextValue};
    ///
    /// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let factory = RuleExecutionFactory::new(engine);
    ///
    /// let mut input = HashMap::new();
    /// input.insert("terrain_type".to_string(), ContextValue::String("forest".to_string()));
    /// input.insert("tool_type".to_string(), ContextValue::String("herb_spade".to_string()));
    ///
    /// let result = factory.execute_quick("material_discovery", input).await?;
    /// # Ok(())
    /// # }
    /// ```
    pub async fn execute_quick(
        &self,
        rule_id: &str,
        input_data: std::collections::HashMap<String, ContextValue>,
    ) -> Result<ExecutionResult> {
        let mut context = ExecutionContext::new();
        for (key, value) in input_data {
            context.set(key, value);
        }

        self.engine.execute_rule(rule_id, &context).await
    }

    /// 执行规则（带选项）
    pub async fn execute_with_options(
        &self,
        rule_id: &str,
        input_data: std::collections::HashMap<String, ContextValue>,
        options: ExecutionOptions,
    ) -> Result<ExecutionResult> {
        let mut context = ExecutionContext::new();
        context.set_options(options);

        for (key, value) in input_data {
            context.set(key, value);
        }

        self.engine.execute_rule(rule_id, &context).await
    }

    /// 批量执行规则
    pub async fn execute_batch(
        &self,
        requests: Vec<RuleExecutionRequest>,
    ) -> Vec<Result<RuleExecutionResponse>> {
        let mut results = Vec::new();

        for request in requests {
            let result = self
                .engine
                .execute_rule_request(&request)
                .await
                .map_err(|e| e);
            results.push(result);
        }

        results
    }

    /// 并行执行规则
    pub async fn execute_parallel(
        &self,
        requests: Vec<RuleExecutionRequest>,
    ) -> Vec<Result<RuleExecutionResponse>> {
        use tokio::task::JoinSet;

        let mut join_set = JoinSet::new();

        for request in requests {
            let engine = self.engine.clone();
            join_set.spawn(async move { engine.execute_rule_request(&request).await });
        }

        let mut results = Vec::new();
        while let Some(result) = join_set.join_next().await {
            match result {
                Ok(execution_result) => results.push(execution_result),
                Err(join_error) => {
                    results.push(Err(ConfigEngineError::Internal {
                        message: format!("任务执行失败: {}", join_error),
                    }));
                }
            }
        }

        results
    }

    /// 获取引擎统计信息
    pub async fn get_engine_stats(&self) -> RuleEngineStats {
        self.engine.get_stats().await
    }

    /// 获取引擎健康状态
    pub async fn get_engine_health(&self) -> RuleEngineHealth {
        self.engine.health_check().await
    }

    /// 预热规则
    pub async fn warmup_rule(&self, rule_id: &str) -> Result<()> {
        self.engine.warmup_rule(rule_id).await
    }

    /// 批量预热规则
    pub async fn warmup_rules(&self, rule_ids: Vec<String>) -> Result<()> {
        self.engine.warmup_rules(&rule_ids).await
    }
}

/// 规则执行助手
///
/// 提供常用的规则执行辅助功能
pub struct RuleExecutionHelper;

impl RuleExecutionHelper {
    /// 创建材料发现上下文
    pub fn create_material_discovery_context(
        terrain_type: &str,
        tool_type: &str,
        player_level: i64,
        location_modifier: f64,
    ) -> ExecutionContext {
        let mut context = ExecutionContext::new();
        context.set(
            "terrain_type".to_string(),
            ContextValue::String(terrain_type.to_string()),
        );
        context.set(
            "tool_type".to_string(),
            ContextValue::String(tool_type.to_string()),
        );
        context.set("player_level".to_string(), ContextValue::Int(player_level));
        context.set(
            "location_modifier".to_string(),
            ContextValue::Float(location_modifier),
        );
        context
    }

    /// 创建工具需求上下文
    pub fn create_tool_requirements_context(
        resource_type: &str,
        rarity_level: &str,
        environment: &str,
    ) -> ExecutionContext {
        let mut context = ExecutionContext::new();
        context.set(
            "resource_type".to_string(),
            ContextValue::String(resource_type.to_string()),
        );
        context.set(
            "rarity_level".to_string(),
            ContextValue::String(rarity_level.to_string()),
        );
        context.set(
            "environment".to_string(),
            ContextValue::String(environment.to_string()),
        );
        context
    }

    /// 创建稀有度计算上下文
    pub fn create_rarity_calculation_context(
        base_rarity: &str,
        location_modifier: f64,
        tool_modifier: f64,
        player_luck: f64,
    ) -> ExecutionContext {
        let mut context = ExecutionContext::new();
        context.set(
            "base_rarity".to_string(),
            ContextValue::String(base_rarity.to_string()),
        );
        context.set(
            "location_modifier".to_string(),
            ContextValue::Float(location_modifier),
        );
        context.set(
            "tool_modifier".to_string(),
            ContextValue::Float(tool_modifier),
        );
        context.set("player_luck".to_string(), ContextValue::Float(player_luck));
        context
    }

    /// 解析执行结果为材料列表
    pub fn extract_materials_from_result(result: &ExecutionResult) -> Vec<String> {
        if let Some(ContextValue::Array(materials)) = result.get_output("discovered_materials") {
            materials
                .iter()
                .filter_map(|v| match v {
                    ContextValue::String(s) => Some(s.clone()),
                    _ => None,
                })
                .collect()
        } else {
            Vec::new()
        }
    }

    /// 解析执行结果为工具列表
    pub fn extract_tools_from_result(result: &ExecutionResult) -> Vec<String> {
        if let Some(ContextValue::Array(tools)) = result.get_output("required_tools") {
            tools
                .iter()
                .filter_map(|v| match v {
                    ContextValue::String(s) => Some(s.clone()),
                    _ => None,
                })
                .collect()
        } else {
            Vec::new()
        }
    }

    /// 解析执行结果为成功率
    pub fn extract_success_rate_from_result(result: &ExecutionResult) -> Option<f64> {
        result.get_output("success_rate")?.as_float()
    }

    /// 检查执行结果是否成功
    pub fn is_discovery_successful(result: &ExecutionResult) -> bool {
        result
            .get_output("discovery_success")
            .map(|v| v.as_bool())
            .unwrap_or(false)
    }

    /// 创建默认执行选项
    pub fn create_default_options() -> ExecutionOptions {
        ExecutionOptions {
            cache_enabled: true,
            trace_execution: false,
            timeout_ms: Some(5000),
            max_recursion_depth: Some(100),
            debug_mode: false,
            profile_execution: false,
            validate_input: true,
            validate_output: true,
        }
    }

    /// 创建调试执行选项
    pub fn create_debug_options() -> ExecutionOptions {
        ExecutionOptions {
            cache_enabled: false,
            trace_execution: true,
            timeout_ms: Some(10000),
            max_recursion_depth: Some(100),
            debug_mode: true,
            profile_execution: true,
            validate_input: true,
            validate_output: true,
        }
    }

    /// 创建性能优化执行选项
    pub fn create_performance_options() -> ExecutionOptions {
        ExecutionOptions {
            cache_enabled: true,
            trace_execution: false,
            timeout_ms: Some(1000),
            max_recursion_depth: Some(50),
            debug_mode: false,
            profile_execution: false,
            validate_input: false,
            validate_output: false,
        }
    }
}

/// 规则验证器
///
/// 提供规则定义和执行结果的验证功能
pub struct RuleValidator;

impl RuleValidator {
    /// 验证规则执行上下文
    pub fn validate_execution_context(context: &ExecutionContext) -> Result<()> {
        // 检查基本完整性
        context
            .validate()
            .map_err(|e| ConfigEngineError::Internal { message: e })?;

        // 检查必需的系统变量
        let required_system_vars = ["rule_id", "execution_id"];
        for var in &required_system_vars {
            if !context.contains_key(var) {
                log::warn!("缺少系统变量: {}", var);
            }
        }

        Ok(())
    }

    /// 验证执行结果
    pub fn validate_execution_result(result: &ExecutionResult) -> Result<()> {
        if !result.is_success() {
            return Err(ConfigEngineError::Internal {
                message: format!("执行结果表示失败: {:?}", result.error),
            });
        }

        // 检查执行时间合理性
        if result.execution_time_ms > 30000 {
            // 30秒
            log::warn!("执行时间过长: {}ms", result.execution_time_ms);
        }

        // 检查输出数据
        if result.output.is_empty() {
            log::warn!("执行结果没有输出数据");
        }

        Ok(())
    }

    /// 验证规则执行请求
    pub fn validate_execution_request(request: &RuleExecutionRequest) -> Result<()> {
        if request.rule_id.is_empty() {
            return Err(ConfigEngineError::Internal {
                message: "规则ID不能为空".to_string(),
            });
        }

        // 检查超时设置
        if let Some(timeout_ms) = request.options.timeout_ms {
            if timeout_ms > 60000 {
                // 60秒
                log::warn!("超时时间设置过长: {}ms", timeout_ms);
            }
            if timeout_ms < 100 {
                // 100毫秒
                log::warn!("超时时间设置过短: {}ms", timeout_ms);
            }
        }

        // 检查递归深度
        if let Some(max_depth) = request.options.max_recursion_depth {
            if max_depth > 1000 {
                log::warn!("最大递归深度设置过大: {}", max_depth);
            }
        }

        Ok(())
    }
}

/// 规则性能分析器
///
/// 提供规则执行性能分析和优化建议
pub struct RulePerformanceAnalyzer;

impl RulePerformanceAnalyzer {
    /// 分析执行结果性能
    pub fn analyze_execution_performance(result: &ExecutionResult) -> PerformanceAnalysis {
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        // 分析执行时间
        if result.execution_time_ms > 1000 {
            issues.push("执行时间过长".to_string());
            recommendations.push("考虑启用缓存或优化规则逻辑".to_string());
        }

        // 分析性能指标
        if let Some(metrics) = result.metadata.performance_metrics.memory_usage_bytes {
            if metrics > 10 * 1024 * 1024 {
                // 10MB
                issues.push("内存使用量过高".to_string());
                recommendations.push("优化数据结构或减少临时对象".to_string());
            }
        }

        // 分析节点访问
        if result.metadata.performance_metrics.nodes_visited > 100 {
            issues.push("节点访问次数过多".to_string());
            recommendations.push("简化规则逻辑或使用更高效的决策结构".to_string());
        }

        // 分析条件评估
        if result.metadata.performance_metrics.conditions_evaluated > 50 {
            issues.push("条件评估次数过多".to_string());
            recommendations.push("优化条件表达式或减少分支复杂度".to_string());
        }

        PerformanceAnalysis {
            execution_time_ms: result.execution_time_ms,
            performance_score: Self::calculate_performance_score(result),
            issues,
            recommendations,
            metrics_summary: Self::create_metrics_summary(&result.metadata.performance_metrics),
        }
    }

    /// 计算性能分数（0-100，100为最佳）
    fn calculate_performance_score(result: &ExecutionResult) -> u32 {
        let mut score = 100u32;

        // 执行时间影响
        if result.execution_time_ms > 1000 {
            score = score.saturating_sub(30);
        } else if result.execution_time_ms > 500 {
            score = score.saturating_sub(15);
        } else if result.execution_time_ms > 100 {
            score = score.saturating_sub(5);
        }

        // 节点访问影响
        if result.metadata.performance_metrics.nodes_visited > 100 {
            score = score.saturating_sub(20);
        } else if result.metadata.performance_metrics.nodes_visited > 50 {
            score = score.saturating_sub(10);
        }

        // 条件评估影响
        if result.metadata.performance_metrics.conditions_evaluated > 50 {
            score = score.saturating_sub(15);
        } else if result.metadata.performance_metrics.conditions_evaluated > 20 {
            score = score.saturating_sub(5);
        }

        score
    }

    /// 创建性能指标摘要
    fn create_metrics_summary(metrics: &PerformanceMetrics) -> MetricsSummary {
        MetricsSummary {
            total_time_ms: metrics.total_time_ms(),
            parse_time_percent: (metrics.parse_time_us as f64 / metrics.total_time_us() as f64)
                * 100.0,
            validation_time_percent: (metrics.validation_time_us as f64
                / metrics.total_time_us() as f64)
                * 100.0,
            execution_time_percent: (metrics.execution_time_us as f64
                / metrics.total_time_us() as f64)
                * 100.0,
            serialization_time_percent: (metrics.serialization_time_us as f64
                / metrics.total_time_us() as f64)
                * 100.0,
        }
    }
}

/// 性能分析结果
#[derive(Debug, Clone)]
pub struct PerformanceAnalysis {
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    /// 性能分数（0-100）
    pub performance_score: u32,
    /// 发现的问题
    pub issues: Vec<String>,
    /// 优化建议
    pub recommendations: Vec<String>,
    /// 性能指标摘要
    pub metrics_summary: MetricsSummary,
}

/// 性能指标摘要
#[derive(Debug, Clone)]
pub struct MetricsSummary {
    /// 总时间（毫秒）
    pub total_time_ms: f64,
    /// 解析时间占比（%）
    pub parse_time_percent: f64,
    /// 验证时间占比（%）
    pub validation_time_percent: f64,
    /// 执行时间占比（%）
    pub execution_time_percent: f64,
    /// 序列化时间占比（%）
    pub serialization_time_percent: f64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_rule_engine_builder() {
        let builder = RuleEngineBuilder::new()
            .with_default_timeout(3000)
            .with_max_concurrent_executions(50)
            .with_tracing_enabled(true)
            .with_warmup_rules(vec!["rule1".to_string(), "rule2".to_string()]);

        assert_eq!(builder.config.default_timeout_ms, 3000);
        assert_eq!(builder.config.max_concurrent_executions, 50);
        assert!(builder.config.enable_tracing);
        assert_eq!(builder.config.warmup_rules.len(), 2);
    }

    #[test]
    fn test_rule_execution_helper_material_discovery() {
        let context =
            RuleExecutionHelper::create_material_discovery_context("forest", "herb_spade", 10, 1.5);

        assert_eq!(context.get("terrain_type"), Some("forest"));
        assert_eq!(context.get("tool_type"), Some("herb_spade"));
        assert_eq!(
            context.get_raw("player_level"),
            Some(&ContextValue::Int(10))
        );
        assert_eq!(
            context.get_raw("location_modifier"),
            Some(&ContextValue::Float(1.5))
        );
    }

    #[test]
    fn test_rule_execution_helper_extract_materials() {
        let mut output = HashMap::new();
        output.insert(
            "discovered_materials".to_string(),
            ContextValue::Array(vec![
                ContextValue::String("herb1".to_string()),
                ContextValue::String("herb2".to_string()),
            ]),
        );

        let result = ExecutionResult::success(output, 100);
        let materials = RuleExecutionHelper::extract_materials_from_result(&result);

        assert_eq!(materials, vec!["herb1", "herb2"]);
    }

    #[test]
    fn test_rule_validator_execution_request() {
        let mut input_data = HashMap::new();
        input_data.insert(
            "test".to_string(),
            ContextValue::String("value".to_string()),
        );

        let request = RuleExecutionRequest::new("valid_rule".to_string(), input_data);
        assert!(RuleValidator::validate_execution_request(&request).is_ok());

        let invalid_request = RuleExecutionRequest::new("".to_string(), HashMap::new());
        assert!(RuleValidator::validate_execution_request(&invalid_request).is_err());
    }

    #[test]
    fn test_performance_analyzer_score_calculation() {
        let mut output = HashMap::new();
        output.insert(
            "test".to_string(),
            ContextValue::String("value".to_string()),
        );

        let result = ExecutionResult::success(output, 50); // 快速执行
        let analysis = RulePerformanceAnalyzer::analyze_execution_performance(&result);

        assert!(analysis.performance_score > 90); // 应该有高分
        assert!(analysis.issues.is_empty()); // 应该没有问题
    }

    #[test]
    fn test_execution_options_presets() {
        let default_options = RuleExecutionHelper::create_default_options();
        assert!(default_options.cache_enabled);
        assert!(!default_options.trace_execution);

        let debug_options = RuleExecutionHelper::create_debug_options();
        assert!(!debug_options.cache_enabled);
        assert!(debug_options.trace_execution);
        assert!(debug_options.debug_mode);

        let performance_options = RuleExecutionHelper::create_performance_options();
        assert!(performance_options.cache_enabled);
        assert!(!performance_options.trace_execution);
        assert_eq!(performance_options.timeout_ms, Some(1000));
    }
}
