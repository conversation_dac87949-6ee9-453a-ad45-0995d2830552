use crate::battle_unit::{Area, SpaceSize};
/// 空间定位trait
/// 
/// 处理位置、方向和空间关系的相关功能

use crate::shared::*;


/// 空间定位 - 处理位置、方向和空间关系
pub trait SpatialEntity {
    /// 获取当前位置
    fn position(&self) -> Position;
    
    /// 设置位置
    fn set_position(&mut self, position: Position) -> GameResult<()>;
    
    /// 获取朝向
    fn facing_direction(&self) -> Direction;
    
    /// 设置朝向
    fn set_facing_direction(&mut self, direction: Direction) -> GameResult<()>;
    
    /// 获取占用的空间大小
    fn occupied_space(&self) -> SpaceSize;
    
    /// 检查是否在指定区域内
    fn is_in_area(&self, area: &Area) -> bool;
    
    /// 计算到目标的距离
    fn distance_to<T: SpatialEntity>(&self, target: &T) -> f32;
    
    /// 检查是否可见目标（考虑遮挡）
    fn can_see<T: SpatialEntity>(&self, target: &T) -> bool;
}