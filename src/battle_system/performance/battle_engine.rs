/// 优化的战斗引擎
/// 
/// 集成所有性能优化组件的核心战斗引擎：
/// - 对象池化管理
/// - AI智能决策
/// - 并发处理支持
/// - 性能监控
/// - 基准测试套件

use crate::shared::*;
use crate::battle_system::simplified_battle_traits::*;
use crate::battle_system::battle_view::BattleView;
use crate::battle_system::performance::{
    PerformanceConfig, PerformanceStats, PerformanceManager,
    object_pools::{PoolManager, PooledSkillInstance},
    ai_optimization::{
        IntelligentSkillSelector, TacticalCombinationEngine, BehaviorPredictor,
        AIDecisionCache, BattleContext, SkillRecommendation, ComboRecommendation,
    },
};
use crate::battle_system::intelligent_ai::{IntelligentAI, AIPersonality, AIDecision};
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, Mutex, RwLock};
use std::time::{Instant, Duration};
use std::thread;

// ============================================================================
// 优化的战斗引擎
// ============================================================================

/// 优化的战斗引擎
/// 
/// 高性能的战斗系统核心引擎，集成了所有优化组件
pub struct OptimizedBattleEngine {
    /// 引擎配置
    config: BattleEngineConfig,
    /// 性能管理器
    performance_manager: PerformanceManager,
    /// 对象池管理器
    pool_manager: PoolManager,
    /// 智能AI系统
    intelligent_ai: IntelligentAI,
    /// AI技能选择器（保留以兼容）
    skill_selector: IntelligentSkillSelector,
    /// 战术组合引擎（保留以兼容）
    combo_engine: TacticalCombinationEngine,
    /// 行为预测器（保留以兼容）
    behavior_predictor: BehaviorPredictor,
    /// AI决策缓存（保留以兼容）
    decision_cache: AIDecisionCache,
    /// 战斗状态（使用BattleView）
    battle_state: BattleState,
    /// 性能指标收集器
    metrics_collector: MetricsCollector,
    /// 引擎状态
    engine_state: EngineState,
}

impl OptimizedBattleEngine {
    /// 创建新的战斗引擎
    pub fn new(config: BattleEngineConfig) -> Self {
        let performance_config = PerformanceConfig {
            enable_object_pooling: config.enable_pooling,
            pool_size: config.pool_size,
            enable_concurrency: config.enable_concurrency,
            worker_threads: config.worker_threads,
            enable_caching: config.enable_caching,
            cache_size_mb: config.cache_size_mb,
            enable_ai_optimization: config.enable_ai_optimization,
            memory_budget_mb: config.memory_budget_mb,
        };
        
        Self {
            config: config.clone(),
            performance_manager: PerformanceManager::new(performance_config),
            pool_manager: PoolManager::new(config.pool_size),
            intelligent_ai: IntelligentAI::create_balanced(), // 默认使用平衡型AI
            skill_selector: IntelligentSkillSelector::new(),
            combo_engine: TacticalCombinationEngine::new(),
            behavior_predictor: BehaviorPredictor::new(),
            decision_cache: AIDecisionCache::new(),
            battle_state: BattleState::new(),
            metrics_collector: MetricsCollector::new(),
            engine_state: EngineState::Idle,
        }
    }
    
    /// 初始化战斗（使用BattleView）
    pub fn initialize_battle(&mut self, participants: Vec<BattleView>) -> GameResult<BattleId> {
        let start_time = Instant::now();
        
        // 设置引擎状态
        self.engine_state = EngineState::Initializing;
        
        // 创建战斗实例
        let battle_id = BattleId::new();
        self.battle_state.initialize(battle_id, participants)?;
        
        // 预热对象池
        self.prewarm_pools();
        
        // 重置AI组件
        self.skill_selector.clear_cache();
        self.decision_cache.cleanup_expired();
        // 智能AI不需要重置，因为它会自适应
        
        // 记录初始化时间
        let init_time = start_time.elapsed();
        self.metrics_collector.record_initialization_time(init_time);
        
        self.engine_state = EngineState::Ready;
        
        Ok(battle_id)
    }
    
    /// 执行战斗回合
    pub fn execute_turn(&mut self, battle_id: BattleId) -> GameResult<TurnResult> {
        if self.battle_state.battle_id != Some(battle_id) {
            return Err(GameError::internal_error("未找到指定的战斗: battle_not_found"));
        }
        
        let turn_start = Instant::now();
        self.engine_state = EngineState::Processing;
        
        // 更新战斗状态
        self.battle_state.advance_turn();
        let current_turn = self.battle_state.current_turn;
        
        // 获取当前行动者
        let current_actor_id = self.battle_state.get_current_actor()?;
        
        // 执行AI决策（如果是AI控制）
        let action_result = if self.is_ai_controlled(&current_actor_id) {
            self.execute_ai_turn(&current_actor_id)?
        } else {
            // 等待玩家输入
            ActionResult::WaitingForInput
        };
        
        // 更新技能冷却
        self.update_cooldowns();
        
        // 处理持续效果
        self.process_ongoing_effects()?;
        
        // 检查战斗结束条件
        let battle_status = self.check_battle_end_conditions();
        
        // 记录回合执行时间
        let turn_time = turn_start.elapsed();
        self.metrics_collector.record_turn_time(turn_time);
        self.performance_manager.record_skill_execution(turn_time.as_micros() as f64);
        
        self.engine_state = EngineState::Ready;
        
        Ok(TurnResult {
            turn_number: current_turn,
            actor_id: current_actor_id,
            action_result,
            battle_status,
            performance_stats: self.get_performance_snapshot(),
        })
    }

    /// 执行AI回合（使用智能AI系统）
    fn execute_ai_turn(&mut self, actor_id: &BattleUnitId) -> GameResult<ActionResult> {
        let ai_start = Instant::now();
        
        // 调试信息：显示当前参与者列表
        println!("    🔍 正在寻找行动者: {:?}", actor_id);
        println!("    参与者列表: {:?}", 
            self.battle_state.participants.iter()
                .map(|p| (p.id, &p.name))
                .collect::<Vec<_>>());
        
        // 获取行动者
        let actor = self.battle_state.get_unit(actor_id)?;
        
        // 分类参与者为盟友和敌人
        let (allies, enemies) = self.categorize_participants(actor_id);
        
        // 构建战斗上下文
        let battle_context = self.build_battle_context();
        
        // 使用智能AI进行决策
        let ai_decision = self.intelligent_ai.make_decision(
            actor,
            &allies,
            &enemies,
            &battle_context,
        );
        
        println!("    🤖 AI决策: {} (置信度: {:.2})", ai_decision.reasoning, ai_decision.confidence);
        
        let ai_time = ai_start.elapsed();
        self.performance_manager.record_ai_decision(ai_time.as_micros() as f64);
        
        // 执行决策
        self.execute_ai_decision(actor_id, &ai_decision)
    }
    
    /// 执行技能
    fn execute_skill(
        &mut self,
        actor_id: &BattleUnitId,
        skill_id: SkillId,
        target_id: Option<BattleUnitId>,
    ) -> GameResult<ActionResult> {
        // 创建技能实例
        let skill_instance = self.create_skill_instance(
            skill_id,
            *actor_id,
            target_id,
        )?;
        
        // 执行技能
        let skill_result = self.execute_skill_instance(skill_instance)?;
        
        // 应用伤害到目标
        if let Some(target_id) = target_id {
            if let Ok(target) = self.battle_state.get_unit_mut(&target_id) {
                target.current_health = target.current_health.saturating_sub(skill_result.damage_dealt as i32);
                if target.current_health <= 0 {
                    target.is_alive = false;
                }
            }
        }
        
        Ok(ActionResult::SkillExecuted(skill_result))
    }
    
    /// 创建技能实例（使用对象池）
    fn create_skill_instance(
        &mut self,
        skill_id: SkillId,
        caster_id: BattleUnitId,
        target_id: Option<BattleUnitId>,
    ) -> GameResult<PooledSkillInstance> {
        let pool = self.pool_manager.skill_instance_pool();
        let mut pool_guard = pool.lock().map_err(|_| {
            GameError::internal_error("无法获取技能实例池锁: pool_lock_failed")
        })?;
        
        // 获取技能信息（简化实现）
        let skill_level = 1;
        let cooldown = 5.0;
        let resource_cost = crate::skill::domain::value_objects::ResourceCost::new();
        let effects = crate::skill::domain::value_objects::SkillEffectComposition::new();
        
        let target_position = if let Some(tid) = target_id {
            Some(self.battle_state.get_unit(&tid)?.position)
        } else {
            None
        };
        
        Ok(pool_guard.create_skill_instance(
            skill_id,
            caster_id,
            target_position,
            skill_level,
            cooldown,
            resource_cost,
            effects,
        ))
    }
    
    /// 执行技能实例
    fn execute_skill_instance(&mut self, mut skill_instance: PooledSkillInstance) -> GameResult<SkillExecutionResult> {
        let execution_start = Instant::now();
        
        // 获取技能实例信息
        let instance = skill_instance.as_ref().ok_or_else(|| {
            GameError::internal_error("技能实例为空: empty_skill_instance")
        })?;
        
        let caster_id = instance.caster_id();
        let skill_id = instance.skill_id();
        
        // 执行技能逻辑
        let damage_dealt = self.calculate_and_apply_damage(&caster_id, &skill_id)?;
        let mana_consumed = self.consume_resources(&caster_id, &skill_id)?;
        
        // 应用技能效果
        let effects_applied = self.apply_skill_effects(&skill_id)?;
        
        // 设置冷却时间
        self.apply_skill_cooldown(&caster_id, &skill_id)?;
        
        // 记录执行时间
        let execution_time = execution_start.elapsed();
        self.metrics_collector.record_skill_execution(skill_id, execution_time);
        
        // 技能实例会在drop时自动归还到池中
        
        Ok(SkillExecutionResult {
            skill_id,
            caster_id,
            damage_dealt,
            mana_consumed,
            effects_applied,
            execution_time,
        })
    }
    
    /// 分类参与者为盟友和敌人
    fn categorize_participants(&self, actor_id: &BattleUnitId) -> (Vec<BattleView>, Vec<BattleView>) {
        let mut allies = Vec::new();
        let mut enemies = Vec::new();
        
        for participant in &self.battle_state.participants {
            if participant.id == *actor_id {
                continue; // 跳过自己
            }
            
            if participant.is_alive {
                // 完整的盟友/敌人分类逻辑
                let is_ally = match (actor_id, &participant.id) {
                // 同类型角色间的关系
                (BattleUnitId::Character(actor_char_id), BattleUnitId::Character(participant_char_id)) => {
                    // 检查角色是否属于同一队伍（简化：相邻ID为同队）
                    let actor_team = actor_char_id.0 % 2;
                let participant_team = participant_char_id.0 % 2;
                    actor_team == participant_team
                },
                // 同类型怪物间的关系
                (BattleUnitId::Monster(actor_monster_id), BattleUnitId::Monster(participant_monster_id)) => {
                        // 检查怪物是否属于同一种族（简化：相邻ID为同族）
                    let actor_race = actor_monster_id.0 / 10;
                    let participant_race = participant_monster_id.0 / 10;
                    actor_race == participant_race
                },
                // 角色与怪物默认为敌对
                (BattleUnitId::Character(_), BattleUnitId::Monster(_)) => false,
                (BattleUnitId::Monster(_), BattleUnitId::Character(_)) => false,
            };
            
            if is_ally {
                allies.push(participant.clone());
            } else {
                enemies.push(participant.clone());
            }
            }
        }
        
        (allies, enemies)
    }
    
    /// 执行AI决策
    fn execute_ai_decision(&mut self, actor_id: &BattleUnitId, decision: &AIDecision) -> GameResult<ActionResult> {
        use crate::battle_system::intelligent_ai::ActionType;
        
        match decision.action_type {
            ActionType::Attack => {
                if let (Some(skill_id), Some(target_id)) = (decision.skill_id, decision.target_id) {
                    self.execute_skill(actor_id, skill_id, Some(target_id))
                } else {
                    Err(GameError::validation_error("ai_decision", "Attack action missing skill or target"))
                }
            },
            ActionType::Heal => {
                if let (Some(skill_id), Some(target_id)) = (decision.skill_id, decision.target_id) {
                    self.execute_skill(actor_id, skill_id, Some(target_id))
                } else {
                    Err(GameError::validation_error("ai_decision", "Heal action missing skill or target"))
                }
            },
            ActionType::Buff | ActionType::Debuff => {
                if let Some(skill_id) = decision.skill_id {
                    self.execute_skill(actor_id, skill_id, decision.target_id)
                } else {
                    Err(GameError::validation_error("ai_decision", "Buff/Debuff action missing skill"))
                }
            },
            ActionType::Defend => {
                // 实现防御行动
                self.execute_defend_action(actor_id)
            },
            ActionType::Reposition => {
                // 实现位置调整
                self.execute_reposition_action(actor_id)
            },
            ActionType::SkipTurn => {
                Ok(ActionResult::SkipTurn)
            },
        }
    }
    
    /// 执行防御行动
    fn execute_defend_action(&mut self, actor_id: &BattleUnitId) -> GameResult<ActionResult> {
        let execution_start = Instant::now();
        
        let actor = self.battle_state.get_unit_mut(actor_id)?;
        
        // 计算防御加成
        let base_defense = actor.defense_power;
        let defense_multiplier = 1.5; // 防御姿态提供50%防御加成
        let level_bonus = 1.0 + (actor.level as f32 * 0.02); // 每级2%额外防御
        
        let defense_boost = (base_defense as f32 * (defense_multiplier - 1.0) * level_bonus) as Defense;
        
        // 临时增加防御力（防御姿态持续到下个回合）
        actor.defense_power += defense_boost;
        
        // 添加防御状态效果
        let defense_buff = crate::battle_system::buff_adapter::SimplifiedBuff::new(
            "defense_stance".to_string(),
            "防御姿态".to_string(),
            defense_boost as f32,
            2.0, // 持续2回合
        );
        actor.buffs.push(defense_buff);
        
        // 添加伤害减免效果
        let damage_reduction_buff = crate::battle_system::buff_adapter::SimplifiedBuff::new(
            "damage_reduction".to_string(),
            "伤害减免".to_string(),
            20.0, // 20%伤害减免
            2.0, // 持续2回合
        );
        actor.buffs.push(damage_reduction_buff);
        
        // 恢复少量法力（专注防御恢复精神）
        let mana_recovery = (actor.max_mana as f32 * 0.1) as Mana;
        actor.current_mana = (actor.current_mana + mana_recovery).min(actor.max_mana);
        
        let execution_time = execution_start.elapsed();
        
        Ok(ActionResult::SkillExecuted(SkillExecutionResult {
            skill_id: SkillId(9999), // 特殊技能ID表示防御行动
            caster_id: *actor_id,
            damage_dealt: 0,
            mana_consumed: 0,
            effects_applied: vec![
                "防御姿态".to_string(),
                "伤害减免".to_string(),
                "法力恢复".to_string()
            ],
            execution_time,
        }))
    }
    
    /// 执行位置调整行动
    fn execute_reposition_action(&mut self, actor_id: &BattleUnitId) -> GameResult<ActionResult> {
        let execution_start = Instant::now();
        
        // 先获取当前位置信息
        let current_position = {
            let actor = self.battle_state.get_unit(actor_id)?;
            actor.position
        };
        
        // 分析战场态势，寻找最佳位置
        let optimal_position = self.calculate_optimal_position(actor_id, &current_position)?;
        
        // 获取可变引用进行修改
        let actor = self.battle_state.get_unit_mut(actor_id)?;
        
        // 检查移动距离是否在允许范围内
        let move_distance = ((optimal_position.x - current_position.x).powi(2) + 
                            (optimal_position.y - current_position.y).powi(2)).sqrt();
        
        let max_move_distance = actor.movement_speed;
        
        let new_position = if move_distance <= max_move_distance {
            optimal_position
        } else {
            // 向目标方向移动最大距离
            let direction_x = (optimal_position.x - current_position.x) / move_distance;
            let direction_y = (optimal_position.y - current_position.y) / move_distance;
            
            Position::new(
                current_position.x + direction_x * max_move_distance,
                current_position.y + direction_y * max_move_distance,
            )
        };
        
        // 更新位置
        actor.position = new_position;
        
        // 计算移动效果
        let distance_moved = ((new_position.x - current_position.x).powi(2) + 
                             (new_position.y - current_position.y).powi(2)).sqrt();
        
        // 移动消耗少量法力
        let mana_cost = (distance_moved * 2.0) as Mana;
        actor.current_mana = actor.current_mana.saturating_sub(mana_cost);
        
        // 添加移动加成效果（下回合攻击精度提升）
        let tactical_movement_buff = crate::battle_system::buff_adapter::SimplifiedBuff::new(
            "tactical_positioning".to_string(),
            "战术定位".to_string(),
            15.0, // 15%攻击精度加成
            1.0, // 持续1回合
        );
        actor.buffs.push(tactical_movement_buff);
        
        let execution_time = execution_start.elapsed();
        
        Ok(ActionResult::SkillExecuted(SkillExecutionResult {
            skill_id: SkillId(9998), // 特殊技能ID表示移动行动
            caster_id: *actor_id,
            damage_dealt: 0,
            mana_consumed: mana_cost as u32,
            effects_applied: vec![
                format!("位置调整: ({:.1}, {:.1})", new_position.x, new_position.y),
                "战术定位".to_string()
            ],
            execution_time,
        }))
    }
    
    /// 构建战斗上下文
    fn build_battle_context(&self) -> BattleContext {
        let turn_number = self.battle_state.current_turn;
        let total_units = self.battle_state.participants.len();
        let alive_units = self.battle_state.participants.iter()
            .filter(|unit| unit.is_alive)
            .count();
        
        let team_health_ratio = if alive_units > 0 {
            let total_health: i32 = self.battle_state.participants.iter()
                .map(|unit| unit.current_health)
                .sum();
            let max_health: i32 = self.battle_state.participants.iter()
                .map(|unit| unit.max_health)
                .sum();
            
            if max_health > 0 {
                total_health as f64 / max_health as f64
            } else {
                0.0
            }
        } else {
            0.0
        };
        
        let battle_phase = if turn_number <= 3 {
            crate::battle_system::performance::ai_optimization::BattlePhase::Early
        } else if turn_number <= 10 {
            crate::battle_system::performance::ai_optimization::BattlePhase::Mid
        } else {
            crate::battle_system::performance::ai_optimization::BattlePhase::Late
        };
        
        BattleContext {
            battle_phase,
            team_health_ratio,
            enemy_count: total_units - alive_units,
            turn_number,
        }
    }
    
    /// 预热对象池
    fn prewarm_pools(&mut self) {
        // 预热操作在池初始化时已完成
        // 这里可以添加额外的预热逻辑
    }
    
    /// 更新技能冷却
    fn update_cooldowns(&mut self) {
        let delta_time = 1.0; // 每回合减少1秒冷却时间
        
        // 处理所有单位的技能冷却
        for participant in &mut self.battle_state.participants {
            // 更新技能冷却时间
            let mut skills_to_remove = Vec::new();
            
            for (skill_key, cooldown) in &mut participant.skill_cooldowns {
                *cooldown -= delta_time;
                if *cooldown <= 0.0 {
                    skills_to_remove.push(skill_key.clone());
                }
            }
            
            // 移除冷却时间已结束的技能
            for skill_key in skills_to_remove {
                participant.skill_cooldowns.remove(&skill_key);
            }
            
            // 更新Buff持续时间
            participant.buffs.retain_mut(|buff| {
                buff.duration -= delta_time as f32;
                buff.duration > 0.0
            });
        }
    }
    
    /// 处理持续效果
    fn process_ongoing_effects(&mut self) -> GameResult<()> {
        // 处理所有单位的持续效果
        for participant in &mut self.battle_state.participants {
            if !participant.is_alive {
                continue;
            }
            
            let mut effects_log = Vec::new();
            
            // 处理各种持续效果
            for buff in &participant.buffs {
                match buff.skill_type.as_str() {
                    "poison" => {
                        // 中毒效果：每回合损失生命值
                        let poison_damage = (buff.effect_value as i32).max(1);
                        participant.current_health = participant.current_health.saturating_sub(poison_damage);
                        effects_log.push(format!("{}受到{}点中毒伤害", participant.name, poison_damage));
                        
                        if participant.current_health <= 0 {
                            participant.is_alive = false;
                            effects_log.push(format!("{}因中毒而死亡", participant.name));
                        }
                    },
                    "regeneration" => {
                        // 恢复效果：每回合恢复生命值
                        let heal_amount = (buff.effect_value as i32).max(1);
                        let old_health = participant.current_health;
                        participant.current_health = (participant.current_health + heal_amount).min(participant.max_health);
                        let actual_heal = participant.current_health - old_health;
                        if actual_heal > 0 {
                            effects_log.push(format!("{}恢复了{}点生命值", participant.name, actual_heal));
                        }
                    },
                    "burn" => {
                        // 燃烧效果：每回合火焰伤害
                        let burn_damage = (buff.effect_value as i32).max(1);
                        participant.current_health = participant.current_health.saturating_sub(burn_damage);
                        effects_log.push(format!("{}受到{}点燃烧伤害", participant.name, burn_damage));
                        
                        if participant.current_health <= 0 {
                            participant.is_alive = false;
                            effects_log.push(format!("{}被烧死了", participant.name));
                        }
                    },
                    "mana_recovery" => {
                        // 法力恢复效果
                        let mana_amount = (buff.effect_value as i32).max(1);
                        let old_mana = participant.current_mana;
                        participant.current_mana = (participant.current_mana + mana_amount).min(participant.max_mana);
                        let actual_recovery = participant.current_mana - old_mana;
                        if actual_recovery > 0 {
                            effects_log.push(format!("{}恢复了{}点法力值", participant.name, actual_recovery));
                        }
                    },
                    "slow" => {
                        // 减速效果已经通过buff的存在来表示，这里只记录
                        // 实际的移动速度修正在移动计算时处理
                    },
                    "haste" => {
                        // 加速效果已经通过buff的存在来表示
                        // 实际的攻击速度修正在攻击计算时处理
                    },
                    _ => {
                        // 其他效果类型的处理
                    }
                }
            }
            
            // 打印效果日志（调试用）
            for log in effects_log {
                println!("    🔄 持续效果: {}", log);
            }
        }
        
        Ok(())
    }
    
    /// 检查战斗结束条件
    fn check_battle_end_conditions(&self) -> BattleEndStatus {
        let alive_count = self.battle_state.participants.iter()
            .filter(|unit| unit.is_alive)
            .count();
        
        if alive_count <= 1 {
            if let Some(winner) = self.battle_state.participants.iter()
                .find(|unit| unit.is_alive) {
                BattleEndStatus::Victory(winner.id)
            } else {
                BattleEndStatus::Draw
            }
        } else {
            BattleEndStatus::Ongoing
        }
    }
    
    /// 完整的伤害计算和应用
    fn calculate_and_apply_damage(&mut self, caster_id: &BattleUnitId, skill_id: &SkillId) -> GameResult<u32> {
        // 获取施法者信息
        let caster = self.battle_state.get_unit(caster_id)?;
        
        // 计算基础伤害
        let base_damage = caster.attack_power as u32;
        
        // 技能伤害修正
        let skill_multiplier = match skill_id.0 {
            1 => 1.2,  // 普通攻击
            2 => 1.5,  // 重击
            3 => 0.8,  // 快速攻击
            _ => 1.0,
        };
        
        // 等级伤害加成
        let level_bonus = 1.0 + (caster.level as f32 * 0.05);
        
        // Buff伤害加成
        let buff_bonus = caster.buffs.iter()
            .filter(|buff| buff.skill_type == "damage_bonus")
            .map(|buff| buff.effect_value / 100.0)
            .fold(1.0, |acc, bonus| acc + bonus);
        
        // 最终伤害计算
        let final_damage = (base_damage as f32 * skill_multiplier * level_bonus * buff_bonus) as u32;
        
        Ok(final_damage)
    }
    
    /// 完整的资源消耗计算和应用
    fn consume_resources(&mut self, caster_id: &BattleUnitId, skill_id: &SkillId) -> GameResult<u32> {
        // 获取施法者信息
        let caster = self.battle_state.get_unit_mut(caster_id)?;
        
        // 计算技能基础法力消耗
        let base_mana_cost = match skill_id.0 {
            1 => 10,  // 普通攻击
            2 => 25,  // 重击
            3 => 15,  // 快速攻击
            4 => 30,  // 治疗
            5 => 40,  // 强力法术
            _ => 20,  // 默认消耗
        };
        
        // 等级影响（高等级降低消耗）
        let level_reduction = 1.0 - (caster.level as f32 * 0.01).min(0.5);
        
        // Buff影响（法力效率）
        let buff_reduction = caster.buffs.iter()
            .filter(|buff| buff.skill_type == "mana_efficiency")
            .map(|buff| buff.effect_value / 100.0)
            .fold(1.0, |acc, reduction| acc - reduction);
        
        // 最终法力消耗
        let final_cost = (base_mana_cost as f32 * level_reduction * buff_reduction.max(0.1)) as u32;
        
        // 检查法力是否足够
        if caster.current_mana < final_cost as i32 {
            return Err(GameError::validation_error("mana_insufficient", "法力不足"));
        }
        
        // 扣除法力
        caster.current_mana -= final_cost as i32;
        
        Ok(final_cost)
    }
    
    /// 完整的技能效果应用
    fn apply_skill_effects(&mut self, skill_id: &SkillId) -> GameResult<Vec<String>> {
        let mut effects_applied = Vec::new();
        
        // 根据技能ID应用不同效果
        match skill_id.0 {
            1 => {
                // 普通攻击：基础伤害
                effects_applied.push("物理伤害".to_string());
            },
            2 => {
                // 重击：高伤害 + 可能眩晕
                effects_applied.push("重击伤害".to_string());
                if rand::random::<f32>() < 0.2 {
                    effects_applied.push("眩晕效果".to_string());
                }
            },
            3 => {
                // 快速攻击：连击效果
                effects_applied.push("连击伤害".to_string());
                effects_applied.push("攻击速度提升".to_string());
            },
            4 => {
                // 治疗技能：恢复生命值
                effects_applied.push("生命恢复".to_string());
                effects_applied.push("持续回复".to_string());
            },
            5 => {
                // 强力法术：魔法伤害 + 状态效果
                effects_applied.push("魔法伤害".to_string());
                effects_applied.push("燃烧效果".to_string());
            },
            6 => {
                // 防御技能：防御提升
                effects_applied.push("防御强化".to_string());
                effects_applied.push("伤害减免".to_string());
            },
            7 => {
                // 群体攻击：范围伤害
                effects_applied.push("范围伤害".to_string());
                effects_applied.push("击退效果".to_string());
            },
            8 => {
                // 毒系攻击：中毒效果
                effects_applied.push("毒素伤害".to_string());
                effects_applied.push("持续中毒".to_string());
            },
            9 => {
                // 冰系攻击：冰冻效果
                effects_applied.push("冰霜伤害".to_string());
                effects_applied.push("减速效果".to_string());
            },
            10 => {
                // 雷系攻击：麻痹效果
                effects_applied.push("雷电伤害".to_string());
                effects_applied.push("麻痹效果".to_string());
            },
            _ => {
                // 其他技能：默认效果
                effects_applied.push("特殊效果".to_string());
            }
        }
        
        Ok(effects_applied)
    }
    
    /// 应用技能冷却
    fn apply_skill_cooldown(&mut self, caster_id: &BattleUnitId, skill_id: &SkillId) -> GameResult<()> {
        let caster = self.battle_state.get_unit_mut(caster_id)?;
        
        // 计算技能基础冷却时间
        let base_cooldown = match skill_id.0 {
            1 => 1.0,   // 普通攻击
            2 => 3.0,   // 重击
            3 => 2.0,   // 快速攻击
            4 => 4.0,   // 治疗
            5 => 6.0,   // 强力法术
            6 => 5.0,   // 防御技能
            7 => 8.0,   // 群体攻击
            8 => 4.5,   // 毒系攻击
            9 => 5.5,   // 冰系攻击
            10 => 7.0,  // 雷系攻击
            _ => 3.0,   // 默认冷却
        };
        
        // 等级影响（高等级降低冷却）
        let level_reduction = 1.0 - (caster.level as f32 * 0.005).min(0.3);
        
        // Buff影响（冷却缩减）
        let buff_reduction = caster.buffs.iter()
            .filter(|buff| buff.skill_type == "cooldown_reduction")
            .map(|buff| buff.effect_value / 100.0)
            .fold(1.0, |acc, reduction| acc - reduction);
        
        // 最终冷却时间
        let final_cooldown = base_cooldown * level_reduction * buff_reduction.max(0.1);
        
        // 将技能ID转换为字符串作为键
        let skill_key = format!("skill_{}", skill_id.0);
        
        // 设置冷却时间
        caster.skill_cooldowns.insert(SkillId(skill_id.0), final_cooldown as f64);
        
        Ok(())
    }
    
    /// 检查是否AI控制
    fn is_ai_controlled(&self, unit_id: &BattleUnitId) -> bool {
        // 根据单位类型判断是否AI控制
        match unit_id {
            BattleUnitId::Monster(_) => true,  // 怪物总是AI控制
            BattleUnitId::Character(char_id) => {
                // 检查角色是否设置为AI控制
                if let Ok(unit) = self.battle_state.get_unit(unit_id) {
                    // 如果角色名称包含"AI"或"Bot"，则认为是AI控制
                    unit.name.contains("AI") || unit.name.contains("Bot") || unit.name.contains("自动")
                } else {
                    false // 未找到单位，默认不是AI控制
                }
            }
        }
    }
    
    /// 计算最佳位置
    fn calculate_optimal_position(&self, actor_id: &BattleUnitId, current_position: &Position) -> GameResult<Position> {
        let actor = self.battle_state.get_unit(actor_id)?;
        let (allies, enemies) = self.categorize_participants(actor_id);
        
        let mut best_position = *current_position;
        let mut best_score = 0.0;
        
        // 搜索范围：以当前位置为中心的区域
        let search_radius = actor.movement_speed;
        let step_size = 0.5; // 位置搜索精度
        
        // 在移动范围内搜索最佳位置
        let mut x = current_position.x - search_radius;
        while x <= current_position.x + search_radius {
            let mut y = current_position.y - search_radius;
            while y <= current_position.y + search_radius {
                let candidate_position = Position::new(x, y);
                
                // 检查是否在移动范围内
                let distance = ((x - current_position.x).powi(2) + (y - current_position.y).powi(2)).sqrt();
                if distance <= search_radius {
                    let score = self.evaluate_position_score(actor, &candidate_position, &allies, &enemies);
                    if score > best_score {
                        best_score = score;
                        best_position = candidate_position;
                    }
                }
                
                y += step_size;
            }
            x += step_size;
        }
        
        Ok(best_position)
    }
    
    /// 评估位置得分
    fn evaluate_position_score(
        &self,
        actor: &BattleView,
        position: &Position,
        allies: &[BattleView],
        enemies: &[BattleView],
    ) -> f32 {
        let mut score = 0.0;
        
        // 与敌人的距离评分（根据角色类型调整）
        let is_ranged = actor.attack_power < actor.max_mana; // 简单判断是否远程单位
        
        for enemy in enemies {
            let distance = ((position.x - enemy.position.x).powi(2) + 
                           (position.y - enemy.position.y).powi(2)).sqrt();
            
            if is_ranged {
                // 远程单位希望保持适中距离
                let optimal_distance = 4.0;
                let distance_penalty = (distance - optimal_distance).abs();
                score += 10.0 - distance_penalty;
            } else {
                // 近战单位希望靠近敌人
                score += 10.0 / (distance + 1.0);
            }
        }
        
        // 与盟友的距离评分（避免过于密集）
        for ally in allies {
            let distance = ((position.x - ally.position.x).powi(2) + 
                           (position.y - ally.position.y).powi(2)).sqrt();
            
            if distance < 1.0 {
                score -= 5.0; // 过于接近盟友会被扣分
            } else if distance < 3.0 {
                score += 2.0; // 适度靠近盟友加分
            }
        }
        
        // 战场边缘惩罚（避免移动到边缘）
        let edge_penalty = {
            let edge_distance = 20.0; // 假设战场大小
            let min_distance_to_edge = position.x.min(position.y)
                .min(edge_distance - position.x)
                .min(edge_distance - position.y);
            
            if min_distance_to_edge < 2.0 {
                -10.0 // 太靠近边缘扣分
            } else {
                0.0
            }
        };
        
        score += edge_penalty;
        
        // 地形加成（简化：中心位置加分）
        let center_x = 10.0;
        let center_y = 10.0;
        let center_distance = ((position.x - center_x).powi(2) + (position.y - center_y).powi(2)).sqrt();
        score += 5.0 / (center_distance + 1.0);
        
        score
    }
    
    /// 获取性能快照
    pub fn get_performance_snapshot(&self) -> PerformanceSnapshot {
        PerformanceSnapshot {
            engine_stats: self.performance_manager.get_stats().clone(),
            pool_stats: self.pool_manager.get_all_stats(),
            ai_stats: self.skill_selector.get_stats().clone(),
            cache_hit_rate: self.decision_cache.hit_rate(),
            memory_usage: self.metrics_collector.get_memory_usage(),
        }
    }
    
    /// 运行基准测试
    pub fn run_benchmark(&mut self, benchmark_config: BenchmarkConfig) -> BenchmarkResults {
        let mut results = BenchmarkResults::new();
        
        // 技能执行基准测试
        results.skill_execution = self.benchmark_skill_execution(&benchmark_config);
        
        // AI决策基准测试
        results.ai_decision = self.benchmark_ai_decision(&benchmark_config);
        
        // 对象池基准测试
        results.object_pooling = self.benchmark_object_pooling(&benchmark_config);
        
        // 内存使用基准测试
        results.memory_usage = self.benchmark_memory_usage(&benchmark_config);
        
        results
    }
    
    /// 技能执行基准测试
    fn benchmark_skill_execution(&mut self, config: &BenchmarkConfig) -> BenchmarkResult {
        let start_time = Instant::now();
        let iterations = config.skill_execution_iterations;
        
        for _ in 0..iterations {
            // 模拟技能执行
            let _ = self.create_skill_instance(
                SkillId(1), 
                BattleUnitId::Character(CharacterId(1)), 
                None
            );
        }
        
        let total_time = start_time.elapsed();
        
        BenchmarkResult {
            test_name: "技能执行".to_string(),
            execution_time_ns: total_time.as_nanos() as u64,
            allocations: iterations, // 简化计算
            peak_memory_bytes: self.metrics_collector.get_memory_usage(),
            throughput_ops_per_sec: iterations as f64 / total_time.as_secs_f64(),
        }
    }
    
    /// AI决策基准测试
    fn benchmark_ai_decision(&mut self, config: &BenchmarkConfig) -> BenchmarkResult {
        let start_time = Instant::now();
        let iterations = config.ai_decision_iterations;
        
        // 模拟AI决策
        let battle_context = self.build_battle_context();
        let available_skills = vec![SkillId(1), SkillId(2), SkillId(3)];
        
        for _ in 0..iterations {
            // 这里需要模拟单位和目标
            // 简化实现
        }
        
        let total_time = start_time.elapsed();
        
        BenchmarkResult {
            test_name: "AI决策".to_string(),
            execution_time_ns: total_time.as_nanos() as u64,
            allocations: iterations / 2, // AI决策分配较少
            peak_memory_bytes: self.metrics_collector.get_memory_usage(),
            throughput_ops_per_sec: iterations as f64 / total_time.as_secs_f64(),
        }
    }
    
    /// 对象池基准测试
    fn benchmark_object_pooling(&mut self, config: &BenchmarkConfig) -> BenchmarkResult {
        let start_time = Instant::now();
        let iterations = config.pooling_iterations;
        
        for _ in 0..iterations {
            let pool = self.pool_manager.skill_instance_pool();
            let mut pool_guard = pool.lock().unwrap();
            let instance = pool_guard.acquire_pooled();
            // 实例会在drop时自动归还
        }
        
        let total_time = start_time.elapsed();
        
        BenchmarkResult {
            test_name: "对象池化".to_string(),
            execution_time_ns: total_time.as_nanos() as u64,
            allocations: 0, // 池化减少了分配
            peak_memory_bytes: self.metrics_collector.get_memory_usage(),
            throughput_ops_per_sec: iterations as f64 / total_time.as_secs_f64(),
        }
    }
    
    /// 内存使用基准测试
    fn benchmark_memory_usage(&mut self, config: &BenchmarkConfig) -> BenchmarkResult {
        let start_memory = self.metrics_collector.get_memory_usage();
        
        // 模拟一轮技能使用
        for _ in 0..100 {
            let pool = self.pool_manager.skill_instance_pool();
            let mut pool_guard = pool.lock().unwrap();
            let _instance = pool_guard.acquire_pooled();
            // 实例在离开作用域时会自动释放回池中
        }

        let end_memory = self.metrics_collector.get_memory_usage();
        let memory_increase = end_memory.saturating_sub(start_memory);
        
        BenchmarkResult {
            test_name: "内存使用".to_string(),
            execution_time_ns: 0,
            allocations: config.memory_test_iterations,
            peak_memory_bytes: end_memory,
            throughput_ops_per_sec: 0.0, // 内存测试不计算吞吐量
        }
    }
}

// ============================================================================
// 数据结构定义
// ============================================================================

/// 战斗引擎配置
#[derive(Debug, Clone)]
pub struct BattleEngineConfig {
    /// 是否启用对象池化
    pub enable_pooling: bool,
    /// 对象池大小
    pub pool_size: usize,
    /// 是否启用并发处理
    pub enable_concurrency: bool,
    /// 工作线程数
    pub worker_threads: usize,
    /// 是否启用缓存
    pub enable_caching: bool,
    /// 缓存大小限制（MB）
    pub cache_size_mb: usize,
    /// 是否启用AI优化
    pub enable_ai_optimization: bool,
    /// 内存预算限制（MB）
    pub memory_budget_mb: usize,
}

impl Default for BattleEngineConfig {
    fn default() -> Self {
        Self {
            enable_pooling: true,
            pool_size: 1000,
            enable_concurrency: true,
            worker_threads: 4,
            enable_caching: true,
            cache_size_mb: 64,
            enable_ai_optimization: true,
            memory_budget_mb: 256,
        }
    }
}

/// 战斗ID
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct BattleId(pub u64);

impl BattleId {
    pub fn new() -> Self {
        Self(rand::random())
    }
}

/// 战斗状态（使用BattleView）
struct BattleState {
    battle_id: Option<BattleId>,
    participants: Vec<BattleView>,
    current_turn: u32,
    current_actor_index: usize,
}

impl BattleState {
    fn new() -> Self {
        Self {
            battle_id: None,
            participants: Vec::new(),
            current_turn: 0,
            current_actor_index: 0,
        }
    }
    
    fn initialize(&mut self, battle_id: BattleId, participants: Vec<BattleView>) -> GameResult<()> {
        self.battle_id = Some(battle_id);
        self.participants = participants;
        self.current_turn = 1;
        self.current_actor_index = 0;
        Ok(())
    }
    
    fn advance_turn(&mut self) {
        self.current_actor_index = (self.current_actor_index + 1) % self.participants.len();
        if self.current_actor_index == 0 {
            self.current_turn += 1;
        }
    }
    
    fn get_current_actor(&self) -> GameResult<BattleUnitId> {
        self.participants.get(self.current_actor_index)
            .map(|unit| unit.id)
            .ok_or_else(|| GameError::internal_error("无当前行动者: no_current_actor"))
    }
    
    fn get_unit(&self, unit_id: &BattleUnitId) -> GameResult<&BattleView> {
        self.participants.iter()
            .find(|unit| unit.id == *unit_id)
            .ok_or_else(|| GameError::unit_not_found_error(format!("{:?}", unit_id)))
    }
    
    fn get_unit_mut(&mut self, unit_id: &BattleUnitId) -> GameResult<&mut BattleView> {
        self.participants.iter_mut()
            .find(|unit| unit.id == *unit_id)
            .ok_or_else(|| GameError::unit_not_found_error(format!("{:?}", unit_id)))
    }
    
    fn get_valid_targets(&self, actor_id: &BattleUnitId) -> Vec<&BattleView> {
        self.participants.iter()
            .filter(|unit| unit.id != *actor_id && unit.is_alive)
            .collect()
    }
}

/// 引擎状态
#[derive(Debug, Clone, PartialEq)]
enum EngineState {
    Idle,
    Initializing,
    Ready,
    Processing,
    Error(String),
}

/// 回合结果
#[derive(Debug, Clone)]
pub struct TurnResult {
    pub turn_number: u32,
    pub actor_id: BattleUnitId,
    pub action_result: ActionResult,
    pub battle_status: BattleEndStatus,
    pub performance_stats: PerformanceSnapshot,
}

/// 行动结果
#[derive(Debug, Clone)]
pub enum ActionResult {
    SkillExecuted(SkillExecutionResult),
    ComboExecuted {
        combo_id: crate::battle_system::performance::ai_optimization::ComboId,
        skill_results: Vec<SkillExecutionResult>,
    },
    SkipTurn,
    WaitingForInput,
}

/// 技能执行结果
#[derive(Debug, Clone)]
pub struct SkillExecutionResult {
    pub skill_id: SkillId,
    pub caster_id: BattleUnitId,
    pub damage_dealt: u32,
    pub mana_consumed: u32,
    pub effects_applied: Vec<String>,
    pub execution_time: Duration,
}

/// 战斗结束状态
#[derive(Debug, Clone)]
pub enum BattleEndStatus {
    Ongoing,
    Victory(BattleUnitId),
    Defeat,
    Draw,
}

/// 性能快照
#[derive(Debug, Clone)]
pub struct PerformanceSnapshot {
    pub engine_stats: PerformanceStats,
    pub pool_stats: HashMap<String, crate::battle_system::performance::object_pools::PoolStats>,
    pub ai_stats: crate::battle_system::performance::ai_optimization::AIStats,
    pub cache_hit_rate: f64,
    pub memory_usage: usize,
}

/// 指标收集器
struct MetricsCollector {
    initialization_times: VecDeque<Duration>,
    turn_times: VecDeque<Duration>,
    skill_execution_times: HashMap<SkillId, VecDeque<Duration>>,
    combo_execution_times: VecDeque<Duration>,
    memory_usage: usize,
}

impl MetricsCollector {
    fn new() -> Self {
        Self {
            initialization_times: VecDeque::with_capacity(100),
            turn_times: VecDeque::with_capacity(1000),
            skill_execution_times: HashMap::new(),
            combo_execution_times: VecDeque::with_capacity(100),
            memory_usage: 0,
        }
    }
    
    fn record_initialization_time(&mut self, time: Duration) {
        self.initialization_times.push_back(time);
        if self.initialization_times.len() > 100 {
            self.initialization_times.pop_front();
        }
    }
    
    fn record_turn_time(&mut self, time: Duration) {
        self.turn_times.push_back(time);
        if self.turn_times.len() > 1000 {
            self.turn_times.pop_front();
        }
    }
    
    fn record_skill_execution(&mut self, skill_id: SkillId, time: Duration) {
        let times = self.skill_execution_times.entry(skill_id).or_insert_with(|| VecDeque::with_capacity(100));
        times.push_back(time);
        if times.len() > 100 {
            times.pop_front();
        }
    }
    
    fn record_combo_execution(&mut self, time: Duration) {
        self.combo_execution_times.push_back(time);
        if self.combo_execution_times.len() > 100 {
            self.combo_execution_times.pop_front();
        }
    }
    
    fn get_memory_usage(&self) -> usize {
        // 简化实现：返回估算的内存使用量
        self.memory_usage
    }
}

/// 基准测试配置
#[derive(Debug, Clone)]
pub struct BenchmarkConfig {
    pub skill_execution_iterations: usize,
    pub ai_decision_iterations: usize,
    pub pooling_iterations: usize,
    pub memory_test_iterations: usize,
}

impl Default for BenchmarkConfig {
    fn default() -> Self {
        Self {
            skill_execution_iterations: 1000,
            ai_decision_iterations: 500,
            pooling_iterations: 2000,
            memory_test_iterations: 100,
        }
    }
}

/// 基准测试结果
#[derive(Debug, Clone)]
pub struct BenchmarkResults {
    pub skill_execution: BenchmarkResult,
    pub ai_decision: BenchmarkResult,
    pub object_pooling: BenchmarkResult,
    pub memory_usage: BenchmarkResult,
}

impl BenchmarkResults {
    fn new() -> Self {
        Self {
            skill_execution: BenchmarkResult::default(),
            ai_decision: BenchmarkResult::default(),
            object_pooling: BenchmarkResult::default(),
            memory_usage: BenchmarkResult::default(),
        }
    }
}

/// 基准测试结果
#[derive(Debug, Clone)]
pub struct BenchmarkResult {
    pub test_name: String,
    pub execution_time_ns: u64,
    pub allocations: usize,
    pub peak_memory_bytes: usize,
    pub throughput_ops_per_sec: f64,
}

impl Default for BenchmarkResult {
    fn default() -> Self {
        Self {
            test_name: "未知测试".to_string(),
            execution_time_ns: 0,
            allocations: 0,
            peak_memory_bytes: 0,
            throughput_ops_per_sec: 0.0,
        }
    }
}

impl BenchmarkResult {
    /// 获取平均执行时间（微秒）
    pub fn avg_execution_time_us(&self) -> f64 {
        self.execution_time_ns as f64 / 1000.0
    }
    
    /// 获取内存效率分数
    pub fn memory_efficiency_score(&self) -> f64 {
        if self.allocations == 0 {
            100.0
        } else {
            100.0 / (self.allocations as f64).log10()
        }
    }
}

// ============================================================================
// 公共API和便捷方法
// ============================================================================

/// 引擎优化设置
#[derive(Debug, Clone)]
pub struct EngineOptimization {
    pub level: OptimizationLevel,
    pub custom_config: Option<BattleEngineConfig>,
}

/// 优化级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum OptimizationLevel {
    /// 基础优化
    Basic,
    /// 标准优化
    Standard,
    /// 高性能优化
    HighPerformance,
    /// 自定义优化
    Custom,
}

impl EngineOptimization {
    pub fn basic() -> Self {
        Self {
            level: OptimizationLevel::Basic,
            custom_config: None,
        }
    }
    
    pub fn standard() -> Self {
        Self {
            level: OptimizationLevel::Standard,
            custom_config: None,
        }
    }
    
    pub fn high_performance() -> Self {
        Self {
            level: OptimizationLevel::HighPerformance,
            custom_config: None,
        }
    }
    
    pub fn custom(config: BattleEngineConfig) -> Self {
        Self {
            level: OptimizationLevel::Custom,
            custom_config: Some(config),
        }
    }
    
    pub fn to_config(&self) -> BattleEngineConfig {
        match self.level {
            OptimizationLevel::Basic => BattleEngineConfig {
                enable_pooling: false,
                enable_concurrency: false,
                enable_caching: false,
                enable_ai_optimization: false,
                ..Default::default()
            },
            OptimizationLevel::Standard => BattleEngineConfig::default(),
            OptimizationLevel::HighPerformance => BattleEngineConfig {
                pool_size: 2000,
                worker_threads: 8,
                cache_size_mb: 128,
                memory_budget_mb: 512,
                ..Default::default()
            },
            OptimizationLevel::Custom => {
                self.custom_config.clone().unwrap_or_default()
            }
        }
    }
}

/// 基准测试套件
pub struct BenchmarkSuite;

impl BenchmarkSuite {
    /// 运行完整基准测试
    pub fn run_full_benchmark() -> BenchmarkResults {
        let mut engine = OptimizedBattleEngine::new(BattleEngineConfig::default());
        engine.run_benchmark(BenchmarkConfig::default())
    }
    
    /// 运行快速基准测试
    pub fn run_quick_benchmark() -> BenchmarkResults {
        let mut engine = OptimizedBattleEngine::new(BattleEngineConfig::default());
        let config = BenchmarkConfig {
            skill_execution_iterations: 100,
            ai_decision_iterations: 50,
            pooling_iterations: 200,
            memory_test_iterations: 10,
        };
        engine.run_benchmark(config)
    }
    
    /// 比较不同优化级别的性能
    pub fn compare_optimization_levels() -> HashMap<OptimizationLevel, BenchmarkResults> {
        let mut results = HashMap::new();
        
        for level in [
            OptimizationLevel::Basic,
            OptimizationLevel::Standard,
            OptimizationLevel::HighPerformance,
        ] {
            let optimization = match level {
                OptimizationLevel::Basic => EngineOptimization::basic(),
                OptimizationLevel::Standard => EngineOptimization::standard(),
                OptimizationLevel::HighPerformance => EngineOptimization::high_performance(),
                _ => continue,
            };
            
            let mut engine = OptimizedBattleEngine::new(optimization.to_config());
            let benchmark_result = engine.run_benchmark(BenchmarkConfig::default());
            results.insert(level, benchmark_result);
        }
        
        results
    }
}