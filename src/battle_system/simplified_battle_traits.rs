/// 代表实体可以随时间更新其内部状态
pub trait TimeAware {
    /// 根据经过的时间更新内部状态
    ///
    /// # Arguments
    ///
    /// * `delta_seconds`: 自上次更新以来经过的秒数
    fn update(&mut self, delta_seconds: f32);
}
/// 简化的战斗系统trait定义
/// 
/// 将原来的8个复杂trait简化为4个核心trait，遵循接口隔离原则和单一职责原则
/// 同时保持与现有系统的完全向后兼容

use crate::shared::*;
use crate::skill::buff::Buff;

// ============================================================================
// 核心简化Trait定义
// ============================================================================

/// 战斗实体 - 基础战斗单位信息
/// 
/// 包含所有战斗单位都必须具备的基本信息
pub trait BattleEntity {
    /// 获取唯一标识符
    fn entity_id(&self) -> BattleUnitId;
    
    /// 获取显示名称
    fn display_name(&self) -> &str;
    
    /// 获取等级
    fn level(&self) -> Level;
    
    /// 获取当前位置
    fn position(&self) -> Position;
    
    /// 设置位置（可选实现）
    fn set_position(&mut self, position: Position) -> GameResult<()> {
        // 默认实现：不支持移动的单位
        Err(GameError::validation_error("movement", "此单位不支持移动"))
    }
}

/// 生命力 - 生命值和生存状态管理
/// 
/// 管理单位的生命值、法力值和基本生存状态
pub trait LifeForce {
    /// 获取当前生命值
    fn current_health(&self) -> Health;
    
    /// 获取最大生命值
    fn max_health(&self) -> Health;
    
    /// 获取当前法力值（可选）
    fn current_mana(&self) -> Mana {
        0 // 默认无法力值
    }
    
    /// 获取最大法力值（可选）
    fn max_mana(&self) -> Mana {
        0 // 默认无法力值
    }
    
    /// 检查是否存活
    fn is_alive(&self) -> bool {
        self.current_health() > 0
    }
    
    /// 获取生命值百分比
    fn health_percentage(&self) -> f32 {
        let max_hp = self.max_health();
        if max_hp == 0 {
            0.0
        } else {
            (self.current_health() as f32) / (max_hp as f32)
        }
    }
    
    /// 受到伤害
    fn take_damage(&mut self, damage: Health) -> GameResult<DamageResult>;
    
    /// 恢复生命值
    fn heal(&mut self, amount: Health) -> GameResult<HealResult>;
    
    /// 消耗法力（可选）
    fn consume_mana(&mut self, amount: Mana) -> GameResult<()> {
        if amount == 0 {
            return Ok(());
        }
        Err(GameError::validation_error("mana", "此单位没有法力值"))
    }
    
    /// 恢复法力（可选）
    fn restore_mana(&mut self, amount: Mana) -> GameResult<()> {
        if amount == 0 {
            return Ok(());
        }
        Err(GameError::validation_error("mana", "此单位没有法力值"))
    }
}

/// 战斗能力 - 攻击、防御和移动能力
/// 
/// 定义单位的基本战斗属性和行动能力
pub trait CombatCapable {
    /// 获取攻击力
    fn attack_power(&self) -> Attack;
    
    /// 获取防御力
    fn defense_power(&self) -> Defense;
    
    /// 获取移动速度
    fn movement_speed(&self) -> Speed;
    
    /// 获取攻击范围
    fn attack_range(&self) -> Range;

    /// 检查是否能到达目标
    fn can_reach(&self, target: &dyn BattleEntity) -> bool where Self: Sized + BattleEntity {
        is_within_attack_range(self, target)
    }
    
    /// 获取行动速度（用于动作条系统）
    fn action_speed(&self) -> Speed {
        self.movement_speed() // 默认使用移动速度
    }
    
    /// 检查是否能移动
    fn can_move(&self) -> bool {
        true // 默认可以移动
    }
    
    /// 检查是否能攻击
    fn can_attack(&self) -> bool {
        true // 默认可以攻击
    }
    
    /// 检查是否能施法
    fn can_cast_skills(&self) -> bool {
        true // 默认可以施法
    }
}

/// 技能使用者 - 技能系统和状态效果（可选）
/// 
/// 只有需要技能系统的单位才需要实现此trait
pub trait SkillUser {
    /// 获取已学技能列表
    fn learned_skills(&self) -> Vec<SkillId>;
    
    /// 获取技能冷却时间
    fn skill_cooldown(&self, skill_id: &SkillId) -> f64;
    
    /// 检查技能是否可用（综合检查，如冷却、法力等）
    fn can_use_skill(&self, skill_id: &SkillId) -> bool {
        self.is_skill_ready(skill_id) // 默认只检查冷却
    }

    /// 使用技能
    fn use_skill(&mut self, skill_id: &SkillId) -> GameResult<()>;

    /// 检查技能是否已冷却
    fn is_skill_ready(&self, skill_id: &SkillId) -> bool {
        self.skill_cooldown(skill_id) <= 0.0
    }
    
    /// 设置技能冷却
    fn set_skill_cooldown(&mut self, skill_id: SkillId, cooldown: f64) -> GameResult<()>;
    
    /// 更新技能冷却（游戏循环调用）
    fn update_skill_cooldowns(&mut self, delta_time: f32);
    
    /// 获取当前状态效果（可选）
    fn active_statuses(&self) -> Vec<String> {
        Vec::new() // 默认无状态
    }
    
    /// 添加状态效果（可选）
    fn add_status(&mut self, status: String) -> GameResult<()> {
        // 默认实现：忽略状态效果
        Ok(())
    }

    /// 移除状态效果（可选）
    fn remove_status(&mut self, status: &str) -> GameResult<()> {
        // 默认实现：忽略状态效果
        let _ = status;
        Ok(())
    }
    
    /// 添加一个Buff
    fn add_buff(&mut self, buff: Buff) -> GameResult<()>;

    /// 获取所有激活的Buff
    fn get_buffs(&self) -> Vec<Buff>;
}

// ============================================================================
// 独立辅助函数 (用于对象安全)
// ============================================================================

/// 计算两个战斗实体之间的距离
pub fn distance_between<T: BattleEntity + ?Sized, U: BattleEntity + ?Sized>(unit1: &T, unit2: &U) -> f32 {
    unit1.position().distance_to(&unit2.position())
}

/// 检查一个战斗实体是否可以够到另一个实体
pub fn is_within_attack_range<T: BattleEntity + CombatCapable, U: BattleEntity + ?Sized>(attacker: &T, target: &U) -> bool {
    distance_between(attacker, target) <= attacker.attack_range()
}

// ============================================================================
// 组合trait和便捷类型
// ============================================================================

/// 完整战斗单位 - 具备所有战斗能力的单位
pub trait FullBattleUnit: BattleEntity + LifeForce + CombatCapable + SkillUser {
    /// 获取战斗力评分
    fn combat_rating(&self) -> u32 {
        let health_score = (self.max_health() as u32) / 10;
        let attack_score = self.attack_power() as u32;
        let defense_score = self.defense_power() as u32;
        let level_score = self.level() * 10;
        
        health_score + attack_score + defense_score + level_score
    }
    
    /// 获取完整战斗状态
    fn battle_status(&self) -> BattleStatus {
        BattleStatus {
            entity_id: self.entity_id(),
            name: self.display_name().to_string(),
            health: (self.current_health(), self.max_health()),
            mana: (self.current_mana(), self.max_mana()),
            position: self.position(),
            is_alive: self.is_alive(),
            combat_rating: self.combat_rating(),
            active_statuses: self.active_statuses(),
        }
    }
}

/// 基础战斗单位 - 不需要技能系统的简单单位
pub trait BasicBattleUnit: BattleEntity + LifeForce + CombatCapable {
    /// 获取基础战斗状态
    fn basic_status(&self) -> BasicBattleStatus {
        BasicBattleStatus {
            entity_id: self.entity_id(),
            name: self.display_name().to_string(),
            health: (self.current_health(), self.max_health()),
            position: self.position(),
            is_alive: self.is_alive(),
            attack_power: self.attack_power(),
            defense_power: self.defense_power(),
        }
    }
}

// ============================================================================
// 数据结构
// ============================================================================

/// 伤害结果
#[derive(Debug, Clone, Copy, PartialEq, Eq, Default)]
pub struct DamageResult {
    pub actual_damage: Health,
    pub is_critical: bool,
    pub is_miss: bool,
    pub was_fatal: bool,
}

impl DamageResult {
    pub fn new(actual_damage: Health, remaining_health: Health, is_critical: bool, is_miss: bool) -> Self {
        Self {
            actual_damage,
            is_critical,
            is_miss,
            was_fatal: remaining_health <= 0 && !is_miss,
        }
    }
}

/// 治疗结果
#[derive(Debug, Clone, Copy, PartialEq, Eq, Default)]
pub struct HealResult {
    pub actual_heal: Health,
    pub is_critical: bool,
}

impl HealResult {
    pub fn new(actual_heal: Health, is_critical: bool) -> Self {
        Self {
            actual_heal,
            is_critical,
        }
    }
}

/// 完整战斗状态
#[derive(Debug, Clone)]
pub struct BattleStatus {
    pub entity_id: BattleUnitId,
    pub name: String,
    pub health: (Health, Health), // (current, max)
    pub mana: (Mana, Mana),       // (current, max)
    pub position: Position,
    pub is_alive: bool,
    pub combat_rating: u32,
    pub active_statuses: Vec<String>,
}

/// 基础战斗状态
#[derive(Debug, Clone)]
pub struct BasicBattleStatus {
    pub entity_id: BattleUnitId,
    pub name: String,
    pub health: (Health, Health), // (current, max)
    pub position: Position,
    pub is_alive: bool,
    pub attack_power: Attack,
    pub defense_power: Defense,
}

// ============================================================================
// Blanket Implementations for Dynamic Dispatch
// ============================================================================

impl<'a, T: ?Sized + BattleEntity> BattleEntity for &'a T {
    fn entity_id(&self) -> BattleUnitId { (*self).entity_id() }
    fn display_name(&self) -> &str { (*self).display_name() }
    fn level(&self) -> Level { (*self).level() }
    fn position(&self) -> Position { (*self).position() }
}

impl<T: ?Sized + BattleEntity> BattleEntity for Box<T> {
    fn entity_id(&self) -> BattleUnitId { (**self).entity_id() }
    fn display_name(&self) -> &str { (**self).display_name() }
    fn level(&self) -> Level { (**self).level() }
    fn position(&self) -> Position { (**self).position() }
    fn set_position(&mut self, position: Position) -> GameResult<()> { (**self).set_position(position) }
}

impl<T: ?Sized + BattleEntity> BattleEntity for &mut T {
    fn entity_id(&self) -> BattleUnitId { (**self).entity_id() }
    fn display_name(&self) -> &str { (**self).display_name() }
    fn level(&self) -> Level { (**self).level() }
    fn position(&self) -> Position { (**self).position() }
    fn set_position(&mut self, position: Position) -> GameResult<()> { (**self).set_position(position) }
}


impl<'a, T: ?Sized + LifeForce> LifeForce for &'a T {
    fn current_health(&self) -> Health { (*self).current_health() }
    fn max_health(&self) -> Health { (*self).max_health() }
    fn current_mana(&self) -> Mana { (*self).current_mana() }
    fn max_mana(&self) -> Mana { (*self).max_mana() }
    fn take_damage(&mut self, _damage: Health) -> GameResult<DamageResult> { panic!("Cannot call mutable method on an immutable reference"); }
    fn heal(&mut self, _amount: Health) -> GameResult<HealResult> { panic!("Cannot call mutable method on an immutable reference"); }
}

impl<T: ?Sized + LifeForce> LifeForce for Box<T> {
    fn current_health(&self) -> Health { (**self).current_health() }
    fn max_health(&self) -> Health { (**self).max_health() }
    fn is_alive(&self) -> bool { (**self).is_alive() }
    fn take_damage(&mut self, damage: Health) -> GameResult<DamageResult> { (**self).take_damage(damage) }
    fn heal(&mut self, amount: Health) -> GameResult<HealResult> { (**self).heal(amount) }
    fn current_mana(&self) -> Mana { (**self).current_mana() }
    fn max_mana(&self) -> Mana { (**self).max_mana() }
    fn consume_mana(&mut self, amount: Mana) -> GameResult<()> { (**self).consume_mana(amount) }
    fn restore_mana(&mut self, amount: Mana) -> GameResult<()> { (**self).restore_mana(amount) }
}

impl<T: ?Sized + LifeForce> LifeForce for &mut T {
    fn current_health(&self) -> Health { (**self).current_health() }
    fn max_health(&self) -> Health { (**self).max_health() }
    fn is_alive(&self) -> bool { (**self).is_alive() }
    fn take_damage(&mut self, damage: Health) -> GameResult<DamageResult> { (**self).take_damage(damage) }
    fn heal(&mut self, amount: Health) -> GameResult<HealResult> { (**self).heal(amount) }
    fn current_mana(&self) -> Mana { (**self).current_mana() }
    fn max_mana(&self) -> Mana { (**self).max_mana() }
    fn consume_mana(&mut self, amount: Mana) -> GameResult<()> { (**self).consume_mana(amount) }
    fn restore_mana(&mut self, amount: Mana) -> GameResult<()> { (**self).restore_mana(amount) }
}


impl<'a, T: ?Sized + CombatCapable> CombatCapable for &'a T {
    fn attack_power(&self) -> Attack { (*self).attack_power() }
    fn defense_power(&self) -> Defense { (*self).defense_power() }
    fn movement_speed(&self) -> Speed { (*self).movement_speed() }
    fn attack_range(&self) -> Range { (*self).attack_range() }
}

impl<T: ?Sized + CombatCapable> CombatCapable for Box<T> {
    fn attack_power(&self) -> Attack { (**self).attack_power() }
    fn defense_power(&self) -> Defense { (**self).defense_power() }
    fn movement_speed(&self) -> Speed { (**self).movement_speed() }
    fn attack_range(&self) -> Range { (**self).attack_range() }
}

impl<T: ?Sized + CombatCapable> CombatCapable for &mut T {
    fn attack_power(&self) -> Attack { (**self).attack_power() }
    fn defense_power(&self) -> Defense { (**self).defense_power() }
    fn movement_speed(&self) -> Speed { (**self).movement_speed() }
    fn attack_range(&self) -> Range { (**self).attack_range() }
}


impl<'a, T: ?Sized + SkillUser> SkillUser for &'a T {
    fn learned_skills(&self) -> Vec<SkillId> { (*self).learned_skills() }
    fn skill_cooldown(&self, skill_id: &SkillId) -> f64 { (*self).skill_cooldown(skill_id) }
    fn get_buffs(&self) -> Vec<Buff> { (*self).get_buffs() }
    fn use_skill(&mut self, _skill_id: &SkillId) -> GameResult<()> { panic!("Cannot call mutable method on an immutable reference"); }
    fn set_skill_cooldown(&mut self, _skill_id: SkillId, _cooldown: f64) -> GameResult<()> { panic!("Cannot call mutable method on an immutable reference"); }
    fn update_skill_cooldowns(&mut self, _delta_time: f32) { panic!("Cannot call mutable method on an immutable reference"); }
    fn add_buff(&mut self, _buff: Buff) -> GameResult<()> { panic!("Cannot call mutable method on an immutable reference"); }
}

impl<T: ?Sized + SkillUser> SkillUser for Box<T> {
    fn learned_skills(&self) -> Vec<SkillId> { (**self).learned_skills() }
    fn skill_cooldown(&self, skill_id: &SkillId) -> f64 { (**self).skill_cooldown(skill_id) }
    fn is_skill_ready(&self, skill_id: &SkillId) -> bool { (**self).is_skill_ready(skill_id) }
    fn use_skill(&mut self, skill_id: &SkillId) -> GameResult<()> { (**self).use_skill(skill_id) }
    fn set_skill_cooldown(&mut self, skill_id: SkillId, cooldown: f64) -> GameResult<()> { (**self).set_skill_cooldown(skill_id, cooldown) }
    fn update_skill_cooldowns(&mut self, delta_time: f32) { (**self).update_skill_cooldowns(delta_time) }
    fn add_buff(&mut self, buff: Buff) -> GameResult<()> { (**self).add_buff(buff) }
    fn get_buffs(&self) -> Vec<Buff> { (**self).get_buffs() }
}

impl<T: ?Sized + SkillUser> SkillUser for &mut T {
    fn learned_skills(&self) -> Vec<SkillId> { (**self).learned_skills() }
    fn skill_cooldown(&self, skill_id: &SkillId) -> f64 { (**self).skill_cooldown(skill_id) }
    fn is_skill_ready(&self, skill_id: &SkillId) -> bool { (**self).is_skill_ready(skill_id) }
    fn use_skill(&mut self, skill_id: &SkillId) -> GameResult<()> { (**self).use_skill(skill_id) }
    fn set_skill_cooldown(&mut self, skill_id: SkillId, cooldown: f64) -> GameResult<()> { (**self).set_skill_cooldown(skill_id, cooldown) }
    fn update_skill_cooldowns(&mut self, delta_time: f32) { (**self).update_skill_cooldowns(delta_time) }
    fn add_buff(&mut self, buff: Buff) -> GameResult<()> { (**self).add_buff(buff) }
    fn get_buffs(&self) -> Vec<Buff> { (**self).get_buffs() }
}

impl<T: ?Sized + FullBattleUnit> FullBattleUnit for Box<T> {}
impl<T: ?Sized + FullBattleUnit> FullBattleUnit for &mut T {}
impl<'a, T: ?Sized + FullBattleUnit> FullBattleUnit for &'a T {}

impl<T: ?Sized + BasicBattleUnit> BasicBattleUnit for Box<T> {}
impl<T: ?Sized + BasicBattleUnit> BasicBattleUnit for &mut T {}
impl<'a, T: ?Sized + BasicBattleUnit> BasicBattleUnit for &'a T {}
