/// 增强的位置值对象
/// 支持多层次世界的完整定位和各种空间计算
use serde::{Deserialize, Serialize};
use crate::world_map::domain::spatial::{WorldLayer, RegionBoundaries};
use crate::world_map::domain::ids::RegionId;

#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct Position {
    /// X坐标（使用整数避免浮点精度问题）
    pub x: i32,
    /// Y坐标
    pub y: i32,
    /// Z坐标（可选，用于3D世界）
    pub z: Option<i32>,
    /// 世界层次
    pub layer: WorldLayer,
    /// 所属区域ID（可选）
    pub region_id: Option<RegionId>,
}

impl Position {
    /// 创建2D坐标
    pub fn new_2d(x: i32, y: i32, layer: WorldLayer) -> Self {
        Self {
            x,
            y,
            z: None,
            layer,
            region_id: None,
        }
    }

    /// 创建3D坐标
    pub fn new_3d(x: i32, y: i32, z: i32, layer: WorldLayer) -> Self {
        Self {
            x,
            y,
            z: Some(z),
            layer,
            region_id: None,
        }
    }
    
    /// 创建带区域信息的位置
    pub fn with_region(mut self, region_id: RegionId) -> Self {
        self.region_id = Some(region_id);
        self
    }

    /// 判断是否为3D坐标
    pub fn is_3d(&self) -> bool {
        self.z.is_some()
    }
    
    /// 计算到另一个位置的距离
    pub fn distance_to(&self, other: &Position) -> f64 {
        // 只有在同一层级才能计算距离
        if self.layer != other.layer {
            return f64::INFINITY;
        }
        
        let dx = (other.x - self.x) as f64;
        let dy = (other.y - self.y) as f64;
        
        match (self.z, other.z) {
            (Some(z1), Some(z2)) => {
                let dz = (z2 - z1) as f64;
                (dx * dx + dy * dy + dz * dz).sqrt()
            }
            (None, None) => (dx * dx + dy * dy).sqrt(),
            _ => f64::INFINITY, // 维度不匹配
        }
    }
    
    /// 计算曼哈顿距离（网格移动距离）
    pub fn manhattan_distance_to(&self, other: &Position) -> u32 {
        if self.layer != other.layer {
            return u32::MAX;
        }
        
        let dx = (other.x - self.x).abs() as u32;
        let dy = (other.y - self.y).abs() as u32;
        
        match (self.z, other.z) {
            (Some(z1), Some(z2)) => {
                let dz = (z2 - z1).abs() as u32;
                dx + dy + dz
            }
            (None, None) => dx + dy,
            _ => u32::MAX,
        }
    }
    
    /// 检查是否在指定区域边界内
    pub fn is_within_bounds(&self, boundaries: &RegionBoundaries) -> bool {
        boundaries.contains(self.x, self.y, self.z)
    }
    
    /// 获取周围的位置（8方向，2D情况下）
    pub fn get_adjacent_positions(&self) -> Vec<Position> {
        let mut positions = Vec::new();
        
        for dx in -1..=1 {
            for dy in -1..=1 {
                if dx == 0 && dy == 0 {
                    continue; // 跳过自己
                }
                
                let new_pos = Position {
                    x: self.x + dx,
                    y: self.y + dy,
                    z: self.z,
                    layer: self.layer,
                    region_id: self.region_id,
                };
                positions.push(new_pos);
            }
        }
        
        positions
    }
    
    /// 获取指定半径内的所有位置
    pub fn get_positions_within_radius(&self, radius: u32) -> Vec<Position> {
        let mut positions = Vec::new();
        let r = radius as i32;
        
        for dx in -r..=r {
            for dy in -r..=r {
                let distance_sq = dx * dx + dy * dy;
                if distance_sq <= (r * r) {
                    let new_pos = Position {
                        x: self.x + dx,
                        y: self.y + dy,
                        z: self.z,
                        layer: self.layer,
                        region_id: self.region_id,
                    };
                    positions.push(new_pos);
                }
            }
        }
        
        positions
    }
    
    /// 向指定方向移动
    pub fn move_in_direction(&self, direction: Direction, distance: u32) -> Position {
        let dist = distance as i32;
        match direction {
            Direction::North => Position {
                x: self.x,
                y: self.y + dist,
                ..*self
            },
            Direction::South => Position {
                x: self.x,
                y: self.y - dist,
                ..*self
            },
            Direction::East => Position {
                x: self.x + dist,
                y: self.y,
                ..*self
            },
            Direction::West => Position {
                x: self.x - dist,
                y: self.y,
                ..*self
            },
            Direction::Northeast => Position {
                x: self.x + dist,
                y: self.y + dist,
                ..*self
            },
            Direction::Northwest => Position {
                x: self.x - dist,
                y: self.y + dist,
                ..*self
            },
            Direction::Southeast => Position {
                x: self.x + dist,
                y: self.y - dist,
                ..*self
            },
            Direction::Southwest => Position {
                x: self.x - dist,
                y: self.y - dist,
                ..*self
            },
            Direction::Up => Position {
                z: self.z.map(|z| z + dist).or(Some(dist)),
                ..*self
            },
            Direction::Down => Position {
                z: self.z.map(|z| z - dist).or(Some(-dist)),
                ..*self
            },
        }
    }
    
    /// 获取到另一个位置的方向
    pub fn direction_to(&self, other: &Position) -> Option<Direction> {
        if self.layer != other.layer {
            return None;
        }
        
        let dx = other.x - self.x;
        let dy = other.y - self.y;
        
        // 处理3D情况下的Z轴
        if let (Some(z1), Some(z2)) = (self.z, other.z) {
            let dz = z2 - z1;
            if dz.abs() > dx.abs().max(dy.abs()) {
                return if dz > 0 { Some(Direction::Up) } else { Some(Direction::Down) };
            }
        }
        
        // 2D方向计算
        match (dx.signum(), dy.signum()) {
            (0, 1) => Some(Direction::North),
            (0, -1) => Some(Direction::South),
            (1, 0) => Some(Direction::East),
            (-1, 0) => Some(Direction::West),
            (1, 1) => Some(Direction::Northeast),
            (-1, 1) => Some(Direction::Northwest),
            (1, -1) => Some(Direction::Southeast),
            (-1, -1) => Some(Direction::Southwest),
            _ => None,
        }
    }
    
    /// 检查是否可以传送到目标位置
    pub fn can_teleport_to(&self, target: &Position) -> bool {
        self.layer.can_teleport_to(&target.layer)
    }
    
    /// 转换为字符串表示
    pub fn to_coordinate_string(&self) -> String {
        match self.z {
            Some(z) => format!("({}, {}, {}) @ {}", self.x, self.y, z, self.layer),
            None => format!("({}, {}) @ {}", self.x, self.y, self.layer),
        }
    }
}

/// 方向枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Direction {
    North,
    South,
    East,
    West,
    Northeast,
    Northwest,
    Southeast,
    Southwest,
    Up,
    Down,
}

impl Direction {
    /// 获取相反方向
    pub fn opposite(&self) -> Direction {
        match self {
            Direction::North => Direction::South,
            Direction::South => Direction::North,
            Direction::East => Direction::West,
            Direction::West => Direction::East,
            Direction::Northeast => Direction::Southwest,
            Direction::Northwest => Direction::Southeast,
            Direction::Southeast => Direction::Northwest,
            Direction::Southwest => Direction::Northeast,
            Direction::Up => Direction::Down,
            Direction::Down => Direction::Up,
        }
    }
    
    /// 获取所有水平方向
    pub fn horizontal_directions() -> Vec<Direction> {
        vec![
            Direction::North,
            Direction::South,
            Direction::East,
            Direction::West,
            Direction::Northeast,
            Direction::Northwest,
            Direction::Southeast,
            Direction::Southwest,
        ]
    }
    
    /// 获取所有基本方向（4个）
    pub fn cardinal_directions() -> Vec<Direction> {
        vec![
            Direction::North,
            Direction::South,
            Direction::East,
            Direction::West,
        ]
    }
}

impl std::fmt::Display for Direction {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let name = match self {
            Direction::North => "北",
            Direction::South => "南",
            Direction::East => "东",
            Direction::West => "西",
            Direction::Northeast => "东北",
            Direction::Northwest => "西北",
            Direction::Southeast => "东南",
            Direction::Southwest => "西南",
            Direction::Up => "上",
            Direction::Down => "下",
        };
        write!(f, "{}", name)
    }
}

// 为Position实现默认值
impl Default for Position {
    fn default() -> Self {
        Self::new_2d(0, 0, WorldLayer::Mortal)
    }
}

// 为Position实现Display trait
impl std::fmt::Display for Position {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.to_coordinate_string())
    }
}