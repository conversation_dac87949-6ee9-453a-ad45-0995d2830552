/// 基础组件实现
/// 
/// 提供战斗单位的基础数据结构和组件

use crate::shared::*;

use crate::skill::buff::Buff;
use std::collections::HashMap;

/// 身份信息组件
#[derive(Debug, Clone)]
pub struct IdentityComponent {
    pub entity_id: BattleUnitId,
    pub display_name: String,
    pub entity_type: EntityType,
    pub faction: Faction,
    pub level: Level,
    pub experience: crate::Exp,
    pub rarity: Rarity,
    pub is_player_controlled: bool,
}

impl IdentityComponent {
    pub fn new(
        entity_id: BattleUnitId,
        display_name: String,
        entity_type: EntityType,
        faction: Faction,
        level: Level,
    ) -> Self {
        Self {
            entity_id,
            display_name,
            entity_type,
            faction,
            level,
            experience: 0,
            rarity: Rarity::Common,
            is_player_controlled: matches!(faction, Faction::Player),
        }
    }
    
    pub fn with_experience(mut self, experience: crate::Exp) -> Self {
        self.experience = experience;
        self
    }
    
    pub fn with_rarity(mut self, rarity: Rarity) -> Self {
        self.rarity = rarity;
        self
    }
}

/// 位置信息组件
#[derive(Debug, Clone)]
pub struct SpatialComponent {
    pub position: Position,
    pub facing_direction: Direction,
    pub occupied_space: SpaceSize,
    pub last_position: Position,
    pub movement_history: Vec<(Position, GameTime)>,
}

impl SpatialComponent {
    pub fn new(position: Position) -> Self {
        Self {
            position,
            facing_direction: Direction::North,
            occupied_space: SpaceSize::Small,
            last_position: position,
            movement_history: Vec::new(),
        }
    }
    
    pub fn update_position(&mut self, new_position: Position, current_time: GameTime) {
        self.last_position = self.position;
        self.position = new_position;
        self.movement_history.push((new_position, current_time));
        
        // 保持历史记录在合理范围内
        if self.movement_history.len() > 100 {
            self.movement_history.remove(0);
        }
    }
    
    pub fn calculate_distance_to(&self, other_position: Position) -> f32 {
        let dx = self.position.x - other_position.x;
        let dy = self.position.y - other_position.y;
        (dx * dx + dy * dy).sqrt()
    }
}

/// 时间信息组件
#[derive(Debug, Clone)]
pub struct TemporalComponent {
    pub creation_time: GameTime,
    pub is_temporary: bool,
    pub lifetime_limit: Option<f32>,
    pub age_accumulated: f32,
}

impl TemporalComponent {
    pub fn new(creation_time: GameTime) -> Self {
        Self {
            creation_time,
            is_temporary: false,
            lifetime_limit: None,
            age_accumulated: 0.0,
        }
    }
    
    pub fn with_lifetime(mut self, lifetime: f32) -> Self {
        self.is_temporary = true;
        self.lifetime_limit = Some(lifetime);
        self
    }
    
    pub fn update(&mut self, delta_time: f32) -> bool {
        self.age_accumulated += delta_time;
        
        if let Some(limit) = self.lifetime_limit {
            self.age_accumulated < limit
        } else {
            true
        }
    }
    
    pub fn current_age(&self, current_time: GameTime) -> f32 {
        (current_time - self.creation_time) as f32
    }
}

/// 生命值组件
#[derive(Debug, Clone)]
pub struct HealthComponent {
    pub current: Health,
    pub maximum: Health,
    pub base_value: Health,
    pub bonus: Health,
    pub regeneration_rate: Health,
    pub damage_history: Vec<DamageRecord>,
    pub heal_history: Vec<HealRecord>,
}

impl HealthComponent {
    pub fn new(base_health: Health) -> Self {
        Self {
            current: base_health,
            maximum: base_health,
            base_value: base_health,
            bonus: 0,
            regeneration_rate: base_health / 100, // 1% per second
            damage_history: Vec::new(),
            heal_history: Vec::new(),
        }
    }
    
    pub fn update_maximum(&mut self) {
        self.maximum = self.base_value + self.bonus;
        if self.current > self.maximum {
            self.current = self.maximum;
        }
    }
    
    pub fn apply_damage(&mut self, damage: Health, damage_type: DamageType, source: Option<BattleUnitId>) -> Health {
        let actual_damage = damage.min(self.current);
        self.current -= actual_damage;
        
        self.damage_history.push(DamageRecord {
            amount: actual_damage,
            damage_type,
            source,
            timestamp: std::time::SystemTime::now(),
        });
        
        // 保持历史记录大小
        if self.damage_history.len() > 50 {
            self.damage_history.remove(0);
        }
        
        actual_damage
    }
    
    pub fn apply_heal(&mut self, amount: Health, source: Option<BattleUnitId>) -> Health {
        let actual_heal = amount.min(self.maximum - self.current);
        self.current += actual_heal;
        
        self.heal_history.push(HealRecord {
            amount: actual_heal,
            source,
            timestamp: std::time::SystemTime::now(),
        });
        
        // 保持历史记录大小
        if self.heal_history.len() > 50 {
            self.heal_history.remove(0);
        }
        
        actual_heal
    }
    
    pub fn regenerate(&mut self, delta_time: f32) -> Health {
        if self.current < self.maximum && self.regeneration_rate > 0 {
            let regen_amount = (self.regeneration_rate as f32 * delta_time) as Health;
            self.apply_heal(regen_amount, None)
        } else {
            0
        }
    }
    
    pub fn health_percentage(&self) -> f32 {
        if self.maximum > 0 {
            self.current as f32 / self.maximum as f32
        } else {
            0.0
        }
    }
}

/// 法力值组件
#[derive(Debug, Clone)]
pub struct ManaComponent {
    pub current: Mana,
    pub maximum: Mana,
    pub base_value: Mana,
    pub bonus: Mana,
    pub regeneration_rate: Mana,
    pub cost_reduction: f32,
}

impl ManaComponent {
    pub fn new(base_mana: Mana) -> Self {
        Self {
            current: base_mana,
            maximum: base_mana,
            base_value: base_mana,
            bonus: 0,
            regeneration_rate: base_mana / 50, // 2% per second
            cost_reduction: 0.0,
        }
    }
    
    pub fn update_maximum(&mut self) {
        self.maximum = self.base_value + self.bonus;
        if self.current > self.maximum {
            self.current = self.maximum;
        }
    }
    
    pub fn consume(&mut self, amount: Mana) -> bool {
        let reduced_cost = (amount as f32 * (1.0 - self.cost_reduction)) as Mana;
        if self.current >= reduced_cost {
            self.current -= reduced_cost;
            true
        } else {
            false
        }
    }
    
    pub fn restore(&mut self, amount: Mana) {
        self.current = (self.current + amount).min(self.maximum);
    }
    
    pub fn regenerate(&mut self, delta_time: f32) {
        if self.current < self.maximum && self.regeneration_rate > 0 {
            let regen_amount = (self.regeneration_rate as f32 * delta_time) as Mana;
            self.restore(regen_amount);
        }
    }
    
    pub fn mana_percentage(&self) -> f32 {
        if self.maximum > 0 {
            self.current as f32 / self.maximum as f32
        } else {
            0.0
        }
    }
}

/// 耐力组件
#[derive(Debug, Clone)]
pub struct StaminaComponent {
    pub current: Stamina,
    pub maximum: Stamina,
    pub regeneration_rate: Stamina,
    pub fatigue_threshold: f32,
}

impl StaminaComponent {
    pub fn new(max_stamina: Stamina) -> Self {
        Self {
            current: max_stamina,
            maximum: max_stamina,
            regeneration_rate: max_stamina / 20, // 5% per second
            fatigue_threshold: 0.3, // 30% threshold for exhaustion
        }
    }
    
    pub fn consume(&mut self, amount: Stamina) -> bool {
        if self.current >= amount {
            self.current -= amount;
            true
        } else {
            false
        }
    }
    
    pub fn restore(&mut self, amount: Stamina) {
        self.current = (self.current + amount).min(self.maximum);
    }
    
    pub fn regenerate(&mut self, delta_time: f32) {
        if self.current < self.maximum && self.regeneration_rate > 0 {
            let regen_amount = (self.regeneration_rate as f32 * delta_time) as Stamina;
            self.restore(regen_amount);
        }
    }
    
    pub fn fatigue_level(&self) -> FatigueLevel {
        let percentage = self.current as f32 / self.maximum as f32;
        
        if percentage <= 0.1 {
            FatigueLevel::Exhausted
        } else if percentage <= 0.3 {
            FatigueLevel::Tired
        } else if percentage <= 0.6 {
            FatigueLevel::Fatigued
        } else {
            FatigueLevel::Fresh
        }
    }
    
    pub fn is_exhausted(&self) -> bool {
        matches!(self.fatigue_level(), FatigueLevel::Exhausted)
    }
}

/// 属性组件
#[derive(Debug, Clone)]
pub struct AttributeComponent {
    pub base_attributes: HashMap<AttributeType, AttributeValue>,
    pub attribute_bonuses: HashMap<AttributeType, AttributeValue>,
    pub temporary_modifiers: HashMap<AttributeType, Vec<(AttributeValue, f32)>>, // (value, duration)
}

impl AttributeComponent {
    pub fn new() -> Self {
        let mut base_attributes = HashMap::new();
        
        // 初始化基础属性
        base_attributes.insert(AttributeType::Strength, 10);
        base_attributes.insert(AttributeType::Dexterity, 10);
        base_attributes.insert(AttributeType::Intelligence, 10);
        base_attributes.insert(AttributeType::Constitution, 10);
        base_attributes.insert(AttributeType::Charisma, 10);
        base_attributes.insert(AttributeType::Perception, 10);
        base_attributes.insert(AttributeType::Luck, 10);
        
        Self {
            base_attributes,
            attribute_bonuses: HashMap::new(),
            temporary_modifiers: HashMap::new(),
        }
    }
    
    pub fn with_base_attributes(mut self, attributes: HashMap<AttributeType, AttributeValue>) -> Self {
        self.base_attributes = attributes;
        self
    }
    
    pub fn get_attribute(&self, attribute_type: AttributeType) -> AttributeValue {
        let base = self.base_attributes.get(&attribute_type).copied().unwrap_or(10);
        let bonus = self.attribute_bonuses.get(&attribute_type).copied().unwrap_or(0);
        let temp_bonus: AttributeValue = self.temporary_modifiers
            .get(&attribute_type)
            .map(|modifiers| modifiers.iter().map(|(value, _)| value).sum())
            .unwrap_or(0);
        
        base + bonus + temp_bonus
    }
    
    pub fn add_bonus(&mut self, attribute_type: AttributeType, bonus: AttributeValue) {
        *self.attribute_bonuses.entry(attribute_type).or_insert(0) += bonus;
    }
    
    pub fn add_temporary_modifier(&mut self, attribute_type: AttributeType, value: AttributeValue, duration: f32) {
        self.temporary_modifiers
            .entry(attribute_type)
            .or_insert_with(Vec::new)
            .push((value, duration));
    }
    
    pub fn update_temporary_modifiers(&mut self, delta_time: f32) {
        for modifiers in self.temporary_modifiers.values_mut() {
            modifiers.retain_mut(|(_, duration)| {
                *duration -= delta_time;
                *duration > 0.0
            });
        }
        
        // 移除空的修饰符列表
        self.temporary_modifiers.retain(|_, modifiers| !modifiers.is_empty());
    }
    
    pub fn calculate_modifier(&self, attribute_type: AttributeType) -> i32 {
        let attribute_value = self.get_attribute(attribute_type);
        (attribute_value as i32 - 10) / 2
    }
    
    pub fn total_attributes(&self) -> AttributeValue {
        AttributeType::all_types()
            .iter()
            .map(|&attr_type| self.get_attribute(attr_type))
            .sum()
    }
}

/// 抗性组件
#[derive(Debug, Clone)]
pub struct ResistanceComponent {
    pub base_resistances: HashMap<DamageType, Resistance>,
    pub bonus_resistances: HashMap<DamageType, Resistance>,
    pub status_resistances: HashMap<StatusType, Resistance>,
}

impl ResistanceComponent {
    pub fn new() -> Self {
        Self {
            base_resistances: HashMap::new(),
            bonus_resistances: HashMap::new(),
            status_resistances: HashMap::new(),
        }
    }
    
    pub fn get_resistance(&self, damage_type: DamageType) -> Resistance {
        let base = self.base_resistances.get(&damage_type).copied().unwrap_or(0);
        let bonus = self.bonus_resistances.get(&damage_type).copied().unwrap_or(0);
        (base + bonus).min(95) // 最大95%抗性
    }
    
    pub fn get_status_resistance(&self, status_type: StatusType) -> Resistance {
        self.status_resistances.get(&status_type).copied().unwrap_or(0)
    }
    
    pub fn add_resistance(&mut self, damage_type: DamageType, resistance: Resistance) {
        *self.bonus_resistances.entry(damage_type).or_insert(0) += resistance;
    }
    
    pub fn calculate_damage_reduction(&self, damage_type: DamageType, damage: Health) -> Health {
        let resistance = self.get_resistance(damage_type);
        let reduction_factor = resistance as f32 / 100.0;
        (damage as f32 * (1.0 - reduction_factor)) as Health
    }
}

/// 行动组件
#[derive(Debug, Clone)]
pub struct ActionComponent {
    pub current_action_points: ActionPoints,
    pub maximum_action_points: ActionPoints,
    pub action_point_regeneration: ActionPoints,
    pub action_restrictions: Vec<ActionRestriction>,
    pub priority_modifier: i32,
}

impl ActionComponent {
    pub fn new(max_action_points: ActionPoints) -> Self {
        Self {
            current_action_points: max_action_points,
            maximum_action_points: max_action_points,
            action_point_regeneration: max_action_points / 10, // 10% per turn
            action_restrictions: Vec::new(),
            priority_modifier: 0,
        }
    }
    
    pub fn can_perform_action(&self, action_type: ActionType) -> bool {
        !self.action_restrictions.iter().any(|restriction| {
            restriction.blocks_action(action_type)
        })
    }
    
    pub fn consume_action_points(&mut self, amount: ActionPoints) -> bool {
        if self.current_action_points >= amount {
            self.current_action_points -= amount;
            true
        } else {
            false
        }
    }
    
    pub fn restore_action_points(&mut self, amount: ActionPoints) {
        self.current_action_points = (self.current_action_points + amount)
            .min(self.maximum_action_points);
    }
    
    pub fn regenerate_action_points(&mut self) {
        self.restore_action_points(self.action_point_regeneration);
    }
    
    pub fn add_restriction(&mut self, restriction: ActionRestriction) {
        self.action_restrictions.push(restriction);
    }
    
    pub fn remove_restriction(&mut self, restriction_type: ActionRestrictionType) {
        self.action_restrictions.retain(|r| r.restriction_type != restriction_type);
    }
    
    pub fn update_restrictions(&mut self, delta_time: f32) {
        self.action_restrictions.retain_mut(|restriction| {
            restriction.update(delta_time);
            !restriction.is_expired()
        });
    }
}