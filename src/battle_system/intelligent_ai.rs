/// 智能AI决策系统
/// 
/// 实现完整的AI战斗决策，包括：
/// - 技能选择策略
/// - 目标优先级系统
/// - 战术组合推荐
/// - 动态适应算法

use crate::shared::*;
use crate::battle_system::battle_view::BattleView;
use crate::battle_system::performance::ai_optimization::{
    IntelligentSkillSelector, TacticalCombinationEngine, BehaviorPredictor,
    AIDecisionCache, BattleContext, SkillRecommendation, ComboRecommendation,
    BattlePhase, AIStats,
};
use std::collections::HashMap;

/// 完整的AI决策引擎
pub struct IntelligentAI {
    /// 技能选择器
    skill_selector: IntelligentSkillSelector,
    /// 战术组合引擎
    combo_engine: TacticalCombinationEngine,
    /// 行为预测器
    behavior_predictor: BehaviorPredictor,
    /// 决策缓存
    decision_cache: AIDecisionCache,
    /// AI个性化配置
    personality: AIPersonality,
    /// 学习历史
    learning_history: LearningHistory,
}

impl IntelligentAI {
    /// 创建新的AI系统
    pub fn new(personality: AIPersonality) -> Self {
        Self {
            skill_selector: IntelligentSkillSelector::new(),
            combo_engine: TacticalCombinationEngine::new(),
            behavior_predictor: BehaviorPredictor::new(),
            decision_cache: AIDecisionCache::new(),
            personality,
            learning_history: LearningHistory::new(),
        }
    }
    
    /// 创建预设AI类型
    pub fn create_aggressive() -> Self {
        Self::new(AIPersonality::aggressive())
    }
    
    pub fn create_defensive() -> Self {
        Self::new(AIPersonality::defensive())
    }
    
    pub fn create_balanced() -> Self {
        Self::new(AIPersonality::balanced())
    }
    
    pub fn create_tactical() -> Self {
        Self::new(AIPersonality::tactical())
    }
    
    /// 执行完整的AI决策
    pub fn make_decision(
        &mut self,
        actor: &BattleView,
        allies: &[BattleView],
        enemies: &[BattleView],
        battle_context: &BattleContext,
    ) -> AIDecision {
        // 检查缓存
        if let Some(cached_decision) = self.check_decision_cache(actor, battle_context) {
            return cached_decision;
        }
        
        // 分析战场态势
        let battlefield_analysis = self.analyze_battlefield(actor, allies, enemies, battle_context);
        
        // 根据AI个性调整权重
        let weighted_analysis = self.apply_personality_weights(&battlefield_analysis);
        
        // 选择最佳行动
        let decision = self.select_optimal_action(
            actor,
            &weighted_analysis,
            allies,
            enemies,
            battle_context,
        );
        
        // 缓存决策
        self.cache_decision(actor, battle_context, &decision);
        
        // 更新学习历史
        self.learning_history.record_decision(&decision, &battlefield_analysis);
        
        decision
    }
    
    /// 分析战场态势
    fn analyze_battlefield(
        &self,
        actor: &BattleView,
        allies: &[BattleView],
        enemies: &[BattleView],
        battle_context: &BattleContext,
    ) -> BattlefieldAnalysis {
        // 威胁评估
        let threat_assessment = self.assess_threats(actor, enemies);
        
        // 机会识别
        let opportunities = self.identify_opportunities(actor, allies, enemies);
        
        // 资源状态
        let resource_status = self.analyze_resources(actor, allies);
        
        // 战略态势
        let strategic_position = self.evaluate_strategic_position(actor, allies, enemies, battle_context);
        
        let recommended_strategy = self.determine_strategy(&threat_assessment, &opportunities, &strategic_position);
        
        BattlefieldAnalysis {
            threat_assessment,
            opportunities,
            resource_status,
            strategic_position,
            recommended_strategy,
        }
    }
    
    /// 威胁评估
    fn assess_threats(&self, actor: &BattleView, enemies: &[BattleView]) -> ThreatAssessment {
        let mut threats = Vec::new();
        
        for enemy in enemies.iter().filter(|e| e.is_alive) {
            let threat_level = self.calculate_threat_level(actor, enemy);
            let urgency = self.calculate_threat_urgency(actor, enemy);
            
            threats.push(ThreatInfo {
                enemy_id: enemy.id,
                threat_level,
                urgency,
                distance: actor.distance_to(enemy),
                can_reach_next_turn: actor.distance_to(enemy) <= enemy.movement_speed + enemy.attack_range,
            });
        }
        
        // 按威胁级别排序
        threats.sort_by(|a, b| b.threat_level.partial_cmp(&a.threat_level).unwrap());
        
        let overall_threat = threats.iter().map(|t| t.threat_level).sum::<f32>() / threats.len() as f32;
        let imminent_danger = threats.iter().any(|t| t.can_reach_next_turn && t.threat_level > 0.7);
        let primary_threat = threats.first().map(|t| t.enemy_id);
        
        ThreatAssessment {
            threats,
            overall_threat_level: overall_threat,
            imminent_danger,
            primary_threat,
        }
    }
    
    /// 计算威胁等级
    fn calculate_threat_level(&self, actor: &BattleView, enemy: &BattleView) -> f32 {
        let damage_potential = (enemy.attack_power as f32) / (actor.defense_power as f32).max(1.0);
        let health_ratio = (enemy.current_health as f32) / (enemy.max_health as f32);
        let level_factor = (enemy.level as f32) / (actor.level as f32).max(1.0);
        let distance_factor = 1.0 / (actor.distance_to(enemy) + 1.0);
        
        // 技能威胁评估
        let skill_threat = enemy.learned_skills.iter()
            .map(|skill_id| self.evaluate_skill_threat(skill_id, actor))
            .fold(0.0, f32::max);
        
        let base_threat = damage_potential * health_ratio * level_factor * distance_factor;
        let final_threat = base_threat * (1.0 + skill_threat);
        
        final_threat.min(1.0)
    }
    
    /// 评估技能威胁
    fn evaluate_skill_threat(&self, skill_id: &SkillId, target: &BattleView) -> f32 {
        match skill_id.0 {
            1..=10 => 0.1,      // 基础攻击技能
            11..=20 => 0.3,     // 高级攻击技能
            21..=30 => 0.5,     // 特殊技能（高威胁）
            31..=40 => 0.2,     // 辅助技能
            _ => 0.1,
        }
    }
    
    /// 计算威胁紧迫性
    fn calculate_threat_urgency(&self, actor: &BattleView, enemy: &BattleView) -> ThreatUrgency {
        let distance = actor.distance_to(enemy);
        let enemy_speed = enemy.movement_speed;
        let can_reach = distance <= enemy_speed + enemy.attack_range;
        let health_percentage = actor.health_percentage();
        
        if can_reach && health_percentage < 0.3 {
            ThreatUrgency::Critical
        } else if can_reach {
            ThreatUrgency::High
        } else if distance <= enemy_speed * 2.0 {
            ThreatUrgency::Medium
        } else {
            ThreatUrgency::Low
        }
    }
    
    /// 识别机会
    fn identify_opportunities(
        &self,
        actor: &BattleView,
        allies: &[BattleView],
        enemies: &[BattleView],
    ) -> Vec<OpportunityInfo> {
        let mut opportunities = Vec::new();
        
        // 攻击机会
        for enemy in enemies.iter().filter(|e| e.is_alive) {
            if actor.can_reach(enemy) {
                let opportunity_score = self.calculate_attack_opportunity_score(actor, enemy);
                if opportunity_score > 0.3 {
                    opportunities.push(OpportunityInfo {
                        opportunity_type: OpportunityType::Attack,
                        target_id: Some(enemy.id),
                        score: opportunity_score,
                        urgency: self.calculate_opportunity_urgency(opportunity_score, enemy),
                    });
                }
            }
        }
        
        // 治疗机会
        for ally in allies.iter().filter(|a| a.is_alive && a.id != actor.id) {
            if ally.health_percentage() < 0.6 {
                let heal_score = self.calculate_heal_opportunity_score(actor, ally);
                if heal_score > 0.4 {
                    opportunities.push(OpportunityInfo {
                        opportunity_type: OpportunityType::Heal,
                        target_id: Some(ally.id),
                        score: heal_score,
                        urgency: OpportunityUrgency::High,
                    });
                }
            }
        }
        
        // 位置调整机会
        let positioning_score = self.calculate_positioning_opportunity_score(actor, allies, enemies);
        if positioning_score > 0.3 {
            opportunities.push(OpportunityInfo {
                opportunity_type: OpportunityType::Reposition,
                target_id: None,
                score: positioning_score,
                urgency: OpportunityUrgency::Medium,
            });
        }
        
        // 按分数排序
        opportunities.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());
        
        opportunities
    }
    
    /// 计算攻击机会分数
    fn calculate_attack_opportunity_score(&self, actor: &BattleView, enemy: &BattleView) -> f32 {
        let damage_ratio = (actor.attack_power as f32) / (enemy.defense_power as f32).max(1.0);
        let enemy_health_factor = 1.0 - enemy.health_percentage();
        let level_advantage = if actor.level >= enemy.level { 0.2 } else { 0.0 };
        
        let base_score = damage_ratio * 0.4 + enemy_health_factor * 0.4 + level_advantage;
        
        // 考虑击杀机会
        let estimated_damage = damage_ratio * 50.0; // 简化计算
        let kill_opportunity = if estimated_damage >= enemy.current_health as f32 { 0.3 } else { 0.0 };
        
        (base_score + kill_opportunity).min(1.0)
    }
    
    /// 计算治疗机会分数
    fn calculate_heal_opportunity_score(&self, healer: &BattleView, ally: &BattleView) -> f32 {
        let health_need = 1.0 - ally.health_percentage();
        let heal_efficiency = healer.max_mana as f32 / 100.0; // 简化计算
        let urgency_factor = if ally.health_percentage() < 0.3 { 0.4 } else { 0.2 };
        
        (health_need * 0.5 + heal_efficiency * 0.3 + urgency_factor).min(1.0)
    }
    
    /// 计算位置调整机会分数
    fn calculate_positioning_opportunity_score(
        &self,
        actor: &BattleView,
        allies: &[BattleView],
        enemies: &[BattleView],
    ) -> f32 {
        // 距离优化评分
        let optimal_distance_score = self.calculate_optimal_distance_score(actor, enemies);
        
        // 团队协调评分
        let team_coordination_score = self.calculate_team_coordination_score(actor, allies);
        
        // 安全位置评分
        let safety_score = self.calculate_safety_score(actor, enemies);
        
        (optimal_distance_score * 0.4 + team_coordination_score * 0.3 + safety_score * 0.3).min(1.0)
    }
    
    /// 计算最优距离分数
    fn calculate_optimal_distance_score(&self, actor: &BattleView, enemies: &[BattleView]) -> f32 {
        let ideal_distance = actor.attack_range * 0.8; // 略小于最大攻击范围
        
        let avg_distance = enemies.iter()
            .filter(|e| e.is_alive)
            .map(|e| actor.distance_to(e))
            .sum::<f32>() / enemies.len() as f32;
        
        let distance_diff = (avg_distance - ideal_distance).abs();
        (1.0 - distance_diff / 10.0).max(0.0)
    }
    
    /// 分析资源状态
    fn analyze_resources(&self, actor: &BattleView, allies: &[BattleView]) -> ResourceStatus {
        let health_percentage = actor.health_percentage();
        let mana_percentage = if actor.max_mana > 0 {
            actor.current_mana as f32 / actor.max_mana as f32
        } else {
            1.0
        };
        
        let team_health_avg = allies.iter()
            .filter(|a| a.is_alive)
            .map(|a| a.health_percentage())
            .sum::<f32>() / allies.len() as f32;
        
        let available_skills = actor.learned_skills.iter()
            .filter(|skill_id| actor.is_skill_ready(skill_id))
            .count();
        
        ResourceStatus {
            actor_health: health_percentage,
            actor_mana: mana_percentage,
            team_average_health: team_health_avg,
            available_skills,
            critical_resources: health_percentage < 0.3 || mana_percentage < 0.2,
        }
    }
    
    /// 应用个性化权重
    fn apply_personality_weights(&self, analysis: &BattlefieldAnalysis) -> WeightedAnalysis {
        let mut weights = WeightedAnalysis::from(analysis);
        
        match self.personality.archetype {
            AIArchetype::Aggressive => {
                // 攻击性AI更重视攻击机会，轻视威胁
                weights.opportunity_weight = 1.3;
                weights.threat_weight = 0.7;
                weights.positioning_weight = 0.8;
            },
            AIArchetype::Defensive => {
                // 防御性AI更重视威胁，轻视攻击机会
                weights.threat_weight = 1.4;
                weights.opportunity_weight = 0.6;
                weights.positioning_weight = 1.2;
            },
            AIArchetype::Balanced => {
                // 平衡型AI权重均衡
                weights.threat_weight = 1.0;
                weights.opportunity_weight = 1.0;
                weights.positioning_weight = 1.0;
            },
            AIArchetype::Tactical => {
                // 战术型AI更重视位置和组合
                weights.positioning_weight = 1.3;
                weights.combo_weight = 1.5;
                weights.threat_weight = 0.9;
            },
        }
        
        weights
    }
    
    /// 选择最优行动
    fn select_optimal_action(
        &mut self,
        actor: &BattleView,
        analysis: &WeightedAnalysis,
        allies: &[BattleView],
        enemies: &[BattleView],
        battle_context: &BattleContext,
    ) -> AIDecision {
        // 获取技能推荐
        let skill_recommendations = self.skill_selector.recommend_skills(
            actor.learned_skills.clone(),
            battle_context.clone(),
        );
        
        // 评估每个可能的行动
        let mut actions = Vec::new();
        
        // 评估攻击行动
        for recommendation in &skill_recommendations {
            for enemy in enemies.iter().filter(|e| e.is_alive && actor.can_reach(e)) {
                let action_score = self.evaluate_attack_action(
                    actor,
                    enemy,
                    &recommendation.skill_id,
                    analysis,
                );
                
                actions.push(ActionCandidate {
                    action_type: ActionType::Attack,
                    skill_id: Some(recommendation.skill_id),
                    target_id: Some(enemy.id),
                    score: action_score,
                    reasoning: format!(
                        "攻击 {} 使用技能 {:?} (分数: {:.2})",
                        enemy.name, recommendation.skill_id, action_score
                    ),
                });
            }
        }
        
        // 评估治疗行动
        if actor.learned_skills.iter().any(|s| self.is_healing_skill(s)) {
            for ally in allies.iter().filter(|a| a.is_alive && a.health_percentage() < 0.8) {
                let heal_score = self.evaluate_heal_action(actor, ally, analysis);
                
                actions.push(ActionCandidate {
                    action_type: ActionType::Heal,
                    skill_id: self.get_best_healing_skill(actor),
                    target_id: Some(ally.id),
                    score: heal_score,
                    reasoning: format!("治疗 {} (分数: {:.2})", ally.name, heal_score),
                });
            }
        }
        
        // 评估防御行动
        if analysis.threat_assessment.imminent_danger {
            let defend_score = self.evaluate_defend_action(actor, analysis);
            
            actions.push(ActionCandidate {
                action_type: ActionType::Defend,
                skill_id: None,
                target_id: None,
                score: defend_score,
                reasoning: format!("防御姿态 (分数: {:.2})", defend_score),
            });
        }
        
        // 选择最高分数的行动
        actions.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());
        
        if let Some(best_action) = actions.first() {
            AIDecision {
                action_type: best_action.action_type.clone(),
                skill_id: best_action.skill_id,
                target_id: best_action.target_id,
                confidence: best_action.score,
                reasoning: best_action.reasoning.clone(),
                alternative_actions: actions.into_iter().skip(1).take(2).collect(),
            }
        } else {
            // 没有合适的行动，跳过回合
            AIDecision {
                action_type: ActionType::SkipTurn,
                skill_id: None,
                target_id: None,
                confidence: 0.0,
                reasoning: "没有合适的行动选择".to_string(),
                alternative_actions: Vec::new(),
            }
        }
    }
    
    /// 评估攻击行动分数
    fn evaluate_attack_action(
        &self,
        actor: &BattleView,
        target: &BattleView,
        skill_id: &SkillId,
        analysis: &WeightedAnalysis,
    ) -> f32 {
        let damage_potential = self.estimate_damage(actor, target, skill_id);
        let kill_probability = if damage_potential >= target.current_health as f32 { 1.0 } else { 0.0 };
        let threat_reduction = analysis.threat_assessment.threats.iter()
            .find(|t| t.enemy_id == target.id)
            .map(|t| t.threat_level)
            .unwrap_or(0.0);
        
        let base_score = damage_potential / target.max_health as f32;
        let tactical_bonus = kill_probability * 0.3 + threat_reduction * 0.2;
        
        (base_score + tactical_bonus) * analysis.opportunity_weight
    }
    
    /// 估算伤害
    fn estimate_damage(&self, attacker: &BattleView, defender: &BattleView, skill_id: &SkillId) -> f32 {
        let base_damage = match skill_id.0 {
            1..=10 => attacker.attack_power as f32 * 0.8,
            11..=20 => attacker.attack_power as f32 * 1.2,
            21..=30 => attacker.attack_power as f32 * 1.8,
            _ => attacker.attack_power as f32,
        };
        
        let defense_reduction = base_damage * (defender.defense_power as f32 / 100.0).min(0.8);
        (base_damage - defense_reduction).max(0.0)
    }
    
    /// 检查决策缓存
    fn check_decision_cache(&self, actor: &BattleView, battle_context: &BattleContext) -> Option<AIDecision> {
        // 在实际实现中，这里会检查缓存
        None
    }
    
    /// 缓存决策
    fn cache_decision(&mut self, actor: &BattleView, battle_context: &BattleContext, decision: &AIDecision) {
        // 在实际实现中，这里会缓存决策
    }
    
    /// 其他辅助方法...
    fn is_healing_skill(&self, skill_id: &SkillId) -> bool {
        skill_id.0 >= 31 && skill_id.0 <= 40
    }
    
    fn get_best_healing_skill(&self, actor: &BattleView) -> Option<SkillId> {
        actor.learned_skills.iter()
            .filter(|s| self.is_healing_skill(s))
            .next()
            .copied()
    }
    
    fn evaluate_heal_action(&self, healer: &BattleView, target: &BattleView, analysis: &WeightedAnalysis) -> f32 {
        let health_need = 1.0 - target.health_percentage();
        let urgency = if target.health_percentage() < 0.3 { 1.5 } else { 1.0 };
        
        health_need * urgency * analysis.opportunity_weight
    }
    
    fn evaluate_defend_action(&self, actor: &BattleView, analysis: &WeightedAnalysis) -> f32 {
        analysis.threat_assessment.overall_threat_level * analysis.threat_weight
    }
    
    fn determine_strategy(
        &self,
        threat: &ThreatAssessment,
        opportunities: &[OpportunityInfo],
        strategic_position: &StrategicPosition,
    ) -> RecommendedStrategy {
        if threat.imminent_danger {
            RecommendedStrategy::Defensive
        } else if opportunities.len() > 2 && opportunities[0].score > 0.7 {
            RecommendedStrategy::Aggressive
        } else if strategic_position.team_advantage > 0.3 {
            RecommendedStrategy::Opportunistic
        } else {
            RecommendedStrategy::Cautious
        }
    }
    
    fn evaluate_strategic_position(
        &self,
        actor: &BattleView,
        allies: &[BattleView],
        enemies: &[BattleView],
        battle_context: &BattleContext,
    ) -> StrategicPosition {
        let team_strength = allies.iter().map(|a| a.combat_rating()).sum::<u32>();
        let enemy_strength = enemies.iter().map(|e| e.combat_rating()).sum::<u32>();
        
        let team_advantage = if enemy_strength > 0 {
            (team_strength as f32 - enemy_strength as f32) / enemy_strength as f32
        } else {
            1.0
        };
        
        StrategicPosition {
            team_advantage,
            positional_advantage: self.calculate_positional_advantage(actor, allies, enemies),
            resource_advantage: self.calculate_resource_advantage(allies, enemies),
        }
    }
    
    fn calculate_positional_advantage(&self, actor: &BattleView, allies: &[BattleView], enemies: &[BattleView]) -> f32 {
        // 简化计算
        0.0
    }
    
    fn calculate_resource_advantage(&self, allies: &[BattleView], enemies: &[BattleView]) -> f32 {
        let ally_health = allies.iter().map(|a| a.health_percentage()).sum::<f32>();
        let enemy_health = enemies.iter().map(|e| e.health_percentage()).sum::<f32>();
        
        if enemy_health > 0.0 {
            (ally_health - enemy_health) / enemy_health
        } else {
            1.0
        }
    }
    
    fn calculate_team_coordination_score(&self, actor: &BattleView, allies: &[BattleView]) -> f32 {
        // 简化实现
        0.5
    }
    
    fn calculate_safety_score(&self, actor: &BattleView, enemies: &[BattleView]) -> f32 {
        let min_distance = enemies.iter()
            .filter(|e| e.is_alive)
            .map(|e| actor.distance_to(e))
            .fold(f32::INFINITY, f32::min);
        
        (min_distance / 10.0).min(1.0)
    }
    
    fn calculate_opportunity_urgency(&self, score: f32, target: &BattleView) -> OpportunityUrgency {
        if score > 0.8 && target.health_percentage() < 0.3 {
            OpportunityUrgency::Critical
        } else if score > 0.6 {
            OpportunityUrgency::High
        } else {
            OpportunityUrgency::Medium
        }
    }
}

// ============================================================================
// 数据结构定义
// ============================================================================

/// AI个性配置
#[derive(Debug, Clone)]
pub struct AIPersonality {
    pub archetype: AIArchetype,
    pub aggression: f32,      // 0.0 - 1.0
    pub caution: f32,         // 0.0 - 1.0
    pub cooperation: f32,     // 0.0 - 1.0
    pub adaptability: f32,    // 0.0 - 1.0
}

impl AIPersonality {
    pub fn aggressive() -> Self {
        Self {
            archetype: AIArchetype::Aggressive,
            aggression: 0.9,
            caution: 0.3,
            cooperation: 0.5,
            adaptability: 0.6,
        }
    }
    
    pub fn defensive() -> Self {
        Self {
            archetype: AIArchetype::Defensive,
            aggression: 0.2,
            caution: 0.9,
            cooperation: 0.8,
            adaptability: 0.4,
        }
    }
    
    pub fn balanced() -> Self {
        Self {
            archetype: AIArchetype::Balanced,
            aggression: 0.5,
            caution: 0.5,
            cooperation: 0.7,
            adaptability: 0.7,
        }
    }
    
    pub fn tactical() -> Self {
        Self {
            archetype: AIArchetype::Tactical,
            aggression: 0.6,
            caution: 0.7,
            cooperation: 0.9,
            adaptability: 0.9,
        }
    }
}

/// AI原型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum AIArchetype {
    Aggressive,
    Defensive,
    Balanced,
    Tactical,
}

/// 战场分析结果
#[derive(Debug, Clone)]
pub struct BattlefieldAnalysis {
    pub threat_assessment: ThreatAssessment,
    pub opportunities: Vec<OpportunityInfo>,
    pub resource_status: ResourceStatus,
    pub strategic_position: StrategicPosition,
    pub recommended_strategy: RecommendedStrategy,
}

/// 加权分析结果
#[derive(Debug, Clone)]
pub struct WeightedAnalysis {
    pub threat_assessment: ThreatAssessment,
    pub opportunities: Vec<OpportunityInfo>,
    pub resource_status: ResourceStatus,
    pub strategic_position: StrategicPosition,
    pub recommended_strategy: RecommendedStrategy,
    // 权重
    pub threat_weight: f32,
    pub opportunity_weight: f32,
    pub positioning_weight: f32,
    pub combo_weight: f32,
}

impl From<&BattlefieldAnalysis> for WeightedAnalysis {
    fn from(analysis: &BattlefieldAnalysis) -> Self {
        Self {
            threat_assessment: analysis.threat_assessment.clone(),
            opportunities: analysis.opportunities.clone(),
            resource_status: analysis.resource_status.clone(),
            strategic_position: analysis.strategic_position.clone(),
            recommended_strategy: analysis.recommended_strategy.clone(),
            threat_weight: 1.0,
            opportunity_weight: 1.0,
            positioning_weight: 1.0,
            combo_weight: 1.0,
        }
    }
}

/// 威胁评估
#[derive(Debug, Clone)]
pub struct ThreatAssessment {
    pub threats: Vec<ThreatInfo>,
    pub overall_threat_level: f32,
    pub imminent_danger: bool,
    pub primary_threat: Option<BattleUnitId>,
}

/// 威胁信息
#[derive(Debug, Clone)]
pub struct ThreatInfo {
    pub enemy_id: BattleUnitId,
    pub threat_level: f32,
    pub urgency: ThreatUrgency,
    pub distance: f32,
    pub can_reach_next_turn: bool,
}

/// 威胁紧迫性
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ThreatUrgency {
    Low,
    Medium,
    High,
    Critical,
}

/// 机会信息
#[derive(Debug, Clone)]
pub struct OpportunityInfo {
    pub opportunity_type: OpportunityType,
    pub target_id: Option<BattleUnitId>,
    pub score: f32,
    pub urgency: OpportunityUrgency,
}

/// 机会类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum OpportunityType {
    Attack,
    Heal,
    Buff,
    Debuff,
    Reposition,
    Combo,
}

/// 机会紧迫性
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum OpportunityUrgency {
    Low,
    Medium,
    High,
    Critical,
}

/// 资源状态
#[derive(Debug, Clone)]
pub struct ResourceStatus {
    pub actor_health: f32,
    pub actor_mana: f32,
    pub team_average_health: f32,
    pub available_skills: usize,
    pub critical_resources: bool,
}

/// 战略位置
#[derive(Debug, Clone)]
pub struct StrategicPosition {
    pub team_advantage: f32,
    pub positional_advantage: f32,
    pub resource_advantage: f32,
}

/// 推荐策略
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RecommendedStrategy {
    Aggressive,    // 主动攻击
    Defensive,     // 防御姿态
    Opportunistic, // 机会主义
    Cautious,      // 谨慎行动
}

/// AI决策结果
#[derive(Debug, Clone)]
pub struct AIDecision {
    pub action_type: ActionType,
    pub skill_id: Option<SkillId>,
    pub target_id: Option<BattleUnitId>,
    pub confidence: f32,
    pub reasoning: String,
    pub alternative_actions: Vec<ActionCandidate>,
}

/// 行动类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ActionType {
    Attack,
    Heal,
    Buff,
    Debuff,
    Defend,
    Reposition,
    SkipTurn,
}

/// 行动候选
#[derive(Debug, Clone)]
pub struct ActionCandidate {
    pub action_type: ActionType,
    pub skill_id: Option<SkillId>,
    pub target_id: Option<BattleUnitId>,
    pub score: f32,
    pub reasoning: String,
}

/// 学习历史
#[derive(Debug)]
pub struct LearningHistory {
    decisions: Vec<DecisionRecord>,
    success_patterns: HashMap<String, f32>,
}

impl LearningHistory {
    fn new() -> Self {
        Self {
            decisions: Vec::new(),
            success_patterns: HashMap::new(),
        }
    }
    
    fn record_decision(&mut self, decision: &AIDecision, analysis: &BattlefieldAnalysis) {
        let record = DecisionRecord {
            decision: decision.clone(),
            context: analysis.clone(),
            timestamp: std::time::Instant::now(),
            outcome: None, // 将在稍后更新
        };
        
        self.decisions.push(record);
        
        // 保持历史记录在合理范围内
        if self.decisions.len() > 1000 {
            self.decisions.remove(0);
        }
    }
}

/// 决策记录
#[derive(Debug)]
pub struct DecisionRecord {
    pub decision: AIDecision,
    pub context: BattlefieldAnalysis,
    pub timestamp: std::time::Instant,
    pub outcome: Option<DecisionOutcome>,
}

/// 决策结果
#[derive(Debug, Clone)]
pub enum DecisionOutcome {
    Success(f32),  // 成功分数
    Failure(String), // 失败原因
}