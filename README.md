# 🎮 Rust RPG 战斗系统

一个基于Rust的完整RPG战斗系统，采用领域驱动设计(DDD)和模块化架构。

![Rust](https://img.shields.io/badge/Language-Rust-orange.svg)
![Build](https://img.shields.io/badge/Build-Passing-green.svg)
![License](https://img.shields.io/badge/License-MIT-blue.svg)

## 🏆 项目亮点

### ⚔️ 多模式战斗系统
- 🎲 **传统回合制战斗** - 经典RPG体验
- 🔥 **实时战斗** - 现代动作RPG风格  
- ⚡ **准实时战斗** - 平衡策略与速度
- 📊 **动作条战斗** - 基于速度的动态顺序
- 🤝 **同步战斗** - 双方同时行动

### 🏗️ 优雅的架构设计
- 🎯 **领域驱动设计** - 清晰的业务逻辑分离
- 🧩 **Trait组合模式** - 灵活的组件化设计
- 🔌 **适配器架构** - 不同模块间的完美桥接
- ⚡ **零成本抽象** - 充分利用Rust性能优势

### 💥 完整的战斗机制
- 🎯 **智能伤害计算** - 暴击、格挡、多伤害类型
- 🔮 **状态效果系统** - Buff/Debuff生命周期管理
- ✨ **技能释放系统** - 冷却、法力、目标选择
- 📊 **实时战斗日志** - 美观的格式化输出

## 🚀 快速开始

### 安装环境
```bash
# 确保已安装 Rust 1.70+
rustc --version

# 克隆项目
git clone <项目地址>
cd game

# 构建项目
cargo build
```

### 运行演示

#### 🎮 基础战斗演示
```bash
# 基础功能展示
cargo run --bin battle_demo

# 实际战斗过程
cargo run --bin battle_demo_real

# 平衡战斗展示
cargo run --bin battle_demo_balanced
```

#### ⚔️ 多系统演示
```bash
# 5种战斗模式完整展示
cargo run --bin battle_demo_systems

# 状态效果系统测试
cargo run --bin status_effect_test

# 技能系统测试  
cargo run --bin skill_system_test
```

#### 🤖 AI战斗演示
```bash
# AI决策和战斗演示
cargo run --bin ai_battle_demo

# 配置系统演示
cargo run --bin config_demo
```

## 📊 支持的战斗模式

### 🥊 1vs1 单挑决斗
```rust
let mut battle_manager = BattleManager::new();
let result = battle_manager.start_1v1_battle(&mut hero, &mut villain);
```

### 🏹 1vsN 英雄挑战
```rust
let enemies = vec![&mut goblin1, &mut goblin2, &mut goblin3];
let result = battle_manager.start_1vn_battle(&mut hero, enemies);
```

### 🛡️ NvsN 团队对战
```rust
let team_a = vec![&mut hero1, &mut hero2];
let team_b = vec![&mut monster1, &mut monster2];
let result = battle_manager.start_nvn_battle(team_a, team_b);
```

### ⚡ 高性能战斗引擎
```rust
// 使用标准优化配置来创建引擎
let config = EngineOptimization::standard().to_config();
let mut engine = OptimizedBattleEngine::new(config);

// 初始化战斗
let participants: Vec<Box<dyn FullBattleUnit>> = vec![Box::new(player), Box::new(enemy)];
let battle_id = engine.initialize_battle(participants).unwrap();

// 循环执行回合
loop {
    let turn_result = engine.execute_turn(battle_id).unwrap();
    // ... 处理回合结果 ...
    if !matches!(turn_result.battle_status, BattleEndStatus::Ongoing) {
        break;
    }
}
```

## 🔧 系统架构

### 核心组件

```
src/
├── battle_system/           # 🎯 战斗系统核心
│   ├── performance/        # ⚡ 高性能战斗引擎
│   │   └── battle_engine.rs
│   ├── battle_manager.rs   # 传统回合制管理器
│   ├── damage_calculator.rs # 伤害计算引擎
│   ├── status_effect_processor.rs # 状态效果处理器
│   ├── skill_caster.rs     # 技能释放系统
│   ├── battle_event.rs     # 事件日志系统
│   ├── adapters.rs         # 适配器桥接层
│   └── battle_unit.rs      # 战斗单位抽象
├── character.rs            # 🎭 角色系统
├── monster.rs             # 👹 怪物系统
├── skill/                 # ✨ 技能系统
├── equipment/             # ⚔️ 装备系统
├── attribute/             # 📊 属性系统
└── status_panel/          # 🔮 状态面板
```

### BattleUnit Trait体系
```rust
pub trait BattleUnit: BasicAttributes + Vitality + Skillable + 
                      CombatAttributes + StatusEffect + BattleState {
    // 完整的战斗单位接口
}
```

## ⚙️ 配置系统

### 自定义战斗配置
```rust
let config = BattleConfig {
    damage: DamageConfig {
        base_critical_rate: 0.15,      // 15% 基础暴击率
        critical_multiplier: 2.5,      // 2.5倍暴击伤害
        base_block_rate: 0.1,          // 10% 基础格挡率
        block_reduction: 0.5,          // 格挡减少50%伤害
        ..Default::default()
    },
    ..Default::default()
};

let battle_manager = BattleManager::with_config(config);
```

### 战斗模式切换
```rust
// 切换到实时模式
rt_manager.set_battle_type(BattleSystemType::RealTime);

// 切换到动作条模式
rt_manager.set_battle_type(BattleSystemType::ActionQueue);

// 切换到同步模式
rt_manager.set_battle_type(BattleSystemType::Synchronous);
```

## 📈 项目统计

- **📁 文件数量**: 15+ 个核心实现文件
- **📝 代码行数**: 5,000+ 行高质量Rust代码
- **🧪 演示程序**: 8个完整的测试和演示程序
- **📚 文档覆盖**: 完整的使用指南和API文档
- **🔍 测试覆盖**: 多场景全面测试

## 🎯 技术特性

### 🛡️ 安全性保障
- **🔒 类型安全** - 编译时错误检查
- **🛡️ 内存安全** - 零悬垂指针，无内存泄漏
- **⚡ 并发安全** - 适合多线程环境

### 📈 性能优化
- **⚡ 零分配热路径** - 优化的战斗循环
- **🔄 智能缓存** - 减少重复计算
- **📊 高效数据结构** - 优化的缓存访问模式

### 🔌 可扩展性
- **🧩 插件化架构** - 易于扩展和替换组件
- **🎯 接口隔离** - 清晰的模块边界
- **🔄 适配器模式** - 解决兼容性问题

## 📚 文档资源

- **[快速开始指南](QUICK_START_GUIDE.md)** - 新手入门教程
- **[战斗系统完成报告](BATTLE_SYSTEM_COMPLETION_REPORT.md)** - 详细的功能说明
- **[战斗系统指南](BATTLE_SYSTEM_GUIDE.md)** - 深度使用指南

## 🎮 实际应用

这个战斗系统可以直接用于：
- 🎲 **回合制RPG游戏**
- ⚡ **实时动作RPG**
- 🏹 **策略战棋游戏**
- 🤖 **AI对战系统**
- 📊 **游戏平衡测试**

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和测试者！

---

**🏆 现在就开始你的RPG冒险之旅吧！**

```bash
cargo run --bin battle_demo_systems
```

享受精彩的战斗体验！ 🎮⚔️✨ 