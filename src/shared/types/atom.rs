/// 每单位代表的灵气量
pub const ESSENCE_UNIT: f64 = 2.6e18;
/// 每单位数值代表的元素量
pub const ATOM_UNIT: f64 = 1.0;

/// 每日消耗的灵气数（以单位为基准）
pub const DAILY_CONSUMPTION_ATOM: f64 = 7e15;

/// 角色起始灵气单位总量
pub const STARTING_ATOM_UNIT: f64 = 6570.0 * ATOM_UNIT;

/// 三维总数
/// 体质、力量、精神三个基础属性的总和
pub const TOTAL_ATTRIBUTES: u32 = 3;

/// 体质每点所需灵气单位（1年最低消耗）
pub const CONSTITUTION_UNIT: f64 = 24.0 * ATOM_UNIT;
/// 力量每点所需灵气单位
pub const STRENGTH_UNIT: f64 = 24.0 * ATOM_UNIT;
/// 精神每点所需灵气单位（更高，假设为3倍）
pub const SPIRIT_UNIT: f64 = 72.0 * ATOM_UNIT;

/// 五行属性到三维属性的权重表
/// 顺序：金、木、水、火、土
pub const CONSTITUTION_WEIGHTS: [f64; 5] = [0.2, 0.3, 0.3, 0.1, 0.1]; // 体质权重
pub const STRENGTH_WEIGHTS: [f64; 5] = [0.4, 0.1, 0.1, 0.3, 0.1]; // 力量权重
pub const SPIRIT_WEIGHTS: [f64; 5] = [0.1, 0.2, 0.5, 0.1, 0.1]; // 精神权重

/// 五行属性点数转三维属性点数的转换函数
/// 参数：五行属性点（金、木、水、火、土）
/// 返回：(体质, 力量, 精神)
pub fn five_elements_to_base_attributes(
    metal: f64,
    wood: f64,
    water: f64,
    fire: f64,
    earth: f64,
) -> (u32, u32, u32) {
    let elements = [metal, wood, water, fire, earth];
    let constitution_lingqi = elements
        .iter()
        .zip(CONSTITUTION_WEIGHTS.iter())
        .map(|(v, w)| v * w)
        .sum::<f64>()
        * ATOM_UNIT;
    let strength_lingqi = elements
        .iter()
        .zip(STRENGTH_WEIGHTS.iter())
        .map(|(v, w)| v * w)
        .sum::<f64>()
        * ATOM_UNIT;
    let spirit_lingqi = elements
        .iter()
        .zip(SPIRIT_WEIGHTS.iter())
        .map(|(v, w)| v * w)
        .sum::<f64>()
        * ATOM_UNIT;
    let constitution = (constitution_lingqi / CONSTITUTION_UNIT).floor() as u32;
    let strength = (strength_lingqi / STRENGTH_UNIT).floor() as u32;
    let spirit = (spirit_lingqi / SPIRIT_UNIT).floor() as u32;
    (constitution, strength, spirit)
}
