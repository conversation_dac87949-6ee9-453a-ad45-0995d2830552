//! # 插件化验证器模块
//!
//! 提供动态加载、注册和管理验证器插件的功能
//!
//! ## 核心功能
//!
//! - 动态验证器注册和发现
//! - 插件生命周期管理
//! - 验证器能力查询和匹配
//! - 插件热重载和版本管理
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::validation::plugin::{ValidatorPlugin, ValidatorPluginManager};
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let mut manager = ValidatorPluginManager::new();
//!
//! // 注册插件
//! let plugin = CustomValidatorPlugin::new();
//! manager.register_plugin("custom_validator", Box::new(plugin)).await?;
//!
//! // 发现可用的验证器
//! let validators = manager.discover_validators("material").await?;
//!
//! // 执行验证
//! let context = ValidationContext::new();
//! let data = JsonValidatableData::new(json!({"id": "iron_ore"}));
//!
//! for validator in validators {
//!     let result = validator.validate(&mut context, &data)?;
//!     // 处理验证结果
//! }
//! # Ok(())
//! # }
//! ```

use crate::config_engine::validation::{
    ErrorSeverity, ValidatableData, ValidationContext, ValidationError, ValidationResult,
    ValidationSource, Validator,
};
use crate::config_engine::{error::ConfigEngineError, Result};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fmt::Debug;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 验证器插件接口
///
/// 定义可插拔验证器的标准接口
#[async_trait]
pub trait ValidatorPlugin: Send + Sync + Debug {
    /// 获取插件名称
    fn name(&self) -> &str;

    /// 获取插件版本
    fn version(&self) -> &str;

    /// 获取插件描述
    fn description(&self) -> &str;

    /// 获取插件作者
    fn author(&self) -> &str;

    /// 获取支持的数据类型
    fn supported_types(&self) -> Vec<String>;

    /// 获取验证器能力标签
    fn capabilities(&self) -> Vec<ValidatorCapability>;

    /// 检查是否支持特定的数据类型
    fn supports(&self, data_type: &str) -> bool {
        self.supported_types().is_empty() || self.supported_types().contains(&data_type.to_string())
    }

    /// 插件初始化
    async fn initialize(&mut self, config: &PluginConfig) -> Result<()>;

    /// 插件关闭
    async fn shutdown(&mut self) -> Result<()>;

    /// 创建验证器实例
    async fn create_validator(&self, validator_type: &str) -> Result<Box<dyn Validator>>;

    /// 获取可用的验证器类型
    async fn list_validator_types(&self) -> Vec<ValidatorInfo>;

    /// 验证插件健康状态
    async fn health_check(&self) -> PluginHealthStatus;

    /// 获取插件配置模式
    fn config_schema(&self) -> serde_json::Value;

    /// 获取插件统计信息
    async fn get_statistics(&self) -> PluginStatistics;
}

/// 验证器能力标签
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ValidatorCapability {
    /// 同步验证
    Synchronous,
    /// 异步验证
    Asynchronous,
    /// 批量验证
    Batch,
    /// 流式验证
    Stream,
    /// 增量验证
    Incremental,
    /// 缓存验证结果
    Cacheable,
    /// 支持自定义规则
    CustomRules,
    /// 支持表达式
    Expression,
    /// 支持正则表达式
    Regex,
    /// 支持 Schema 验证
    Schema,
    /// 支持业务规则
    BusinessRules,
    /// 支持完整性检查
    Integrity,
    /// 支持性能验证
    Performance,
    /// 支持跨域验证
    CrossDomain,
    /// 自定义能力
    Custom(String),
}

/// 验证器信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidatorInfo {
    /// 验证器类型标识
    pub validator_type: String,
    /// 验证器名称
    pub name: String,
    /// 验证器描述
    pub description: String,
    /// 支持的数据类型
    pub supported_types: Vec<String>,
    /// 验证器能力
    pub capabilities: Vec<ValidatorCapability>,
    /// 是否启用
    pub enabled: bool,
    /// 优先级
    pub priority: i32,
    /// 配置参数
    pub parameters: HashMap<String, serde_json::Value>,
}

impl ValidatorInfo {
    pub fn new(validator_type: String, name: String) -> Self {
        Self {
            validator_type,
            name,
            description: String::new(),
            supported_types: Vec::new(),
            capabilities: Vec::new(),
            enabled: true,
            priority: 0,
            parameters: HashMap::new(),
        }
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = description;
        self
    }

    pub fn with_supported_type(mut self, data_type: String) -> Self {
        self.supported_types.push(data_type);
        self
    }

    pub fn with_capability(mut self, capability: ValidatorCapability) -> Self {
        self.capabilities.push(capability);
        self
    }

    pub fn with_priority(mut self, priority: i32) -> Self {
        self.priority = priority;
        self
    }

    pub fn with_parameter(mut self, key: String, value: serde_json::Value) -> Self {
        self.parameters.insert(key, value);
        self
    }

    pub fn disabled(mut self) -> Self {
        self.enabled = false;
        self
    }
}

/// 插件配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginConfig {
    /// 插件ID
    pub plugin_id: String,
    /// 配置参数
    pub parameters: HashMap<String, serde_json::Value>,
    /// 启用的验证器类型
    pub enabled_validators: Vec<String>,
    /// 日志级别
    pub log_level: String,
    /// 最大内存使用（字节）
    pub max_memory_usage: Option<usize>,
    /// 超时设置（毫秒）
    pub timeout_ms: Option<u64>,
    /// 是否启用缓存
    pub enable_cache: bool,
    /// 缓存大小限制
    pub cache_size: Option<usize>,
}

impl PluginConfig {
    pub fn new(plugin_id: String) -> Self {
        Self {
            plugin_id,
            parameters: HashMap::new(),
            enabled_validators: Vec::new(),
            log_level: "info".to_string(),
            max_memory_usage: None,
            timeout_ms: Some(30000), // 30秒默认超时
            enable_cache: true,
            cache_size: Some(1000),
        }
    }

    pub fn with_parameter(mut self, key: String, value: serde_json::Value) -> Self {
        self.parameters.insert(key, value);
        self
    }

    pub fn with_enabled_validator(mut self, validator_type: String) -> Self {
        self.enabled_validators.push(validator_type);
        self
    }

    pub fn with_log_level(mut self, level: String) -> Self {
        self.log_level = level;
        self
    }

    pub fn with_timeout(mut self, timeout_ms: u64) -> Self {
        self.timeout_ms = Some(timeout_ms);
        self
    }

    pub fn with_memory_limit(mut self, max_bytes: usize) -> Self {
        self.max_memory_usage = Some(max_bytes);
        self
    }

    pub fn disable_cache(mut self) -> Self {
        self.enable_cache = false;
        self
    }

    pub fn get_parameter(&self, key: &str) -> Option<&serde_json::Value> {
        self.parameters.get(key)
    }
}

/// 插件健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginHealthStatus {
    /// 是否健康
    pub healthy: bool,
    /// 状态消息
    pub message: String,
    /// 检查时间戳
    pub checked_at: chrono::DateTime<chrono::Utc>,
    /// 内存使用情况
    pub memory_usage: Option<usize>,
    /// 错误计数
    pub error_count: usize,
    /// 最后一次错误
    pub last_error: Option<String>,
    /// 运行时间
    pub uptime: std::time::Duration,
}

impl PluginHealthStatus {
    pub fn healthy() -> Self {
        Self {
            healthy: true,
            message: "Plugin is healthy".to_string(),
            checked_at: chrono::Utc::now(),
            memory_usage: None,
            error_count: 0,
            last_error: None,
            uptime: std::time::Duration::from_secs(0),
        }
    }

    pub fn unhealthy(message: String) -> Self {
        Self {
            healthy: false,
            message,
            checked_at: chrono::Utc::now(),
            memory_usage: None,
            error_count: 0,
            last_error: None,
            uptime: std::time::Duration::from_secs(0),
        }
    }

    pub fn with_memory_usage(mut self, usage: usize) -> Self {
        self.memory_usage = Some(usage);
        self
    }

    pub fn with_error_count(mut self, count: usize) -> Self {
        self.error_count = count;
        self
    }

    pub fn with_last_error(mut self, error: String) -> Self {
        self.last_error = Some(error);
        self
    }

    pub fn with_uptime(mut self, uptime: std::time::Duration) -> Self {
        self.uptime = uptime;
        self
    }
}

/// 插件统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginStatistics {
    /// 总验证次数
    pub total_validations: u64,
    /// 成功验证次数
    pub successful_validations: u64,
    /// 失败验证次数
    pub failed_validations: u64,
    /// 平均执行时间（毫秒）
    pub average_execution_time_ms: f64,
    /// 内存使用峰值
    pub peak_memory_usage: usize,
    /// 创建的验证器数量
    pub created_validators: usize,
    /// 活跃的验证器数量
    pub active_validators: usize,
}

impl PluginStatistics {
    pub fn new() -> Self {
        Self {
            total_validations: 0,
            successful_validations: 0,
            failed_validations: 0,
            average_execution_time_ms: 0.0,
            peak_memory_usage: 0,
            created_validators: 0,
            active_validators: 0,
        }
    }

    pub fn success_rate(&self) -> f64 {
        if self.total_validations == 0 {
            0.0
        } else {
            self.successful_validations as f64 / self.total_validations as f64
        }
    }
}

impl Default for PluginStatistics {
    fn default() -> Self {
        Self::new()
    }
}

/// 验证器插件管理器
pub struct ValidatorPluginManager {
    /// 已注册的插件
    plugins: Arc<RwLock<HashMap<String, Box<dyn ValidatorPlugin>>>>,
    /// 插件配置
    plugin_configs: Arc<RwLock<HashMap<String, PluginConfig>>>,
    /// 验证器实例缓存
    validator_cache: Arc<RwLock<HashMap<String, Box<dyn Validator>>>>,
    /// 插件发现器
    plugin_discoverers: Arc<RwLock<Vec<Box<dyn PluginDiscoverer>>>>,
    /// 管理器配置
    manager_config: PluginManagerConfig,
    /// 统计信息
    statistics: Arc<RwLock<ManagerStatistics>>,
}

impl ValidatorPluginManager {
    /// 创建新的插件管理器
    pub fn new() -> Self {
        Self::with_config(PluginManagerConfig::default())
    }

    /// 使用配置创建插件管理器
    pub fn with_config(config: PluginManagerConfig) -> Self {
        Self {
            plugins: Arc::new(RwLock::new(HashMap::new())),
            plugin_configs: Arc::new(RwLock::new(HashMap::new())),
            validator_cache: Arc::new(RwLock::new(HashMap::new())),
            plugin_discoverers: Arc::new(RwLock::new(Vec::new())),
            manager_config: config,
            statistics: Arc::new(RwLock::new(ManagerStatistics::new())),
        }
    }

    /// 注册插件
    pub async fn register_plugin(
        &self,
        plugin_id: String,
        mut plugin: Box<dyn ValidatorPlugin>,
    ) -> Result<()> {
        // 验证插件ID的唯一性
        {
            let plugins = self.plugins.read().await;
            if plugins.contains_key(&plugin_id) {
                return Err(ConfigEngineError::InvalidConfiguration(format!(
                    "Plugin with ID '{}' already exists",
                    plugin_id
                )));
            }
        }

        // 创建默认配置
        let config = PluginConfig::new(plugin_id.clone());

        // 初始化插件
        plugin.initialize(&config).await?;

        // 存储插件和配置
        {
            let mut plugins = self.plugins.write().await;
            plugins.insert(plugin_id.clone(), plugin);
        }

        {
            let mut configs = self.plugin_configs.write().await;
            configs.insert(plugin_id.clone(), config);
        }

        // 更新统计信息
        {
            let mut stats = self.statistics.write().await;
            stats.registered_plugins += 1;
        }

        log::info!("Plugin '{}' registered successfully", plugin_id);
        Ok(())
    }

    /// 注销插件
    pub async fn unregister_plugin(&self, plugin_id: &str) -> Result<bool> {
        let plugin_removed = {
            let mut plugins = self.plugins.write().await;

            if let Some(mut plugin) = plugins.remove(plugin_id) {
                // 关闭插件
                if let Err(e) = plugin.shutdown().await {
                    log::warn!("Error shutting down plugin '{}': {}", plugin_id, e);
                }
                true
            } else {
                false
            }
        };

        if plugin_removed {
            // 移除配置
            {
                let mut configs = self.plugin_configs.write().await;
                configs.remove(plugin_id);
            }

            // 清除相关的验证器缓存
            self.clear_validator_cache_for_plugin(plugin_id).await;

            // 更新统计信息
            {
                let mut stats = self.statistics.write().await;
                stats.registered_plugins -= 1;
            }

            log::info!("Plugin '{}' unregistered successfully", plugin_id);
        }

        Ok(plugin_removed)
    }

    /// 发现指定数据类型的所有验证器
    pub async fn discover_validators(&self, data_type: &str) -> Result<Vec<Box<dyn Validator>>> {
        let mut validators = Vec::new();
        let plugins = self.plugins.read().await;

        for (plugin_id, plugin) in plugins.iter() {
            if plugin.supports(data_type) {
                // 获取插件支持的验证器类型
                let validator_types = plugin.list_validator_types().await;

                for validator_info in validator_types {
                    if validator_info.enabled
                        && (validator_info.supported_types.is_empty()
                            || validator_info
                                .supported_types
                                .contains(&data_type.to_string()))
                    {
                        // 尝试从缓存获取验证器
                        let cache_key = format!("{}:{}", plugin_id, validator_info.validator_type);
                        let validator = self
                            .get_or_create_validator(
                                &cache_key,
                                plugin.as_ref(),
                                &validator_info.validator_type,
                            )
                            .await?;
                        validators.push(validator);
                    }
                }
            }
        }

        // 按优先级排序
        validators.sort_by(|a, b| a.priority().cmp(&b.priority()));

        Ok(validators)
    }

    /// 发现具有特定能力的验证器
    pub async fn discover_validators_by_capability(
        &self,
        capability: &ValidatorCapability,
    ) -> Result<Vec<Box<dyn Validator>>> {
        let mut validators = Vec::new();
        let plugins = self.plugins.read().await;

        for (plugin_id, plugin) in plugins.iter() {
            if plugin.capabilities().contains(capability) {
                let validator_types = plugin.list_validator_types().await;

                for validator_info in validator_types {
                    if validator_info.enabled && validator_info.capabilities.contains(capability) {
                        let cache_key = format!("{}:{}", plugin_id, validator_info.validator_type);
                        let validator = self
                            .get_or_create_validator(
                                &cache_key,
                                plugin.as_ref(),
                                &validator_info.validator_type,
                            )
                            .await?;
                        validators.push(validator);
                    }
                }
            }
        }

        Ok(validators)
    }

    /// 获取插件信息
    pub async fn get_plugin_info(&self, plugin_id: &str) -> Option<PluginInfo> {
        let plugins = self.plugins.read().await;

        if let Some(plugin) = plugins.get(plugin_id) {
            Some(PluginInfo {
                id: plugin_id.to_string(),
                name: plugin.name().to_string(),
                version: plugin.version().to_string(),
                description: plugin.description().to_string(),
                author: plugin.author().to_string(),
                supported_types: plugin.supported_types(),
                capabilities: plugin.capabilities(),
                health_status: plugin.health_check().await,
                statistics: plugin.get_statistics().await,
            })
        } else {
            None
        }
    }

    /// 列出所有已注册的插件
    pub async fn list_plugins(&self) -> Vec<String> {
        let plugins = self.plugins.read().await;
        plugins.keys().cloned().collect()
    }

    /// 重新加载插件
    pub async fn reload_plugin(&self, plugin_id: &str) -> Result<()> {
        // 获取插件的当前配置
        let config = {
            let configs = self.plugin_configs.read().await;
            configs.get(plugin_id).cloned()
        };

        if let Some(config) = config {
            // 先关闭现有插件
            {
                let mut plugins = self.plugins.write().await;
                if let Some(mut plugin) = plugins.get_mut(plugin_id) {
                    plugin.shutdown().await?;
                    plugin.initialize(&config).await?;
                }
            }

            // 清除验证器缓存
            self.clear_validator_cache_for_plugin(plugin_id).await;

            log::info!("Plugin '{}' reloaded successfully", plugin_id);
            Ok(())
        } else {
            Err(ConfigEngineError::InvalidConfiguration(format!(
                "Plugin '{}' not found",
                plugin_id
            )))
        }
    }

    /// 更新插件配置
    pub async fn update_plugin_config(
        &self,
        plugin_id: String,
        config: PluginConfig,
    ) -> Result<()> {
        // 验证插件存在
        {
            let plugins = self.plugins.read().await;
            if !plugins.contains_key(&plugin_id) {
                return Err(ConfigEngineError::InvalidConfiguration(format!(
                    "Plugin '{}' not found",
                    plugin_id
                )));
            }
        }

        // 更新配置
        {
            let mut configs = self.plugin_configs.write().await;
            configs.insert(plugin_id.clone(), config.clone());
        }

        // 重新初始化插件
        {
            let mut plugins = self.plugins.write().await;
            if let Some(plugin) = plugins.get_mut(&plugin_id) {
                plugin.shutdown().await?;
                plugin.initialize(&config).await?;
            }
        }

        // 清除验证器缓存
        self.clear_validator_cache_for_plugin(&plugin_id).await;

        log::info!("Plugin '{}' configuration updated", plugin_id);
        Ok(())
    }

    /// 获取插件配置
    pub async fn get_plugin_config(&self, plugin_id: &str) -> Option<PluginConfig> {
        let configs = self.plugin_configs.read().await;
        configs.get(plugin_id).cloned()
    }

    /// 执行插件健康检查
    pub async fn health_check_all_plugins(&self) -> HashMap<String, PluginHealthStatus> {
        let mut results = HashMap::new();
        let plugins = self.plugins.read().await;

        for (plugin_id, plugin) in plugins.iter() {
            let health_status = plugin.health_check().await;
            results.insert(plugin_id.clone(), health_status);
        }

        results
    }

    /// 注册插件发现器
    pub async fn register_discoverer(&self, discoverer: Box<dyn PluginDiscoverer>) {
        let mut discoverers = self.plugin_discoverers.write().await;
        discoverers.push(discoverer);
    }

    /// 自动发现并注册插件
    pub async fn auto_discover_plugins(&self) -> Result<usize> {
        let mut registered_count = 0;
        let discoverers = self.plugin_discoverers.read().await;

        for discoverer in discoverers.iter() {
            let discovered_plugins = discoverer.discover().await?;

            for plugin in discovered_plugins {
                let plugin_id = format!("{}_{}", plugin.name(), plugin.version());

                match self.register_plugin(plugin_id, plugin).await {
                    Ok(_) => {
                        registered_count += 1;
                    }
                    Err(e) => {
                        log::warn!("Failed to register discovered plugin: {}", e);
                    }
                }
            }
        }

        log::info!(
            "Auto-discovered and registered {} plugins",
            registered_count
        );
        Ok(registered_count)
    }

    /// 清除所有验证器缓存
    pub async fn clear_all_validator_cache(&self) {
        let mut cache = self.validator_cache.write().await;
        cache.clear();
        log::debug!("Cleared all validator cache");
    }

    /// 获取管理器统计信息
    pub async fn get_statistics(&self) -> ManagerStatistics {
        let stats = self.statistics.read().await;
        stats.clone()
    }

    /// 关闭所有插件
    pub async fn shutdown_all_plugins(&self) -> Result<()> {
        let mut plugins = self.plugins.write().await;
        let mut errors = Vec::new();

        for (plugin_id, plugin) in plugins.iter_mut() {
            if let Err(e) = plugin.shutdown().await {
                errors.push(format!("Failed to shutdown plugin '{}': {}", plugin_id, e));
            }
        }

        plugins.clear();

        if !errors.is_empty() {
            log::warn!("Errors during plugin shutdown: {}", errors.join("; "));
        }

        log::info!("All plugins shut down");
        Ok(())
    }

    /// 获取或创建验证器实例
    async fn get_or_create_validator(
        &self,
        cache_key: &str,
        plugin: &dyn ValidatorPlugin,
        validator_type: &str,
    ) -> Result<Box<dyn Validator>> {
        // 检查缓存
        if self.manager_config.enable_validator_cache {
            let cache = self.validator_cache.read().await;
            if let Some(validator) = cache.get(cache_key) {
                // 注意：这里需要克隆验证器，但 Validator trait 没有 Clone
                // 实际实现中需要考虑如何处理这个问题
                // 暂时直接创建新实例
            }
        }

        // 创建新的验证器实例
        let validator = plugin.create_validator(validator_type).await?;

        // 缓存验证器（如果启用）
        if self.manager_config.enable_validator_cache {
            // 由于 Validator trait 没有 Clone，这里暂时不缓存
            // 实际实现中可能需要重新设计缓存策略
        }

        Ok(validator)
    }

    /// 清除指定插件的验证器缓存
    async fn clear_validator_cache_for_plugin(&self, plugin_id: &str) {
        let mut cache = self.validator_cache.write().await;
        cache.retain(|key, _| !key.starts_with(&format!("{}:", plugin_id)));
    }
}

impl Default for ValidatorPluginManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 插件管理器配置
#[derive(Debug, Clone)]
pub struct PluginManagerConfig {
    /// 是否启用验证器缓存
    pub enable_validator_cache: bool,
    /// 验证器缓存大小限制
    pub validator_cache_size: usize,
    /// 插件发现间隔（秒）
    pub discovery_interval_seconds: u64,
    /// 插件健康检查间隔（秒）
    pub health_check_interval_seconds: u64,
    /// 是否启用插件热重载
    pub enable_hot_reload: bool,
    /// 最大并发验证器数量
    pub max_concurrent_validators: usize,
}

impl Default for PluginManagerConfig {
    fn default() -> Self {
        Self {
            enable_validator_cache: true,
            validator_cache_size: 1000,
            discovery_interval_seconds: 300,   // 5分钟
            health_check_interval_seconds: 60, // 1分钟
            enable_hot_reload: false,
            max_concurrent_validators: 100,
        }
    }
}

/// 插件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginInfo {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub supported_types: Vec<String>,
    pub capabilities: Vec<ValidatorCapability>,
    pub health_status: PluginHealthStatus,
    pub statistics: PluginStatistics,
}

/// 管理器统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ManagerStatistics {
    pub registered_plugins: usize,
    pub active_plugins: usize,
    pub total_validators_created: u64,
    pub cached_validators: usize,
    pub plugin_discovery_runs: u64,
    pub health_check_runs: u64,
}

impl ManagerStatistics {
    pub fn new() -> Self {
        Self {
            registered_plugins: 0,
            active_plugins: 0,
            total_validators_created: 0,
            cached_validators: 0,
            plugin_discovery_runs: 0,
            health_check_runs: 0,
        }
    }
}

impl Default for ManagerStatistics {
    fn default() -> Self {
        Self::new()
    }
}

/// 插件发现器接口
#[async_trait]
pub trait PluginDiscoverer: Send + Sync {
    /// 发现可用的插件
    async fn discover(&self) -> Result<Vec<Box<dyn ValidatorPlugin>>>;

    /// 获取发现器名称
    fn name(&self) -> &str;

    /// 获取发现器描述
    fn description(&self) -> &str;
}

/// 目录插件发现器
#[derive(Debug)]
pub struct DirectoryPluginDiscoverer {
    /// 搜索目录
    pub search_directories: Vec<String>,
    /// 插件文件模式
    pub file_patterns: Vec<String>,
}

impl DirectoryPluginDiscoverer {
    pub fn new(search_directories: Vec<String>) -> Self {
        Self {
            search_directories,
            file_patterns: vec![
                "*.so".to_string(),    // Linux 动态库
                "*.dll".to_string(),   // Windows 动态库
                "*.dylib".to_string(), // macOS 动态库
            ],
        }
    }

    pub fn with_patterns(mut self, patterns: Vec<String>) -> Self {
        self.file_patterns = patterns;
        self
    }
}

#[async_trait]
impl PluginDiscoverer for DirectoryPluginDiscoverer {
    async fn discover(&self) -> Result<Vec<Box<dyn ValidatorPlugin>>> {
        // 实际实现中应该扫描目录，加载动态库，并创建插件实例
        // 这里提供一个简化的实现框架
        let mut plugins = Vec::new();

        for directory in &self.search_directories {
            // 扫描目录中的插件文件
            // 加载动态库
            // 创建插件实例
            // 暂时返回空结果
        }

        Ok(plugins)
    }

    fn name(&self) -> &str {
        "DirectoryPluginDiscoverer"
    }

    fn description(&self) -> &str {
        "Discovers plugins from specified directories"
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // 测试用的简单验证器插件
    #[derive(Debug)]
    struct TestValidatorPlugin {
        name: String,
        version: String,
        initialized: bool,
    }

    impl TestValidatorPlugin {
        fn new(name: String) -> Self {
            Self {
                name,
                version: "1.0.0".to_string(),
                initialized: false,
            }
        }
    }

    #[async_trait]
    impl ValidatorPlugin for TestValidatorPlugin {
        fn name(&self) -> &str {
            &self.name
        }

        fn version(&self) -> &str {
            &self.version
        }

        fn description(&self) -> &str {
            "Test validator plugin"
        }

        fn author(&self) -> &str {
            "Test Author"
        }

        fn supported_types(&self) -> Vec<String> {
            vec!["test_type".to_string()]
        }

        fn capabilities(&self) -> Vec<ValidatorCapability> {
            vec![ValidatorCapability::Synchronous, ValidatorCapability::Regex]
        }

        async fn initialize(&mut self, _config: &PluginConfig) -> Result<()> {
            self.initialized = true;
            Ok(())
        }

        async fn shutdown(&mut self) -> Result<()> {
            self.initialized = false;
            Ok(())
        }

        async fn create_validator(&self, _validator_type: &str) -> Result<Box<dyn Validator>> {
            Ok(Box::new(TestValidator::new()))
        }

        async fn list_validator_types(&self) -> Vec<ValidatorInfo> {
            vec![
                ValidatorInfo::new("test_validator".to_string(), "Test Validator".to_string())
                    .with_supported_type("test_type".to_string())
                    .with_capability(ValidatorCapability::Synchronous),
            ]
        }

        async fn health_check(&self) -> PluginHealthStatus {
            if self.initialized {
                PluginHealthStatus::healthy()
            } else {
                PluginHealthStatus::unhealthy("Plugin not initialized".to_string())
            }
        }

        fn config_schema(&self) -> serde_json::Value {
            serde_json::json!({
                "type": "object",
                "properties": {
                    "test_param": {"type": "string"}
                }
            })
        }

        async fn get_statistics(&self) -> PluginStatistics {
            PluginStatistics::new()
        }
    }

    // 测试用的简单验证器
    struct TestValidator;

    impl TestValidator {
        fn new() -> Self {
            Self
        }
    }

    impl Validator for TestValidator {
        fn validator_name(&self) -> &str {
            "TestValidator"
        }

        fn validate(
            &self,
            _context: &mut ValidationContext,
            _data: &dyn ValidatableData,
        ) -> crate::config_engine::Result<()> {
            Ok(())
        }

        fn priority(&self) -> i32 {
            0
        }

        fn supports(&self, _data_type: &str) -> bool {
            true
        }
    }

    #[tokio::test]
    async fn test_plugin_registration() {
        let manager = ValidatorPluginManager::new();
        let plugin = Box::new(TestValidatorPlugin::new("test_plugin".to_string()));

        let result = manager
            .register_plugin("test_plugin".to_string(), plugin)
            .await;
        assert!(result.is_ok());

        let plugins = manager.list_plugins().await;
        assert_eq!(plugins.len(), 1);
        assert!(plugins.contains(&"test_plugin".to_string()));
    }

    #[tokio::test]
    async fn test_plugin_unregistration() {
        let manager = ValidatorPluginManager::new();
        let plugin = Box::new(TestValidatorPlugin::new("test_plugin".to_string()));

        manager
            .register_plugin("test_plugin".to_string(), plugin)
            .await
            .unwrap();

        let removed = manager.unregister_plugin("test_plugin").await.unwrap();
        assert!(removed);

        let plugins = manager.list_plugins().await;
        assert!(plugins.is_empty());
    }

    #[tokio::test]
    async fn test_validator_discovery() {
        let manager = ValidatorPluginManager::new();
        let plugin = Box::new(TestValidatorPlugin::new("test_plugin".to_string()));

        manager
            .register_plugin("test_plugin".to_string(), plugin)
            .await
            .unwrap();

        let validators = manager.discover_validators("test_type").await.unwrap();
        assert_eq!(validators.len(), 1);
    }

    #[tokio::test]
    async fn test_plugin_info() {
        let manager = ValidatorPluginManager::new();
        let plugin = Box::new(TestValidatorPlugin::new("test_plugin".to_string()));

        manager
            .register_plugin("test_plugin".to_string(), plugin)
            .await
            .unwrap();

        let info = manager.get_plugin_info("test_plugin").await;
        assert!(info.is_some());

        let info = info.unwrap();
        assert_eq!(info.name, "test_plugin");
        assert_eq!(info.version, "1.0.0");
        assert!(info.health_status.healthy);
    }

    #[tokio::test]
    async fn test_plugin_config_update() {
        let manager = ValidatorPluginManager::new();
        let plugin = Box::new(TestValidatorPlugin::new("test_plugin".to_string()));

        manager
            .register_plugin("test_plugin".to_string(), plugin)
            .await
            .unwrap();

        let new_config = PluginConfig::new("test_plugin".to_string()).with_parameter(
            "test_param".to_string(),
            serde_json::Value::String("test_value".to_string()),
        );

        let result = manager
            .update_plugin_config("test_plugin".to_string(), new_config.clone())
            .await;
        assert!(result.is_ok());

        let retrieved_config = manager.get_plugin_config("test_plugin").await;
        assert!(retrieved_config.is_some());
        assert_eq!(retrieved_config.unwrap().parameters, new_config.parameters);
    }

    #[tokio::test]
    async fn test_health_check_all_plugins() {
        let manager = ValidatorPluginManager::new();
        let plugin1 = Box::new(TestValidatorPlugin::new("plugin1".to_string()));
        let plugin2 = Box::new(TestValidatorPlugin::new("plugin2".to_string()));

        manager
            .register_plugin("plugin1".to_string(), plugin1)
            .await
            .unwrap();
        manager
            .register_plugin("plugin2".to_string(), plugin2)
            .await
            .unwrap();

        let health_results = manager.health_check_all_plugins().await;
        assert_eq!(health_results.len(), 2);
        assert!(health_results["plugin1"].healthy);
        assert!(health_results["plugin2"].healthy);
    }

    #[tokio::test]
    async fn test_capability_discovery() {
        let manager = ValidatorPluginManager::new();
        let plugin = Box::new(TestValidatorPlugin::new("test_plugin".to_string()));

        manager
            .register_plugin("test_plugin".to_string(), plugin)
            .await
            .unwrap();

        let validators = manager
            .discover_validators_by_capability(&ValidatorCapability::Synchronous)
            .await
            .unwrap();
        assert_eq!(validators.len(), 1);

        let validators = manager
            .discover_validators_by_capability(&ValidatorCapability::Asynchronous)
            .await
            .unwrap();
        assert_eq!(validators.len(), 0);
    }

    #[test]
    fn test_plugin_config_builder() {
        let config = PluginConfig::new("test_plugin".to_string())
            .with_parameter(
                "param1".to_string(),
                serde_json::Value::String("value1".to_string()),
            )
            .with_enabled_validator("validator1".to_string())
            .with_log_level("debug".to_string())
            .with_timeout(60000)
            .with_memory_limit(1024 * 1024);

        assert_eq!(config.plugin_id, "test_plugin");
        assert_eq!(
            config.parameters["param1"],
            serde_json::Value::String("value1".to_string())
        );
        assert!(config
            .enabled_validators
            .contains(&"validator1".to_string()));
        assert_eq!(config.log_level, "debug");
        assert_eq!(config.timeout_ms, Some(60000));
        assert_eq!(config.max_memory_usage, Some(1024 * 1024));
    }

    #[test]
    fn test_validator_info_builder() {
        let info = ValidatorInfo::new("test_validator".to_string(), "Test Validator".to_string())
            .with_description("A test validator".to_string())
            .with_supported_type("test_type".to_string())
            .with_capability(ValidatorCapability::Synchronous)
            .with_priority(10)
            .with_parameter("param1".to_string(), serde_json::Value::Bool(true));

        assert_eq!(info.validator_type, "test_validator");
        assert_eq!(info.name, "Test Validator");
        assert_eq!(info.description, "A test validator");
        assert!(info.supported_types.contains(&"test_type".to_string()));
        assert!(info
            .capabilities
            .contains(&ValidatorCapability::Synchronous));
        assert_eq!(info.priority, 10);
        assert_eq!(info.parameters["param1"], serde_json::Value::Bool(true));
    }
}
