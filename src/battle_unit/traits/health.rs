/// 生命值系统trait
/// 
/// 管理生命值的各个方面，包括伤害、治疗和生命状态

use crate::shared::*;
use crate::battle_system::simplified_battle_traits::HealResult;
use crate::battle_unit::{DamageInfo, DamageRecord, DetailedDamageResult, HealRecord};

/// 生命值系统 - 管理生命值的各个方面
pub trait HealthSystem {
    /// 获取当前生命值
    fn current_health(&self) -> Health;
    
    /// 获取最大生命值
    fn max_health(&self) -> Health;
    
    /// 获取基础生命值（不含装备加成）
    fn base_health(&self) -> Health;
    
    /// 获取生命值加成
    fn health_bonus(&self) -> Health;
    
    /// 获取生命恢复速度（每秒）
    fn health_regeneration_rate(&self) -> Health;
    
    /// 检查是否存活
    fn is_alive(&self) -> bool;
    
    /// 检查是否处于濒死状态
    fn is_near_death(&self) -> bool;
    
    /// 获取生命值百分比
    fn health_percentage(&self) -> f32;
    
    /// 受到直接伤害
    fn take_direct_damage(&mut self, damage: Health) -> GameResult<crate::battle_system::simplified_battle_traits::DamageResult>;
    
    /// 受到计算后的伤害（考虑防御、抗性等）
    fn take_calculated_damage(&mut self, damage_info: DamageInfo) -> GameResult<DetailedDamageResult>;
    
    /// 恢复生命值
    fn heal(&mut self, amount: Health) -> GameResult<HealResult>;
    
    /// 设置生命值（管理员功能）
    fn set_health(&mut self, health: Health) -> GameResult<()>;
    
    /// 获取伤害记录
    fn damage_history(&self) -> Vec<DamageRecord>;
    
    /// 获取治疗记录  
    fn heal_history(&self) -> Vec<HealRecord>;
}