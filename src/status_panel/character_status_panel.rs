use crate::character::domain::Character;
use crate::status_panel::StatusPanel;
use std::collections::HashMap;
use serde_json::json;


impl StatusPanel for Character {
    fn print_status(&self) {
        println!("角色：{} (Lv.{})", self.name(), self.level());
        println!("装备：");
        // 显示已装备的装备
        for (slot, eq_id) in self.equipment().get_all_equipped() {
            // NOTE: We only have access to the ID here. To get the name,
            // we'd need to query an EquipmentRepository.
            println!("  - {}: ID {}", slot, eq_id.0);
        }
        println!("总属性：");
    }

    fn get_status_data(&self) -> HashMap<String, serde_json::Value> {
        let mut data = HashMap::new();
        
        // 基本信息
        data.insert("name".to_string(), json!(self.name()));
        data.insert("level".to_string(), json!(self.level()));
        data.insert("hp".to_string(), json!(self.current_health()));
        data.insert("max_hp".to_string(), json!(self.max_health()));
        data.insert("exp".to_string(), json!(self.experience()));
        
        // 装备信息
        let equipment: Vec<serde_json::Value> = self.equipment().get_all_equipped().iter()
            .map(|(slot, eq_id)| {
                json!({
                    "slot": slot,
                    "id": eq_id.0
                })
            })
            .collect();
        data.insert("equipment".to_string(), serde_json::Value::Array(equipment));

        data
    }
}
