/// 内存优化模块
/// 
/// 提供战斗系统的内存优化策略：
/// - 内存池管理
/// - 紧凑数据结构
/// - 内存分析工具
/// - 缓存优化数据布局
/// - 内存预算管理

use crate::shared::*;
use std::collections::HashMap;
use std::mem;

// ============================================================================
// 内存池管理
// ============================================================================

/// 通用内存池
/// 
/// 为固定大小的对象提供内存池管理
pub struct MemoryPool<T> {
    /// 可用内存块
    available_blocks: Vec<*mut T>,
    /// 已分配内存块
    allocated_blocks: Vec<*mut T>,
    /// 块大小
    block_size: usize,
    /// 池容量
    capacity: usize,
}

impl<T> MemoryPool<T> {
    /// 创建新的内存池
    pub fn new(capacity: usize) -> Self {
        let block_size = mem::size_of::<T>();
        Self {
            available_blocks: Vec::with_capacity(capacity),
            allocated_blocks: Vec::new(),
            block_size,
            capacity,
        }
    }
    
    /// 分配内存块
    pub fn allocate(&mut self) -> Option<*mut T> {
        if let Some(block) = self.available_blocks.pop() {
            self.allocated_blocks.push(block);
            Some(block)
        } else if self.allocated_blocks.len() < self.capacity {
            // 分配新的内存块
            let layout = std::alloc::Layout::new::<T>();
            unsafe {
                let ptr = std::alloc::alloc(layout) as *mut T;
                if !ptr.is_null() {
                    self.allocated_blocks.push(ptr);
                    Some(ptr)
                } else {
                    None
                }
            }
        } else {
            None
        }
    }
    
    /// 释放内存块
    pub fn deallocate(&mut self, ptr: *mut T) {
        if let Some(pos) = self.allocated_blocks.iter().position(|&p| p == ptr) {
            self.allocated_blocks.remove(pos);
            self.available_blocks.push(ptr);
        }
    }
    
    /// 获取池统计信息
    pub fn stats(&self) -> MemoryPoolStats {
        MemoryPoolStats {
            total_capacity: self.capacity,
            allocated_count: self.allocated_blocks.len(),
            available_count: self.available_blocks.len(),
            block_size: self.block_size,
            total_memory_bytes: self.capacity * self.block_size,
            used_memory_bytes: self.allocated_blocks.len() * self.block_size,
        }
    }
}

impl<T> Drop for MemoryPool<T> {
    fn drop(&mut self) {
        // 清理所有分配的内存
        let layout = std::alloc::Layout::new::<T>();
        for &ptr in &self.allocated_blocks {
            unsafe {
                std::alloc::dealloc(ptr as *mut u8, layout);
            }
        }
        for &ptr in &self.available_blocks {
            unsafe {
                std::alloc::dealloc(ptr as *mut u8, layout);
            }
        }
    }
}

// ============================================================================
// 紧凑战斗状态
// ============================================================================

/// 紧凑的战斗状态表示
/// 
/// 使用紧凑的内存布局来减少缓存未命中
#[repr(C, packed)]
#[derive(Debug, Clone, Copy)]
pub struct CompactBattleState {
    /// 单位ID (4字节)
    pub unit_id: u32,
    /// 生命值 (4字节)
    pub health: u32,
    /// 法力值 (4字节)
    pub mana: u32,
    /// X坐标 (4字节)
    pub pos_x: f32,
    /// Y坐标 (4字节)
    pub pos_y: f32,
    /// 状态标志位 (1字节)
    pub status_flags: u8,
    /// 填充字节保证对齐 (3字节)
    _padding: [u8; 3],
}

impl CompactBattleState {
    pub fn new(unit_id: u32, health: u32, mana: u32, pos_x: f32, pos_y: f32) -> Self {
        Self {
            unit_id,
            health,
            mana,
            pos_x,
            pos_y,
            status_flags: 0,
            _padding: [0; 3],
        }
    }
    
    /// 设置状态标志
    pub fn set_status_flag(&mut self, flag: StatusFlag) {
        self.status_flags |= flag as u8;
    }
    
    /// 清除状态标志
    pub fn clear_status_flag(&mut self, flag: StatusFlag) {
        self.status_flags &= !(flag as u8);
    }
    
    /// 检查状态标志
    pub fn has_status_flag(&self, flag: StatusFlag) -> bool {
        (self.status_flags & (flag as u8)) != 0
    }
}

/// 状态标志位
#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum StatusFlag {
    Alive = 1 << 0,      // 存活
    CanMove = 1 << 1,    // 可移动
    CanAttack = 1 << 2,  // 可攻击
    CanCast = 1 << 3,    // 可施法
    Stunned = 1 << 4,    // 眩晕
    Silenced = 1 << 5,   // 沉默
    Immobilized = 1 << 6, // 定身
    Reserved = 1 << 7,   // 保留位
}

// ============================================================================
// 内存分析器
// ============================================================================

/// 内存分析器
/// 
/// 分析和监控内存使用情况
pub struct MemoryProfiler {
    /// 内存分配记录
    allocations: HashMap<String, AllocationRecord>,
    /// 峰值内存使用
    peak_usage: usize,
    /// 当前内存使用
    current_usage: usize,
}

impl MemoryProfiler {
    pub fn new() -> Self {
        Self {
            allocations: HashMap::new(),
            peak_usage: 0,
            current_usage: 0,
        }
    }
    
    /// 记录内存分配
    pub fn record_allocation(&mut self, name: String, size: usize) {
        self.current_usage += size;
        if self.current_usage > self.peak_usage {
            self.peak_usage = self.current_usage;
        }
        
        let record = self.allocations.entry(name).or_insert_with(|| AllocationRecord {
            total_allocated: 0,
            current_allocated: 0,
            allocation_count: 0,
            deallocation_count: 0,
        });
        
        record.total_allocated += size;
        record.current_allocated += size;
        record.allocation_count += 1;
    }
    
    /// 记录内存释放
    pub fn record_deallocation(&mut self, name: &str, size: usize) {
        self.current_usage = self.current_usage.saturating_sub(size);
        
        if let Some(record) = self.allocations.get_mut(name) {
            record.current_allocated = record.current_allocated.saturating_sub(size);
            record.deallocation_count += 1;
        }
    }
    
    /// 获取内存使用报告
    pub fn get_memory_report(&self) -> MemoryReport {
        MemoryReport {
            current_usage: self.current_usage,
            peak_usage: self.peak_usage,
            allocation_records: self.allocations.clone(),
        }
    }
    
    /// 重置统计
    pub fn reset(&mut self) {
        self.allocations.clear();
        self.peak_usage = 0;
        self.current_usage = 0;
    }
}

// ============================================================================
// 缓存优化数据结构
// ============================================================================

/// 缓存优化的数据布局
/// 
/// 将经常一起访问的数据放在同一缓存行中
#[repr(C, align(64))] // 64字节缓存行对齐
pub struct CacheOptimizedData {
    /// 热数据 - 经常访问的数据 (前32字节)
    pub hot_data: HotData,
    /// 填充到缓存行边界
    _padding1: [u8; 32],
    
    /// 温数据 - 中等频率访问的数据 (第二个缓存行)
    pub warm_data: WarmData,
    /// 填充到缓存行边界
    _padding2: [u8; 32],
    
    /// 冷数据 - 很少访问的数据
    pub cold_data: ColdData,
}

#[repr(C, packed)]
#[derive(Debug, Clone, Copy)]
pub struct HotData {
    pub unit_id: u32,
    pub health: u32,
    pub mana: u32,
    pub position_x: f32,
    pub position_y: f32,
    pub status_flags: u32,
    pub last_action_time: u64,
}

#[repr(C, packed)]
#[derive(Debug, Clone, Copy)]
pub struct WarmData {
    pub max_health: u32,
    pub max_mana: u32,
    pub attack_power: u32,
    pub defense_power: u32,
    pub movement_speed: f32,
    pub attack_range: f32,
    pub level: u32,
    pub experience: u32,
}

#[repr(C)]
#[derive(Debug, Clone)]
pub struct ColdData {
    pub name: String,
    pub description: String,
    pub creation_time: std::time::SystemTime,
    pub statistics: HashMap<String, u64>,
}

// ============================================================================
// 内存预算管理
// ============================================================================

/// 内存预算管理器
pub struct MemoryBudget {
    /// 总预算
    total_budget: usize,
    /// 各组件预算分配
    component_budgets: HashMap<String, ComponentBudget>,
    /// 当前使用量
    current_usage: usize,
}

impl MemoryBudget {
    pub fn new(total_budget_mb: usize) -> Self {
        Self {
            total_budget: total_budget_mb * 1024 * 1024,
            component_budgets: HashMap::new(),
            current_usage: 0,
        }
    }
    
    /// 设置组件预算
    pub fn set_component_budget(&mut self, component: String, budget_mb: usize) -> GameResult<()> {
        let budget_bytes = budget_mb * 1024 * 1024;
        
        // 检查预算是否超出总预算
        let total_allocated: usize = self.component_budgets.values()
            .map(|b| b.allocated_budget)
            .sum();
            
        if total_allocated + budget_bytes > self.total_budget {
            return Err(GameError::validation_error(
                "memory_budget", 
                "组件预算超出总预算限制"
            ));
        }
        
        self.component_budgets.insert(component, ComponentBudget {
            allocated_budget: budget_bytes,
            used_budget: 0,
        });
        
        Ok(())
    }
    
    /// 申请内存
    pub fn request_memory(&mut self, component: &str, size: usize) -> GameResult<()> {
        if let Some(budget) = self.component_budgets.get_mut(component) {
            if budget.used_budget + size > budget.allocated_budget {
                return Err(GameError::validation_error(
                    "memory_budget",
                    "组件内存使用超出预算"
                ));
            }
            
            budget.used_budget += size;
            self.current_usage += size;
            Ok(())
        } else {
            Err(GameError::config_error(
                "MemoryBudget", 
                &format!("未找到组件 '{}' 的预算配置", component)
            ))
        }
    }
    
    /// 释放内存
    pub fn release_memory(&mut self, component: &str, size: usize) {
        if let Some(budget) = self.component_budgets.get_mut(component) {
            budget.used_budget = budget.used_budget.saturating_sub(size);
            self.current_usage = self.current_usage.saturating_sub(size);
        }
    }
    
    /// 获取预算使用报告
    pub fn get_budget_report(&self) -> BudgetReport {
        BudgetReport {
            total_budget: self.total_budget,
            current_usage: self.current_usage,
            utilization_ratio: self.current_usage as f64 / self.total_budget as f64,
            component_budgets: self.component_budgets.clone(),
        }
    }
}

// ============================================================================
// 数据结构定义
// ============================================================================

/// 内存池统计
#[derive(Debug, Clone)]
pub struct MemoryPoolStats {
    pub total_capacity: usize,
    pub allocated_count: usize,
    pub available_count: usize,
    pub block_size: usize,
    pub total_memory_bytes: usize,
    pub used_memory_bytes: usize,
}

/// 分配记录
#[derive(Debug, Clone)]
pub struct AllocationRecord {
    pub total_allocated: usize,
    pub current_allocated: usize,
    pub allocation_count: u64,
    pub deallocation_count: u64,
}

/// 内存报告
#[derive(Debug, Clone)]
pub struct MemoryReport {
    pub current_usage: usize,
    pub peak_usage: usize,
    pub allocation_records: HashMap<String, AllocationRecord>,
}

/// 组件预算
#[derive(Debug, Clone)]
pub struct ComponentBudget {
    pub allocated_budget: usize,
    pub used_budget: usize,
}

/// 预算报告
#[derive(Debug, Clone)]
pub struct BudgetReport {
    pub total_budget: usize,
    pub current_usage: usize,
    pub utilization_ratio: f64,
    pub component_budgets: HashMap<String, ComponentBudget>,
}

// ============================================================================
// 便捷函数
// ============================================================================

/// 获取类型的内存大小信息
pub fn get_type_memory_info<T>() -> TypeMemoryInfo {
    TypeMemoryInfo {
        size: mem::size_of::<T>(),
        alignment: mem::align_of::<T>(),
        type_name: std::any::type_name::<T>().to_string(),
    }
}

/// 类型内存信息
#[derive(Debug, Clone)]
pub struct TypeMemoryInfo {
    pub size: usize,
    pub alignment: usize,
    pub type_name: String,
}

/// 计算数据结构的缓存行对齐
pub fn calculate_cache_line_padding(size: usize) -> usize {
    const CACHE_LINE_SIZE: usize = 64;
    let remainder = size % CACHE_LINE_SIZE;
    if remainder == 0 {
        0
    } else {
        CACHE_LINE_SIZE - remainder
    }
}