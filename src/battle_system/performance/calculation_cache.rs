/// 计算缓存模块
/// 
/// 提供战斗计算的缓存机制：
/// - 伤害计算缓存
/// - 技能效果缓存
/// - 路径查找缓存
/// - 缓存策略管理
/// - 缓存生命周期管理

use crate::shared::*;
use std::collections::HashMap;
use std::hash::{Hash, Hasher};
use std::time::{Instant, Duration};

// ============================================================================
// 伤害计算缓存
// ============================================================================

/// 伤害计算缓存
/// 
/// 缓存复杂的伤害计算结果，避免重复计算
pub struct DamageCalculationCache {
    /// 缓存条目
    cache: HashMap<DamageKey, CachedDamageResult>,
    /// 缓存策略
    strategy: CacheStrategy,
    /// 统计信息
    stats: CacheStats,
}

impl DamageCalculationCache {
    pub fn new(strategy: CacheStrategy) -> Self {
        Self {
            cache: HashMap::new(),
            strategy,
            stats: CacheStats::new(),
        }
    }
    
    /// 获取伤害计算结果
    pub fn get_damage(
        &mut self,
        attacker_id: BattleUnitId,
        defender_id: BattleUnitId,
        skill_id: SkillId,
        context: &DamageContext,
    ) -> Option<DamageResult> {
        let key = DamageKey::new(attacker_id, defender_id, skill_id, context);
        
        if let Some(cached) = self.cache.get(&key) {
            if !cached.is_expired() {
                self.stats.record_hit();
                return Some(cached.result.clone());
            } else {
                // 缓存过期，移除
                self.cache.remove(&key);
            }
        }
        
        self.stats.record_miss();
        None
    }
    
    /// 缓存伤害计算结果
    pub fn cache_damage(
        &mut self,
        attacker_id: BattleUnitId,
        defender_id: BattleUnitId,
        skill_id: SkillId,
        context: &DamageContext,
        result: DamageResult,
    ) {
        let key = DamageKey::new(attacker_id, defender_id, skill_id, context);
        let ttl = self.strategy.get_ttl("damage_calculation");
        
        let cached_result = CachedDamageResult {
            result,
            cached_at: Instant::now(),
            ttl,
        };
        
        // 检查缓存容量
        if self.cache.len() >= self.strategy.max_entries {
            self.evict_entries();
        }
        
        self.cache.insert(key, cached_result);
    }
    
    /// 清理过期条目
    pub fn cleanup_expired(&mut self) {
        let before_count = self.cache.len();
        self.cache.retain(|_, cached| !cached.is_expired());
        let removed_count = before_count - self.cache.len();
        self.stats.expired_entries += removed_count;
    }
    
    /// 驱逐条目（LRU策略）
    fn evict_entries(&mut self) {
        // 简化实现：移除最旧的条目
        if let Some(oldest_key) = self.cache.iter()
            .min_by_key(|(_, cached)| cached.cached_at)
            .map(|(key, _)| key.clone())
        {
            self.cache.remove(&oldest_key);
            self.stats.evicted_entries += 1;
        }
    }
    
    pub fn get_stats(&self) -> &CacheStats {
        &self.stats
    }
    
    pub fn clear(&mut self) {
        self.cache.clear();
        self.stats.reset();
    }
}

/// 伤害计算键
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct DamageKey {
    attacker_id: BattleUnitId,
    defender_id: BattleUnitId,
    skill_id: SkillId,
    context_hash: u64,
}

impl DamageKey {
    fn new(
        attacker_id: BattleUnitId,
        defender_id: BattleUnitId,
        skill_id: SkillId,
        context: &DamageContext,
    ) -> Self {
        let mut hasher = std::collections::hash_map::DefaultHasher::new();
        context.hash(&mut hasher);
        let context_hash = hasher.finish();
        
        Self {
            attacker_id,
            defender_id,
            skill_id,
            context_hash,
        }
    }
}

/// 伤害上下文
#[derive(Debug, Clone)]
pub struct DamageContext {
    pub attacker_attack: i32,
    pub defender_defense: i32,
    pub skill_level: Level,
    pub critical_hit: bool,
    pub damage_modifiers: Vec<f32>,
}

impl Hash for DamageContext {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.attacker_attack.hash(state);
        self.defender_defense.hash(state);
        self.skill_level.hash(state);
        self.critical_hit.hash(state);
        // 简化处理浮点数数组
        for modifier in &self.damage_modifiers {
            let converted = (*modifier * 1000.0) as i32;
            converted.hash(state);
        }
    }
}

/// 缓存的伤害结果
#[derive(Debug, Clone)]
struct CachedDamageResult {
    result: DamageResult,
    cached_at: Instant,
    ttl: Duration,
}

impl CachedDamageResult {
    fn is_expired(&self) -> bool {
        self.cached_at.elapsed() > self.ttl
    }
}

/// 伤害结果
#[derive(Debug, Clone)]
pub struct DamageResult {
    pub final_damage: i32,
    pub is_critical: bool,
    pub damage_type: String,
    pub resistances_applied: Vec<String>,
}

// ============================================================================
// 技能效果缓存
// ============================================================================

/// 技能效果缓存
pub struct SkillEffectCache {
    cache: HashMap<EffectKey, CachedEffectResult>,
    strategy: CacheStrategy,
    stats: CacheStats,
}

impl SkillEffectCache {
    pub fn new(strategy: CacheStrategy) -> Self {
        Self {
            cache: HashMap::new(),
            strategy,
            stats: CacheStats::new(),
        }
    }
    
    /// 获取技能效果结果
    pub fn get_effect(
        &mut self,
        skill_id: SkillId,
        caster_level: Level,
        target_state: &TargetState,
    ) -> Option<EffectResult> {
        let key = EffectKey::new(skill_id, caster_level, target_state);
        
        if let Some(cached) = self.cache.get(&key) {
            if !cached.is_expired() {
                self.stats.record_hit();
                return Some(cached.result.clone());
            } else {
                self.cache.remove(&key);
            }
        }
        
        self.stats.record_miss();
        None
    }
    
    /// 缓存技能效果结果
    pub fn cache_effect(
        &mut self,
        skill_id: SkillId,
        caster_level: Level,
        target_state: &TargetState,
        result: EffectResult,
    ) {
        let key = EffectKey::new(skill_id, caster_level, target_state);
        let ttl = self.strategy.get_ttl("skill_effect");
        
        let cached_result = CachedEffectResult {
            result,
            cached_at: Instant::now(),
            ttl,
        };
        
        if self.cache.len() >= self.strategy.max_entries {
            self.evict_oldest();
        }
        
        self.cache.insert(key, cached_result);
    }
    
    fn evict_oldest(&mut self) {
        if let Some(oldest_key) = self.cache.iter()
            .min_by_key(|(_, cached)| cached.cached_at)
            .map(|(key, _)| key.clone())
        {
            self.cache.remove(&oldest_key);
            self.stats.evicted_entries += 1;
        }
    }
    
    pub fn cleanup_expired(&mut self) {
        let before_count = self.cache.len();
        self.cache.retain(|_, cached| !cached.is_expired());
        self.stats.expired_entries += before_count - self.cache.len();
    }
    
    pub fn get_stats(&self) -> &CacheStats {
        &self.stats
    }
}

/// 技能效果键
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct EffectKey {
    skill_id: SkillId,
    caster_level: Level,
    target_state_hash: u64,
}

impl EffectKey {
    fn new(skill_id: SkillId, caster_level: Level, target_state: &TargetState) -> Self {
        let mut hasher = std::collections::hash_map::DefaultHasher::new();
        target_state.hash(&mut hasher);
        let target_state_hash = hasher.finish();
        
        Self {
            skill_id,
            caster_level,
            target_state_hash,
        }
    }
}

/// 目标状态
#[derive(Debug, Clone)]
pub struct TargetState {
    pub health_percentage: f32,
    pub mana_percentage: f32,
    pub active_statuses: Vec<String>,
    pub level: Level,
}

impl Hash for TargetState {
    fn hash<H: Hasher>(&self, state: &mut H) {
        let health_int = (self.health_percentage * 100.0) as i32;
        health_int.hash(state);
        let mana_int = (self.mana_percentage * 100.0) as i32;
        mana_int.hash(state);
        self.active_statuses.hash(state);
        self.level.hash(state);
    }
}

/// 缓存的效果结果
#[derive(Debug, Clone)]
struct CachedEffectResult {
    result: EffectResult,
    cached_at: Instant,
    ttl: Duration,
}

impl CachedEffectResult {
    fn is_expired(&self) -> bool {
        self.cached_at.elapsed() > self.ttl
    }
}

/// 效果结果
#[derive(Debug, Clone)]
pub struct EffectResult {
    pub effects_applied: Vec<String>,
    pub duration: f32,
    pub magnitude: f32,
    pub success: bool,
}

// ============================================================================
// 路径查找缓存
// ============================================================================

/// 路径查找缓存
pub struct PathfindingCache {
    cache: HashMap<PathKey, CachedPath>,
    strategy: CacheStrategy,
    stats: CacheStats,
}

impl PathfindingCache {
    pub fn new(strategy: CacheStrategy) -> Self {
        Self {
            cache: HashMap::new(),
            strategy,
            stats: CacheStats::new(),
        }
    }
    
    /// 获取路径
    pub fn get_path(
        &mut self,
        from: Position,
        to: Position,
        movement_type: MovementType,
    ) -> Option<Vec<Position>> {
        let key = PathKey::new(from, to, movement_type);
        
        if let Some(cached) = self.cache.get(&key) {
            if !cached.is_expired() {
                self.stats.record_hit();
                return Some(cached.path.clone());
            } else {
                self.cache.remove(&key);
            }
        }
        
        self.stats.record_miss();
        None
    }
    
    /// 缓存路径
    pub fn cache_path(
        &mut self,
        from: Position,
        to: Position,
        movement_type: MovementType,
        path: Vec<Position>,
    ) {
        let key = PathKey::new(from, to, movement_type);
        let ttl = self.strategy.get_ttl("pathfinding");
        
        let cached_path = CachedPath {
            path,
            cached_at: Instant::now(),
            ttl,
        };
        
        if self.cache.len() >= self.strategy.max_entries {
            self.evict_oldest();
        }
        
        self.cache.insert(key, cached_path);
    }
    
    fn evict_oldest(&mut self) {
        if let Some(oldest_key) = self.cache.iter()
            .min_by_key(|(_, cached)| cached.cached_at)
            .map(|(key, _)| key.clone())
        {
            self.cache.remove(&oldest_key);
            self.stats.evicted_entries += 1;
        }
    }
    
    pub fn cleanup_expired(&mut self) {
        let before_count = self.cache.len();
        self.cache.retain(|_, cached| !cached.is_expired());
        self.stats.expired_entries += before_count - self.cache.len();
    }
    
    pub fn get_stats(&self) -> &CacheStats {
        &self.stats
    }
}

/// 路径键
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct PathKey {
    from: (i32, i32), // 简化Position为整数坐标
    to: (i32, i32),
    movement_type: MovementType,
}

impl PathKey {
    fn new(from: Position, to: Position, movement_type: MovementType) -> Self {
        Self {
            from: (from.x as i32, from.y as i32),
            to: (to.x as i32, to.y as i32),
            movement_type,
        }
    }
}

/// 缓存的路径
#[derive(Debug, Clone)]
struct CachedPath {
    path: Vec<Position>,
    cached_at: Instant,
    ttl: Duration,
}

impl CachedPath {
    fn is_expired(&self) -> bool {
        self.cached_at.elapsed() > self.ttl
    }
}

// ============================================================================
// 缓存管理器
// ============================================================================

/// 中央缓存管理器
pub struct CacheManager {
    damage_cache: DamageCalculationCache,
    effect_cache: SkillEffectCache,
    pathfinding_cache: PathfindingCache,
    global_strategy: CacheStrategy,
}

impl CacheManager {
    pub fn new(strategy: CacheStrategy) -> Self {
        Self {
            damage_cache: DamageCalculationCache::new(strategy.clone()),
            effect_cache: SkillEffectCache::new(strategy.clone()),
            pathfinding_cache: PathfindingCache::new(strategy.clone()),
            global_strategy: strategy,
        }
    }
    
    /// 获取伤害缓存
    pub fn damage_cache(&mut self) -> &mut DamageCalculationCache {
        &mut self.damage_cache
    }
    
    /// 获取效果缓存
    pub fn effect_cache(&mut self) -> &mut SkillEffectCache {
        &mut self.effect_cache
    }
    
    /// 获取路径缓存
    pub fn pathfinding_cache(&mut self) -> &mut PathfindingCache {
        &mut self.pathfinding_cache
    }
    
    /// 清理所有过期缓存
    pub fn cleanup_all_expired(&mut self) {
        self.damage_cache.cleanup_expired();
        self.effect_cache.cleanup_expired();
        self.pathfinding_cache.cleanup_expired();
    }
    
    /// 获取总体缓存统计
    pub fn get_global_stats(&self) -> GlobalCacheStats {
        GlobalCacheStats {
            damage_stats: self.damage_cache.get_stats().clone(),
            effect_stats: self.effect_cache.get_stats().clone(),
            pathfinding_stats: self.pathfinding_cache.get_stats().clone(),
        }
    }
    
    /// 清空所有缓存
    pub fn clear_all(&mut self) {
        self.damage_cache.clear();
        self.effect_cache.cleanup_expired();
        self.pathfinding_cache.cleanup_expired();
    }
}

// ============================================================================
// 缓存策略
// ============================================================================

/// 缓存策略
#[derive(Debug, Clone)]
pub struct CacheStrategy {
    /// 最大缓存条目数
    pub max_entries: usize,
    /// 不同类型缓存的TTL配置
    pub ttl_config: HashMap<String, Duration>,
    /// 清理策略
    pub cleanup_strategy: CleanupStrategy,
}

impl CacheStrategy {
    pub fn new() -> Self {
        let mut ttl_config = HashMap::new();
        ttl_config.insert("damage_calculation".to_string(), Duration::from_secs(30));
        ttl_config.insert("skill_effect".to_string(), Duration::from_secs(60));
        ttl_config.insert("pathfinding".to_string(), Duration::from_secs(120));
        
        Self {
            max_entries: 1000,
            ttl_config,
            cleanup_strategy: CleanupStrategy::LRU,
        }
    }
    
    /// 获取指定类型的TTL
    pub fn get_ttl(&self, cache_type: &str) -> Duration {
        self.ttl_config.get(cache_type)
            .cloned()
            .unwrap_or(Duration::from_secs(60))
    }
    
    /// 配置TTL
    pub fn set_ttl(&mut self, cache_type: String, ttl: Duration) {
        self.ttl_config.insert(cache_type, ttl);
    }
}

/// 清理策略
#[derive(Debug, Clone, PartialEq)]
pub enum CleanupStrategy {
    LRU,        // 最近最少使用
    FIFO,       // 先进先出
    Random,     // 随机清理
}

// ============================================================================
// 统计信息
// ============================================================================

/// 缓存统计
#[derive(Debug, Clone)]
pub struct CacheStats {
    pub total_requests: u64,
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub evicted_entries: usize,
    pub expired_entries: usize,
}

impl CacheStats {
    pub fn new() -> Self {
        Self {
            total_requests: 0,
            cache_hits: 0,
            cache_misses: 0,
            evicted_entries: 0,
            expired_entries: 0,
        }
    }
    
    pub fn record_hit(&mut self) {
        self.total_requests += 1;
        self.cache_hits += 1;
    }
    
    pub fn record_miss(&mut self) {
        self.total_requests += 1;
        self.cache_misses += 1;
    }
    
    pub fn hit_rate(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            self.cache_hits as f64 / self.total_requests as f64
        }
    }
    
    pub fn miss_rate(&self) -> f64 {
        1.0 - self.hit_rate()
    }
    
    pub fn reset(&mut self) {
        *self = Self::new();
    }
}

/// 全局缓存统计
#[derive(Debug, Clone)]
pub struct GlobalCacheStats {
    pub damage_stats: CacheStats,
    pub effect_stats: CacheStats,
    pub pathfinding_stats: CacheStats,
}

impl GlobalCacheStats {
    /// 计算总体命中率
    pub fn overall_hit_rate(&self) -> f64 {
        let total_requests = self.damage_stats.total_requests
            + self.effect_stats.total_requests
            + self.pathfinding_stats.total_requests;
            
        let total_hits = self.damage_stats.cache_hits
            + self.effect_stats.cache_hits
            + self.pathfinding_stats.cache_hits;
            
        if total_requests == 0 {
            0.0
        } else {
            total_hits as f64 / total_requests as f64
        }
    }
}