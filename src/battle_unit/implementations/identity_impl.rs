/// 身份相关trait实现
/// 
/// 实现BattleEntityIdentity trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;

use crate::battle_unit::traits::BattleEntityIdentity;

impl BattleEntityIdentity for BattleUnitAdapter {
    fn entity_id(&self) -> BattleUnitId {
        self.character.id().0 as BattleUnitId
    }
    
    fn display_name(&self) -> &str {
        self.character.name()
    }
    
    fn entity_type(&self) -> EntityType {
        EntityType::PlayerCharacter // 基于Character的都是玩家角色
    }
    
    fn faction(&self) -> Faction {
        Faction::Player
    }
    
    fn level(&self) -> Level {
        self.character.level()
    }
    
    fn experience(&self) -> crate::Exp {
        self.character.experience()
    }
    
    fn rarity(&self) -> Rarity {
        Rarity::Common // 角色默认普通稀有度
    }
    
    fn is_player_controlled(&self) -> bool {
        true
    }
    
    fn is_ai_controlled(&self) -> bool {
        false
    }
}