pub mod atom;
pub mod combat;
pub mod game_mechanics;
pub mod identifiers;
/// 统一的类型系统模块
///
/// 此模块提供了整个游戏系统的统一类型定义，
/// 避免重复和冲突，确保类型使用的一致性
// 重新导出所有类型模块
pub mod primitives;
pub mod resources;
pub mod result_types;
pub mod spatial;
pub mod temporal;
pub mod value_objects;

// 重新导出基础数值类型
pub use primitives::*;

// 重新导出标识符类型
pub use identifiers::*;

// 重新导出空间类型
pub use spatial::*;

// 重新导出时间类型
pub use temporal::*;

// 重新导出资源类型
pub use resources::*;

// 重新导出战斗类型
pub use combat::*;

// 重新导出游戏机制类型
pub use game_mechanics::*;

// 重新导出值对象
pub use value_objects::*;

// 重新导出结果类型
pub use result_types::*;

// ============================================================================
// 类型别名用于向后兼容
// ============================================================================

/// 百分比范围，常用于概率计算
pub type PercentRange = ValueRange<Percent>;

/// 等级范围，常用于条件判断
pub type LevelRange = ValueRange<Level>;

// 重新导入 serde 用于序列化
use serde::{Deserialize, Serialize};
