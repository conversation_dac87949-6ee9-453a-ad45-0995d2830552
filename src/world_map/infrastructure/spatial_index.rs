use crate::world_map::{BuildingId, NodeId, RegionId};
use crate::{Direction, Position, WorldLayer, WorldLayerType};
/// 空间索引系统
/// 提供高效的空间查询功能，支持快速查找指定区域内的对象
use std::collections::HashMap;

/// 空间索引 - 用于快速查询空间中的对象
#[derive(Debug, Clone)]
pub struct SpatialIndex {
    /// 网格大小（每个网格的边长）
    grid_size: i32,
    /// 按层级分组的网格
    grids: HashMap<WorldLayer, Grid>,
    /// 世界地图层映射
    layers: HashMap<WorldLayerType, WorldLayer>,
}

/// 网格系统 - 将空间划分为网格以提高查询效率
#[derive(Debug, Clone)]
struct Grid {
    /// 网格中的对象
    cells: HashMap<(i32, i32), GridCell>,
}

/// 网格单元 - 存储在特定网格位置的对象
#[derive(Debug, Clone, Default)]
struct GridCell {
    /// 区域ID列表
    regions: Vec<RegionId>,
    /// 资源节点ID列表
    resource_nodes: Vec<NodeId>,
    /// 建筑ID列表
    buildings: Vec<BuildingId>,
}

/// 空间查询结果
#[derive(Debug, Clone, Default)]
pub struct SpatialQueryResult {
    /// 找到的区域
    pub regions: Vec<RegionId>,
    /// 找到的资源节点
    pub resource_nodes: Vec<NodeId>,
    /// 找到的建筑
    pub buildings: Vec<BuildingId>,
}

/// 查询范围
#[derive(Debug, Clone)]
pub enum QueryRange {
    /// 单点查询
    Point(Position),
    /// 圆形范围
    Circle { center: Position, radius: f32 },
    /// 矩形范围
    Rectangle { min: Position, max: Position },
    /// 指定半径内的所有位置
    Radius { center: Position, radius: u32 },
}

impl SpatialIndex {
    /// 创建新的空间索引
    pub fn new(grid_size: i32) -> Self {
        Self {
            grid_size,
            grids: HashMap::new(),
            layers: HashMap::new(),
        }
    }

    /// 创建默认的空间索引（网格大小为50）
    pub fn default() -> Self {
        Self::new(50)
    }

    /// 选择世界地图层,通过类型选择
    pub fn select_layer(&mut self, layer_type: WorldLayerType) -> Option<WorldLayer> {
        // 如果layers映射中已经存在，直接返回
        if let Some(layer) = self.layers.get(&layer_type) {
            return Some(layer.clone());
        }

        // 如果不存在，创建一个新的WorldLayer
        let layer = self.create_world_layer_from_type(layer_type);
        self.layers.insert(layer_type, layer.clone());

        Some(layer)
    }

    /// 通过WorldLayerType获取对应的Grid
    pub fn get_grid_by_layer_type(&mut self, layer_type: WorldLayerType) -> Option<&mut Grid> {
        // 首先确保层级映射存在
        if !self.layers.contains_key(&layer_type) {
            let layer = self.create_world_layer_from_type(layer_type);
            self.layers.insert(layer_type, layer.clone());
            self.grids.insert(layer, Grid::new());
        }

        // 获取对应的WorldLayer
        if let Some(world_layer) = self.layers.get(&layer_type) {
            // 确保grids中存在对应的Grid
            let grid = self
                .grids
                .entry(world_layer.clone())
                .or_insert_with(Grid::new);
            return Some(grid);
        }

        None
    }

    /// 通过WorldLayerType获取对应的Grid（只读）
    pub fn get_grid_by_layer_type_readonly(&self, layer_type: WorldLayerType) -> Option<&Grid> {
        if let Some(world_layer) = self.layers.get(&layer_type) {
            return self.grids.get(world_layer);
        }
        None
    }

    /// 从WorldLayerType创建WorldLayer
    fn create_world_layer_from_type(&self, layer_type: WorldLayerType) -> WorldLayer {
        let (id, name) = match layer_type {
            WorldLayerType::Mortal => ("mortal".to_string(), "凡间".to_string()),
            WorldLayerType::Spirit => ("spirit".to_string(), "灵界".to_string()),
            WorldLayerType::Immortal => ("immortal".to_string(), "仙界".to_string()),
            WorldLayerType::Chaos => ("chaos".to_string(), "混沌界".to_string()),
            WorldLayerType::SecretRealm(realm_id) => (
                format!("secret_realm_{}", realm_id),
                format!("秘境-{}", realm_id),
            ),
        };

        WorldLayer::new(id, name, Vec::new())
    }

    /// 添加区域到索引
    pub fn add_region(&mut self, region_id: RegionId, position: Position) {
        let grid_pos = self.position_to_grid(position);
        let layer_type = position.layer.unwrap_or(WorldLayerType::Mortal);

        // 获取或创建对应的Grid
        if let Some(grid) = self.get_grid_by_layer_type(layer_type) {
            let cell = grid.cells.entry(grid_pos).or_insert_with(GridCell::default);

            if !cell.regions.contains(&region_id) {
                cell.regions.push(region_id);
            }
        }
    }

    /// 添加资源节点到索引
    pub fn add_resource_node(&mut self, node_id: NodeId, position: Position) {
        let grid_pos = self.position_to_grid(position);
        let layer_type = position.layer.unwrap_or(WorldLayerType::Mortal);

        // 获取或创建对应的Grid
        if let Some(grid) = self.get_grid_by_layer_type(layer_type) {
            let cell = grid.cells.entry(grid_pos).or_insert_with(GridCell::default);

            if !cell.resource_nodes.contains(&node_id) {
                cell.resource_nodes.push(node_id);
            }
        }
    }

    /// 添加建筑到索引
    pub fn add_building(&mut self, building_id: BuildingId, position: Position) {
        let grid_pos = self.position_to_grid(position);
        let layer_type = position.layer.unwrap_or(WorldLayerType::Mortal);

        // 获取或创建对应的Grid
        if let Some(grid) = self.get_grid_by_layer_type(layer_type) {
            let cell = grid.cells.entry(grid_pos).or_insert_with(GridCell::default);

            if !cell.buildings.contains(&building_id) {
                cell.buildings.push(building_id);
            }
        }
    }

    /// 从索引中移除区域
    pub fn remove_region(&mut self, region_id: RegionId, position: Position) {
        let grid_pos = self.position_to_grid(position);
        let layer_type = position.layer.unwrap_or(WorldLayerType::Mortal);

        if let Some(grid) = self.get_grid_by_layer_type(layer_type) {
            if let Some(cell) = grid.cells.get_mut(&grid_pos) {
                cell.regions.retain(|&id| id != region_id);
            }
        }
    }

    /// 从索引中移除资源节点
    pub fn remove_resource_node(&mut self, node_id: NodeId, position: Position) {
        let grid_pos = self.position_to_grid(position);
        let layer_type = position.layer.unwrap_or(WorldLayerType::Mortal);

        if let Some(grid) = self.get_grid_by_layer_type(layer_type) {
            if let Some(cell) = grid.cells.get_mut(&grid_pos) {
                cell.resource_nodes.retain(|&id| id != node_id);
            }
        }
    }

    /// 从索引中移除建筑
    pub fn remove_building(&mut self, building_id: BuildingId, position: Position) {
        let grid_pos = self.position_to_grid(position);
        let layer_type = position.layer.unwrap_or(WorldLayerType::Mortal);

        if let Some(grid) = self.get_grid_by_layer_type(layer_type) {
            if let Some(cell) = grid.cells.get_mut(&grid_pos) {
                cell.buildings.retain(|&id| id != building_id);
            }
        }
    }

    /// 查询指定范围内的对象
    pub fn query(&self, range: QueryRange) -> SpatialQueryResult {
        match range {
            QueryRange::Point(position) => self.query_point(position),
            QueryRange::Circle { center, radius } => self.query_circle(center, radius),
            QueryRange::Rectangle { min, max } => self.query_rectangle(min, max),
            QueryRange::Radius { center, radius } => self.query_radius(center, radius),
        }
    }

    /// 查询单点
    fn query_point(&self, position: Position) -> SpatialQueryResult {
        let mut result = SpatialQueryResult::default();
        let layer_type = position.layer.unwrap_or(WorldLayerType::Mortal);

        if let Some(grid) = self.get_grid_by_layer_type_readonly(layer_type) {
            let grid_pos = self.position_to_grid(position);
            if let Some(cell) = grid.cells.get(&grid_pos) {
                result.regions.extend_from_slice(&cell.regions);
                result
                    .resource_nodes
                    .extend_from_slice(&cell.resource_nodes);
                result.buildings.extend_from_slice(&cell.buildings);
            }
        }

        result
    }

    /// 查询圆形范围
    fn query_circle(&self, center: Position, radius: f32) -> SpatialQueryResult {
        let mut result = SpatialQueryResult::default();
        let layer_type = center.layer.unwrap_or(WorldLayerType::Mortal);

        if let Some(grid) = self.get_grid_by_layer_type_readonly(layer_type) {
            // 计算需要检查的网格范围
            let grid_radius = (radius / self.grid_size as f32).ceil() as i32;
            let center_grid = self.position_to_grid(center);

            for dx in -grid_radius..=grid_radius {
                for dy in -grid_radius..=grid_radius {
                    let grid_pos = (center_grid.0 + dx, center_grid.1 + dy);
                    if let Some(cell) = grid.cells.get(&grid_pos) {
                        // 简化实现：将整个网格的内容都加入结果
                        // 实际应用中可以进一步过滤距离
                        result.regions.extend_from_slice(&cell.regions);
                        result
                            .resource_nodes
                            .extend_from_slice(&cell.resource_nodes);
                        result.buildings.extend_from_slice(&cell.buildings);
                    }
                }
            }
        }

        // 去重
        result.regions.sort_unstable();
        result.regions.dedup();
        result.resource_nodes.sort_unstable();
        result.resource_nodes.dedup();
        result.buildings.sort_unstable();
        result.buildings.dedup();

        result
    }

    /// 查询矩形范围
    fn query_rectangle(&self, min: Position, max: Position) -> SpatialQueryResult {
        let mut result = SpatialQueryResult::default();

        // 确保在同一层级
        if min.layer != max.layer {
            return result;
        }

        let layer_type = min.layer.unwrap_or(WorldLayerType::Mortal);

        if let Some(grid) = self.get_grid_by_layer_type_readonly(layer_type) {
            let min_grid = self.position_to_grid(min);
            let max_grid = self.position_to_grid(max);

            for x in min_grid.0..=max_grid.0 {
                for y in min_grid.1..=max_grid.1 {
                    let grid_pos = (x, y);
                    if let Some(cell) = grid.cells.get(&grid_pos) {
                        result.regions.extend_from_slice(&cell.regions);
                        result
                            .resource_nodes
                            .extend_from_slice(&cell.resource_nodes);
                        result.buildings.extend_from_slice(&cell.buildings);
                    }
                }
            }
        }

        // 去重
        result.regions.sort_unstable();
        result.regions.dedup();
        result.resource_nodes.sort_unstable();
        result.resource_nodes.dedup();
        result.buildings.sort_unstable();
        result.buildings.dedup();

        result
    }

    /// 查询指定半径内的所有位置
    fn query_radius(&self, center: Position, radius: u32) -> SpatialQueryResult {
        self.query_circle(center, radius as f32)
    }

    /// 获取指定位置周围的对象
    pub fn get_nearby_objects(&self, position: Position, radius: f32) -> SpatialQueryResult {
        self.query(QueryRange::Circle {
            center: position,
            radius,
        })
    }

    /// 获取指定方向上的对象
    pub fn get_objects_in_direction(
        &self,
        position: Position,
        direction: Direction,
        distance: u32,
    ) -> SpatialQueryResult {
        let target_position = position.move_in_direction(direction, distance as f32);
        let min_x = position.x.min(target_position.x) as i32;
        let max_x = position.x.max(target_position.x) as i32;
        let min_y = position.y.min(target_position.y) as i32;
        let max_y = position.y.max(target_position.y) as i32;
        let layer = position.layer.unwrap_or(WorldLayerType::Mortal);

        let min_pos = Position::new_grid(min_x, min_y, layer);
        let max_pos = Position::new_grid(max_x, max_y, layer);

        self.query(QueryRange::Rectangle {
            min: min_pos,
            max: max_pos,
        })
    }

    /// 检查指定位置是否有对象
    pub fn has_objects_at(&self, position: Position) -> bool {
        let result = self.query_point(position);
        !result.regions.is_empty()
            || !result.resource_nodes.is_empty()
            || !result.buildings.is_empty()
    }

    /// 查找最近的资源节点
    pub fn find_nearest_resource_node(
        &self,
        position: Position,
        max_radius: f32,
    ) -> Option<NodeId> {
        let result = self.query_circle(position, max_radius);
        result.resource_nodes.first().copied()
    }

    /// 查找最近的建筑
    pub fn find_nearest_building(&self, position: Position, max_radius: f32) -> Option<BuildingId> {
        let result = self.query_circle(position, max_radius);
        result.buildings.first().copied()
    }

    /// 获取统计信息
    pub fn get_statistics(&self) -> SpatialIndexStatistics {
        let mut stats = SpatialIndexStatistics::default();

        for (layer, grid) in &self.grids {
            // 获取对应的WorldLayerType
            let layer_type = self
                .layers
                .iter()
                .find(|(_, v)| *v == layer)
                .map(|(k, _)| *k)
                .unwrap_or(WorldLayerType::Mortal);

            let layer_stats = LayerStatistics {
                layer: layer_type,
                grid_count: grid.cells.len(),
                total_regions: grid.cells.values().map(|cell| cell.regions.len()).sum(),
                total_resource_nodes: grid
                    .cells
                    .values()
                    .map(|cell| cell.resource_nodes.len())
                    .sum(),
                total_buildings: grid.cells.values().map(|cell| cell.buildings.len()).sum(),
            };
            stats.layers.push(layer_stats);
        }

        stats
    }

    /// 清空索引
    pub fn clear(&mut self) {
        self.grids.clear();
    }

    /// 清空指定层级的索引
    pub fn clear_layer(&mut self, layer_type: WorldLayerType) {
        if let Some(world_layer) = self.layers.get(&layer_type) {
            self.grids.remove(world_layer);
        }
        self.layers.remove(&layer_type);
    }

    /// 将位置转换为网格坐标
    fn position_to_grid(&self, position: Position) -> (i32, i32) {
        (
            (position.x as i32) / self.grid_size,
            (position.y as i32) / self.grid_size,
        )
    }
}

impl Grid {
    fn new() -> Self {
        Self {
            cells: HashMap::new(),
        }
    }
}

/// 空间索引统计信息
#[derive(Debug, Clone, Default)]
pub struct SpatialIndexStatistics {
    pub layers: Vec<LayerStatistics>,
}

/// 层级统计信息
#[derive(Debug, Clone)]
pub struct LayerStatistics {
    pub layer: WorldLayerType,
    pub grid_count: usize,
    pub total_regions: usize,
    pub total_resource_nodes: usize,
    pub total_buildings: usize,
}

impl SpatialIndexStatistics {
    /// 获取总的对象数量
    pub fn total_objects(&self) -> usize {
        self.layers
            .iter()
            .map(|layer| layer.total_regions + layer.total_resource_nodes + layer.total_buildings)
            .sum()
    }

    /// 获取总的网格数量
    pub fn total_grids(&self) -> usize {
        self.layers.iter().map(|layer| layer.grid_count).sum()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::world_map::domain::ids::*;
    use crate::WorldLayerType;

    #[test]
    fn test_spatial_index_basic() {
        let mut index = SpatialIndex::new(10);
        let position = Position::new_grid(5, 5, WorldLayerType::Mortal);
        let region_id = RegionId::new(1);

        index.add_region(region_id, position);

        let result = index.query_point(position);
        assert_eq!(result.regions.len(), 1);
        assert_eq!(result.regions[0], region_id);
    }

    #[test]
    fn test_spatial_index_circle_query() {
        let mut index = SpatialIndex::new(10);
        let center = Position::new_grid(50, 50, WorldLayerType::Mortal);
        let node_id = NodeId::new(1);

        // 添加一个在范围内的节点
        let nearby_position = Position::new_grid(55, 55, WorldLayerType::Mortal);
        index.add_resource_node(node_id, nearby_position);

        let result = index.query_circle(center, 20.0);
        assert!(!result.resource_nodes.is_empty());
    }

    #[test]
    fn test_spatial_index_remove() {
        let mut index = SpatialIndex::new(10);
        let position = Position::new_grid(5, 5, WorldLayerType::Mortal);
        let building_id = BuildingId::new(1);

        index.add_building(building_id, position);
        assert!(index.has_objects_at(position));

        index.remove_building(building_id, position);
        assert!(!index.has_objects_at(position));
    }
}
