/// 耐力系统trait实现
/// 
/// 实现StaminaSystem trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;
use crate::battle_unit::types::*;
use crate::battle_unit::traits::StaminaSystem;
use crate::attribute::{AttributeType, CoreAttribute};

impl StaminaSystem for BattleUnitAdapter {
    fn current_stamina(&self) -> Stamina {
        self.character.current_stamina()
    }
    
    fn max_stamina(&self) -> Stamina {
        self.character.max_stamina()
    }
    
    fn base_stamina(&self) -> Stamina {
        // 基于体质计算基础耐力值
        let constitution = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Earth)
        );
        constitution * 3 + 30 // 基础30 + 体质*3
    }
    
    fn stamina_bonus(&self) -> Stamina {
        self.max_stamina() - self.base_stamina()
    }
    
    fn stamina_regeneration_rate(&self) -> Stamina {
        let constitution_mod = BattleUnitAdapter::calculate_modifier(
            self.calculate_total_attribute(
                AttributeType::Base(CoreAttribute::Earth)
            )
        );
        (2 + constitution_mod).max(1) as Stamina
    }
    
    fn stamina_percentage(&self) -> f32 {
        if self.max_stamina() > 0 {
            self.current_stamina() as f32 / self.max_stamina() as f32
        } else {
            0.0
        }
    }
    
    fn consume_stamina(&mut self, amount: Stamina) -> GameResult<()> {
        let reduced_cost = (amount as f32 * (1.0 - self.stamina_cost_reduction())) as Stamina;
        if self.current_stamina() >= reduced_cost {
            // TODO: 使用Character的consume_stamina方法
            // self.character.consume_stamina(reduced_cost)?;
            Ok(())
        } else {
            Err("耐力不足".into())
        }
    }
    
    fn restore_stamina(&mut self, amount: Stamina) -> GameResult<()> {
        // TODO: 使用Character的restore_stamina方法
        // self.character.restore_stamina(amount)?;
        Ok(())
    }
    
    fn set_stamina(&mut self, stamina: Stamina) -> GameResult<()> {
        // TODO: 实现Character的set_stamina方法
        // self.character.set_stamina(stamina)?;
        Ok(())
    }
    
    fn has_enough_stamina(&self, required: Stamina) -> bool {
        let reduced_cost = (required as f32 * (1.0 - self.stamina_cost_reduction())) as Stamina;
        self.current_stamina() >= reduced_cost
    }
    
    fn stamina_cost_reduction(&self) -> f32 {
        // 基于装备和buff计算耐力消耗减免
        let mut reduction = 0.0;
        
        // 从装备获取减免
        let equipment_attrs = self.equipment_bar.get_total_equipment_attributes();
        // TODO: 添加耐力消耗减免属性到装备系统
        
        // 从buff获取减免
        for buff in &self.buff_manager.active_buffs {
            // TODO: 检查buff是否提供耐力消耗减免
        }
        
        reduction.min(0.8) // 最大80%减免
    }
    
    fn is_exhausted(&self) -> bool {
        self.current_stamina() == 0
    }
    
    fn fatigue_level(&self) -> FatigueLevel {
        let percentage = self.stamina_percentage();
        match percentage {
            p if p > 0.8 => FatigueLevel::Energetic,
            p if p > 0.6 => FatigueLevel::Normal,
            p if p > 0.4 => FatigueLevel::Tired,
            p if p > 0.2 => FatigueLevel::Exhausted,
            _ => FatigueLevel::Collapsed,
        }
    }
}