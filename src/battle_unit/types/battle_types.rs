//! # 战斗单位基础类型定义
//!
//! 定义战斗系统中的核心数据类型、枚举和结构体

use serde::{Deserialize, Serialize};

/// 耐力值类型
pub type StaminaPoints = i32;

/// 实体类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EntityType {
    /// 玩家角色
    Player,
    /// NPC
    Npc,
    /// 普通怪物
    Monster,
    /// 精英怪物
    EliteMonster,
    /// Boss怪物
    BossMonster,
    /// 召唤物
    Summon,
    /// 陷阱
    Trap,
    /// 建筑物
    Structure,
    /// 环境物体
    Environmental,
}

impl EntityType {
    /// 是否为敌对单位
    pub fn is_hostile(&self) -> bool {
        matches!(
            self,
            EntityType::Monster
                | EntityType::EliteMonster
                | EntityType::BossMonster
                | EntityType::Trap
        )
    }

    /// 是否为可控制单位
    pub fn is_controllable(&self) -> bool {
        matches!(self, EntityType::Player | EntityType::Summon)
    }

    /// 获取基础经验奖励倍数
    pub fn experience_multiplier(&self) -> f32 {
        match self {
            EntityType::Monster => 1.0,
            EntityType::EliteMonster => 2.5,
            EntityType::BossMonster => 5.0,
            EntityType::Trap => 0.1,
            _ => 0.0,
        }
    }
}

/// 阵营枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Faction {
    /// 玩家阵营
    Player,
    /// 友方阵营
    Allied,
    /// 中立阵营
    Neutral,
    /// 敌对阵营
    Hostile,
    /// 野生动物
    Wildlife,
    /// 恶魔阵营
    Demon,
    /// 不死族
    Undead,
    /// 元素阵营
    Elemental,
    /// 机械阵营
    Mechanical,
}

impl Faction {
    /// 检查两个阵营是否敌对
    pub fn is_hostile_to(&self, other: &Faction) -> bool {
        match (self, other) {
            (Faction::Player, Faction::Hostile) => true,
            (Faction::Player, Faction::Demon) => true,
            (Faction::Player, Faction::Undead) => true,
            (Faction::Hostile, Faction::Player) => true,
            (Faction::Hostile, Faction::Allied) => true,
            (Faction::Demon, Faction::Player) => true,
            (Faction::Demon, Faction::Allied) => true,
            (Faction::Undead, Faction::Player) => true,
            (Faction::Undead, Faction::Allied) => true,
            _ => false,
        }
    }

    /// 检查两个阵营是否友好
    pub fn is_allied_to(&self, other: &Faction) -> bool {
        match (self, other) {
            (a, b) if a == b => true,
            (Faction::Player, Faction::Allied) => true,
            (Faction::Allied, Faction::Player) => true,
            _ => false,
        }
    }

    /// 获取阵营颜色代码（用于UI显示）
    pub fn color_code(&self) -> &'static str {
        match self {
            Faction::Player => "#00FF00",     // 绿色
            Faction::Allied => "#0080FF",     // 蓝色
            Faction::Neutral => "#FFFF00",    // 黄色
            Faction::Hostile => "#FF0000",    // 红色
            Faction::Wildlife => "#808080",   // 灰色
            Faction::Demon => "#800080",      // 紫色
            Faction::Undead => "#400040",     // 深紫色
            Faction::Elemental => "#00FFFF",  // 青色
            Faction::Mechanical => "#C0C0C0", // 银色
        }
    }
}

/// 稀有度枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum Rarity {
    /// 普通
    Common,
    /// 优秀
    Uncommon,
    /// 稀有
    Rare,
    /// 史诗
    Epic,
    /// 传说
    Legendary,
    /// 神话
    Mythic,
    /// 远古
    Ancient,
    /// 太古
    Primordial,
}

impl Rarity {
    /// 获取稀有度等级（数值越高越稀有）
    pub fn level(&self) -> u8 {
        match self {
            Rarity::Common => 1,
            Rarity::Uncommon => 2,
            Rarity::Rare => 3,
            Rarity::Epic => 4,
            Rarity::Legendary => 5,
            Rarity::Mythic => 6,
            Rarity::Ancient => 7,
            Rarity::Primordial => 8,
        }
    }

    /// 获取稀有度颜色代码
    pub fn color_code(&self) -> &'static str {
        match self {
            Rarity::Common => "#FFFFFF",     // 白色
            Rarity::Uncommon => "#00FF00",   // 绿色
            Rarity::Rare => "#0080FF",       // 蓝色
            Rarity::Epic => "#8000FF",       // 紫色
            Rarity::Legendary => "#FF8000",  // 橙色
            Rarity::Mythic => "#FF0080",     // 粉色
            Rarity::Ancient => "#FF0000",    // 红色
            Rarity::Primordial => "#FFD700", // 金色
        }
    }

    /// 获取属性加成倍数
    pub fn stat_multiplier(&self) -> f32 {
        match self {
            Rarity::Common => 1.0,
            Rarity::Uncommon => 1.2,
            Rarity::Rare => 1.5,
            Rarity::Epic => 2.0,
            Rarity::Legendary => 3.0,
            Rarity::Mythic => 4.5,
            Rarity::Ancient => 7.0,
            Rarity::Primordial => 10.0,
        }
    }
}

/// 疲劳等级枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum FatigueLevel {
    /// 精力充沛
    Energetic,
    /// 正常
    Normal,
    /// 轻微疲劳
    SlightlyTired,
    /// 疲劳
    Tired,
    /// 非常疲劳
    VeryTired,
    /// 精疲力竭
    Exhausted,
}

impl FatigueLevel {
    /// 获取疲劳等级对应的数值（0-100）
    pub fn value(&self) -> u8 {
        match self {
            FatigueLevel::Energetic => 0,
            FatigueLevel::Normal => 20,
            FatigueLevel::SlightlyTired => 40,
            FatigueLevel::Tired => 60,
            FatigueLevel::VeryTired => 80,
            FatigueLevel::Exhausted => 100,
        }
    }

    /// 获取行动效率倍数
    pub fn action_efficiency(&self) -> f32 {
        match self {
            FatigueLevel::Energetic => 1.2,
            FatigueLevel::Normal => 1.0,
            FatigueLevel::SlightlyTired => 0.9,
            FatigueLevel::Tired => 0.7,
            FatigueLevel::VeryTired => 0.5,
            FatigueLevel::Exhausted => 0.3,
        }
    }

    /// 从数值创建疲劳等级
    pub fn from_value(value: u8) -> Self {
        match value {
            0..=10 => FatigueLevel::Energetic,
            11..=30 => FatigueLevel::Normal,
            31..=50 => FatigueLevel::SlightlyTired,
            51..=70 => FatigueLevel::Tired,
            71..=90 => FatigueLevel::VeryTired,
            _ => FatigueLevel::Exhausted,
        }
    }
}

/// 耐力结构体
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Stamina {
    /// 当前耐力值
    pub current: StaminaPoints,
    /// 最大耐力值
    pub max: StaminaPoints,
    /// 基础耐力值
    pub base: StaminaPoints,
    /// 耐力加成
    pub bonus: StaminaPoints,
    /// 耐力恢复速度（每秒）
    pub regeneration_rate: StaminaPoints,
    /// 疲劳等级
    pub fatigue_level: FatigueLevel,
}

impl Stamina {
    /// 创建新的耐力实例
    pub fn new(base: StaminaPoints) -> Self {
        Self {
            current: base,
            max: base,
            base,
            bonus: 0,
            regeneration_rate: base / 20, // 默认20秒恢复满
            fatigue_level: FatigueLevel::Normal,
        }
    }

    /// 更新最大耐力值
    pub fn update_max(&mut self) {
        self.max = self.base + self.bonus;
        if self.current > self.max {
            self.current = self.max;
        }
    }

    /// 消耗耐力
    pub fn consume(&mut self, amount: StaminaPoints) -> bool {
        if self.current >= amount {
            self.current -= amount;
            self.update_fatigue_level();
            true
        } else {
            false
        }
    }

    /// 恢复耐力
    pub fn restore(&mut self, amount: StaminaPoints) {
        self.current = (self.current + amount).min(self.max);
        self.update_fatigue_level();
    }

    /// 更新疲劳等级
    fn update_fatigue_level(&mut self) {
        let percentage = if self.max > 0 {
            ((self.max - self.current) * 100 / self.max) as u8
        } else {
            0
        };
        self.fatigue_level = FatigueLevel::from_value(percentage);
    }

    /// 获取耐力百分比
    pub fn percentage(&self) -> f32 {
        if self.max > 0 {
            self.current as f32 / self.max as f32
        } else {
            0.0
        }
    }

    /// 检查是否可以执行指定消耗的行动
    pub fn can_perform_action(&self, cost: StaminaPoints) -> bool {
        self.current >= cost
    }
}

impl Default for Stamina {
    fn default() -> Self {
        Self::new(100)
    }
}

/// 移动类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MovementType {
    /// 地面行走
    Walk,
    /// 奔跑
    Run,
    /// 潜行
    Sneak,
    /// 游泳
    Swim,
    /// 飞行
    Fly,
    /// 传送
    Teleport,
    /// 冲刺
    Dash,
    /// 跳跃
    Jump,
    /// 爬行
    Crawl,
    /// 滑行
    Glide,
}

impl MovementType {
    /// 获取移动速度倍数
    pub fn speed_multiplier(&self) -> f32 {
        match self {
            MovementType::Walk => 1.0,
            MovementType::Run => 2.0,
            MovementType::Sneak => 0.5,
            MovementType::Swim => 0.7,
            MovementType::Fly => 1.5,
            MovementType::Teleport => 10.0,
            MovementType::Dash => 3.0,
            MovementType::Jump => 1.2,
            MovementType::Crawl => 0.3,
            MovementType::Glide => 1.8,
        }
    }

    /// 获取耐力消耗倍数
    pub fn stamina_cost_multiplier(&self) -> f32 {
        match self {
            MovementType::Walk => 1.0,
            MovementType::Run => 2.5,
            MovementType::Sneak => 0.8,
            MovementType::Swim => 3.0,
            MovementType::Fly => 4.0,
            MovementType::Teleport => 10.0,
            MovementType::Dash => 5.0,
            MovementType::Jump => 1.5,
            MovementType::Crawl => 0.5,
            MovementType::Glide => 2.0,
        }
    }

    /// 检查是否需要特殊能力
    pub fn requires_special_ability(&self) -> bool {
        matches!(
            self,
            MovementType::Fly | MovementType::Teleport | MovementType::Glide
        )
    }
}

/// 元素类型枚举（从skill模块移动到这里避免循环依赖）
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ElementType {
    /// 物理
    Physical,
    /// 火
    Fire,
    /// 水
    Water,
    /// 风
    Wind,
    /// 土
    Earth,
    /// 雷电
    Lightning,
    /// 冰
    Ice,
    /// 光明
    Light,
    /// 黑暗
    Dark,
    /// 毒
    Poison,
    /// 神圣
    Holy,
    /// 混沌
    Chaos,
}

impl ElementType {
    /// 获取相克的元素类型
    pub fn counters(&self) -> Vec<ElementType> {
        match self {
            ElementType::Fire => vec![ElementType::Ice, ElementType::Water],
            ElementType::Water => vec![ElementType::Fire, ElementType::Earth],
            ElementType::Wind => vec![ElementType::Earth, ElementType::Lightning],
            ElementType::Earth => vec![ElementType::Wind, ElementType::Water],
            ElementType::Lightning => vec![ElementType::Water, ElementType::Wind],
            ElementType::Ice => vec![ElementType::Fire, ElementType::Lightning],
            ElementType::Light => vec![ElementType::Dark],
            ElementType::Dark => vec![ElementType::Light],
            ElementType::Holy => vec![ElementType::Dark, ElementType::Chaos],
            ElementType::Chaos => vec![ElementType::Holy, ElementType::Light],
            _ => vec![],
        }
    }

    /// 获取被克制的元素类型
    pub fn weak_to(&self) -> Vec<ElementType> {
        match self {
            ElementType::Fire => vec![ElementType::Water, ElementType::Ice],
            ElementType::Water => vec![ElementType::Lightning, ElementType::Ice],
            ElementType::Wind => vec![ElementType::Fire, ElementType::Lightning],
            ElementType::Earth => vec![ElementType::Wind, ElementType::Fire],
            ElementType::Lightning => vec![ElementType::Earth, ElementType::Wind],
            ElementType::Ice => vec![ElementType::Fire, ElementType::Lightning],
            ElementType::Light => vec![ElementType::Dark],
            ElementType::Dark => vec![ElementType::Light, ElementType::Holy],
            ElementType::Holy => vec![ElementType::Chaos],
            ElementType::Chaos => vec![ElementType::Holy, ElementType::Light],
            _ => vec![],
        }
    }

    /// 获取伤害倍数
    pub fn damage_multiplier(&self, target_element: &ElementType) -> f32 {
        if self.counters().contains(target_element) {
            1.5 // 克制时伤害增加50%
        } else if self.weak_to().contains(target_element) {
            0.75 // 被克制时伤害减少25%
        } else {
            1.0 // 正常伤害
        }
    }

    /// 获取元素颜色代码
    pub fn color_code(&self) -> &'static str {
        match self {
            ElementType::Physical => "#C0C0C0",  // 银色
            ElementType::Fire => "#FF4500",      // 橙红色
            ElementType::Water => "#0080FF",     // 蓝色
            ElementType::Wind => "#00FF80",      // 青绿色
            ElementType::Earth => "#8B4513",     // 棕色
            ElementType::Lightning => "#FFFF00", // 黄色
            ElementType::Ice => "#87CEEB",       // 天蓝色
            ElementType::Light => "#FFFACD",     // 柠檬绸色
            ElementType::Dark => "#2F2F2F",      // 深灰色
            ElementType::Poison => "#9932CC",    // 深兰花紫
            ElementType::Holy => "#FFD700",      // 金色
            ElementType::Chaos => "#8B008B",     // 深洋红色
        }
    }

    pub fn display_name(&self) -> &'static str {
        match self {
            ElementType::Physical => "物理",
            ElementType::Fire => "火",
            ElementType::Water => "水",
            ElementType::Wind => "风",
            ElementType::Earth => "土",
            ElementType::Lightning => "雷",
            ElementType::Ice => "冰",
            ElementType::Light => "光",
            ElementType::Dark => "暗",
            ElementType::Poison => "毒",
            ElementType::Holy => "圣",
            ElementType::Chaos => "混",
        }
    }
}
