//! # 配置源实现
//!
//! 提供多种配置源的具体实现，支持文件系统、环境变量、HTTP API等

use async_trait::async_trait;
use std::collections::HashMap;
use std::env;
use std::path::{Path, PathBuf};
use tokio::fs;

use crate::config_engine::{
    config::{
        loader::{ConfigFormat, ConfigLoader},
        RuleDefinition, TypeDefinition,
    },
    error::{ConfigEngineError, ConfigurationError},
    ConfigSourceType, Result,
};

/// 配置源抽象接口
///
/// 所有配置源都必须实现此trait，提供统一的配置加载接口
#[async_trait]
pub trait ConfigSource: Send + Sync {
    /// 获取配置源名称
    fn name(&self) -> &str;

    /// 获取配置源优先级（数字越大优先级越高）
    fn priority(&self) -> i32;

    /// 加载规则定义
    async fn load_rules(&self) -> Result<HashMap<String, RuleDefinition>>;

    /// 加载类型定义
    async fn load_types(&self) -> Result<HashMap<String, TypeDefinition>>;

    /// 检查配置是否有更新
    async fn check_for_updates(&self) -> Result<bool>;

    /// 获取配置源的元信息
    fn get_info(&self) -> ConfigSourceInfo;

    /// 检查是否处理指定路径的文件
    fn handles_path(&self, path: &Path) -> bool;
}

/// 配置源信息
#[derive(Debug, Clone)]
pub struct ConfigSourceInfo {
    pub name: String,
    pub source_type: String,
    pub priority: i32,
    pub description: String,
    pub last_check: Option<chrono::DateTime<chrono::Utc>>,
}

/// 文件系统配置源
///
/// 从本地文件系统加载配置文件，支持多种格式
pub struct FileSystemConfigSource {
    name: String,
    base_path: PathBuf,
    priority: i32,
    supported_formats: Vec<ConfigFormat>,
    last_modified: Option<std::time::SystemTime>,
}

impl FileSystemConfigSource {
    /// 创建新的文件系统配置源
    ///
    /// # 参数
    /// - `name`: 配置源名称
    /// - `base_path`: 配置文件根目录
    /// - `priority`: 优先级
    ///
    /// # 示例
    ///
    /// ```rust
    /// use game::config_engine::config::FileSystemConfigSource;
    ///
    /// let source = FileSystemConfigSource::new(
    ///     "local_configs".to_string(),
    ///     "./configs".to_string(),
    ///     100
    /// );
    /// ```
    pub fn new(name: String, base_path: String, priority: i32) -> Self {
        Self {
            name,
            base_path: PathBuf::from(base_path),
            priority,
            supported_formats: vec![
                ConfigFormat::Json,
                ConfigFormat::Yaml,
                ConfigFormat::Toml,
                ConfigFormat::Jdm,
            ],
            last_modified: None,
        }
    }

    /// 扫描目录中的配置文件
    async fn scan_config_files(&self) -> Result<Vec<(PathBuf, ConfigFormat)>> {
        let mut files = Vec::new();

        if !self.base_path.exists() {
            return Err(ConfigEngineError::Configuration(
                ConfigurationError::FileNotFound {
                    file: self.base_path.to_string_lossy().to_string(),
                },
            ));
        }

        let mut dir = fs::read_dir(&self.base_path).await.map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::LoadFailure {
                source: self.name.clone(),
                reason: format!("无法读取目录: {}", e),
            })
        })?;

        while let Some(entry) = dir.next_entry().await.map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::LoadFailure {
                source: self.name.clone(),
                reason: format!("读取目录项失败: {}", e),
            })
        })? {
            let path = entry.path();

            if path.is_file() {
                if let Some(format) = self.detect_file_format(&path) {
                    files.push((path, format));
                }
            } else if path.is_dir() {
                // 递归扫描子目录
                let sub_source = FileSystemConfigSource::new(
                    format!("{}/sub", self.name),
                    path.to_string_lossy().to_string(),
                    self.priority,
                );
                let sub_files = sub_source.scan_config_files().await?;
                files.extend(sub_files);
            }
        }

        Ok(files)
    }

    /// 检测文件格式
    fn detect_file_format(&self, path: &Path) -> Option<ConfigFormat> {
        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
            match extension.to_lowercase().as_str() {
                "json" => Some(ConfigFormat::Json),
                "yaml" | "yml" => Some(ConfigFormat::Yaml),
                "toml" => Some(ConfigFormat::Toml),
                "jdm" => Some(ConfigFormat::Jdm),
                _ => None,
            }
        } else {
            None
        }
    }

    /// 加载单个配置文件
    async fn load_config_file(
        &self,
        path: &Path,
        format: ConfigFormat,
    ) -> Result<(Vec<RuleDefinition>, Vec<TypeDefinition>)> {
        let content = fs::read_to_string(path).await.map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::LoadFailure {
                source: self.name.clone(),
                reason: format!("读取文件 {} 失败: {}", path.display(), e),
            })
        })?;

        let parsed = ConfigLoader::parse_content(&content, format).map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::ParseError {
                details: format!("解析文件 {} 失败: {}", path.display(), e),
            })
        })?;

        Ok((parsed.rules, parsed.types))
    }

    /// 获取目录的最后修改时间
    async fn get_last_modified(&self) -> Result<std::time::SystemTime> {
        let metadata = fs::metadata(&self.base_path).await.map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::LoadFailure {
                source: self.name.clone(),
                reason: format!("获取目录元信息失败: {}", e),
            })
        })?;

        metadata.modified().map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::LoadFailure {
                source: self.name.clone(),
                reason: format!("获取修改时间失败: {}", e),
            })
        })
    }
}

#[async_trait]
impl ConfigSource for FileSystemConfigSource {
    fn name(&self) -> &str {
        &self.name
    }

    fn priority(&self) -> i32 {
        self.priority
    }

    async fn load_rules(&self) -> Result<HashMap<String, RuleDefinition>> {
        let files = self.scan_config_files().await?;
        let mut rules = HashMap::new();

        for (path, format) in files {
            let (file_rules, _) = self.load_config_file(&path, format).await?;

            for rule in file_rules {
                if rules.contains_key(&rule.id) {
                    log::warn!(
                        "规则ID冲突: {} 在文件 {} 中重复定义",
                        rule.id,
                        path.display()
                    );
                }
                rules.insert(rule.id.clone(), rule);
            }
        }

        log::info!("从 {} 加载了 {} 个规则", self.name, rules.len());
        Ok(rules)
    }

    async fn load_types(&self) -> Result<HashMap<String, TypeDefinition>> {
        let files = self.scan_config_files().await?;
        let mut types = HashMap::new();

        for (path, format) in files {
            let (_, file_types) = self.load_config_file(&path, format).await?;

            for type_def in file_types {
                if types.contains_key(&type_def.id) {
                    log::warn!(
                        "类型ID冲突: {} 在文件 {} 中重复定义",
                        type_def.id,
                        path.display()
                    );
                }
                types.insert(type_def.id.clone(), type_def);
            }
        }

        log::info!("从 {} 加载了 {} 个类型定义", self.name, types.len());
        Ok(types)
    }

    async fn check_for_updates(&self) -> Result<bool> {
        let current_modified = self.get_last_modified().await?;

        if let Some(last_modified) = self.last_modified {
            Ok(current_modified > last_modified)
        } else {
            Ok(true) // 首次检查，认为有更新
        }
    }

    fn get_info(&self) -> ConfigSourceInfo {
        ConfigSourceInfo {
            name: self.name.clone(),
            source_type: "FileSystem".to_string(),
            priority: self.priority,
            description: format!("文件系统配置源: {}", self.base_path.display()),
            last_check: None,
        }
    }

    fn handles_path(&self, path: &Path) -> bool {
        // 检查路径是否在基础路径下，且是支持的配置文件格式
        if let Ok(relative_path) = path.strip_prefix(&self.base_path) {
            self.detect_file_format(path).is_some()
        } else {
            false
        }
    }
}

/// 环境变量配置源
///
/// 从环境变量加载配置，适用于运行时覆盖和容器化部署
pub struct EnvironmentConfigSource {
    name: String,
    prefix: String,
    priority: i32,
    separator: String,
}

impl EnvironmentConfigSource {
    /// 创建新的环境变量配置源
    ///
    /// # 参数
    /// - `name`: 配置源名称
    /// - `prefix`: 环境变量前缀（如"GAME_CONFIG"）
    /// - `priority`: 优先级
    ///
    /// # 示例
    ///
    /// ```rust
    /// use game::config_engine::config::EnvironmentConfigSource;
    ///
    /// let source = EnvironmentConfigSource::new(
    ///     "env_config".to_string(),
    ///     "GAME_CONFIG".to_string(),
    ///     200
    /// );
    /// ```
    pub fn new(name: String, prefix: String, priority: i32) -> Self {
        Self {
            name,
            prefix,
            priority,
            separator: "_".to_string(),
        }
    }

    pub fn with_separator(mut self, separator: String) -> Self {
        self.separator = separator;
        self
    }

    /// 扫描环境变量
    fn scan_env_vars(&self) -> HashMap<String, String> {
        let prefix_with_sep = format!("{}{}", self.prefix, self.separator);
        let mut vars = HashMap::new();

        for (key, value) in env::vars() {
            if key.starts_with(&prefix_with_sep) {
                let config_key = key[prefix_with_sep.len()..].to_string();
                vars.insert(config_key, value);
            }
        }

        vars
    }

    /// 解析规则配置
    fn parse_rule_configs(
        &self,
        env_vars: &HashMap<String, String>,
    ) -> Result<HashMap<String, RuleDefinition>> {
        let mut rules = HashMap::new();

        for (key, value) in env_vars {
            if key.starts_with("RULE_") {
                let rule_key = &key[5..]; // 移除"RULE_"前缀

                if let Some(rule_id_end) = rule_key.find('_') {
                    let rule_id = rule_key[..rule_id_end].to_lowercase();
                    let property = &rule_key[rule_id_end + 1..];

                    let rule = rules.entry(rule_id.clone()).or_insert_with(|| {
                        RuleDefinition::new(
                            rule_id.clone(),
                            rule_id.clone(),
                            "{}".to_string(), // 默认空JDM
                        )
                    });

                    match property.to_uppercase().as_str() {
                        "NAME" => rule.name = value.clone(),
                        "VERSION" => rule.version = value.clone(),
                        "DESCRIPTION" => rule.description = Some(value.clone()),
                        "JDM" => rule.jdm_content = value.clone(),
                        "ENABLED" => rule.enabled = value.parse().unwrap_or(true),
                        _ => {
                            rule.tags.insert(property.to_lowercase(), value.clone());
                        }
                    }
                }
            }
        }

        Ok(rules)
    }

    /// 解析类型配置
    fn parse_type_configs(
        &self,
        env_vars: &HashMap<String, String>,
    ) -> Result<HashMap<String, TypeDefinition>> {
        let mut types = HashMap::new();

        for (key, value) in env_vars {
            if key.starts_with("TYPE_") {
                let type_key = &key[5..]; // 移除"TYPE_"前缀

                if let Some(type_id_end) = type_key.find('_') {
                    let type_id = type_key[..type_id_end].to_lowercase();
                    let property = &type_key[type_id_end + 1..];

                    let type_def = types
                        .entry(type_id.clone())
                        .or_insert_with(|| TypeDefinition::new(type_id.clone(), type_id.clone()));

                    match property.to_uppercase().as_str() {
                        "NAME" => type_def.name = value.clone(),
                        "DESCRIPTION" => type_def.description = Some(value.clone()),
                        _ => {
                            type_def.tags.insert(property.to_lowercase(), value.clone());
                        }
                    }
                }
            }
        }

        Ok(types)
    }
}

#[async_trait]
impl ConfigSource for EnvironmentConfigSource {
    fn name(&self) -> &str {
        &self.name
    }

    fn priority(&self) -> i32 {
        self.priority
    }

    async fn load_rules(&self) -> Result<HashMap<String, RuleDefinition>> {
        let env_vars = self.scan_env_vars();
        let rules = self.parse_rule_configs(&env_vars)?;

        log::info!("从环境变量 {} 加载了 {} 个规则", self.name, rules.len());
        Ok(rules)
    }

    async fn load_types(&self) -> Result<HashMap<String, TypeDefinition>> {
        let env_vars = self.scan_env_vars();
        let types = self.parse_type_configs(&env_vars)?;

        log::info!("从环境变量 {} 加载了 {} 个类型定义", self.name, types.len());
        Ok(types)
    }

    async fn check_for_updates(&self) -> Result<bool> {
        // 环境变量在进程生命周期内通常不变，所以返回false
        // 实际项目中可能需要更复杂的检测逻辑
        Ok(false)
    }

    fn get_info(&self) -> ConfigSourceInfo {
        ConfigSourceInfo {
            name: self.name.clone(),
            source_type: "Environment".to_string(),
            priority: self.priority,
            description: format!("环境变量配置源: 前缀 {}", self.prefix),
            last_check: None,
        }
    }

    fn handles_path(&self, _path: &Path) -> bool {
        // 环境变量配置源不处理文件路径
        false
    }
}

/// HTTP配置源
///
/// 从远程HTTP API加载配置，支持集中式配置管理
#[cfg(feature = "reqwest")]
pub struct HttpConfigSource {
    name: String,
    base_url: String,
    priority: i32,
    client: reqwest::Client,
    auth_token: Option<String>,
    timeout: std::time::Duration,
}

#[cfg(feature = "reqwest")]
impl HttpConfigSource {
    /// 创建新的HTTP配置源
    pub fn new(name: String, base_url: String, priority: i32) -> Self {
        Self {
            name,
            base_url,
            priority,
            client: reqwest::Client::new(),
            auth_token: None,
            timeout: std::time::Duration::from_secs(30),
        }
    }

    pub fn with_auth_token(mut self, token: String) -> Self {
        self.auth_token = Some(token);
        self
    }

    pub fn with_timeout(mut self, timeout: std::time::Duration) -> Self {
        self.timeout = timeout;
        self
    }

    /// 构建HTTP请求
    fn build_request(&self, endpoint: &str) -> reqwest::RequestBuilder {
        let url = format!("{}/{}", self.base_url.trim_end_matches('/'), endpoint);
        let mut request = self.client.get(&url).timeout(self.timeout);

        if let Some(token) = &self.auth_token {
            request = request.bearer_auth(token);
        }

        request
    }

    /// 发送HTTP请求并处理响应
    async fn fetch_json<T>(&self, endpoint: &str) -> Result<T>
    where
        T: serde::de::DeserializeOwned,
    {
        let response = self.build_request(endpoint).send().await.map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::LoadFailure {
                source: self.name.clone(),
                reason: format!("HTTP请求失败: {}", e),
            })
        })?;

        if !response.status().is_success() {
            return Err(ConfigEngineError::Configuration(
                ConfigurationError::LoadFailure {
                    source: self.name.clone(),
                    reason: format!("HTTP错误: {}", response.status()),
                },
            ));
        }

        response.json().await.map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::ParseError {
                details: format!("解析HTTP响应失败: {}", e),
            })
        })
    }
}

#[cfg(feature = "reqwest")]
#[async_trait]
impl ConfigSource for HttpConfigSource {
    fn name(&self) -> &str {
        &self.name
    }

    fn priority(&self) -> i32 {
        self.priority
    }

    async fn load_rules(&self) -> Result<HashMap<String, RuleDefinition>> {
        let rules: Vec<RuleDefinition> = self.fetch_json("api/v1/rules").await?;

        let mut rules_map = HashMap::new();
        for rule in rules {
            rules_map.insert(rule.id.clone(), rule);
        }

        log::info!("从HTTP API {} 加载了 {} 个规则", self.name, rules_map.len());
        Ok(rules_map)
    }

    async fn load_types(&self) -> Result<HashMap<String, TypeDefinition>> {
        let types: Vec<TypeDefinition> = self.fetch_json("api/v1/types").await?;

        let mut types_map = HashMap::new();
        for type_def in types {
            types_map.insert(type_def.id.clone(), type_def);
        }

        log::info!(
            "从HTTP API {} 加载了 {} 个类型定义",
            self.name,
            types_map.len()
        );
        Ok(types_map)
    }

    async fn check_for_updates(&self) -> Result<bool> {
        #[derive(serde::Deserialize)]
        struct VersionInfo {
            version: String,
            last_modified: chrono::DateTime<chrono::Utc>,
        }

        let version_info: VersionInfo = self.fetch_json("api/v1/version").await?;

        // 这里可以实现更复杂的版本比较逻辑
        // 简单起见，总是返回true（假设远程配置可能有更新）
        Ok(true)
    }

    fn get_info(&self) -> ConfigSourceInfo {
        ConfigSourceInfo {
            name: self.name.clone(),
            source_type: "HTTP".to_string(),
            priority: self.priority,
            description: format!("HTTP配置源: {}", self.base_url),
            last_check: None,
        }
    }

    fn handles_path(&self, _path: &Path) -> bool {
        // HTTP配置源不处理文件路径
        false
    }
}

/// 从ConfigSourceType创建具体的ConfigSource实例
pub fn create_config_source(source_type: ConfigSourceType) -> Result<Box<dyn ConfigSource>> {
    match source_type {
        ConfigSourceType::FileSystem { path } => {
            Ok(Box::new(FileSystemConfigSource::new(
                "filesystem".to_string(),
                path,
                100, // 默认优先级
            )))
        }
        ConfigSourceType::Environment { prefix } => {
            Ok(Box::new(EnvironmentConfigSource::new(
                "environment".to_string(),
                prefix,
                200, // 环境变量优先级较高
            )))
        }
        #[cfg(feature = "reqwest")]
        ConfigSourceType::Http { url, auth } => {
            let mut source = HttpConfigSource::new(
                "http".to_string(),
                url,
                300, // HTTP配置源优先级最高
            );

            if let Some(token) = auth {
                source = source.with_auth_token(token);
            }

            Ok(Box::new(source))
        }
        #[cfg(feature = "sqlx")]
        ConfigSourceType::Database { connection_string } => {
            // 这里将来实现DatabaseConfigSource
            todo!("Database config source not implemented yet")
        }
        ConfigSourceType::Memory => {
            // 这里将来实现MemoryConfigSource（用于测试）
            todo!("Memory config source not implemented yet")
        }

        // 如果feature未启用但尝试使用相关功能，返回错误
        #[cfg(not(feature = "reqwest"))]
        ConfigSourceType::Http { .. } => Err(ConfigEngineError::InvalidConfiguration(
            "HTTP config source requires 'reqwest' feature".to_string(),
        )),
        #[cfg(not(feature = "sqlx"))]
        ConfigSourceType::Database { .. } => Err(ConfigEngineError::InvalidConfiguration(
            "Database config source requires 'sqlx' feature".to_string(),
        )),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio::fs;

    #[tokio::test]
    async fn test_filesystem_config_source() {
        let temp_dir = TempDir::new().unwrap();
        let config_content = r#"{
            "rules": [
                {
                    "id": "test_rule",
                    "name": "Test Rule",
                    "version": "1.0.0",
                    "jdm_content": "{\"contentType\": \"application/vnd.gorules.decision\"}",
                    "enabled": true
                }
            ]
        }"#;

        let config_file = temp_dir.path().join("rules.json");
        fs::write(&config_file, config_content).await.unwrap();

        let source = FileSystemConfigSource::new(
            "test_fs".to_string(),
            temp_dir.path().to_string_lossy().to_string(),
            100,
        );

        let rules = source.load_rules().await.unwrap();
        assert_eq!(rules.len(), 1);
        assert!(rules.contains_key("test_rule"));
    }

    #[tokio::test]
    async fn test_environment_config_source() {
        // 设置测试环境变量
        env::set_var("TEST_CONFIG_RULE_DISCOVERY_NAME", "Material Discovery");
        env::set_var("TEST_CONFIG_RULE_DISCOVERY_VERSION", "1.1.0");
        env::set_var("TEST_CONFIG_RULE_DISCOVERY_ENABLED", "true");

        let source =
            EnvironmentConfigSource::new("test_env".to_string(), "TEST_CONFIG".to_string(), 200);

        let rules = source.load_rules().await.unwrap();
        assert!(rules.contains_key("discovery"));

        let rule = &rules["discovery"];
        assert_eq!(rule.name, "Material Discovery");
        assert_eq!(rule.version, "1.1.0");
        assert!(rule.enabled);

        // 清理环境变量
        env::remove_var("TEST_CONFIG_RULE_DISCOVERY_NAME");
        env::remove_var("TEST_CONFIG_RULE_DISCOVERY_VERSION");
        env::remove_var("TEST_CONFIG_RULE_DISCOVERY_ENABLED");
    }

    #[test]
    fn test_create_config_source() {
        let fs_source = create_config_source(ConfigSourceType::FileSystem {
            path: "./test".to_string(),
        })
        .unwrap();

        assert_eq!(fs_source.name(), "filesystem");
        assert_eq!(fs_source.priority(), 100);

        let env_source = create_config_source(ConfigSourceType::Environment {
            prefix: "TEST".to_string(),
        })
        .unwrap();

        assert_eq!(env_source.name(), "environment");
        assert_eq!(env_source.priority(), 200);
    }
}
