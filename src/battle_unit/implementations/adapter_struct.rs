/// 战斗单位适配器结构体定义
/// 
/// 定义核心数据结构，整合现有模块

use crate::shared::*;
use crate::battle_unit::types::*;
use crate::character::Character;
use crate::attribute::AttributeSet;
use crate::skill::buff::BuffManager;
use crate::equipment::EquipmentBar;
use std::collections::HashMap;

/// 战斗修饰符
#[derive(Debug, Clone, Default)]
pub struct CombatModifiers {
    pub attack_bonus: Attack,
    pub defense_bonus: Defense,
    pub speed_multiplier: f32,
    pub damage_multiplier: f32,
    pub accuracy_bonus: Accuracy,
    pub evasion_bonus: Evasion,
    pub critical_rate_bonus: CriticalRate,
    pub critical_damage_bonus: CriticalDamage,
}

/// 基于现有模块的战斗单位适配器
/// 
/// 复用项目中已有的各个模块，组合成完整的战斗单位
#[derive(Debug, Clone)]
pub struct BattleUnitAdapter {
    // 核心身份信息 - 复用character模块
    pub character: Character,
    
    // 属性管理 - 复用attribute模块
    pub attribute_set: AttributeSet,
    pub attribute_bonuses: HashMap<crate::attribute::AttributeType, i32>,
    
    // 空间位置信息
    pub position: Position,
    pub facing_direction: Direction,
    pub movement_history: Vec<(Position, f64)>,
    
    // 时间管理
    pub creation_time: f64,
    pub last_update_time: f64,
    
    // 状态管理 - 复用buff模块
    pub buff_manager: BuffManager,
    pub status_effects: Vec<StatusEffect>,
    pub status_immunities: Vec<StatusType>,
    
    // 装备管理 - 复用equipment模块
    pub equipment_bar: EquipmentBar,
    
    // 技能管理 - 复用skill模块
    pub learned_skills: Vec<u32>,
    pub skill_cooldowns: HashMap<u32, f64>,
    pub skill_levels: HashMap<u32, u32>,
    pub current_casting_skill: Option<u32>,
    pub skill_points: u32,
    
    // 战斗状态
    pub action_points: ActionPoints,
    pub movement_fatigue: f32,
    pub combat_modifiers: CombatModifiers,
    
    // 抗性数据
    pub damage_resistances: HashMap<DamageType, Resistance>,
    pub status_resistances: HashMap<StatusType, Resistance>,
}

impl BattleUnitAdapter {
    /// 创建新的战斗单位适配器
    pub fn new(character: Character) -> Self {
        let position = character.position();
        let creation_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs_f64();
        
        // 从角色初始化属性
        let mut attribute_set = AttributeSet::new();
        
        // 设置基础五行属性（从角色体质、力量等转换）
        attribute_set.add(crate::attribute::Attribute::new(
            crate::attribute::AttributeType::Base(crate::attribute::CoreAttribute::Earth),
            character.constitution() as f64,
        ));
        attribute_set.add(crate::attribute::Attribute::new(
            crate::attribute::AttributeType::Base(crate::attribute::CoreAttribute::Fire),
            character.strength() as f64,
        ));
        
        Self {
            character,
            attribute_set,
            attribute_bonuses: HashMap::new(),
            position,
            facing_direction: Direction::North,
            movement_history: Vec::new(),
            creation_time,
            last_update_time: creation_time,
            buff_manager: BuffManager::new(),
            status_effects: Vec::new(),
            status_immunities: Vec::new(),
            equipment_bar: EquipmentBar::default(),
            learned_skills: Vec::new(),
            skill_cooldowns: HashMap::new(),
            skill_levels: HashMap::new(),
            current_casting_skill: None,
            skill_points: 0,
            action_points: 100,
            movement_fatigue: 0.0,
            combat_modifiers: CombatModifiers::default(),
            damage_resistances: HashMap::new(),
            status_resistances: HashMap::new(),
        }
    }
}