/// 技能学习trait
/// 
/// 管理技能的学习、遗忘和升级

use crate::shared::*;


/// 技能学习与管理
pub trait SkillLearning {
    /// 获取已学技能列表
    fn learned_skills(&self) -> Vec<SkillId>;
    
    /// 获取可学习技能列表
    fn learnable_skills(&self) -> Vec<SkillId>;
    
    /// 学习新技能
    fn learn_skill(&mut self, skill_id: SkillId) -> GameResult<()>;
    
    /// 遗忘技能
    fn forget_skill(&mut self, skill_id: SkillId) -> GameResult<()>;
    
    /// 检查是否已学会技能
    fn has_learned_skill(&self, skill_id: &SkillId) -> bool;
    
    /// 获取技能等级
    fn skill_level(&self, skill_id: &SkillId) -> u32;
    
    /// 升级技能
    fn upgrade_skill(&mut self, skill_id: SkillId) -> GameResult<()>;
    
    /// 获取技能点数
    fn skill_points(&self) -> u32;
    
    /// 消耗技能点数
    fn consume_skill_points(&mut self, amount: u32) -> GameResult<()>;
}