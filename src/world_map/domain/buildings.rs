/// 建筑系统相关的类型定义
/// 处理功能建筑的类型、效果和管理

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc}; // Duration 暂时未使用
use std::collections::HashMap;
use crate::world_map::domain::ids::BuildingId;
// use crate::world_map::domain::ids::{PlayerId, FactionId}; // 暂时未使用

// ============================================================================
// 建筑类型
// ============================================================================

/// 建筑类型 - 定义不同功能的建筑
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum BuildingType {
    /// 洞府 - 修士的居住和修炼场所
    CaveAbode,
    /// 炼丹房 - 专门用于炼制丹药
    AlchemyLab,
    /// 炼器坊 - 专门用于炼制法器
    CraftingWorkshop,
    /// 阵法台 - 布置和研究阵法
    FormationPlatform,
    /// 灵田 - 种植灵草和药材
    SpiritualFarm,
    /// 兽栏 - 饲养和训练灵兽
    BeastPen,
    /// 藏书楼 - 存放和研究典籍
    Library,
    /// 观星台 - 观测天象和修炼
    Observatory,
    /// 传送阵 - 远距离传送
    TeleportationArray,
    /// 商铺 - 买卖物品
    Shop,
    /// 仓库 - 存储物品
    Warehouse,
    /// 炼器炉 - 高级炼器设施
    RefinementFurnace,
}

impl BuildingType {
    /// 获取建筑的最大等级
    pub fn max_level(&self) -> u8 {
        match self {
            BuildingType::CaveAbode => 10,
            BuildingType::AlchemyLab => 8,
            BuildingType::CraftingWorkshop => 8,
            BuildingType::FormationPlatform => 6,
            BuildingType::SpiritualFarm => 5,
            BuildingType::BeastPen => 5,
            BuildingType::Library => 7,
            BuildingType::Observatory => 5,
            BuildingType::TeleportationArray => 3,
            BuildingType::Shop => 6,
            BuildingType::Warehouse => 8,
            BuildingType::RefinementFurnace => 9,
        }
    }
    
    /// 获取建筑的基础建造时间 (小时)
    pub fn base_construction_time(&self) -> u32 {
        match self {
            BuildingType::CaveAbode => 72,
            BuildingType::AlchemyLab => 24,
            BuildingType::CraftingWorkshop => 24,
            BuildingType::FormationPlatform => 48,
            BuildingType::SpiritualFarm => 12,
            BuildingType::BeastPen => 8,
            BuildingType::Library => 36,
            BuildingType::Observatory => 60,
            BuildingType::TeleportationArray => 168,
            BuildingType::Shop => 16,
            BuildingType::Warehouse => 20,
            BuildingType::RefinementFurnace => 96,
        }
    }
    
    /// 获取建筑类型的基础占地面积
    pub fn base_footprint_size(&self) -> (u32, u32) {
        match self {
            BuildingType::CaveAbode => (5, 5),
            BuildingType::AlchemyLab => (3, 3),
            BuildingType::CraftingWorkshop => (4, 4),
            BuildingType::FormationPlatform => (6, 6),
            BuildingType::SpiritualFarm => (8, 8),
            BuildingType::BeastPen => (6, 4),
            BuildingType::Library => (4, 6),
            BuildingType::Observatory => (3, 3),
            BuildingType::TeleportationArray => (2, 2),
            BuildingType::Shop => (3, 4),
            BuildingType::Warehouse => (5, 8),
            BuildingType::RefinementFurnace => (4, 4),
        }
    }
    
    /// 获取建筑类型的功能分类
    pub fn functional_category(&self) -> BuildingCategory {
        match self {
            BuildingType::CaveAbode => BuildingCategory::Residential,
            BuildingType::AlchemyLab => BuildingCategory::Production,
            BuildingType::CraftingWorkshop => BuildingCategory::Production,
            BuildingType::RefinementFurnace => BuildingCategory::Production,
            BuildingType::FormationPlatform => BuildingCategory::Utility,
            BuildingType::SpiritualFarm => BuildingCategory::Resource,
            BuildingType::BeastPen => BuildingCategory::Resource,
            BuildingType::Library => BuildingCategory::Knowledge,
            BuildingType::Observatory => BuildingCategory::Knowledge,
            BuildingType::TeleportationArray => BuildingCategory::Transport,
            BuildingType::Shop => BuildingCategory::Commerce,
            BuildingType::Warehouse => BuildingCategory::Storage,
        }
    }
}

/// 建筑功能分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum BuildingCategory {
    /// 居住类 - 提供居住和基础修炼功能
    Residential,
    /// 生产类 - 用于制作物品
    Production,
    /// 资源类 - 产生或处理资源
    Resource,
    /// 功能类 - 提供特殊功能
    Utility,
    /// 知识类 - 与学习和研究相关
    Knowledge,
    /// 交通类 - 与移动和传送相关
    Transport,
    /// 商业类 - 与交易相关
    Commerce,
    /// 存储类 - 存储物品
    Storage,
}

// ============================================================================
// 建筑效果
// ============================================================================

/// 建筑效果 - 建筑提供的各种效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum BuildingEffect {
    /// 修炼速度加成
    CultivationSpeedBonus { multiplier: f32 },
    /// 炼丹成功率加成
    AlchemySuccessBonus { percentage: f32 },
    /// 炼器成功率加成
    CraftingSuccessBonus { percentage: f32 },
    /// 灵气恢复速度加成
    SpiritualRecoveryBonus { multiplier: f32 },
    /// 体力恢复速度加成
    StaminaRecoveryBonus { multiplier: f32 },
    /// 资源产出加成
    ResourceProductionBonus { resource_type: String, multiplier: f32 },
    /// 存储容量增加
    StorageCapacityIncrease { capacity: u32 },
    /// 经验值获得加成
    ExperienceBonus { multiplier: f32 },
    /// 防御力加成
    DefenseBonus { value: u32 },
    /// 特殊技能冷却减少
    SkillCooldownReduction { percentage: f32 },
    /// 传送能力
    TeleportationAccess { destinations: Vec<String> },
    /// 商品价格折扣
    TradeDiscount { percentage: f32 },
    /// 研究速度加成
    ResearchSpeedBonus { multiplier: f32 },
}

impl BuildingEffect {
    /// 获取效果的描述文本
    pub fn description(&self) -> String {
        match self {
            BuildingEffect::CultivationSpeedBonus { multiplier } => {
                format!("修炼速度提升 {}%", (multiplier - 1.0) * 100.0)
            }
            BuildingEffect::AlchemySuccessBonus { percentage } => {
                format!("炼丹成功率提升 {}%", percentage)
            }
            BuildingEffect::CraftingSuccessBonus { percentage } => {
                format!("炼器成功率提升 {}%", percentage)
            }
            BuildingEffect::SpiritualRecoveryBonus { multiplier } => {
                format!("灵气恢复速度提升 {}%", (multiplier - 1.0) * 100.0)
            }
            BuildingEffect::StaminaRecoveryBonus { multiplier } => {
                format!("体力恢复速度提升 {}%", (multiplier - 1.0) * 100.0)
            }
            BuildingEffect::ResourceProductionBonus { resource_type, multiplier } => {
                format!("{} 产出提升 {}%", resource_type, (multiplier - 1.0) * 100.0)
            }
            BuildingEffect::StorageCapacityIncrease { capacity } => {
                format!("存储容量增加 {} 格", capacity)
            }
            BuildingEffect::ExperienceBonus { multiplier } => {
                format!("经验值获得提升 {}%", (multiplier - 1.0) * 100.0)
            }
            BuildingEffect::DefenseBonus { value } => {
                format!("防御力提升 {}", value)
            }
            BuildingEffect::SkillCooldownReduction { percentage } => {
                format!("技能冷却时间减少 {}%", percentage)
            }
            BuildingEffect::TeleportationAccess { destinations } => {
                format!("可传送至: {}", destinations.join(", "))
            }
            BuildingEffect::TradeDiscount { percentage } => {
                format!("交易价格优惠 {}%", percentage)
            }
            BuildingEffect::ResearchSpeedBonus { multiplier } => {
                format!("研究速度提升 {}%", (multiplier - 1.0) * 100.0)
            }
        }
    }
}

/// 被动加成 - 建筑提供的持续性被动效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct PassiveBonus {
    /// 加成类型
    pub bonus_type: BonusType,
    /// 加成数值
    pub value: f32,
    /// 影响范围
    pub range: Option<f32>,
    /// 生效条件
    pub conditions: Vec<BonusCondition>,
}

/// 加成类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum BonusType {
    /// 灵气密度加成
    SpiritualDensity,
    /// 资源生成速度
    ResourceGeneration,
    /// 修炼效率
    CultivationEfficiency,
    /// 安全性提升
    SecurityBonus,
    /// 舒适度提升
    ComfortBonus,
    /// 生产效率
    ProductionEfficiency,
}

/// 加成生效条件
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum BonusCondition {
    /// 建筑等级要求
    MinimumLevel { level: u8 },
    /// 维护状态良好
    WellMaintained,
    /// 有人居住/使用
    Occupied,
    /// 特定时间段
    TimeWindow { start_hour: u8, end_hour: u8 },
    /// 周围有特定建筑
    NearbyBuilding { building_type: BuildingType, max_distance: f32 },
}

// ============================================================================
// 材料需求
// ============================================================================

/// 材料需求 - 建造或维护建筑所需的材料
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MaterialRequirement {
    /// 材料名称
    pub material_name: String,
    /// 需要数量
    pub quantity: u32,
    /// 最低品质要求
    pub min_quality: Option<crate::basic_definition::MaterialGrade>,
    /// 是否可替代
    pub substitutable: bool,
    /// 替代材料列表
    pub alternatives: Vec<MaterialAlternative>,
}

/// 替代材料
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MaterialAlternative {
    /// 替代材料名称
    pub material_name: String,
    /// 替代比例 (需要的数量倍数)
    pub ratio: f32,
    /// 效果影响 (对建筑性能的影响)
    pub effect_modifier: f32,
}

// ============================================================================
// 建筑限制
// ============================================================================

/// 建筑限制 - 定义在特定区域可以建造什么建筑
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BuildingRestrictions {
    /// 允许的建筑类型
    pub allowed_types: Vec<BuildingType>,
    /// 禁止的建筑类型
    pub forbidden_types: Vec<BuildingType>,
    /// 每种类型的最大数量限制
    pub quantity_limits: HashMap<BuildingType, u32>,
    /// 建筑间最小距离要求
    pub minimum_distances: HashMap<(BuildingType, BuildingType), f32>,
    /// 地形要求
    pub terrain_requirements: HashMap<BuildingType, Vec<crate::world_map::domain::terrain::TerrainType>>,
    /// 灵气密度要求
    pub spiritual_density_requirements: HashMap<BuildingType, f32>,
}

impl BuildingRestrictions {
    /// 创建无限制的建筑规则
    pub fn unrestricted() -> Self {
        Self {
            allowed_types: vec![
                BuildingType::CaveAbode,
                BuildingType::AlchemyLab,
                BuildingType::CraftingWorkshop,
                BuildingType::FormationPlatform,
                BuildingType::SpiritualFarm,
                BuildingType::BeastPen,
                BuildingType::Library,
                BuildingType::Observatory,
                BuildingType::Shop,
                BuildingType::Warehouse,
            ],
            forbidden_types: vec![],
            quantity_limits: HashMap::new(),
            minimum_distances: HashMap::new(),
            terrain_requirements: HashMap::new(),
            spiritual_density_requirements: HashMap::new(),
        }
    }
    
    /// 检查是否可以建造指定类型的建筑
    pub fn can_build(&self, building_type: BuildingType) -> bool {
        !self.forbidden_types.contains(&building_type) &&
        (self.allowed_types.is_empty() || self.allowed_types.contains(&building_type))
    }
    
    /// 检查区域内某类建筑是否已达到数量上限
    pub fn check_quantity_limit(&self, building_type: BuildingType, current_count: u32) -> bool {
        match self.quantity_limits.get(&building_type) {
            Some(limit) => current_count < *limit,
            None => true, // 无限制
        }
    }
}

// ============================================================================
// 建筑计划
// ============================================================================

/// 建筑计划 - 描述建筑的建造计划
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BuildingPlan {
    /// 建筑类型
    pub building_type: BuildingType,
    /// 建筑名称
    pub name: String,
    /// 建造位置
    pub position: crate::world_map::Position,
    /// 建筑朝向
    pub orientation: f32,
    /// 自定义配置
    pub custom_config: HashMap<String, String>,
    /// 是否立即开始建造
    pub start_immediately: bool,
}

// ============================================================================
// 建造结果
// ============================================================================

/// 建造结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ConstructionResult {
    /// 成功开始建造
    Success {
        building_id: BuildingId,
        estimated_completion: DateTime<Utc>,
        message: String,
    },
    /// 建造失败
    Failure {
        reason: ConstructionFailureReason,
        message: String,
    },
    /// 需要确认（材料不足等情况）
    NeedsConfirmation {
        required_materials: Vec<MaterialRequirement>,
        estimated_cost: u32,
        message: String,
    },
}

/// 建造失败原因
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ConstructionFailureReason {
    /// 位置不合适
    UnsuitableLocation,
    /// 材料不足
    InsufficientMaterials,
    /// 权限不足
    InsufficientPermissions,
    /// 违反建筑限制
    ViolatesRestrictions,
    /// 资源不足
    InsufficientResources,
    /// 技能不足
    InsufficientSkill,
    /// 地形不适合
    UnsuitableTerrain,
}

/// 升级结果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum UpgradeResult {
    /// 升级成功
    Success {
        new_level: u8,
        new_effects: Vec<BuildingEffect>,
        message: String,
    },
    /// 升级失败
    Failure {
        reason: UpgradeFailureReason,
        message: String,
    },
}

/// 升级失败原因
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum UpgradeFailureReason {
    /// 已达最高等级
    MaxLevelReached,
    /// 材料不足
    InsufficientMaterials,
    /// 条件不满足
    ConditionsNotMet,
    /// 正在维护中
    UnderMaintenance,
}

// ============================================================================
// 风水加成
// ============================================================================

/// 风水加成 - 基于位置和环境的特殊加成
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FengShuiBonus {
    /// 总体风水评分 (0.0 - 10.0)
    pub overall_score: f32,
    /// 具体加成效果
    pub bonuses: Vec<FengShuiEffect>,
    /// 风水评价描述
    pub description: String,
}

/// 风水效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FengShuiEffect {
    /// 效果类型
    pub effect_type: FengShuiType,
    /// 效果强度
    pub strength: f32,
    /// 效果描述
    pub description: String,
}

/// 风水类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum FengShuiType {
    /// 聚气 - 增强灵气聚集
    QiGathering,
    /// 藏风 - 防护和稳定
    WindShielding,
    /// 得水 - 财运和资源
    WaterAccess,
    /// 向阳 - 活力和生机
    SolarAlignment,
    /// 背山 - 支撑和稳固
    MountainBacking,
    /// 明堂 - 开阔和发展
    OpenFrontyard,
}

// ============================================================================
// 默认实现
// ============================================================================

impl Default for BuildingRestrictions {
    fn default() -> Self {
        Self::unrestricted()
    }
}