/// AI优化模块
/// 
/// 提供智能的AI决策优化，包括：
/// - 智能技能选择算法
/// - 战术组合引擎
/// - 行为预测系统
/// - 决策缓存优化
/// - 自适应学习机制

use crate::shared::*;
use crate::skill::domain::skill_aggregate::*;
use crate::battle_system::simplified_battle_traits::*;
use std::collections::{HashMap, BinaryHeap, VecDeque};
use std::cmp::{Ordering, Reverse};
use std::time::Instant;

// ============================================================================
// 智能技能选择器
// ============================================================================

/// 智能技能选择器
/// 
/// 基于多种因素智能选择最优技能：
/// - 目标状态分析
/// - 资源效率计算
/// - 战术价值评估
/// - 风险收益分析
pub struct IntelligentSkillSelector {
    /// 技能评分缓存
    skill_scores: HashMap<SkillSelectionKey, f64>,
    /// 战术权重配置
    tactical_weights: TacticalWeights,
    /// 决策历史
    decision_history: VecDeque<SkillDecision>,
    /// 性能统计
    stats: AIStats,
}

impl IntelligentSkillSelector {
    pub fn new() -> Self {
        Self {
            skill_scores: HashMap::new(),
            tactical_weights: TacticalWeights::default(),
            decision_history: VecDeque::with_capacity(1000),
            stats: AIStats::default(),
        }
    }
    
    /// 选择最优技能
    pub fn select_optimal_skill<T: FullBattleUnit>(
        &mut self,
        caster: &T,
        targets: &[&dyn FullBattleUnit],
        available_skills: &[SkillId],
        battle_context: &BattleContext,
    ) -> Option<SkillRecommendation> {
        let start_time = Instant::now();
        
        let mut candidates = Vec::new();
        
        // 评估每个可用技能
        for &skill_id in available_skills {
            if let Some(recommendation) = self.evaluate_skill(
                caster, targets, skill_id, battle_context
            ) {
                candidates.push(recommendation);
            }
        }
        
        // 按评分排序，选择最优技能
        candidates.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(Ordering::Equal));
        
        let result = candidates.into_iter().next();
        
        // 记录决策时间
        let decision_time = start_time.elapsed();
        self.stats.total_decisions += 1;
        self.stats.total_decision_time += decision_time;
        
        // 记录决策历史
        if let Some(ref recommendation) = result {
            self.decision_history.push_back(SkillDecision {
                skill_id: recommendation.skill_id,
                target_id: recommendation.target_id,
                score: recommendation.score,
                timestamp: Instant::now(),
                context_hash: battle_context.get_hash(),
            });
            
            // 限制历史记录长度
            if self.decision_history.len() > 1000 {
                self.decision_history.pop_front();
            }
        }
        
        result
    }
    
    /// 评估单个技能
    fn evaluate_skill<T: FullBattleUnit>(
        &self,
        caster: &T,
        targets: &[&dyn FullBattleUnit],
        skill_id: SkillId,
        battle_context: &BattleContext,
    ) -> Option<SkillRecommendation> {
        // 检查技能可用性
        if !caster.is_skill_ready(&skill_id) {
            return None;
        }
        
        let mut best_score = 0.0;
        let mut best_target = None;
        
        // 评估对每个目标使用该技能的效果
        for target in targets {
            let score = self.calculate_skill_score(caster, target, skill_id, battle_context);
            if score > best_score {
                best_score = score;
                best_target = Some(target.entity_id());
            }
        }
        
        if best_score > 0.0 {
            Some(SkillRecommendation {
                skill_id,
                target_id: best_target,
                score: best_score,
                reasoning: self.generate_reasoning(skill_id, best_score),
            })
        } else {
            None
        }
    }
    
    /// 计算技能评分
    fn calculate_skill_score(
        &self,
        caster: &dyn FullBattleUnit,
        target: &dyn FullBattleUnit,
        skill_id: SkillId,
        battle_context: &BattleContext,
    ) -> f64 {
        let key = SkillSelectionKey {
            skill_id,
            caster_id: caster.entity_id(),
            target_id: target.entity_id(),
            context_hash: battle_context.get_hash(),
        };
        
        // 检查缓存
        if let Some(&cached_score) = self.skill_scores.get(&key) {
            return cached_score;
        }
        
        // 计算各项评分
        let damage_score = self.calculate_damage_score(caster, target, skill_id);
        let utility_score = self.calculate_utility_score(caster, target, skill_id);
        let efficiency_score = self.calculate_efficiency_score(caster, skill_id);
        let tactical_score = self.calculate_tactical_score(caster, target, skill_id, battle_context);
        let risk_score = self.calculate_risk_score(caster, target, skill_id);
        
        // 加权计算总分
        let total_score = damage_score * self.tactical_weights.damage_weight
            + utility_score * self.tactical_weights.utility_weight
            + efficiency_score * self.tactical_weights.efficiency_weight
            + tactical_score * self.tactical_weights.tactical_weight
            - risk_score * self.tactical_weights.risk_weight;
        
        total_score.max(0.0)
    }
    
    /// 计算伤害评分
    fn calculate_damage_score(
        &self,
        caster: &dyn FullBattleUnit,
        target: &dyn FullBattleUnit,
        skill_id: SkillId,
    ) -> f64 {
        let caster_attack = caster.attack_power() as f64;
        let target_defense = target.defense_power() as f64;
        let target_health = target.current_health() as f64;
        
        // 简化的伤害计算
        let estimated_damage = (caster_attack - target_defense * 0.5).max(1.0);
        let damage_ratio = estimated_damage / target_health;
        
        // 如果能击杀目标，获得额外分数
        if damage_ratio >= 1.0 {
            100.0 + damage_ratio * 50.0
        } else {
            damage_ratio * 50.0
        }
    }
    
    /// 计算效用评分（治疗、buff等）
    fn calculate_utility_score(
        &self,
        caster: &dyn FullBattleUnit,
        target: &dyn FullBattleUnit,
        skill_id: SkillId,
    ) -> f64 {
        // 简化实现：基于目标生命值计算治疗技能价值
        let health_ratio = target.health_percentage();
        
        // 如果是治疗技能且目标生命值较低
        if health_ratio < 0.5 {
            ((1.0 - health_ratio) * 30.0) as f64
        } else {
            0.0
        }
    }
    
    /// 计算效率评分
    fn calculate_efficiency_score(
        &self,
        caster: &dyn FullBattleUnit,
        skill_id: SkillId,
    ) -> f64 {
        // 基于法力消耗和冷却时间计算效率
        let mana_ratio = caster.current_mana() as f64 / caster.max_mana().max(1) as f64;
        let cooldown = caster.skill_cooldown(&skill_id);
        
        // 法力值充足且冷却时间短的技能评分更高
        let mana_efficiency = mana_ratio * 20.0;
        let cooldown_efficiency = if cooldown <= 0.0 { 20.0 } else { 20.0 / cooldown };
        
        mana_efficiency + cooldown_efficiency
    }
    
    /// 计算战术评分
    fn calculate_tactical_score(
        &self,
        caster: &dyn FullBattleUnit,
        target: &dyn FullBattleUnit,
        skill_id: SkillId,
        battle_context: &BattleContext,
    ) -> f64 {
        let mut score = 0.0;
        
        // 根据战斗阶段调整评分
        match battle_context.battle_phase {
            BattlePhase::Early => {
                // 早期偏向资源节约型技能
                score += 10.0;
            },
            BattlePhase::Mid => {
                // 中期平衡伤害和控制
                score += 15.0;
            },
            BattlePhase::Late => {
                // 后期偏向高伤害技能
                score += 20.0;
            },
        }
        
        // 根据队伍状况调整
        if battle_context.team_health_ratio < 0.3 {
            // 队伍生命值危险，偏向防御技能
            score += 25.0;
        }
        
        score
    }
    
    /// 计算风险评分
    fn calculate_risk_score(
        &self,
        caster: &dyn FullBattleUnit,
        target: &dyn FullBattleUnit,
        skill_id: SkillId,
    ) -> f64 {
        let mut risk = 0.0;
        
        // 自身生命值低时使用高消耗技能风险更大
        let health_ratio = caster.health_percentage();
        if health_ratio < 0.3 {
            risk += 20.0;
        }
        
        // 目标仍有强攻击力时风险较大
        let target_threat = target.attack_power() as f64 * target.health_percentage() as f64;
        risk += target_threat * 0.1;
        
        risk
    }
    
    /// 生成决策推理
    fn generate_reasoning(&self, skill_id: SkillId, score: f64) -> String {
        if score > 80.0 {
            format!("技能{}极优选择，评分: {:.1}", skill_id.0, score)
        } else if score > 60.0 {
            format!("技能{}良好选择，评分: {:.1}", skill_id.0, score)
        } else if score > 40.0 {
            format!("技能{}一般选择，评分: {:.1}", skill_id.0, score)
        } else {
            format!("技能{}较差选择，评分: {:.1}", skill_id.0, score)
        }
    }
    
    /// 更新战术权重（学习功能）
    pub fn update_weights_from_result(&mut self, decision: &SkillDecision, battle_result: BattleResult) {
        // 简化的学习算法：根据战斗结果调整权重
        let adjustment = match battle_result {
            BattleResult::Victory => 0.01,
            BattleResult::Defeat => -0.01,
            BattleResult::Draw => 0.0,
        };
        
        // 根据使用的技能类型调整对应权重
        self.tactical_weights.adjust_for_skill(decision.skill_id, adjustment);
    }
    
    pub fn get_stats(&self) -> &AIStats {
        &self.stats
    }
    
    pub fn clear_cache(&mut self) {
        self.skill_scores.clear();
    }
    
    /// 推荐技能列表（为兼容intelligent_ai.rs）
    pub fn recommend_skills(
        &mut self,
        available_skills: Vec<SkillId>,
        battle_context: BattleContext,
    ) -> Vec<SkillRecommendation> {
        let mut recommendations = Vec::new();
        
        // 为每个可用技能生成推荐
        for skill_id in available_skills {
            let base_score = match skill_id.0 {
                1..=10 => 60.0,   // 基础攻击技能
                11..=20 => 75.0,  // 高级攻击技能
                21..=30 => 85.0,  // 特殊技能
                31..=40 => 70.0,  // 辅助技能
                _ => 50.0,        // 其他技能
            };
            
            // 根据战斗阶段调整分数
            let phase_bonus = match battle_context.battle_phase {
                BattlePhase::Early => 1.0,
                BattlePhase::Mid => 1.1,
                BattlePhase::Late => 1.2,
            };
            
            // 根据队伍状况调整分数
            let health_factor = if battle_context.team_health_ratio < 0.5 {
                if skill_id.0 >= 31 && skill_id.0 <= 40 { 1.3 } else { 0.9 } // 低血量时优先辅助技能
            } else {
                1.0
            };
            
            let final_score = base_score * phase_bonus * health_factor;
            
            recommendations.push(SkillRecommendation {
                skill_id,
                target_id: None, // 目标会在具体使用时确定
                score: final_score,
                reasoning: format!("技能评分: {:.1}，适合当前战况", final_score),
            });
        }
        
        // 按分数排序
        recommendations.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());
        
        recommendations
    }
}

// ============================================================================
// 战术组合引擎
// ============================================================================

/// 战术组合引擎
/// 
/// 分析和推荐技能组合策略
pub struct TacticalCombinationEngine {
    /// 已知的技能组合
    known_combos: HashMap<ComboId, SkillCombo>,
    /// 组合执行历史
    combo_history: Vec<ComboExecution>,
    /// 组合发现缓存
    discovery_cache: HashMap<Vec<SkillId>, f64>,
}

impl TacticalCombinationEngine {
    pub fn new() -> Self {
        let mut engine = Self {
            known_combos: HashMap::new(),
            combo_history: Vec::new(),
            discovery_cache: HashMap::new(),
        };
        
        // 初始化基础技能组合
        engine.initialize_basic_combos();
        engine
    }
    
    /// 初始化基础技能组合
    fn initialize_basic_combos(&mut self) {
        // 示例：控制+输出组合
        self.known_combos.insert(ComboId(1), SkillCombo {
            id: ComboId(1),
            name: "控制连击".to_string(),
            skills: vec![SkillId(101), SkillId(102)], // 眩晕 + 高伤害技能
            synergy_bonus: 1.3,
            description: "先控制目标，再使用高伤害技能".to_string(),
            conditions: vec![ComboCondition::TargetNotStunned],
        });
        
        // 示例：治疗+护盾组合
        self.known_combos.insert(ComboId(2), SkillCombo {
            id: ComboId(2),
            name: "生存保障".to_string(),
            skills: vec![SkillId(201), SkillId(202)], // 治疗 + 护盾
            synergy_bonus: 1.2,
            description: "回复生命值并提供护盾保护".to_string(),
            conditions: vec![ComboCondition::SelfHealthLow],
        });
    }
    
    /// 推荐最佳技能组合
    pub fn recommend_combo(
        &mut self,
        caster: &dyn FullBattleUnit,
        targets: &[&dyn FullBattleUnit],
        available_skills: &[SkillId],
        battle_context: &BattleContext,
    ) -> Option<ComboRecommendation> {
        let mut best_combo = None;
        let mut best_score = 0.0;
        
        // 检查已知组合
        for combo in self.known_combos.values() {
            if let Some(score) = self.evaluate_combo(caster, targets, combo, battle_context) {
                if score > best_score {
                    best_score = score;
                    best_combo = Some(combo.clone());
                }
            }
        }
        
        // 尝试发现新组合
        if let Some((new_combo, score)) = self.discover_combo(available_skills, caster, battle_context) {
            if score > best_score {
                best_score = score;
                best_combo = Some(new_combo);
            }
        }
        
        best_combo.map(|combo| {
            let execution_plan = self.create_execution_plan(&combo.skills);
            ComboRecommendation {
                combo,
                score: best_score,
                execution_plan,
            }
        })
    }
    
    /// 评估技能组合
    fn evaluate_combo(
        &self,
        caster: &dyn FullBattleUnit,
        targets: &[&dyn FullBattleUnit],
        combo: &SkillCombo,
        battle_context: &BattleContext,
    ) -> Option<f64> {
        // 检查组合条件
        for condition in &combo.conditions {
            if !self.check_combo_condition(condition, caster, targets, battle_context) {
                return None;
            }
        }
        
        // 检查技能可用性
        for &skill_id in &combo.skills {
            if !caster.is_skill_ready(&skill_id) {
                return None;
            }
        }
        
        // 计算组合价值
        let base_value = combo.skills.len() as f64 * 30.0;
        let synergy_value = base_value * combo.synergy_bonus;
        let context_bonus = self.calculate_context_bonus(combo, battle_context);
        
        Some(synergy_value + context_bonus)
    }
    
    /// 发现新的技能组合
    fn discover_combo(
        &mut self,
        available_skills: &[SkillId],
        caster: &dyn FullBattleUnit,
        battle_context: &BattleContext,
    ) -> Option<(SkillCombo, f64)> {
        // 简化实现：尝试2技能组合
        for i in 0..available_skills.len() {
            for j in i+1..available_skills.len() {
                let skills = vec![available_skills[i], available_skills[j]];
                
                // 检查缓存
                if let Some(&cached_score) = self.discovery_cache.get(&skills) {
                    if cached_score > 50.0 {
                        let combo = SkillCombo {
                            id: ComboId(self.known_combos.len() as u32 + 1000),
                            name: format!("发现组合{}-{}", skills[0].0, skills[1].0),
                            skills: skills.clone(),
                            synergy_bonus: 1.1,
                            description: "自动发现的技能组合".to_string(),
                            conditions: vec![],
                        };
                        return Some((combo, cached_score));
                    }
                    continue;
                }
                
                // 计算新组合的潜在价值
                let score = self.calculate_combo_potential(&skills, caster, battle_context);
                self.discovery_cache.insert(skills.clone(), score);
                
                if score > 50.0 {
                    let combo = SkillCombo {
                        id: ComboId(self.known_combos.len() as u32 + 1000),
                        name: format!("发现组合{}-{}", skills[0].0, skills[1].0),
                        skills: skills.clone(),
                        synergy_bonus: 1.1,
                        description: "自动发现的技能组合".to_string(),
                        conditions: vec![],
                    };
                    return Some((combo, score));
                }
            }
        }
        
        None
    }
    
    /// 计算组合潜力
    fn calculate_combo_potential(
        &self,
        skills: &[SkillId],
        caster: &dyn FullBattleUnit,
        battle_context: &BattleContext,
    ) -> f64 {
        // 简化的组合潜力计算
        let mut potential = 0.0;
        
        // 基础组合价值
        potential += skills.len() as f64 * 20.0;
        
        // 技能类型互补性
        potential += self.calculate_skill_synergy(skills);
        
        // 战斗上下文适应性
        potential += self.calculate_context_adaptation(skills, battle_context);
        
        potential
    }
    
    /// 计算技能协同性
    fn calculate_skill_synergy(&self, skills: &[SkillId]) -> f64 {
        // 简化实现：某些技能ID组合有天然协同性
        if skills.len() >= 2 {
            let skill1 = skills[0].0;
            let skill2 = skills[1].0;
            
            // 控制+伤害技能协同
            if (skill1 % 100 == 1 && skill2 % 100 == 2) || 
               (skill1 % 100 == 2 && skill2 % 100 == 1) {
                return 20.0;
            }
            
            // 治疗+防护技能协同
            if skill1 / 100 == 2 && skill2 / 100 == 2 {
                return 15.0;
            }
        }
        
        5.0 // 基础协同价值
    }
    
    /// 计算上下文适应性
    fn calculate_context_adaptation(&self, skills: &[SkillId], battle_context: &BattleContext) -> f64 {
        let mut adaptation = 0.0;
        
        match battle_context.battle_phase {
            BattlePhase::Early => adaptation += 5.0,
            BattlePhase::Mid => adaptation += 10.0,
            BattlePhase::Late => adaptation += 15.0,
        }
        
        if battle_context.team_health_ratio < 0.5 {
            // 危险情况下，治疗组合价值更高
            for &skill_id in skills {
                if skill_id.0 / 100 == 2 { // 假设200系列是治疗技能
                    adaptation += 10.0;
                }
            }
        }
        
        adaptation
    }
    
    /// 检查组合条件
    fn check_combo_condition(
        &self,
        condition: &ComboCondition,
        caster: &dyn FullBattleUnit,
        targets: &[&dyn FullBattleUnit],
        battle_context: &BattleContext,
    ) -> bool {
        match condition {
            ComboCondition::SelfHealthLow => caster.health_percentage() < 0.4,
            ComboCondition::TargetNotStunned => {
                // 简化：假设目标没有眩晕状态
                !targets.is_empty()
            },
            ComboCondition::BattlePhase(phase) => battle_context.battle_phase == *phase,
            ComboCondition::MinimumMana(amount) => caster.current_mana() >= *amount,
        }
    }
    
    /// 计算上下文奖励
    fn calculate_context_bonus(&self, combo: &SkillCombo, battle_context: &BattleContext) -> f64 {
        let mut bonus = 0.0;
        
        // 根据战斗阶段给予奖励
        match battle_context.battle_phase {
            BattlePhase::Early if combo.name.contains("保守") => bonus += 10.0,
            BattlePhase::Late if combo.name.contains("爆发") => bonus += 15.0,
            _ => {}
        }
        
        bonus
    }
    
    /// 创建执行计划
    fn create_execution_plan(&self, skills: &[SkillId]) -> Vec<SkillAction> {
        skills.iter().enumerate().map(|(i, &skill_id)| {
            SkillAction {
                skill_id,
                delay: i as f32 * 1.0, // 每个技能间隔1秒
                target_selection: TargetSelection::BestTarget,
            }
        }).collect()
    }
    
    /// 记录组合执行结果
    pub fn record_combo_execution(&mut self, execution: ComboExecution) {
        self.combo_history.push(execution);
        
        // 限制历史记录长度
        if self.combo_history.len() > 100 {
            self.combo_history.remove(0);
        }
    }
}

// ============================================================================
// 行为预测器
// ============================================================================

/// 行为预测器
/// 
/// 预测敌方可能的行动，辅助决策
pub struct BehaviorPredictor {
    /// 行为模式历史
    behavior_patterns: HashMap<BattleUnitId, Vec<BehaviorRecord>>,
    /// 预测模型
    prediction_models: HashMap<String, PredictionModel>,
}

impl BehaviorPredictor {
    pub fn new() -> Self {
        Self {
            behavior_patterns: HashMap::new(),
            prediction_models: HashMap::new(),
        }
    }
    
    /// 记录行为
    pub fn record_behavior(&mut self, unit_id: BattleUnitId, behavior: BehaviorRecord) {
        let patterns = self.behavior_patterns.entry(unit_id).or_insert_with(Vec::new);
        patterns.push(behavior);
        
        // 限制记录长度
        if patterns.len() > 50 {
            patterns.remove(0);
        }
    }
    
    /// 预测下一步行为
    pub fn predict_next_action(&self, unit_id: BattleUnitId) -> Option<ActionPrediction> {
        let patterns = self.behavior_patterns.get(&unit_id)?;
        if patterns.len() < 3 {
            return None; // 数据不足
        }
        
        // 简化的预测：基于最近的行为模式
        let recent_actions: Vec<_> = patterns.iter().rev().take(5).collect();
        let most_common_action = self.find_most_common_action(&recent_actions);
        
        Some(ActionPrediction {
            predicted_action: most_common_action,
            confidence: self.calculate_prediction_confidence(&recent_actions),
            reasoning: "基于历史行为模式预测".to_string(),
        })
    }
    
    /// 查找最常见的行为
    fn find_most_common_action(&self, records: &[&BehaviorRecord]) -> String {
        let mut action_counts = HashMap::new();
        
        for record in records {
            *action_counts.entry(&record.action_type).or_insert(0) += 1;
        }
        
        action_counts.into_iter()
            .max_by_key(|(_, count)| *count)
            .map(|(action, _)| action.clone())
            .unwrap_or_else(|| "unknown".to_string())
    }
    
    /// 计算预测置信度
    fn calculate_prediction_confidence(&self, records: &[&BehaviorRecord]) -> f64 {
        if records.is_empty() {
            return 0.0;
        }
        
        let mut action_counts = HashMap::new();
        for record in records {
            *action_counts.entry(&record.action_type).or_insert(0) += 1;
        }
        
        let max_count = action_counts.values().max().unwrap_or(&0);
        (*max_count as f64) / (records.len() as f64)
    }
}

// ============================================================================
// AI决策缓存
// ============================================================================

/// AI决策缓存
/// 
/// 缓存AI决策结果，避免重复计算
pub struct AIDecisionCache {
    /// 决策缓存
    cache: HashMap<DecisionKey, CachedDecision>,
    /// 缓存命中统计
    hit_count: u64,
    /// 缓存未命中统计
    miss_count: u64,
}

impl AIDecisionCache {
    pub fn new() -> Self {
        Self {
            cache: HashMap::new(),
            hit_count: 0,
            miss_count: 0,
        }
    }
    
    /// 获取缓存的决策
    pub fn get_decision(&mut self, key: &DecisionKey) -> Option<CachedDecision> {
        if let Some(decision) = self.cache.get(key).cloned() {
            if self.is_expired(&decision) {
                // 如果已过期，从缓存中移除
                self.cache.remove(key);
                None
            } else {
                Some(decision)
            }
        } else {
            None
        }
    }
    
    /// 检查决策是否过期
    fn is_expired(&self, decision: &CachedDecision) -> bool {
        decision.cached_at.elapsed() > decision.ttl
    }
    
    /// 缓存决策
    pub fn cache_decision(&mut self, key: DecisionKey, decision: CachedDecision) {
        self.cache.insert(key, decision);
    }
    
    /// 获取缓存命中率
    pub fn hit_rate(&self) -> f64 {
        let total = self.hit_count + self.miss_count;
        if total == 0 {
            0.0
        } else {
            self.hit_count as f64 / total as f64
        }
    }
    
    /// 清理过期缓存
    pub fn cleanup_expired(&mut self) {
        let now = Instant::now();
        self.cache.retain(|_, decision| (now - decision.cached_at) < decision.ttl);
    }
}

// ============================================================================
// 数据结构定义
// ============================================================================

/// 技能选择键
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct SkillSelectionKey {
    skill_id: SkillId,
    caster_id: BattleUnitId,
    target_id: BattleUnitId,
    context_hash: u64,
}

/// 战术权重
#[derive(Debug, Clone)]
struct TacticalWeights {
    damage_weight: f64,
    utility_weight: f64,
    efficiency_weight: f64,
    tactical_weight: f64,
    risk_weight: f64,
}

impl Default for TacticalWeights {
    fn default() -> Self {
        Self {
            damage_weight: 0.3,
            utility_weight: 0.2,
            efficiency_weight: 0.2,
            tactical_weight: 0.2,
            risk_weight: 0.1,
        }
    }
}

impl TacticalWeights {
    fn adjust_for_skill(&mut self, skill_id: SkillId, adjustment: f64) {
        // 根据技能类型调整权重
        let skill_type = skill_id.0 / 100; // 简化的技能分类
        match skill_type {
            1 => self.damage_weight = (self.damage_weight + adjustment).clamp(0.1, 0.8),
            2 => self.utility_weight = (self.utility_weight + adjustment).clamp(0.1, 0.8),
            3 => self.efficiency_weight = (self.efficiency_weight + adjustment).clamp(0.1, 0.8),
            _ => self.tactical_weight = (self.tactical_weight + adjustment).clamp(0.1, 0.8),
        }
    }
}

/// 技能推荐
#[derive(Debug, Clone)]
pub struct SkillRecommendation {
    pub skill_id: SkillId,
    pub target_id: Option<BattleUnitId>,
    pub score: f64,
    pub reasoning: String,
}

/// 技能决策记录
#[derive(Debug, Clone)]
struct SkillDecision {
    skill_id: SkillId,
    target_id: Option<BattleUnitId>,
    score: f64,
    timestamp: Instant,
    context_hash: u64,
}

/// 战斗上下文
#[derive(Debug, Clone)]
pub struct BattleContext {
    pub battle_phase: BattlePhase,
    pub team_health_ratio: f64,
    pub enemy_count: usize,
    pub turn_number: u32,
}

impl BattleContext {
    pub fn get_hash(&self) -> u64 {
        // 简化的哈希计算
        (self.turn_number as u64) * 1000 + (self.team_health_ratio * 100.0) as u64
    }
}

/// 战斗阶段
#[derive(Debug, Clone, PartialEq)]
pub enum BattlePhase {
    Early,
    Mid,
    Late,
}

/// 战斗结果
#[derive(Debug, Clone)]
pub enum BattleResult {
    Victory,
    Defeat,
    Draw,
}

/// AI统计信息
#[derive(Debug, Clone, Default)]
pub struct AIStats {
    pub total_decisions: u64,
    pub total_decision_time: std::time::Duration,
    pub cache_hits: u64,
    pub cache_misses: u64,
}

impl AIStats {
    pub fn average_decision_time_ms(&self) -> f64 {
        if self.total_decisions == 0 {
            0.0
        } else {
            self.total_decision_time.as_millis() as f64 / self.total_decisions as f64
        }
    }
}

/// 技能组合ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct ComboId(pub u32);

/// 技能组合
#[derive(Debug, Clone)]
pub struct SkillCombo {
    pub id: ComboId,
    pub name: String,
    pub skills: Vec<SkillId>,
    pub synergy_bonus: f64,
    pub description: String,
    pub conditions: Vec<ComboCondition>,
}

/// 组合条件
#[derive(Debug, Clone)]
pub enum ComboCondition {
    SelfHealthLow,
    TargetNotStunned,
    BattlePhase(BattlePhase),
    MinimumMana(Mana),
}

/// 组合推荐
#[derive(Debug, Clone)]
pub struct ComboRecommendation {
    pub combo: SkillCombo,
    pub score: f64,
    pub execution_plan: Vec<SkillAction>,
}

/// 技能行动
#[derive(Debug, Clone)]
pub struct SkillAction {
    pub skill_id: SkillId,
    pub delay: f32, // 延迟时间（秒）
    pub target_selection: TargetSelection,
}

/// 目标选择策略
#[derive(Debug, Clone)]
pub enum TargetSelection {
    BestTarget,
    LowestHealth,
    HighestThreat,
    Self_,
}

/// 组合执行记录
#[derive(Debug, Clone)]
pub struct ComboExecution {
    pub combo_id: ComboId,
    pub success: bool,
    pub damage_dealt: f64,
    pub duration: std::time::Duration,
    pub battle_result: BattleResult,
}

/// 行为记录
#[derive(Debug, Clone)]
pub struct BehaviorRecord {
    pub action_type: String,
    pub target_id: Option<BattleUnitId>,
    pub timestamp: Instant,
    pub context: String,
}

/// 预测模型
#[derive(Debug, Clone)]
pub struct PredictionModel {
    pub model_type: String,
    pub accuracy: f64,
    pub last_updated: Instant,
}

/// 行为预测
#[derive(Debug, Clone)]
pub struct ActionPrediction {
    pub predicted_action: String,
    pub confidence: f64,
    pub reasoning: String,
}

/// 决策缓存键
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct DecisionKey {
    pub situation_hash: u64,
    pub available_skills: Vec<SkillId>,
    pub target_states: Vec<u64>, // 简化的目标状态哈希
}

/// 缓存的决策
#[derive(Debug, Clone)]
pub struct CachedDecision {
    pub recommendation: SkillRecommendation,
    pub cached_at: Instant,
    pub ttl: std::time::Duration, // 生存时间
}

impl CachedDecision {
    pub fn is_expired(&self) -> bool {
        self.cached_at.elapsed() > self.ttl
    }
}
