/// 访问控制系统相关的类型定义
/// 处理权限管理、访问限制和安全控制

use serde::{Deserialize, Serialize};
use std::collections::{HashSet, HashMap};
use crate::world_map::domain::ids::{PlayerId, FactionId};

// ============================================================================
// 访问控制级别
// ============================================================================

/// 访问控制级别 - 定义不同的访问权限等级
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AccessLevel {
    /// 公开 - 所有人都可以访问
    Public,
    /// 好友 - 仅好友可以访问
    Friends,
    /// 门派 - 仅同门派成员可以访问
    Faction,
    /// 联盟 - 盟友势力可以访问
    Alliance,
    /// 私有 - 仅拥有者可以访问
    Private,
    /// 限制 - 需要特殊权限
    Restricted,
}

impl AccessLevel {
    /// 获取访问级别的严格程度（数值越大越严格）
    pub fn strictness_level(&self) -> u8 {
        match self {
            AccessLevel::Public => 0,
            AccessLevel::Friends => 1,
            AccessLevel::Alliance => 2,
            AccessLevel::Faction => 3,
            AccessLevel::Restricted => 4,
            AccessLevel::Private => 5,
        }
    }
    
    /// 检查是否比另一个访问级别更严格
    pub fn is_stricter_than(&self, other: &AccessLevel) -> bool {
        self.strictness_level() > other.strictness_level()
    }
}

// ============================================================================
// 访问权限
// ============================================================================

/// 访问权限 - 完整的访问控制配置
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AccessControl {
    /// 基础访问级别
    pub level: AccessLevel,
    /// 特殊授权的玩家列表
    pub authorized_players: HashSet<PlayerId>,
    /// 禁止访问的玩家列表（黑名单）
    pub banned_players: HashSet<PlayerId>,
    /// 授权的门派列表
    pub authorized_factions: HashSet<FactionId>,
    /// 禁止的门派列表
    pub banned_factions: HashSet<FactionId>,
    /// 额外的访问条件
    pub additional_conditions: Vec<AccessCondition>,
    /// 时间限制
    pub time_restrictions: Vec<TimeRestriction>,
}

impl AccessControl {
    /// 创建公开访问控制
    pub fn public() -> Self {
        Self {
            level: AccessLevel::Public,
            authorized_players: HashSet::new(),
            banned_players: HashSet::new(),
            authorized_factions: HashSet::new(),
            banned_factions: HashSet::new(),
            additional_conditions: Vec::new(),
            time_restrictions: Vec::new(),
        }
    }
    
    /// 创建私有访问控制
    pub fn private() -> Self {
        Self {
            level: AccessLevel::Private,
            authorized_players: HashSet::new(),
            banned_players: HashSet::new(),
            authorized_factions: HashSet::new(),
            banned_factions: HashSet::new(),
            additional_conditions: Vec::new(),
            time_restrictions: Vec::new(),
        }
    }
    
    /// 创建门派访问控制
    pub fn faction_only() -> Self {
        Self {
            level: AccessLevel::Faction,
            authorized_players: HashSet::new(),
            banned_players: HashSet::new(),
            authorized_factions: HashSet::new(),
            banned_factions: HashSet::new(),
            additional_conditions: Vec::new(),
            time_restrictions: Vec::new(),
        }
    }
    
    /// 添加授权玩家
    pub fn authorize_player(&mut self, player_id: PlayerId) {
        self.authorized_players.insert(player_id);
        self.banned_players.remove(&player_id); // 移除可能存在的禁令
    }
    
    /// 禁止玩家访问
    pub fn ban_player(&mut self, player_id: PlayerId) {
        self.banned_players.insert(player_id);
        self.authorized_players.remove(&player_id); // 移除可能存在的授权
    }
    
    /// 添加授权门派
    pub fn authorize_faction(&mut self, faction_id: FactionId) {
        self.authorized_factions.insert(faction_id);
        self.banned_factions.remove(&faction_id);
    }
    
    /// 禁止门派访问
    pub fn ban_faction(&mut self, faction_id: FactionId) {
        self.banned_factions.insert(faction_id);
        self.authorized_factions.remove(&faction_id);
    }
    
    /// 检查玩家是否有访问权限
    pub fn can_access(&self, context: &AccessContext) -> AccessResult {
        // 首先检查是否被禁止
        if self.banned_players.contains(&context.player_id) {
            return AccessResult::Denied {
                reason: AccessDenialReason::PlayerBanned,
                message: "您已被禁止访问此地".to_string(),
            };
        }
        
        // 检查门派是否被禁止
        if let Some(faction_id) = context.player_faction {
            if self.banned_factions.contains(&faction_id) {
                return AccessResult::Denied {
                    reason: AccessDenialReason::FactionBanned,
                    message: "您的门派已被禁止访问此地".to_string(),
                };
            }
        }
        
        // 检查特殊授权
        if self.authorized_players.contains(&context.player_id) {
            return self.check_additional_conditions(context);
        }
        
        // 检查门派授权
        if let Some(faction_id) = context.player_faction {
            if self.authorized_factions.contains(&faction_id) {
                return self.check_additional_conditions(context);
            }
        }
        
        // 根据访问级别判断
        let base_access = match self.level {
            AccessLevel::Public => true,
            AccessLevel::Private => false,
            AccessLevel::Friends => {
                context.owner_friends.map_or(false, |friends| {
                    friends.contains(&context.player_id)
                })
            }
            AccessLevel::Faction => {
                match (context.player_faction, context.owner_faction) {
                    (Some(pf), Some(of)) => pf == of,
                    _ => false,
                }
            }
            AccessLevel::Alliance => {
                context.alliance_members.map_or(false, |members| {
                    context.player_faction.map_or(false, |pf| members.contains(&pf))
                })
            }
            AccessLevel::Restricted => false, // 需要特殊条件
        };
        
        if base_access {
            self.check_additional_conditions(context)
        } else {
            AccessResult::Denied {
                reason: AccessDenialReason::InsufficientPermissions,
                message: "您没有访问此地的权限".to_string(),
            }
        }
    }
    
    /// 检查额外的访问条件
    fn check_additional_conditions(&self, context: &AccessContext) -> AccessResult {
        // 检查时间限制
        for time_restriction in &self.time_restrictions {
            if !time_restriction.is_allowed_at(&context.current_time) {
                return AccessResult::Denied {
                    reason: AccessDenialReason::TimeRestriction,
                    message: format!("当前时间不允许访问: {}", time_restriction.description()),
                };
            }
        }
        
        // 检查额外条件
        for condition in &self.additional_conditions {
            match condition.check(context) {
                ConditionResult::Met => continue,
                ConditionResult::NotMet { reason } => {
                    return AccessResult::Denied {
                        reason: AccessDenialReason::ConditionNotMet,
                        message: reason,
                    };
                }
            }
        }
        
        AccessResult::Granted {
            message: "访问已授权".to_string(),
            restrictions: self.get_applicable_restrictions(context),
        }
    }
    
    /// 获取适用的限制条件
    fn get_applicable_restrictions(&self, _context: &AccessContext) -> Vec<AccessRestriction> {
        // 这里可以根据具体情况返回适用的限制
        Vec::new()
    }
}

// ============================================================================
// 访问上下文
// ============================================================================

/// 访问上下文 - 包含进行访问检查所需的所有信息
#[derive(Debug, Clone)]
pub struct AccessContext<'a> {
    /// 请求访问的玩家ID
    pub player_id: PlayerId,
    /// 玩家所属门派
    pub player_faction: Option<FactionId>,
    /// 玩家等级
    pub player_level: u8,
    /// 玩家声望值
    pub player_reputation: HashMap<String, i32>,
    /// 玩家拥有的称号
    pub player_titles: HashSet<String>,
    /// 拥有者的门派
    pub owner_faction: Option<FactionId>,
    /// 拥有者的好友列表
    pub owner_friends: Option<&'a HashSet<PlayerId>>,
    /// 联盟成员门派
    pub alliance_members: Option<&'a HashSet<FactionId>>,
    /// 当前时间
    pub current_time: crate::world_map::domain::time::GameTime,
    /// 玩家当前位置
    pub player_position: crate::world_map::Position,
    /// 额外的上下文信息
    pub extra_data: HashMap<String, String>,
}

// ============================================================================
// 访问条件
// ============================================================================

/// 访问条件 - 定义额外的访问要求
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AccessCondition {
    /// 最低等级要求
    MinimumLevel { level: u8 },
    /// 声望要求
    ReputationRequirement { 
        faction: String, 
        min_reputation: i32 
    },
    /// 称号要求
    TitleRequirement { title: String },
    /// 物品要求
    ItemRequirement { 
        item_name: String, 
        quantity: u32,
        consume_on_access: bool,
    },
    /// 技能要求
    SkillRequirement { 
        skill_name: String, 
        min_level: u8 
    },
    /// 任务完成要求
    QuestRequirement { quest_id: String },
    /// 支付要求
    PaymentRequirement { 
        amount: u32, 
        currency: String 
    },
    /// 团队要求
    GroupRequirement { 
        min_members: u8,
        max_members: Option<u8>,
    },
    /// 自定义条件
    CustomCondition { 
        condition_id: String,
        parameters: HashMap<String, String>,
    },
}

impl AccessCondition {
    /// 检查条件是否满足
    pub fn check(&self, context: &AccessContext) -> ConditionResult {
        match self {
            AccessCondition::MinimumLevel { level } => {
                if context.player_level >= *level {
                    ConditionResult::Met
                } else {
                    ConditionResult::NotMet {
                        reason: format!("需要等级 {} 或更高（当前等级: {}）", level, context.player_level),
                    }
                }
            }
            AccessCondition::ReputationRequirement { faction, min_reputation } => {
                let current_rep = context.player_reputation.get(faction).unwrap_or(&0);
                if current_rep >= min_reputation {
                    ConditionResult::Met
                } else {
                    ConditionResult::NotMet {
                        reason: format!("{}声望不足（需要: {}, 当前: {}）", faction, min_reputation, current_rep),
                    }
                }
            }
            AccessCondition::TitleRequirement { title } => {
                if context.player_titles.contains(title) {
                    ConditionResult::Met
                } else {
                    ConditionResult::NotMet {
                        reason: format!("需要称号: {}", title),
                    }
                }
            }
            // 其他条件的实现...
            _ => {
                // 暂时返回满足，后续可以根据具体需求实现
                ConditionResult::Met
            }
        }
    }
}

/// 条件检查结果
#[derive(Debug, Clone, PartialEq)]
pub enum ConditionResult {
    /// 条件满足
    Met,
    /// 条件不满足
    NotMet { reason: String },
}

// ============================================================================
// 时间限制
// ============================================================================

/// 时间限制 - 定义特定时间段的访问限制
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TimeRestriction {
    /// 每日时间窗口
    DailyWindow { 
        start_hour: u8, 
        end_hour: u8,
        allowed: bool, // true表示允许时间，false表示禁止时间
    },
    /// 特定日期
    SpecificDates { 
        dates: Vec<(u32, u8, u8)>, // (年, 月, 日)
        allowed: bool,
    },
    /// 季节限制
    SeasonalRestriction { 
        seasons: Vec<crate::world_map::domain::time::Season>,
        allowed: bool,
    },
    /// 特殊事件期间
    EventRestriction { 
        event_types: Vec<String>,
        during_event: bool, // true表示事件期间允许，false表示事件期间禁止
    },
}

impl TimeRestriction {
    /// 检查当前时间是否被允许
    pub fn is_allowed_at(&self, current_time: &crate::world_map::domain::time::GameTime) -> bool {
        match self {
            TimeRestriction::DailyWindow { start_hour, end_hour, allowed } => {
                let in_window = if start_hour <= end_hour {
                    current_time.hour >= *start_hour && current_time.hour <= *end_hour
                } else {
                    // 跨日情况，如 23:00 - 06:00
                    current_time.hour >= *start_hour || current_time.hour <= *end_hour
                };
                in_window == *allowed
            }
            TimeRestriction::SeasonalRestriction { seasons, allowed } => {
                let in_season = seasons.contains(&current_time.season);
                in_season == *allowed
            }
            TimeRestriction::SpecificDates { dates, allowed } => {
                let current_date = (current_time.year, current_time.month, current_time.day);
                let is_specific_date = dates.contains(&current_date);
                is_specific_date == *allowed
            }
            TimeRestriction::EventRestriction { event_types, during_event } => {
                // 检查当前是否有指定的事件发生
                // TODO: 需要从世界事件系统获取当前事件信息
                let has_matching_event = self.check_active_events(current_time, event_types);
                has_matching_event == *during_event
            }
        }
    }
    
    /// 检查当前是否有指定的事件活跃
    fn check_active_events(&self, current_time: &crate::world_map::domain::time::GameTime, event_types: &[String]) -> bool {
        // 简化实现：根据时间和事件类型模拟事件活跃状态
        for event_type in event_types {
            match event_type.as_str() {
                "月圆之夜" => {
                    // 每月满15日为月圆之夜
                    if current_time.day == 15 {
                        return true;
                    }
                }
                "灵气潮汐" => {
                    // 每天的特定时间段为灵气潮汐
                    if current_time.hour >= 18 && current_time.hour <= 22 {
                        return true;
                    }
                }
                "天象异常" => {
                    // 简化实现：每季度的第一个月有天象异常
                    let season_start_month = match current_time.season {
                        crate::world_map::domain::time::Season::Spring => 3,
                        crate::world_map::domain::time::Season::Summer => 6,
                        crate::world_map::domain::time::Season::Autumn => 9,
                        crate::world_map::domain::time::Season::Winter => 12,
                    };
                    if current_time.month == season_start_month {
                        return true;
                    }
                }
                "古遗迹开启" => {
                    // 每年的特定日期古遗迹开启
                    if current_time.month == 1 && current_time.day == 1 {
                        return true;
                    }
                    if current_time.month == 7 && current_time.day == 15 {
                        return true;
                    }
                }
                _ => {
                    // 未知事件类型，默认不活跃
                    continue;
                }
            }
        }
        false
    }
    
    /// 获取时间限制的描述
    pub fn description(&self) -> String {
        match self {
            TimeRestriction::DailyWindow { start_hour, end_hour, allowed } => {
                if *allowed {
                    format!("只在 {}:00 - {}:00 开放", start_hour, end_hour)
                } else {
                    format!("在 {}:00 - {}:00 期间关闭", start_hour, end_hour)
                }
            }
            TimeRestriction::SeasonalRestriction { seasons, allowed } => {
                let season_names: Vec<String> = seasons.iter().map(|s| s.to_string()).collect();
                if *allowed {
                    format!("只在 {} 开放", season_names.join("、"))
                } else {
                    format!("在 {} 期间关闭", season_names.join("、"))
                }
            }
            TimeRestriction::SpecificDates { dates, allowed } => {
                if *allowed {
                    format!("只在特定日期开放（共{}个日期）", dates.len())
                } else {
                    format!("在特定日期关闭（共{}个日期）", dates.len())
                }
            }
            TimeRestriction::EventRestriction { event_types, during_event } => {
                if *during_event {
                    format!("只在以下事件期间开放: {}", event_types.join("、"))
                } else {
                    format!("在以下事件期间关闭: {}", event_types.join("、"))
                }
            }
        }
    }
}

// ============================================================================
// 访问结果和限制
// ============================================================================

/// 访问结果
#[derive(Debug, Clone, PartialEq)]
pub enum AccessResult {
    /// 访问被授权
    Granted {
        message: String,
        restrictions: Vec<AccessRestriction>,
    },
    /// 访问被拒绝
    Denied {
        reason: AccessDenialReason,
        message: String,
    },
}

/// 访问拒绝原因
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AccessDenialReason {
    /// 权限不足
    InsufficientPermissions,
    /// 玩家被禁止
    PlayerBanned,
    /// 门派被禁止
    FactionBanned,
    /// 时间限制
    TimeRestriction,
    /// 条件不满足
    ConditionNotMet,
    /// 等级不足
    InsufficientLevel,
    /// 声望不足
    InsufficientReputation,
    /// 缺少称号
    MissingTitle,
    /// 缺少物品
    MissingItem,
    /// 技能不足
    InsufficientSkill,
    /// 资源不足
    InsufficientResources,
}

/// 访问限制 - 即使获得访问权限，也可能有一些限制
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AccessRestriction {
    /// 时间限制
    TimeLimit { duration_minutes: u32 },
    /// 行动限制
    ActionRestriction { forbidden_actions: Vec<String> },
    /// 区域限制
    AreaRestriction { allowed_areas: Vec<String> },
    /// 使用费用
    UsageFee { cost_per_hour: u32, currency: String },
    /// 监督要求
    SupervisionRequired,
}

// ============================================================================
// 默认实现
// ============================================================================

impl Default for AccessControl {
    fn default() -> Self {
        Self::public()
    }
}