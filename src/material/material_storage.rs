use crate::material::material_core::{Material, MaterialAttribute, StorageRequirement};
use crate::MaterialGrade;
use chrono::{DateTime, Duration, Utc};
/// 材料存储和管理系统
/// 实现材料仓库、特殊存储需求和库存管理
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
// ============================================================================
// 存储容器系统
// ============================================================================

/// 存储容器类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ContainerType {
    /// 普通仓库 - 基础存储
    BasicWarehouse,
    /// 灵材阁 - 专门存放灵材
    SpiritMaterialHall,
    /// 药庐 - 专门存放药材
    HerbStorage,
    /// 器材库 - 存放炼器材料
    CraftingMaterialStorage,
    /// 隔灵玉盒 - 高能量材料专用
    SpiritIsolationBox,
    /// 定魂符匣 - 不稳定材料专用
    SoulStabilizationBox,
    /// 养灵台 - 活体材料专用
    SpiritNurturingPlatform,
    /// 时间凝滞箱 - 延缓材料衰减
    TimeStasisBox,
}

impl ContainerType {
    /// 获取容器的中文名称
    pub fn chinese_name(&self) -> &'static str {
        match self {
            ContainerType::BasicWarehouse => "普通仓库",
            ContainerType::SpiritMaterialHall => "灵材阁",
            ContainerType::HerbStorage => "药庐",
            ContainerType::CraftingMaterialStorage => "器材库",
            ContainerType::SpiritIsolationBox => "隔灵玉盒",
            ContainerType::SoulStabilizationBox => "定魂符匣",
            ContainerType::SpiritNurturingPlatform => "养灵台",
            ContainerType::TimeStasisBox => "时间凝滞箱",
        }
    }

    /// 获取容器的基础容量
    pub fn base_capacity(&self) -> u32 {
        match self {
            ContainerType::BasicWarehouse => 1000,
            ContainerType::SpiritMaterialHall => 500,
            ContainerType::HerbStorage => 300,
            ContainerType::CraftingMaterialStorage => 400,
            ContainerType::SpiritIsolationBox => 50,
            ContainerType::SoulStabilizationBox => 30,
            ContainerType::SpiritNurturingPlatform => 20,
            ContainerType::TimeStasisBox => 100,
        }
    }

    /// 获取容器支持的存储要求
    pub fn supported_requirements(&self) -> Vec<StorageRequirement> {
        match self {
            ContainerType::BasicWarehouse => vec![],
            ContainerType::SpiritMaterialHall => vec![StorageRequirement::EnvironmentalCondition {
                condition: "灵气浓度".to_string(),
                value: "高".to_string(),
            }],
            ContainerType::HerbStorage => vec![
                StorageRequirement::EnvironmentalCondition {
                    condition: "湿度".to_string(),
                    value: "适中".to_string(),
                },
                StorageRequirement::EnvironmentalCondition {
                    condition: "温度".to_string(),
                    value: "恒定".to_string(),
                },
            ],
            ContainerType::CraftingMaterialStorage => {
                vec![StorageRequirement::EnvironmentalCondition {
                    condition: "防潮".to_string(),
                    value: "是".to_string(),
                }]
            }
            ContainerType::SpiritIsolationBox => vec![
                StorageRequirement::SpecialContainer {
                    container_type: "隔灵容器".to_string(),
                },
                StorageRequirement::Isolation {
                    reason: "高能量隔离".to_string(),
                },
            ],
            ContainerType::SoulStabilizationBox => vec![
                StorageRequirement::SpecialContainer {
                    container_type: "稳定容器".to_string(),
                },
                StorageRequirement::Isolation {
                    reason: "不稳定材料隔离".to_string(),
                },
            ],
            ContainerType::SpiritNurturingPlatform => {
                vec![StorageRequirement::EnvironmentalCondition {
                    condition: "生机供给".to_string(),
                    value: "持续".to_string(),
                }]
            }
            ContainerType::TimeStasisBox => vec![StorageRequirement::SpecialContainer {
                container_type: "时间容器".to_string(),
            }],
        }
    }

    /// 检查是否可以存储特定材料
    pub fn can_store(&self, material: &Material) -> bool {
        let container_requirements = self.supported_requirements();

        // 检查材料的存储需求是否被容器支持
        for material_req in &material.storage_requirements {
            let mut requirement_met = false;

            for container_req in &container_requirements {
                if storage_requirements_compatible(material_req, container_req) {
                    requirement_met = true;
                    break;
                }
            }

            // 如果有存储需求但容器不支持，则不能存储
            if !requirement_met && !material.storage_requirements.is_empty() {
                match self {
                    ContainerType::BasicWarehouse => {
                        // 基础仓库只能存储无特殊要求的材料
                        return false;
                    }
                    _ => {
                        // 其他专用容器检查具体需求
                        if matches!(
                            material_req,
                            StorageRequirement::SpecialContainer { .. }
                                | StorageRequirement::Isolation { .. }
                        ) {
                            return false;
                        }
                    }
                }
            }
        }

        true
    }
}

/// 检查存储需求兼容性
fn storage_requirements_compatible(
    material_req: &StorageRequirement,
    container_req: &StorageRequirement,
) -> bool {
    match (material_req, container_req) {
        (
            StorageRequirement::SpecialContainer {
                container_type: mat_type,
            },
            StorageRequirement::SpecialContainer {
                container_type: cont_type,
            },
        ) => mat_type == cont_type,
        (
            StorageRequirement::EnvironmentalCondition {
                condition: mat_cond,
                ..
            },
            StorageRequirement::EnvironmentalCondition {
                condition: cont_cond,
                ..
            },
        ) => mat_cond == cont_cond,
        (
            StorageRequirement::Isolation { reason: mat_reason },
            StorageRequirement::Isolation {
                reason: cont_reason,
            },
        ) => mat_reason.contains(cont_reason) || cont_reason.contains(mat_reason),
        _ => false,
    }
}

// ============================================================================
// 储物装备系统
// ============================================================================

/// 储物装备 - 可佩戴的存储道具
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StorageEquipment {
    /// 装备ID
    pub id: String,
    /// 装备名称
    pub name: String,
    /// 装备类型
    pub equipment_type: StorageEquipmentType,
    /// 装备品阶
    pub grade: MaterialGrade,
    /// 存储空间（格子数）
    pub storage_slots: u32,
    /// 特殊效果
    pub special_effects: Vec<StorageEffect>,
    /// 是否绑定
    pub bound: bool,
}

/// 储物装备类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum StorageEquipmentType {
    /// 储物袋
    StorageBag,
    /// 储物戒指
    StorageRing,
    /// 储物镯
    StorageBracelet,
    /// 储物腰带
    StorageBelt,
    /// 储物吊坠
    StoragePendant,
    /// 芥子袋
    DimensionalPouch,
    /// 须弥戒
    DimensionalRing,
    /// 乾坤袋
    CosmicBag,
}

impl StorageEquipmentType {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            StorageEquipmentType::StorageBag => "储物袋",
            StorageEquipmentType::StorageRing => "储物戒指",
            StorageEquipmentType::StorageBracelet => "储物镯",
            StorageEquipmentType::StorageBelt => "储物腰带",
            StorageEquipmentType::StoragePendant => "储物吊坠",
            StorageEquipmentType::DimensionalPouch => "芥子袋",
            StorageEquipmentType::DimensionalRing => "须弥戒",
            StorageEquipmentType::CosmicBag => "乾坤袋",
        }
    }

    /// 获取基础存储容量范围
    pub fn base_capacity_range(&self, grade: MaterialGrade) -> (u32, u32) {
        let base_multiplier = match grade {
            MaterialGrade::Mortal => 1.0,
            MaterialGrade::Spiritual => 2.0,
            MaterialGrade::Immortal => 4.0,
            MaterialGrade::Divine => 8.0,
            MaterialGrade::Sacred => 16.0,
            MaterialGrade::Chaos => 32.0,
        };

        let (min_base, max_base) = match self {
            StorageEquipmentType::StorageBag => (10, 30),
            StorageEquipmentType::StorageRing => (5, 20),
            StorageEquipmentType::StorageBracelet => (8, 25),
            StorageEquipmentType::StorageBelt => (15, 40),
            StorageEquipmentType::StoragePendant => (6, 18),
            StorageEquipmentType::DimensionalPouch => (20, 60),
            StorageEquipmentType::DimensionalRing => (30, 80),
            StorageEquipmentType::CosmicBag => (50, 150),
        };

        (
            (min_base as f32 * base_multiplier) as u32,
            (max_base as f32 * base_multiplier) as u32,
        )
    }
}

/// 储物效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum StorageEffect {
    /// 材料保鲜 - 延缓材料衰减
    MaterialPreservation {
        effectiveness: f64, // 0.0-1.0，效果强度
    },
    /// 自动分类 - 自动整理相同材料
    AutoSorting,
    /// 快速检索 - 加速查找材料
    QuickSearch,
    /// 容量扩展 - 额外增加存储空间
    CapacityBonus { additional_slots: u32 },
    /// 属性亲和 - 对特定属性材料有特殊保护
    AttributeAffinity {
        attribute: MaterialAttribute,
        protection_level: f64,
    },
    /// 重量减轻 - 减少携带负担
    WeightReduction { reduction_percent: f64 },
}

// ============================================================================
// 材料储存实例
// ============================================================================

/// 储存位置 - 材料在仓库中的具体位置
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct StorageLocation {
    /// 容器ID
    pub container_id: String,
    /// 槽位编号
    pub slot_number: u32,
}

/// 储存的材料实例
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StoredMaterial {
    /// 材料定义
    pub material: Material,
    /// 数量
    pub quantity: u32,
    /// 存储时间
    pub stored_at: DateTime<Utc>,
    /// 到期时间（如果有）
    pub expires_at: Option<DateTime<Utc>>,
    /// 当前状态
    pub condition: MaterialCondition,
    /// 存储位置
    pub location: StorageLocation,
}

/// 材料状况
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MaterialCondition {
    /// 完美状态
    Perfect,
    /// 良好状态
    Good,
    /// 一般状态
    Fair,
    /// 轻微衰减
    SlightDecay,
    /// 明显衰减
    ModerateDecay,
    /// 严重衰减
    SevereDecay,
    /// 即将失效
    NearExpiry,
    /// 已失效
    Expired,
}

impl MaterialCondition {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            MaterialCondition::Perfect => "完美",
            MaterialCondition::Good => "良好",
            MaterialCondition::Fair => "一般",
            MaterialCondition::SlightDecay => "轻微衰减",
            MaterialCondition::ModerateDecay => "明显衰减",
            MaterialCondition::SevereDecay => "严重衰减",
            MaterialCondition::NearExpiry => "即将失效",
            MaterialCondition::Expired => "已失效",
        }
    }

    /// 获取状况对材料效能的影响
    pub fn effectiveness_modifier(&self) -> f64 {
        match self {
            MaterialCondition::Perfect => 1.0,
            MaterialCondition::Good => 0.95,
            MaterialCondition::Fair => 0.85,
            MaterialCondition::SlightDecay => 0.75,
            MaterialCondition::ModerateDecay => 0.6,
            MaterialCondition::SevereDecay => 0.4,
            MaterialCondition::NearExpiry => 0.2,
            MaterialCondition::Expired => 0.0,
        }
    }
}

impl StoredMaterial {
    /// 创建新的储存材料实例
    pub fn new(
        material: Material,
        quantity: u32,
        location: StorageLocation,
        storage_duration: Option<Duration>,
    ) -> Self {
        let now = Utc::now();
        let expires_at = storage_duration.map(|duration| now + duration);

        Self {
            material,
            quantity,
            stored_at: now,
            expires_at,
            condition: MaterialCondition::Perfect,
            location,
        }
    }

    /// 更新材料状况
    pub fn update_condition(&mut self, current_time: DateTime<Utc>) {
        if let Some(expiry) = self.expires_at {
            let time_remaining = expiry - current_time;
            let total_duration = expiry - self.stored_at;

            if time_remaining.num_seconds() <= 0 {
                self.condition = MaterialCondition::Expired;
            } else {
                let decay_ratio = 1.0
                    - (time_remaining.num_seconds() as f64 / total_duration.num_seconds() as f64);

                self.condition = match decay_ratio {
                    r if r < 0.1 => MaterialCondition::Perfect,
                    r if r < 0.2 => MaterialCondition::Good,
                    r if r < 0.4 => MaterialCondition::Fair,
                    r if r < 0.6 => MaterialCondition::SlightDecay,
                    r if r < 0.8 => MaterialCondition::ModerateDecay,
                    r if r < 0.95 => MaterialCondition::SevereDecay,
                    _ => MaterialCondition::NearExpiry,
                };
            }
        }
    }

    /// 检查是否可以使用
    pub fn is_usable(&self) -> bool {
        !matches!(self.condition, MaterialCondition::Expired)
    }

    /// 获取实际可用数量
    pub fn usable_quantity(&self) -> u32 {
        if self.is_usable() {
            self.quantity
        } else {
            0
        }
    }
}

// ============================================================================
// 材料仓库系统
// ============================================================================

/// 材料仓库 - 管理材料存储的核心系统
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MaterialWarehouse {
    /// 仓库ID
    pub id: String,
    /// 仓库名称
    pub name: String,
    /// 拥有者ID
    pub owner_id: String,
    /// 存储容器
    pub containers: HashMap<String, StorageContainer>,
    /// 储存的材料
    pub stored_materials: HashMap<String, StoredMaterial>, // material_instance_id -> stored_material
    /// 容量统计
    pub capacity_stats: CapacityStatistics,
    /// 访问权限
    pub access_permissions: Vec<AccessPermission>,
}

/// 存储容器
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct StorageContainer {
    /// 容器ID
    pub id: String,
    /// 容器名称
    pub name: String,
    /// 容器类型
    pub container_type: ContainerType,
    /// 最大容量
    pub max_capacity: u32,
    /// 已使用容量
    pub used_capacity: u32,
    /// 容器状态
    pub status: ContainerStatus,
    /// 维护需求
    pub maintenance_requirements: Vec<MaintenanceRequirement>,
    /// 上次维护时间
    pub last_maintenance: DateTime<Utc>,
}

/// 容器状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ContainerStatus {
    /// 正常运行
    Operational,
    /// 需要维护
    NeedsMaintenance,
    /// 容量不足
    NearCapacity,
    /// 已满
    Full,
    /// 故障
    Malfunction,
    /// 停用
    Disabled,
}

impl ContainerStatus {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            ContainerStatus::Operational => "正常运行",
            ContainerStatus::NeedsMaintenance => "需要维护",
            ContainerStatus::NearCapacity => "容量不足",
            ContainerStatus::Full => "已满",
            ContainerStatus::Malfunction => "故障",
            ContainerStatus::Disabled => "停用",
        }
    }
}

/// 维护需求
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MaintenanceRequirement {
    /// 维护类型
    pub maintenance_type: String,
    /// 所需材料
    pub required_materials: Vec<(String, u32)>, // (material_id, quantity)
    /// 维护周期（小时）
    pub cycle_hours: u32,
    /// 维护描述
    pub description: String,
}

/// 容量统计
#[derive(Debug, Clone, Default, PartialEq, Serialize, Deserialize)]
pub struct CapacityStatistics {
    /// 总容量
    pub total_capacity: u32,
    /// 已使用容量
    pub used_capacity: u32,
    /// 按容器类型的容量分布
    pub capacity_by_type: HashMap<ContainerType, (u32, u32)>, // (used, total)
    /// 按材料属性的存储分布
    pub storage_by_attribute: HashMap<MaterialAttribute, u32>,
}

/// 访问权限
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AccessPermission {
    /// 用户ID
    pub user_id: String,
    /// 权限类型
    pub permission_type: PermissionType,
    /// 授权时间
    pub granted_at: DateTime<Utc>,
    /// 过期时间
    pub expires_at: Option<DateTime<Utc>>,
}

/// 权限类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PermissionType {
    /// 只读权限
    ReadOnly,
    /// 存取权限
    DepositWithdraw,
    /// 管理权限
    Manage,
    /// 完全控制
    FullControl,
}

impl MaterialWarehouse {
    /// 创建新仓库
    pub fn new(id: String, name: String, owner_id: String) -> Self {
        Self {
            id,
            name,
            owner_id,
            containers: HashMap::new(),
            stored_materials: HashMap::new(),
            capacity_stats: CapacityStatistics::default(),
            access_permissions: Vec::new(),
        }
    }

    /// 添加存储容器
    pub fn add_container(&mut self, container: StorageContainer) {
        self.capacity_stats.total_capacity += container.max_capacity;
        self.capacity_stats
            .capacity_by_type
            .entry(container.container_type)
            .or_insert((0, 0))
            .1 += container.max_capacity;

        self.containers.insert(container.id.clone(), container);
    }

    /// 存储材料
    pub fn store_material(
        &mut self,
        material: Material,
        quantity: u32,
        preferred_container_type: Option<ContainerType>,
    ) -> Result<String, String> {
        // 寻找合适的容器
        let container_id =
            self.find_suitable_container(&material, quantity, preferred_container_type)?;

        // 生成材料实例ID
        let instance_id = format!("{}_{}", material.id, Utc::now().timestamp());

        // 获取下一个可用槽位
        let slot_number = self.get_next_available_slot(&container_id)?;

        // 创建存储位置
        let location = StorageLocation {
            container_id: container_id.clone(),
            slot_number,
        };

        // 创建储存材料实例
        let storage_duration = material
            .storage_requirements
            .iter()
            .find_map(|req| match req {
                StorageRequirement::TimeLimit { duration_hours } => {
                    Some(Duration::hours(*duration_hours as i64))
                }
                _ => None,
            });

        let stored_material = StoredMaterial::new(material, quantity, location, storage_duration);

        // 更新容器使用容量
        if let Some(container) = self.containers.get_mut(&container_id) {
            container.used_capacity += quantity;

            // 更新容器状态
            let usage_ratio = container.used_capacity as f64 / container.max_capacity as f64;
            container.status = match usage_ratio {
                r if r >= 1.0 => ContainerStatus::Full,
                r if r >= 0.9 => ContainerStatus::NearCapacity,
                _ => ContainerStatus::Operational,
            };
        }

        // 更新统计信息
        self.capacity_stats.used_capacity += quantity;
        *self
            .capacity_stats
            .storage_by_attribute
            .entry(stored_material.material.primary_attribute)
            .or_insert(0) += quantity;

        // 储存材料
        self.stored_materials
            .insert(instance_id.clone(), stored_material);

        Ok(instance_id)
    }

    /// 寻找合适的容器
    fn find_suitable_container(
        &self,
        material: &Material,
        quantity: u32,
        preferred_type: Option<ContainerType>,
    ) -> Result<String, String> {
        // 首先尝试首选容器类型
        if let Some(preferred) = preferred_type {
            if let Some(container_id) = self.find_container_by_type(preferred, material, quantity) {
                return Ok(container_id);
            }
        }

        // 寻找任何能存储该材料的容器
        for (container_id, container) in &self.containers {
            if container.container_type.can_store(material)
                && container.used_capacity + quantity <= container.max_capacity
                && matches!(
                    container.status,
                    ContainerStatus::Operational | ContainerStatus::NearCapacity
                )
            {
                return Ok(container_id.clone());
            }
        }

        Err("未找到合适的存储容器".to_string())
    }

    /// 根据类型寻找容器
    fn find_container_by_type(
        &self,
        container_type: ContainerType,
        material: &Material,
        quantity: u32,
    ) -> Option<String> {
        for (container_id, container) in &self.containers {
            if container.container_type == container_type
                && container.container_type.can_store(material)
                && container.used_capacity + quantity <= container.max_capacity
                && matches!(
                    container.status,
                    ContainerStatus::Operational | ContainerStatus::NearCapacity
                )
            {
                return Some(container_id.clone());
            }
        }
        None
    }

    /// 获取下一个可用槽位
    fn get_next_available_slot(&self, container_id: &str) -> Result<u32, String> {
        let used_slots: std::collections::HashSet<u32> = self
            .stored_materials
            .values()
            .filter(|stored| stored.location.container_id == container_id)
            .map(|stored| stored.location.slot_number)
            .collect();

        if let Some(container) = self.containers.get(container_id) {
            for slot in 1..=container.max_capacity {
                if !used_slots.contains(&slot) {
                    return Ok(slot);
                }
            }
        }

        Err("容器已满，无可用槽位".to_string())
    }

    /// 取出材料
    pub fn withdraw_material(
        &mut self,
        instance_id: &str,
        quantity: u32,
    ) -> Result<(Material, u32), String> {
        if let Some(stored_material) = self.stored_materials.get_mut(instance_id) {
            if !stored_material.is_usable() {
                return Err("材料已过期，无法使用".to_string());
            }

            if stored_material.quantity < quantity {
                return Err("库存数量不足".to_string());
            }

            let material = stored_material.material.clone();

            // 更新数量
            stored_material.quantity -= quantity;

            // 更新容器使用容量
            if let Some(container) = self
                .containers
                .get_mut(&stored_material.location.container_id)
            {
                container.used_capacity -= quantity;
            }

            // 更新统计信息
            self.capacity_stats.used_capacity -= quantity;
            if let Some(count) = self
                .capacity_stats
                .storage_by_attribute
                .get_mut(&material.primary_attribute)
            {
                *count -= quantity;
            }

            // 如果数量为0，移除储存记录
            if stored_material.quantity == 0 {
                self.stored_materials.remove(instance_id);
            }

            Ok((material, quantity))
        } else {
            Err("未找到指定的材料实例".to_string())
        }
    }

    /// 查找材料
    pub fn find_materials(&self, material_id: &str) -> Vec<&StoredMaterial> {
        self.stored_materials
            .values()
            .filter(|stored| stored.material.id == material_id && stored.is_usable())
            .collect()
    }

    /// 获取总库存
    pub fn get_material_total(&self, material_id: &str) -> u32 {
        self.stored_materials
            .values()
            .filter(|stored| stored.material.id == material_id && stored.is_usable())
            .map(|stored| stored.usable_quantity())
            .sum()
    }

    /// 更新所有材料状况
    pub fn update_all_materials(&mut self) {
        let current_time = Utc::now();
        for stored_material in self.stored_materials.values_mut() {
            stored_material.update_condition(current_time);
        }
    }

    /// 获取容量使用率
    pub fn get_capacity_usage(&self) -> f64 {
        if self.capacity_stats.total_capacity > 0 {
            self.capacity_stats.used_capacity as f64 / self.capacity_stats.total_capacity as f64
        } else {
            0.0
        }
    }
}
