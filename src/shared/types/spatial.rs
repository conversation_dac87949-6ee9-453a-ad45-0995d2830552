/// 空间和位置相关类型
///
/// 此模块定义了游戏中所有与位置、空间、区域相关的类型
use serde::{Deserialize, Serialize};
use std::hash::Hash;

// 引入世界地图相关类型
use crate::world_map::RegionId;
// ============================================================================
// 导入或定义世界层次和边界类型
// ============================================================================

/// 世界层次 - 修仙世界的多层次空间结构
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum WorldLayerType {
    /// 凡间 - 普通修士生活的世界
    Mortal,
    /// 灵界 - 灵气充沛的修炼世界
    Spirit,
    /// 仙界 - 仙人居住的高级世界
    Immortal,
    /// 混沌界 - 最高层次的混沌空间
    Chaos,
    /// 秘境 - 独立的小世界空间
    SecretRealm(u32),
}

/// 世界地图层
#[derive(Debug, Clone, Serialize, Deserialize, Hash)]
pub struct WorldLayer {
    pub id: String,
    pub name: String,
    pub data: Vec<Position>,
}

// Implement Eq manually since Vec<Position> doesn't implement Eq
impl Eq for WorldLayer {}

// Override PartialEq to ignore the data field for HashMap purposes
impl PartialEq for WorldLayer {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id && self.name == other.name
    }
}

impl WorldLayer {
    pub fn new(id: String, name: String, data: Vec<Position>) -> Self {
        Self { id, name, data }
    }
}

impl WorldLayerType {
    /// 获取层级的灵气密度基础倍数
    pub fn spiritual_density_base(&self) -> f32 {
        match self {
            WorldLayerType::Mortal => 1.0,
            WorldLayerType::Spirit => 3.0,
            WorldLayerType::Immortal => 8.0,
            WorldLayerType::Chaos => 20.0,
            WorldLayerType::SecretRealm(_) => 5.0,
        }
    }

    /// 获取层级的危险系数
    pub fn danger_multiplier(&self) -> f32 {
        match self {
            WorldLayerType::Mortal => 1.0,
            WorldLayerType::Spirit => 2.0,
            WorldLayerType::Immortal => 5.0,
            WorldLayerType::Chaos => 10.0,
            WorldLayerType::SecretRealm(_) => 3.0,
        }
    }

    /// 判断是否可以从当前层级传送到目标层级
    pub fn can_teleport_to(&self, target: &WorldLayerType) -> bool {
        match (self, target) {
            // 凡间可以去灵界和秘境
            (WorldLayerType::Mortal, WorldLayerType::Spirit) => true,
            (WorldLayerType::Mortal, WorldLayerType::SecretRealm(_)) => true,

            // 灵界可以去凡间、仙界和秘境
            (WorldLayerType::Spirit, WorldLayerType::Mortal) => true,
            (WorldLayerType::Spirit, WorldLayerType::Immortal) => true,
            (WorldLayerType::Spirit, WorldLayerType::SecretRealm(_)) => true,

            // 仙界可以去所有层级
            (WorldLayerType::Immortal, _) => true,

            // 混沌界可以去所有层级
            (WorldLayerType::Chaos, _) => true,

            // 秘境可以回到原来的层级（简化处理）
            (WorldLayerType::SecretRealm(_), WorldLayerType::Mortal) => true,
            (WorldLayerType::SecretRealm(_), WorldLayerType::Spirit) => true,

            // 同层级内传送总是允许的
            (a, b) if a == b => true,

            _ => false,
        }
    }
}

// ============================================================================
// 建筑占地面积（从 world_map 合并）
// ============================================================================

/// 建筑占地面积 - 定义建筑在世界中占用的空间
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BuildingFootprint {
    /// 建筑的形状类型
    pub shape: FootprintShape,
    /// 建筑的方向（角度，以度为单位）
    pub orientation: f32,
}

/// 建筑占地形状
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum FootprintShape {
    /// 矩形占地
    Rectangle { width: u32, height: u32 },
    /// 圆形占地
    Circle { radius: f32 },
    /// 多边形占地
    Polygon { vertices: Vec<(i32, i32)> },
}

impl BuildingFootprint {
    /// 创建矩形占地
    pub fn rectangle(width: u32, height: u32) -> Self {
        Self {
            shape: FootprintShape::Rectangle { width, height },
            orientation: 0.0,
        }
    }

    /// 创建圆形占地
    pub fn circle(radius: f32) -> Self {
        Self {
            shape: FootprintShape::Circle { radius },
            orientation: 0.0,
        }
    }

    /// 检查指定位置是否在建筑占地范围内
    pub fn contains_point(&self, building_center: (i32, i32), point: (i32, i32)) -> bool {
        let (cx, cy) = building_center;
        let (px, py) = point;

        match &self.shape {
            FootprintShape::Rectangle { width, height } => {
                let half_width = *width as i32 / 2;
                let half_height = *height as i32 / 2;

                px >= cx - half_width
                    && px <= cx + half_width
                    && py >= cy - half_height
                    && py <= cy + half_height
            }
            FootprintShape::Circle { radius } => {
                let dx = (px - cx) as f32;
                let dy = (py - cy) as f32;
                let distance = (dx * dx + dy * dy).sqrt();
                distance <= *radius
            }
            FootprintShape::Polygon { vertices } => {
                // 使用射线投射算法检查点是否在多边形内
                self.point_in_polygon(building_center, point, vertices)
            }
        }
    }

    /// 点在多边形内的判断（射线投射算法）
    fn point_in_polygon(
        &self,
        building_center: (i32, i32),
        point: (i32, i32),
        vertices: &[(i32, i32)],
    ) -> bool {
        let (cx, cy) = building_center;
        let (px, py) = point;

        // 将多边形顶点相对于建筑中心进行偏移
        let offset_vertices: Vec<(i32, i32)> =
            vertices.iter().map(|(vx, vy)| (cx + vx, cy + vy)).collect();

        let mut inside = false;
        let n = offset_vertices.len();

        for i in 0..n {
            let j = (i + n - 1) % n;
            let (xi, yi) = offset_vertices[i];
            let (xj, yj) = offset_vertices[j];

            if ((yi > py) != (yj > py)) && (px < (xj - xi) * (py - yi) / (yj - yi) + xi) {
                inside = !inside;
            }
        }

        inside
    }

    /// 计算建筑占地面积
    pub fn area(&self) -> f32 {
        match &self.shape {
            FootprintShape::Rectangle { width, height } => (*width * *height) as f32,
            FootprintShape::Circle { radius } => std::f32::consts::PI * radius * radius,
            FootprintShape::Polygon { vertices } => {
                // 使用鞋带公式计算多边形面积
                if vertices.len() < 3 {
                    return 0.0;
                }

                let mut area = 0.0;
                let n = vertices.len();

                for i in 0..n {
                    let j = (i + 1) % n;
                    let (xi, yi) = vertices[i];
                    let (xj, yj) = vertices[j];
                    area += (xi * yj - xj * yi) as f32;
                }

                (area / 2.0).abs()
            }
        }
    }
}

impl std::fmt::Display for WorldLayerType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            WorldLayerType::Mortal => write!(f, "凡间"),
            WorldLayerType::Spirit => write!(f, "灵界"),
            WorldLayerType::Immortal => write!(f, "仙界"),
            WorldLayerType::Chaos => write!(f, "混沌界"),
            WorldLayerType::SecretRealm(id) => write!(f, "秘境-{}", id),
        }
    }
}

impl Default for WorldLayerType {
    fn default() -> Self {
        WorldLayerType::Mortal
    }
}

// ============================================================================
// 位置和空间类型
// ============================================================================

/// 统一的位置坐标系统
///
/// 支持多种用途的位置表示：
/// - 2D/3D 坐标
/// - 浮点数精确计算（distance_to）
/// - 整数网格定位（grid_x, grid_y）
/// - 多层次世界系统
/// - 区域管理
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct Position {
    /// 精确X坐标（浮点数，用于平滑移动和精确计算）
    pub x: f32,
    /// 精确Y坐标（浮点数，用于平滑移动和精确计算）
    pub y: f32,
    /// Z坐标（可选，用于3D世界）
    pub z: Option<f32>,
    /// 网格X坐标（整数，用于世界地图和区域定位）
    pub grid_x: i32,
    /// 网格Y坐标（整数，用于世界地图和区域定位）
    pub grid_y: i32,
    /// 网格Z坐标（可选，用于3D世界网格）
    pub grid_z: Option<i32>,
    /// 世界层次（可选，用于多层次世界）
    pub layer: Option<WorldLayerType>,
    /// 所属区域ID（可选）
    pub region_id: Option<RegionId>,
}

impl Position {
    /// 创建简单的2D浮点坐标（向下兼容）
    pub fn new(x: f32, y: f32) -> Self {
        Self {
            x,
            y,
            z: None,
            grid_x: x.round() as i32,
            grid_y: y.round() as i32,
            grid_z: None,
            layer: None,
            region_id: None,
        }
    }

    /// 创建带网格坐标的2D位置
    pub fn new_with_grid(x: f32, y: f32, grid_x: i32, grid_y: i32) -> Self {
        Self {
            x,
            y,
            z: None,
            grid_x,
            grid_y,
            grid_z: None,
            layer: None,
            region_id: None,
        }
    }

    /// 创建网格位置（用于世界地图）
    pub fn new_grid(grid_x: i32, grid_y: i32, layer: WorldLayerType) -> Self {
        Self {
            x: grid_x as f32,
            y: grid_y as f32,
            z: None,
            grid_x,
            grid_y,
            grid_z: None,
            layer: Some(layer),
            region_id: None,
        }
    }

    /// 创建3D位置
    pub fn new_3d(x: f32, y: f32, z: f32) -> Self {
        Self {
            x,
            y,
            z: Some(z),
            grid_x: x.round() as i32,
            grid_y: y.round() as i32,
            grid_z: Some(z.round() as i32),
            layer: None,
            region_id: None,
        }
    }

    /// 创建完整的3D网格位置
    pub fn new_3d_grid(grid_x: i32, grid_y: i32, grid_z: i32, layer: WorldLayerType) -> Self {
        Self {
            x: grid_x as f32,
            y: grid_y as f32,
            z: Some(grid_z as f32),
            grid_x,
            grid_y,
            grid_z: Some(grid_z),
            layer: Some(layer),
            region_id: None,
        }
    }

    /// 设置区域ID
    pub fn with_region(mut self, region_id: RegionId) -> Self {
        self.region_id = Some(region_id);
        self
    }

    /// 设置世界层次
    pub fn with_layer(mut self, layer: WorldLayerType) -> Self {
        self.layer = Some(layer);
        self
    }

    /// 计算精确距离（浮点数）
    pub fn distance_to(&self, other: &Position) -> f32 {
        // 如果有层次信息，检查是否在同一层
        if let (Some(layer1), Some(layer2)) = (self.layer, other.layer) {
            if layer1 != layer2 {
                return f32::INFINITY;
            }
        }

        let dx = other.x - self.x;
        let dy = other.y - self.y;

        match (self.z, other.z) {
            (Some(z1), Some(z2)) => {
                let dz = z2 - z1;
                (dx * dx + dy * dy + dz * dz).sqrt()
            }
            (None, None) => (dx * dx + dy * dy).sqrt(),
            _ => f32::INFINITY, // 维度不匹配
        }
    }

    /// 计算网格距离（曼哈顿距离）
    pub fn grid_distance_to(&self, other: &Position) -> u32 {
        if let (Some(layer1), Some(layer2)) = (self.layer, other.layer) {
            if layer1 != layer2 {
                return u32::MAX;
            }
        }

        let dx = (other.grid_x - self.grid_x).abs() as u32;
        let dy = (other.grid_y - self.grid_y).abs() as u32;

        match (self.grid_z, other.grid_z) {
            (Some(z1), Some(z2)) => {
                let dz = (z2 - z1).abs() as u32;
                dx + dy + dz
            }
            (None, None) => dx + dy,
            _ => u32::MAX,
        }
    }

    /// 获取原点位置
    pub fn origin() -> Self {
        Self {
            x: 0.0,
            y: 0.0,
            z: None,
            grid_x: 0,
            grid_y: 0,
            grid_z: None,
            layer: None,
            region_id: None,
        }
    }

    /// 获取标准化向量
    pub fn normalized(&self) -> Self {
        let len = (self.x * self.x + self.y * self.y).sqrt();
        if len > 0.0 {
            Self {
                x: self.x / len,
                y: self.y / len,
                z: self.z.map(|z| z / len),
                ..*self
            }
        } else {
            *self
        }
    }

    /// 判断是否为3D位置
    pub fn is_3d(&self) -> bool {
        self.z.is_some()
    }

    /// 获取周围的网格位置（8方向，2D情况下）
    pub fn get_adjacent_grid_positions(&self) -> Vec<Position> {
        let mut positions = Vec::new();

        for dx in -1..=1 {
            for dy in -1..=1 {
                if dx == 0 && dy == 0 {
                    continue; // 跳过自己
                }

                let new_pos = Position {
                    x: (self.grid_x + dx) as f32,
                    y: (self.grid_y + dy) as f32,
                    z: self.z,
                    grid_x: self.grid_x + dx,
                    grid_y: self.grid_y + dy,
                    grid_z: self.grid_z,
                    layer: self.layer,
                    region_id: self.region_id,
                };
                positions.push(new_pos);
            }
        }

        positions
    }

    /// 向指定方向移动
    pub fn move_in_direction(&self, direction: Direction, distance: f32) -> Position {
        let (dx, dy) = direction.to_vector();
        Position {
            x: self.x + dx * distance,
            y: self.y + dy * distance,
            z: self.z,
            grid_x: (self.x + dx * distance).round() as i32,
            grid_y: (self.y + dy * distance).round() as i32,
            grid_z: self.grid_z,
            layer: self.layer,
            region_id: self.region_id,
        }
    }

    /// 检查是否可以传送到目标位置
    pub fn can_teleport_to(&self, target: &Position) -> bool {
        match (self.layer, target.layer) {
            (Some(layer1), Some(layer2)) => layer1.can_teleport_to(&layer2),
            _ => true, // 没有层次限制
        }
    }

    /// 同步网格坐标和精确坐标
    pub fn sync_to_grid(&mut self) {
        self.x = self.grid_x as f32;
        self.y = self.grid_y as f32;
        if let Some(grid_z) = self.grid_z {
            self.z = Some(grid_z as f32);
        }
    }

    /// 同步精确坐标到网格
    pub fn sync_to_precise(&mut self) {
        self.grid_x = self.x.round() as i32;
        self.grid_y = self.y.round() as i32;
        if let Some(z) = self.z {
            self.grid_z = Some(z.round() as i32);
        }
    }
}

impl Hash for Position {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.x.to_bits().hash(state);
        self.y.to_bits().hash(state);
        self.z.map(|z| z.to_bits()).hash(state);
        self.grid_x.hash(state);
        self.grid_y.hash(state);
        self.grid_z.hash(state);
        self.layer.hash(state);
        self.region_id.hash(state);
    }
}

impl PartialEq for Position {
    fn eq(&self, other: &Self) -> bool {
        self.x == other.x
            && self.y == other.y
            && self.z == other.z
            && self.grid_x == other.grid_x
            && self.grid_y == other.grid_y
            && self.grid_z == other.grid_z
            && self.layer == other.layer
            && self.region_id == other.region_id
    }
}

impl Eq for Position {}

impl Default for Position {
    fn default() -> Self {
        Self::origin()
    }
}

impl std::fmt::Display for Position {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let coords = match self.z {
            Some(z) => format!("({:.1}, {:.1}, {:.1})", self.x, self.y, z),
            None => format!("({:.1}, {:.1})", self.x, self.y),
        };

        let grid = match self.grid_z {
            Some(gz) => format!("[{}, {}, {}]", self.grid_x, self.grid_y, gz),
            None => format!("[{}, {}]", self.grid_x, self.grid_y),
        };

        match self.layer {
            Some(layer) => write!(f, "{} {} @ {:?}", coords, grid, layer),
            None => write!(f, "{} {}", coords, grid),
        }
    }
}

/// 区域结构
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Area {
    pub center: Position,
    pub size: SpaceSize,
    pub shape: AreaShape,
}

impl Area {
    pub fn new(center: Position, shape: AreaShape) -> Self {
        Self {
            center,
            size: SpaceSize::Medium,
            shape,
        }
    }

    pub fn contains_point(&self, point: Position) -> bool {
        match self.shape {
            AreaShape::Circle { radius } => self.center.distance_to(&point) <= radius,
            AreaShape::Rectangle { width, height } => {
                let dx = (point.x - self.center.x).abs();
                let dy = (point.y - self.center.y).abs();
                dx <= width / 2.0 && dy <= height / 2.0
            }
            AreaShape::Cone { radius, angle: _ } => {
                // 简化实现，当作圆形处理
                self.center.distance_to(&point) <= radius
            }
            AreaShape::Point => {
                point.grid_x == self.center.grid_x && point.grid_y == self.center.grid_y
            }
            AreaShape::Line { length } => {
                // 简化实现
                self.center.distance_to(&point) <= length
            }
            _ => false,
        }
    }
}

/// 区域形状
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AreaShape {
    /// 点目标
    Point,
    /// 圆形区域
    Circle { radius: f32 },
    /// 矩形区域
    Rectangle { width: f32, height: f32 },
    /// 直线区域
    Line { length: f32 },
    /// 锥形区域（统一定义：半径+角度）
    Cone { radius: f32, angle: f32 },
    /// 多边形区域
    Polygon { vertices: Vec<(f32, f32)> },
}

/// 空间大小
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SpaceSize {
    Tiny,       // 1x1
    Small,      // 2x2
    Medium,     // 3x3
    Large,      // 4x4
    Huge,       // 5x5
    Gargantuan, // 6x6+
}

impl SpaceSize {
    /// 获取空间大小的数值表示
    pub fn to_numeric(&self) -> u32 {
        match self {
            SpaceSize::Tiny => 1,
            SpaceSize::Small => 2,
            SpaceSize::Medium => 3,
            SpaceSize::Large => 4,
            SpaceSize::Huge => 5,
            SpaceSize::Gargantuan => 6,
        }
    }
}

/// 方向枚举（增强版，支持3D）
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Direction {
    North,
    South,
    East,
    West,
    Northeast,
    Northwest,
    Southeast,
    Southwest,
    Up,
    Down,
    None,
}

impl Direction {
    pub fn to_vector(&self) -> (f32, f32) {
        match self {
            Direction::North => (0.0, 1.0),
            Direction::South => (0.0, -1.0),
            Direction::East => (1.0, 0.0),
            Direction::West => (-1.0, 0.0),
            Direction::Northeast => (0.707, 0.707),
            Direction::Northwest => (-0.707, 0.707),
            Direction::Southeast => (0.707, -0.707),
            Direction::Southwest => (-0.707, -0.707),
            Direction::Up => (0.0, 0.0),   // Z方向在2D中不体现
            Direction::Down => (0.0, 0.0), // Z方向在2D中不体现
            Direction::None => (0.0, 0.0),
        }
    }

    /// 获取相反方向
    pub fn opposite(&self) -> Direction {
        match self {
            Direction::North => Direction::South,
            Direction::South => Direction::North,
            Direction::East => Direction::West,
            Direction::West => Direction::East,
            Direction::Northeast => Direction::Southwest,
            Direction::Northwest => Direction::Southeast,
            Direction::Southeast => Direction::Northwest,
            Direction::Southwest => Direction::Northeast,
            Direction::Up => Direction::Down,
            Direction::Down => Direction::Up,
            Direction::None => Direction::None,
        }
    }

    /// 获取所有水平方向
    pub fn horizontal_directions() -> Vec<Direction> {
        vec![
            Direction::North,
            Direction::South,
            Direction::East,
            Direction::West,
            Direction::Northeast,
            Direction::Northwest,
            Direction::Southeast,
            Direction::Southwest,
        ]
    }

    /// 获取所有基本方向（4个）
    pub fn cardinal_directions() -> Vec<Direction> {
        vec![
            Direction::North,
            Direction::South,
            Direction::East,
            Direction::West,
        ]
    }
}

impl std::fmt::Display for Direction {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let name = match self {
            Direction::North => "北",
            Direction::South => "南",
            Direction::East => "东",
            Direction::West => "西",
            Direction::Northeast => "东北",
            Direction::Northwest => "西北",
            Direction::Southeast => "东南",
            Direction::Southwest => "西南",
            Direction::Up => "上",
            Direction::Down => "下",
            Direction::None => "无",
        };
        write!(f, "{}", name)
    }
}

// ============================================================================
// 实用工具类型
// ============================================================================

/// 范围类型，用于表示数值范围
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct ValueRange<T> {
    pub min: T,
    pub max: T,
}

impl<T> ValueRange<T>
where
    T: PartialOrd + Copy,
{
    pub fn new(min: T, max: T) -> Self {
        Self { min, max }
    }

    pub fn contains(&self, value: T) -> bool {
        value >= self.min && value <= self.max
    }
}

// ============================================================================
// 区域边界定义（从world_map合并）
// ============================================================================

/// 区域边界 - 定义一个区域的空间范围
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct RegionBoundaries {
    /// 最小X坐标
    pub min_x: i32,
    /// 最大X坐标
    pub max_x: i32,
    /// 最小Y坐标
    pub min_y: i32,
    /// 最大Y坐标
    pub max_y: i32,
    /// 最小Z坐标（可选，用于3D世界）
    pub min_z: Option<i32>,
    /// 最大Z坐标（可选，用于3D世界）
    pub max_z: Option<i32>,
}

impl RegionBoundaries {
    /// 创建2D矩形边界
    pub fn new_2d(min_x: i32, max_x: i32, min_y: i32, max_y: i32) -> Self {
        Self {
            min_x,
            max_x,
            min_y,
            max_y,
            min_z: None,
            max_z: None,
        }
    }

    /// 创建3D立方体边界
    pub fn new_3d(min_x: i32, max_x: i32, min_y: i32, max_y: i32, min_z: i32, max_z: i32) -> Self {
        Self {
            min_x,
            max_x,
            min_y,
            max_y,
            min_z: Some(min_z),
            max_z: Some(max_z),
        }
    }

    /// 检查坐标是否在边界内
    pub fn contains(&self, x: i32, y: i32, z: Option<i32>) -> bool {
        if x < self.min_x || x > self.max_x || y < self.min_y || y > self.max_y {
            return false;
        }

        match (self.min_z, self.max_z, z) {
            (Some(min_z), Some(max_z), Some(z)) => z >= min_z && z <= max_z,
            (None, None, None) => true, // 2D情况
            _ => false,                 // 维度不匹配
        }
    }

    /// 检查Position是否在边界内
    pub fn contains_position(&self, pos: &Position) -> bool {
        self.contains(pos.grid_x, pos.grid_y, pos.grid_z)
    }

    /// 计算区域面积（2D）或体积（3D）
    pub fn area_or_volume(&self) -> u64 {
        let width = (self.max_x - self.min_x + 1) as u64;
        let height = (self.max_y - self.min_y + 1) as u64;

        match (self.min_z, self.max_z) {
            (Some(min_z), Some(max_z)) => {
                let depth = (max_z - min_z + 1) as u64;
                width * height * depth
            }
            _ => width * height,
        }
    }

    /// 获取边界的中心点
    pub fn center(&self) -> Position {
        let center_x = (self.min_x + self.max_x) / 2;
        let center_y = (self.min_y + self.max_y) / 2;

        match (self.min_z, self.max_z) {
            (Some(min_z), Some(max_z)) => {
                let center_z = (min_z + max_z) / 2;
                Position::new_3d_grid(center_x, center_y, center_z, WorldLayerType::default())
            }
            _ => Position::new_grid(center_x, center_y, WorldLayerType::default()),
        }
    }

    /// 检查两个区域是否重叠
    pub fn overlaps(&self, other: &RegionBoundaries) -> bool {
        // 检查X和Y轴是否重叠
        let x_overlap = self.min_x <= other.max_x && self.max_x >= other.min_x;
        let y_overlap = self.min_y <= other.max_y && self.max_y >= other.min_y;

        if !x_overlap || !y_overlap {
            return false;
        }

        // 检查Z轴是否重叠（如果存在）
        match (self.min_z, self.max_z, other.min_z, other.max_z) {
            (Some(self_min_z), Some(self_max_z), Some(other_min_z), Some(other_max_z)) => {
                self_min_z <= other_max_z && self_max_z >= other_min_z
            }
            (None, None, None, None) => true, // 都是2D，已经检查了XY重叠
            _ => false,                       // 维度不匹配
        }
    }
}
