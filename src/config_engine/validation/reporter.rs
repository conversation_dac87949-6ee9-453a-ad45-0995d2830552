//! # 验证报告器模块
//!
//! 生成详细的验证报告
//!
//! ## 核心功能
//!
//! - 验证结果格式化
//! - 多种输出格式支持
//! - 报告统计和分析
//! - 自定义报告模板

use crate::config_engine::validation::{ErrorSeverity, ValidationResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 验证报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationReport {
    /// 报告标题
    pub title: String,
    /// 生成时间
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 验证结果
    pub result: ValidationResult,
    /// 报告类型
    pub report_type: ReportType,
    /// 验证目标信息
    pub target_info: TargetInfo,
    /// 验证统计信息
    pub statistics: ValidationStatistics,
    /// 附加数据
    pub metadata: HashMap<String, String>,
}

/// 报告类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReportType {
    /// 配置验证报告
    ConfigValidation,
    /// 规则验证报告
    RuleValidation,
    /// 类型验证报告
    TypeValidation,
    /// 完整性检查报告
    IntegrityCheck,
    /// 综合验证报告
    ComprehensiveValidation,
}

/// 验证目标信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TargetInfo {
    /// 目标名称
    pub name: String,
    /// 目标类型
    pub target_type: String,
    /// 目标版本
    pub version: Option<String>,
    /// 目标文件路径
    pub path: Option<String>,
    /// 目标大小（字节）
    pub size: Option<u64>,
    /// 最后修改时间
    pub last_modified: Option<chrono::DateTime<chrono::Utc>>,
}

/// 验证统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationStatistics {
    /// 验证项目总数
    pub total_validations: u32,
    /// 成功验证数
    pub successful_validations: u32,
    /// 失败验证数
    pub failed_validations: u32,
    /// 错误数量按级别分组
    pub errors_by_severity: HashMap<ErrorSeverity, u32>,
    /// 警告数量按级别分组
    pub warnings_by_severity: HashMap<ErrorSeverity, u32>,
    /// 验证耗时（毫秒）
    pub validation_duration_ms: u64,
    /// 验证器统计
    pub validator_stats: HashMap<String, ValidatorStats>,
}

/// 验证器统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidatorStats {
    /// 验证器名称
    pub name: String,
    /// 执行次数
    pub execution_count: u32,
    /// 成功次数
    pub success_count: u32,
    /// 失败次数
    pub failure_count: u32,
    /// 平均执行时间（毫秒）
    pub avg_execution_time_ms: f64,
    /// 发现的错误数
    pub errors_found: u32,
    /// 发现的警告数
    pub warnings_found: u32,
}

impl ValidationReport {
    pub fn new(
        title: String,
        result: ValidationResult,
        report_type: ReportType,
        target_info: TargetInfo,
    ) -> Self {
        let statistics = ValidationStatistics::from_result(&result);

        Self {
            title,
            timestamp: chrono::Utc::now(),
            result,
            report_type,
            target_info,
            statistics,
            metadata: HashMap::new(),
        }
    }

    pub fn add_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
    }

    pub fn is_valid(&self) -> bool {
        self.result.is_valid
    }

    pub fn has_errors(&self) -> bool {
        !self.result.errors.is_empty()
    }

    pub fn has_warnings(&self) -> bool {
        !self.result.warnings.is_empty()
    }

    pub fn error_count(&self) -> usize {
        self.result.errors.len()
    }

    pub fn warning_count(&self) -> usize {
        self.result.warnings.len()
    }

    pub fn success_rate(&self) -> f64 {
        if self.statistics.total_validations == 0 {
            1.0
        } else {
            self.statistics.successful_validations as f64 / self.statistics.total_validations as f64
        }
    }
}

impl ValidationStatistics {
    pub fn from_result(result: &ValidationResult) -> Self {
        let mut errors_by_severity = HashMap::new();
        let mut warnings_by_severity = HashMap::new();

        for error in &result.errors {
            *errors_by_severity.entry(error.severity).or_insert(0) += 1;
        }

        for warning in &result.warnings {
            *warnings_by_severity.entry(warning.severity).or_insert(0) += 1;
        }

        Self {
            total_validations: 1,
            successful_validations: if result.is_valid { 1 } else { 0 },
            failed_validations: if result.is_valid { 0 } else { 1 },
            errors_by_severity,
            warnings_by_severity,
            validation_duration_ms: 0, // 需要在调用时设置
            validator_stats: HashMap::new(),
        }
    }

    pub fn new() -> Self {
        Self {
            total_validations: 0,
            successful_validations: 0,
            failed_validations: 0,
            errors_by_severity: HashMap::new(),
            warnings_by_severity: HashMap::new(),
            validation_duration_ms: 0,
            validator_stats: HashMap::new(),
        }
    }
}

impl Default for ValidationStatistics {
    fn default() -> Self {
        Self::new()
    }
}

impl TargetInfo {
    pub fn new(name: String, target_type: String) -> Self {
        Self {
            name,
            target_type,
            version: None,
            path: None,
            size: None,
            last_modified: None,
        }
    }

    pub fn with_version(mut self, version: String) -> Self {
        self.version = Some(version);
        self
    }

    pub fn with_path(mut self, path: String) -> Self {
        self.path = Some(path);
        self
    }

    pub fn with_size(mut self, size: u64) -> Self {
        self.size = Some(size);
        self
    }

    pub fn with_last_modified(mut self, last_modified: chrono::DateTime<chrono::Utc>) -> Self {
        self.last_modified = Some(last_modified);
        self
    }
}

impl ValidatorStats {
    pub fn new(name: String) -> Self {
        Self {
            name,
            execution_count: 0,
            success_count: 0,
            failure_count: 0,
            avg_execution_time_ms: 0.0,
            errors_found: 0,
            warnings_found: 0,
        }
    }

    pub fn record_execution(
        &mut self,
        success: bool,
        execution_time_ms: u64,
        errors: u32,
        warnings: u32,
    ) {
        self.execution_count += 1;

        if success {
            self.success_count += 1;
        } else {
            self.failure_count += 1;
        }

        // 更新平均执行时间
        let total_time = self.avg_execution_time_ms * (self.execution_count - 1) as f64
            + execution_time_ms as f64;
        self.avg_execution_time_ms = total_time / self.execution_count as f64;

        self.errors_found += errors;
        self.warnings_found += warnings;
    }

    pub fn success_rate(&self) -> f64 {
        if self.execution_count == 0 {
            0.0
        } else {
            self.success_count as f64 / self.execution_count as f64
        }
    }
}

/// 验证报告格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReportFormat {
    Json,
    Html,
    Text,
    Markdown,
    Xml,
}

/// 验证报告生成器
pub struct ValidationReporter;

impl ValidationReporter {
    pub fn new() -> Self {
        Self
    }

    /// 生成验证报告
    pub fn generate_report(&self, result: &ValidationResult, format: ReportFormat) -> String {
        match format {
            ReportFormat::Json => self.generate_json_report(result),
            ReportFormat::Html => self.generate_html_report(result),
            ReportFormat::Text => self.generate_text_report(result),
            ReportFormat::Markdown => self.generate_markdown_report(result),
            ReportFormat::Xml => self.generate_xml_report(result),
        }
    }

    /// 生成JSON格式报告
    fn generate_json_report(&self, result: &ValidationResult) -> String {
        serde_json::to_string_pretty(result).unwrap_or_else(|_| "{}".to_string())
    }

    /// 生成HTML格式报告
    fn generate_html_report(&self, result: &ValidationResult) -> String {
        let mut html = String::new();
        html.push_str(
            "<!DOCTYPE html>\n<html>\n<head>\n<title>Validation Report</title>\n</head>\n<body>\n",
        );
        html.push_str(&format!("<h1>Validation Report</h1>\n"));
        html.push_str(&format!("<p>Valid: {}</p>\n", result.is_valid));
        html.push_str(&format!("<p>Errors: {}</p>\n", result.errors.len()));
        html.push_str(&format!("<p>Warnings: {}</p>\n", result.warnings.len()));

        if !result.errors.is_empty() {
            html.push_str("<h2>Errors</h2>\n<ul>\n");
            for error in &result.errors {
                html.push_str(&format!("<li>{}</li>\n", error.message));
            }
            html.push_str("</ul>\n");
        }

        if !result.warnings.is_empty() {
            html.push_str("<h2>Warnings</h2>\n<ul>\n");
            for warning in &result.warnings {
                html.push_str(&format!("<li>{}</li>\n", warning.message));
            }
            html.push_str("</ul>\n");
        }

        html.push_str("</body>\n</html>");
        html
    }

    /// 生成文本格式报告
    fn generate_text_report(&self, result: &ValidationResult) -> String {
        let mut text = String::new();
        text.push_str("=== Validation Report ===\n");
        text.push_str(&format!("Valid: {}\n", result.is_valid));
        text.push_str(&format!("Errors: {}\n", result.errors.len()));
        text.push_str(&format!("Warnings: {}\n", result.warnings.len()));
        text.push_str("\n");

        if !result.errors.is_empty() {
            text.push_str("Errors:\n");
            for error in &result.errors {
                text.push_str(&format!("- {}\n", error.message));
            }
            text.push_str("\n");
        }

        if !result.warnings.is_empty() {
            text.push_str("Warnings:\n");
            for warning in &result.warnings {
                text.push_str(&format!("- {}\n", warning.message));
            }
        }

        text
    }

    /// 生成Markdown格式报告
    fn generate_markdown_report(&self, result: &ValidationResult) -> String {
        let mut md = String::new();
        md.push_str("# Validation Report\n\n");
        md.push_str(&format!("- **Valid**: {}\n", result.is_valid));
        md.push_str(&format!("- **Errors**: {}\n", result.errors.len()));
        md.push_str(&format!("- **Warnings**: {}\n\n", result.warnings.len()));

        if !result.errors.is_empty() {
            md.push_str("## Errors\n\n");
            for error in &result.errors {
                md.push_str(&format!("- {}\n", error.message));
            }
            md.push_str("\n");
        }

        if !result.warnings.is_empty() {
            md.push_str("## Warnings\n\n");
            for warning in &result.warnings {
                md.push_str(&format!("- {}\n", warning.message));
            }
        }

        md
    }

    /// 生成XML格式报告
    fn generate_xml_report(&self, result: &ValidationResult) -> String {
        let mut xml = String::new();
        xml.push_str("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        xml.push_str("<ValidationReport>\n");
        xml.push_str(&format!("  <Valid>{}</Valid>\n", result.is_valid));
        xml.push_str(&format!(
            "  <ErrorCount>{}</ErrorCount>\n",
            result.errors.len()
        ));
        xml.push_str(&format!(
            "  <WarningCount>{}</WarningCount>\n",
            result.warnings.len()
        ));

        if !result.errors.is_empty() {
            xml.push_str("  <Errors>\n");
            for error in &result.errors {
                xml.push_str(&format!("    <Error>{}</Error>\n", error.message));
            }
            xml.push_str("  </Errors>\n");
        }

        if !result.warnings.is_empty() {
            xml.push_str("  <Warnings>\n");
            for warning in &result.warnings {
                xml.push_str(&format!("    <Warning>{}</Warning>\n", warning.message));
            }
            xml.push_str("  </Warnings>\n");
        }

        xml.push_str("</ValidationReport>");
        xml
    }
}

impl Default for ValidationReporter {
    fn default() -> Self {
        Self::new()
    }
}
