/// 技能系统trait实现
/// 
/// 实现SkillSystem trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;
use crate::battle_unit::types::*;
use crate::battle_unit::traits::SkillSystem;
use crate::skill::domain::{skill_aggregate::*, value_objects::*};

impl SkillSystem for BattleUnitAdapter {
    fn learned_skills(&self) -> Vec<SkillId> {
        self.character.learned_skills()
    }
    
    fn can_learn_skill(&self, skill_id: SkillId) -> bool {
        // 检查技能学习条件
        if self.learned_skills().contains(&skill_id) {
            return false; // 已经学会
        }
        
        // TODO: 检查技能学习前置条件
        // - 等级要求
        // - 属性要求
        // - 前置技能要求
        // - 职业限制等
        
        true
    }
    
    fn learn_skill(&mut self, skill_id: SkillId) -> GameResult<()> {
        if !self.can_learn_skill(skill_id) {
            return Err("无法学习该技能".into());
        }
        
        // TODO: 实现Character的学习技能方法
        // self.character.learn_skill(skill_id)?;
        
        Ok(())
    }
    
    fn skill_level(&self, skill_id: SkillId) -> SkillLevel {
        // TODO: 从Character获取技能等级
        1
    }
    
    fn can_use_skill(&self, skill_id: SkillId) -> bool {
        // 检查是否已学会技能
        if !self.learned_skills().contains(&skill_id) {
            return false;
        }
        
        // 检查技能冷却
        if !self.is_skill_ready(skill_id) {
            return false;
        }
        
        // 检查法力消耗
        let mana_cost = self.skill_mana_cost(skill_id);
        if !self.has_enough_mana(mana_cost) {
            return false;
        }
        
        // 检查耐力消耗
        let stamina_cost = self.skill_stamina_cost(skill_id);
        if !self.has_enough_stamina(stamina_cost) {
            return false;
        }
        
        // 检查行动点数
        let action_cost = self.skill_action_cost(skill_id);
        if self.action_points() < action_cost {
            return false;
        }
        
        // 检查状态限制
        if self.has_skill_blocking_status() {
            return false;
        }
        
        true
    }
    
    fn use_skill(&mut self, skill_id: SkillId, target: Option<BattleUnitId>) -> GameResult<SkillResult> {
        if !self.can_use_skill(skill_id) {
            return Err("无法使用该技能".into());
        }
        
        // 消耗资源
        let mana_cost = self.skill_mana_cost(skill_id);
        let stamina_cost = self.skill_stamina_cost(skill_id);
        let action_cost = self.skill_action_cost(skill_id);
        
        self.consume_mana(mana_cost)?;
        self.consume_stamina(stamina_cost)?;
        self.consume_action_points(action_cost)?;
        
        // 设置技能冷却
        self.set_skill_cooldown(skill_id)?;
        
        // 执行技能效果
        let skill_result = self.execute_skill_effects(skill_id, target)?;
        
        // 触发技能相关事件
        self.trigger_skill_events(skill_id, &skill_result)?;
        
        Ok(skill_result)
    }
    
    fn skill_mana_cost(&self, skill_id: SkillId) -> Mana {
        // TODO: 从技能定义获取基础法力消耗
        let base_cost = self.get_skill_base_mana_cost(skill_id);
        
        // 应用法力消耗减免
        let reduction = self.mana_cost_reduction();
        (base_cost as f32 * (1.0 - reduction)) as Mana
    }
    
    fn skill_stamina_cost(&self, skill_id: SkillId) -> Stamina {
        // TODO: 从技能定义获取基础耐力消耗
        let base_cost = self.get_skill_base_stamina_cost(skill_id);
        
        // 应用耐力消耗减免
        let reduction = self.stamina_cost_reduction();
        (base_cost as f32 * (1.0 - reduction)) as Stamina
    }
    
    fn skill_action_cost(&self, skill_id: SkillId) -> ActionPoints {
        // TODO: 从技能定义获取基础行动点消耗
        self.get_skill_base_action_cost(skill_id)
    }
    
    fn skill_cooldown_remaining(&self, skill_id: SkillId) -> GameTime {
        // TODO: 从技能冷却管理器获取剩余冷却时间
        0
    }
    
    fn is_skill_ready(&self, skill_id: SkillId) -> bool {
        self.skill_cooldown_remaining(skill_id) == 0
    }
    
    fn skill_range(&self, skill_id: SkillId) -> SkillRange {
        // TODO: 从技能定义获取技能范围
        self.get_skill_base_range(skill_id)
    }
    
    fn skill_area_of_effect(&self, skill_id: SkillId) -> Option<AreaOfEffect> {
        // TODO: 从技能定义获取技能AOE
        None
    }
    
    fn get_available_skills(&self) -> Vec<SkillId> {
        self.learned_skills().into_iter()
            .filter(|&skill_id| self.can_use_skill(skill_id))
            .collect()
    }
    
    fn calculate_skill_damage(&self, skill_id: SkillId, target: &dyn BattleUnitAdapter) -> SkillDamageResult {
        // TODO: 实现复杂的技能伤害计算
        // 考虑技能等级、属性加成、目标抗性等
        
        let base_damage = self.get_skill_base_damage(skill_id);
        let attribute_scaling = self.calculate_skill_attribute_scaling(skill_id);
        let level_scaling = self.calculate_skill_level_scaling(skill_id);
        
        let total_damage = base_damage + attribute_scaling + level_scaling;
        
        // 计算目标抗性
        let damage_type = self.get_skill_damage_type(skill_id);
        let resistance = target.calculate_damage_reduction(damage_type);
        let final_damage = (total_damage as f32 * (1.0 - resistance)) as Damage;
        
        SkillDamageResult {
            base_damage,
            final_damage: final_damage.max(1),
            damage_type,
            critical_chance: self.calculate_skill_critical_chance(skill_id),
            additional_effects: self.get_skill_additional_effects(skill_id),
        }
    }
    
    fn upgrade_skill(&mut self, skill_id: SkillId) -> GameResult<()> {
        let current_level = self.skill_level(skill_id);
        
        // 检查是否可以升级
        if !self.can_upgrade_skill(skill_id) {
            return Err("无法升级该技能".into());
        }
        
        // TODO: 实现技能升级逻辑
        // 消耗经验、材料等
        
        Ok(())
    }
}

impl BattleUnitAdapter {
    fn has_skill_blocking_status(&self) -> bool {
        self.has_status(StatusType::Stun) ||
        self.has_status(StatusType::Sleep) ||
        self.has_status(StatusType::Paralysis)
    }
    
    fn set_skill_cooldown(&mut self, skill_id: SkillId) -> GameResult<()> {
        // TODO: 实现技能冷却设置
        Ok(())
    }
    
    fn execute_skill_effects(&mut self, skill_id: SkillId, target: Option<BattleUnitId>) -> GameResult<SkillResult> {
        // TODO: 实现技能效果执行
        // 这里需要与技能系统集成
        
        Ok(SkillResult {
            skill_id,
            caster_id: self.unit_id(),
            target_id: target,
            damage_dealt: 0,
            healing_done: 0,
            effects_applied: vec![],
            mana_consumed: self.skill_mana_cost(skill_id),
            stamina_consumed: self.skill_stamina_cost(skill_id),
            action_points_consumed: self.skill_action_cost(skill_id),
            execution_time: self.calculate_skill_execution_time(skill_id),
        })
    }
    
    fn trigger_skill_events(&mut self, skill_id: SkillId, result: &SkillResult) -> GameResult<()> {
        // TODO: 触发技能相关事件（经验获得、连击触发等）
        Ok(())
    }
    
    fn get_skill_base_mana_cost(&self, skill_id: SkillId) -> Mana {
        // TODO: 从技能定义获取基础法力消耗
        match skill_id.0 {
            1 => 10, // 基础攻击技能
            2 => 20, // 中级技能
            3 => 30, // 高级技能
            _ => 15,
        }
    }
    
    fn get_skill_base_stamina_cost(&self, skill_id: SkillId) -> Stamina {
        // TODO: 从技能定义获取基础耐力消耗
        match skill_id.0 {
            1 => 5,
            2 => 10,
            3 => 15,
            _ => 8,
        }
    }
    
    fn get_skill_base_action_cost(&self, skill_id: SkillId) -> ActionPoints {
        // TODO: 从技能定义获取基础行动点消耗
        match skill_id.0 {
            1 => 1, // 快速技能
            2 => 2, // 普通技能
            3 => 3, // 强力技能
            _ => 2,
        }
    }
    
    fn get_skill_base_range(&self, skill_id: SkillId) -> SkillRange {
        // TODO: 从技能定义获取技能范围
        match skill_id.0 {
            1 => 1, // 近战技能
            2 => 3, // 中距离技能
            3 => 5, // 远程技能
            _ => 2,
        }
    }
    
    fn get_skill_base_damage(&self, skill_id: SkillId) -> Damage {
        // TODO: 从技能定义获取基础伤害
        match skill_id.0 {
            1 => 20,
            2 => 35,
            3 => 50,
            _ => 25,
        }
    }
    
    fn calculate_skill_attribute_scaling(&self, skill_id: SkillId) -> Damage {
        // TODO: 基于技能类型和角色属性计算属性缩放
        let attack_power = self.attack_power();
        (attack_power * 0.5) as Damage // 50%攻击力缩放
    }
    
    fn calculate_skill_level_scaling(&self, skill_id: SkillId) -> Damage {
        let skill_level = self.skill_level(skill_id);
        skill_level as Damage * 5 // 每级+5伤害
    }
    
    fn get_skill_damage_type(&self, skill_id: SkillId) -> DamageType {
        // TODO: 从技能定义获取伤害类型
        DamageType::Physical
    }
    
    fn calculate_skill_critical_chance(&self, skill_id: SkillId) -> CriticalChance {
        // 技能基础暴击率 + 角色暴击率
        let base_skill_crit = 0.05; // 5%基础暴击
        let character_crit = self.critical_chance();
        base_skill_crit + character_crit
    }
    
    fn get_skill_additional_effects(&self, skill_id: SkillId) -> Vec<SkillEffect> {
        // TODO: 从技能定义获取附加效果
        vec![]
    }
    
    fn can_upgrade_skill(&self, skill_id: SkillId) -> bool {
        // TODO: 检查技能升级条件
        true
    }
    
    fn calculate_skill_execution_time(&self, skill_id: SkillId) -> GameTime {
        // TODO: 基于技能类型和角色属性计算执行时间
        100 // 基础执行时间
    }
}