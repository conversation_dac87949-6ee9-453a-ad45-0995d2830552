/// 行动能力trait
/// 
/// 定义单位可以执行的基本行动和行动点数管理

use crate::shared::*;
use crate::battle_unit::ActionPoints;

/// 行动能力 - 定义单位可以执行的基本行动
pub trait ActionCapabilities {
    /// 检查是否能移动
    fn can_move(&self) -> bool;
    
    /// 检查是否能攻击
    fn can_attack(&self) -> bool;
    
    /// 检查是否能施法
    fn can_cast_spells(&self) -> bool;
    
    /// 检查是否能使用物品
    fn can_use_items(&self) -> bool;
    
    /// 检查是否能防御
    fn can_defend(&self) -> bool;
    
    /// 检查是否能闪避
    fn can_dodge(&self) -> bool;
    
    /// 检查是否能格挡
    fn can_block(&self) -> bool;
    
    /// 检查是否能反击
    fn can_counter_attack(&self) -> bool;
    
    /// 获取行动点数
    fn action_points(&self) -> ActionPoints;
    
    /// 消耗行动点数
    fn consume_action_points(&mut self, amount: ActionPoints) -> GameResult<()>;
    
    /// 恢复行动点数
    fn restore_action_points(&mut self, amount: ActionPoints) -> GameResult<()>;
    
    /// 获取行动优先级
    fn action_priority(&self) -> Priority;
}