//! # 配置验证器
//!
//! 提供配置数据的Schema验证、业务规则验证和完整性检查

use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

use crate::config_engine::{
    config::{FieldDefinition, FieldType, MergedConfig, RuleDefinition, TypeDefinition},
    core::ContextValue,
    error::{ConfigEngineError, ValidationError},
    Result,
};

/// 配置验证器
///
/// 负责对配置进行全面的验证，包括格式验证、业务规则验证、完整性检查等
pub struct ConfigValidator {
    /// 预定义的Schema规则
    schema_rules: HashMap<String, SchemaRule>,
    /// 自定义验证器
    custom_validators: HashMap<String, Box<dyn CustomValidator>>,
}

impl ConfigValidator {
    /// 创建新的配置验证器
    pub fn new() -> Self {
        let mut validator = Self {
            schema_rules: HashMap::new(),
            custom_validators: HashMap::new(),
        };

        // 添加内置的Schema规则
        validator.add_builtin_schema_rules();

        validator
    }

    /// 添加内置Schema规则
    fn add_builtin_schema_rules(&mut self) {
        // 规则ID验证规则
        self.schema_rules.insert(
            "rule_id".to_string(),
            SchemaRule {
                name: "规则ID验证".to_string(),
                description: "验证规则ID格式".to_string(),
                pattern: Some("^[a-zA-Z][a-zA-Z0-9_-]*$".to_string()),
                min_length: Some(1),
                max_length: Some(64),
                allowed_values: None,
                required: true,
            },
        );

        // 类型ID验证规则
        self.schema_rules.insert(
            "type_id".to_string(),
            SchemaRule {
                name: "类型ID验证".to_string(),
                description: "验证类型ID格式".to_string(),
                pattern: Some("^[A-Z][a-zA-Z0-9]*$".to_string()),
                min_length: Some(1),
                max_length: Some(32),
                allowed_values: None,
                required: true,
            },
        );

        // 版本号验证规则
        self.schema_rules.insert(
            "version".to_string(),
            SchemaRule {
                name: "版本号验证".to_string(),
                description: "验证语义版本号格式".to_string(),
                pattern: Some(r"^\d+\.\d+\.\d+(-[a-zA-Z0-9-]+)?$".to_string()),
                min_length: None,
                max_length: None,
                allowed_values: None,
                required: true,
            },
        );

        // JDM内容验证规则
        self.schema_rules.insert(
            "jdm_content".to_string(),
            SchemaRule {
                name: "JDM内容验证".to_string(),
                description: "验证JDM格式".to_string(),
                pattern: None,
                min_length: Some(10),
                max_length: None,
                allowed_values: None,
                required: true,
            },
        );
    }

    /// 添加自定义验证器
    pub fn add_custom_validator<V>(&mut self, name: String, validator: V)
    where
        V: CustomValidator + 'static,
    {
        self.custom_validators.insert(name, Box::new(validator));
    }

    /// 验证完整配置
    pub fn validate_merged_config(&self, config: &MergedConfig) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();

        // 验证所有规则定义
        for (rule_id, rule) in &config.rules {
            match self.validate_rule_definition(rule) {
                Ok(rule_result) => result.merge(rule_result),
                Err(e) => result.add_error(format!("规则 {} 验证失败: {}", rule_id, e)),
            }
        }

        // 验证所有类型定义
        for (type_id, type_def) in &config.types {
            match self.validate_type_definition(type_def) {
                Ok(type_result) => result.merge(type_result),
                Err(e) => result.add_error(format!("类型 {} 验证失败: {}", type_id, e)),
            }
        }

        // 验证引用完整性
        self.validate_reference_integrity(config, &mut result)?;

        // 验证业务规则
        self.validate_business_rules(config, &mut result)?;

        Ok(result)
    }

    /// 验证规则定义
    pub fn validate_rule_definition(&self, rule: &RuleDefinition) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();

        // 验证规则ID
        self.validate_field_with_rule(
            "rule_id",
            &ContextValue::String(rule.id.clone()),
            &mut result,
        )?;

        // 验证规则名称
        if rule.name.is_empty() {
            result.add_error("规则名称不能为空".to_string());
        }

        // 验证版本号
        self.validate_field_with_rule(
            "version",
            &ContextValue::String(rule.version.clone()),
            &mut result,
        )?;

        // 验证JDM内容
        self.validate_jdm_content(&rule.jdm_content, &mut result)?;

        // 验证Schema
        if let Some(schema) = &rule.input_schema {
            self.validate_json_schema(schema, "input_schema", &mut result)?;
        }

        if let Some(schema) = &rule.output_schema {
            self.validate_json_schema(schema, "output_schema", &mut result)?;
        }

        // 应用自定义验证器
        for (validator_name, validator) in &self.custom_validators {
            match validator.validate_rule(rule) {
                Ok(custom_result) => result.merge(custom_result),
                Err(e) => {
                    result.add_warning(format!("自定义验证器 {} 失败: {}", validator_name, e))
                }
            }
        }

        Ok(result)
    }

    /// 验证类型定义
    pub fn validate_type_definition(&self, type_def: &TypeDefinition) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();

        // 验证类型ID
        self.validate_field_with_rule(
            "type_id",
            &ContextValue::String(type_def.id.clone()),
            &mut result,
        )?;

        // 验证类型名称
        if type_def.name.is_empty() {
            result.add_error("类型名称不能为空".to_string());
        }

        // 验证字段定义
        for (field_name, field_def) in &type_def.fields {
            self.validate_field_definition(&type_def.id, field_name, field_def, &mut result)?;
        }

        // 验证验证规则语法
        for validation_rule in &type_def.validation_rules {
            self.validate_validation_rule_syntax(validation_rule, &mut result)?;
        }

        // 检查循环引用
        self.check_circular_references(&type_def.id, &type_def.fields, &mut result)?;

        Ok(result)
    }

    /// 验证字段定义
    fn validate_field_definition(
        &self,
        type_id: &str,
        field_name: &str,
        field_def: &FieldDefinition,
        result: &mut ValidationResult,
    ) -> Result<()> {
        // 验证字段名格式
        if !Self::is_valid_field_name(field_name) {
            result.add_error(format!(
                "类型 {} 的字段名 {} 格式不正确",
                type_id, field_name
            ));
        }

        // 验证默认值类型匹配
        if let Some(default_value) = &field_def.default_value {
            if !self.is_value_compatible_with_type(default_value, &field_def.field_type)? {
                result.add_error(format!(
                    "类型 {} 的字段 {} 的默认值类型不匹配",
                    type_id, field_name
                ));
            }
        }

        // 验证字段类型
        self.validate_field_type(&field_def.field_type, result)?;

        // 验证字段验证规则
        for validation_rule in &field_def.validation_rules {
            self.validate_validation_rule_syntax(validation_rule, result)?;
        }

        Ok(())
    }

    /// 验证字段类型
    fn validate_field_type(
        &self,
        field_type: &FieldType,
        result: &mut ValidationResult,
    ) -> Result<()> {
        match field_type {
            FieldType::Array(element_type) => {
                self.validate_field_type(element_type, result)?;
            }
            FieldType::Enum(values) => {
                if values.is_empty() {
                    result.add_error("枚举类型不能为空".to_string());
                }

                // 检查重复值
                let mut seen = HashSet::new();
                for value in values {
                    if !seen.insert(value) {
                        result.add_error(format!("枚举值重复: {}", value));
                    }
                }
            }
            FieldType::Reference(ref_type) => {
                // 引用验证将在完整性检查中进行
                if ref_type.is_empty() {
                    result.add_error("引用类型名不能为空".to_string());
                }
            }
            _ => {} // 基础类型不需要额外验证
        }

        Ok(())
    }

    /// 验证字段值与规则的匹配
    fn validate_field_with_rule(
        &self,
        rule_name: &str,
        value: &ContextValue,
        result: &mut ValidationResult,
    ) -> Result<()> {
        if let Some(rule) = self.schema_rules.get(rule_name) {
            self.apply_schema_rule(rule, value, result)?;
        }
        Ok(())
    }

    /// 应用Schema规则
    fn apply_schema_rule(
        &self,
        rule: &SchemaRule,
        value: &ContextValue,
        result: &mut ValidationResult,
    ) -> Result<()> {
        // 检查是否必填
        if rule.required && matches!(value, ContextValue::Null) {
            result.add_error(format!("{} 是必填字段", rule.name));
            return Ok(());
        }

        if let ContextValue::String(s) = value {
            // 检查长度限制
            if let Some(min_len) = rule.min_length {
                if s.len() < min_len {
                    result.add_error(format!(
                        "{} 长度不能少于 {} 个字符，实际: {}",
                        rule.name,
                        min_len,
                        s.len()
                    ));
                }
            }

            if let Some(max_len) = rule.max_length {
                if s.len() > max_len {
                    result.add_error(format!(
                        "{} 长度不能超过 {} 个字符，实际: {}",
                        rule.name,
                        max_len,
                        s.len()
                    ));
                }
            }

            // 检查正则表达式
            if let Some(pattern) = &rule.pattern {
                let regex = regex::Regex::new(pattern).map_err(|e| {
                    ConfigEngineError::Validation(ValidationError::InvalidRule {
                        rule: rule.name.clone(),
                        reason: format!("正则表达式错误: {}", e),
                    })
                })?;

                if !regex.is_match(s) {
                    result.add_error(format!("{} 格式不正确，应匹配: {}", rule.name, pattern));
                }
            }

            // 检查允许值
            if let Some(allowed_values) = &rule.allowed_values {
                if !allowed_values.contains(s) {
                    result.add_error(format!(
                        "{} 值不在允许范围内: {:?}",
                        rule.name, allowed_values
                    ));
                }
            }
        }

        Ok(())
    }

    /// 验证JDM内容
    fn validate_jdm_content(&self, jdm_content: &str, result: &mut ValidationResult) -> Result<()> {
        // 首先检查是否为有效JSON
        let jdm_value: serde_json::Value = serde_json::from_str(jdm_content).map_err(|e| {
            result.add_error(format!("JDM内容不是有效JSON: {}", e));
            ConfigEngineError::Validation(ValidationError::InvalidFormat {
                details: format!("JDM JSON解析失败: {}", e),
            })
        })?;

        // 验证JDM格式要求
        self.validate_jdm_structure(&jdm_value, result)?;

        Ok(())
    }

    /// 验证JDM结构
    fn validate_jdm_structure(
        &self,
        jdm: &serde_json::Value,
        result: &mut ValidationResult,
    ) -> Result<()> {
        // 检查contentType
        if let Some(content_type) = jdm.get("contentType").and_then(|v| v.as_str()) {
            if content_type != "application/vnd.gorules.decision" {
                result.add_warning(format!("JDM contentType 不是标准格式: {}", content_type));
            }
        } else {
            result.add_warning("JDM缺少contentType字段".to_string());
        }

        // 检查基本结构
        let required_fields = ["nodes", "edges"];
        for field in &required_fields {
            if !jdm.get(field).is_some() {
                result.add_warning(format!("JDM缺少 {} 字段", field));
            }
        }

        // 验证nodes数组
        if let Some(nodes) = jdm.get("nodes").and_then(|v| v.as_array()) {
            for (i, node) in nodes.iter().enumerate() {
                self.validate_jdm_node(node, i, result)?;
            }
        }

        // 验证edges数组
        if let Some(edges) = jdm.get("edges").and_then(|v| v.as_array()) {
            for (i, edge) in edges.iter().enumerate() {
                self.validate_jdm_edge(edge, i, result)?;
            }
        }

        Ok(())
    }

    /// 验证JDM节点
    fn validate_jdm_node(
        &self,
        node: &serde_json::Value,
        index: usize,
        result: &mut ValidationResult,
    ) -> Result<()> {
        // 检查必需字段
        let required_fields = ["id", "type"];
        for field in &required_fields {
            if !node.get(field).is_some() {
                result.add_error(format!("JDM节点 {} 缺少 {} 字段", index, field));
            }
        }

        // 验证节点类型
        if let Some(node_type) = node.get("type").and_then(|v| v.as_str()) {
            let valid_types = [
                "inputNode",
                "outputNode",
                "decisionTableNode",
                "switchNode",
                "functionNode",
            ];
            if !valid_types.contains(&node_type) {
                result.add_warning(format!("JDM节点 {} 类型可能不正确: {}", index, node_type));
            }
        }

        Ok(())
    }

    /// 验证JDM边
    fn validate_jdm_edge(
        &self,
        edge: &serde_json::Value,
        index: usize,
        result: &mut ValidationResult,
    ) -> Result<()> {
        // 检查必需字段
        let required_fields = ["id", "sourceId", "targetId"];
        for field in &required_fields {
            if !edge.get(field).is_some() {
                result.add_error(format!("JDM边 {} 缺少 {} 字段", index, field));
            }
        }

        Ok(())
    }

    /// 验证JSON Schema
    fn validate_json_schema(
        &self,
        schema: &serde_json::Value,
        schema_name: &str,
        result: &mut ValidationResult,
    ) -> Result<()> {
        // 检查Schema基本结构
        if !schema.is_object() {
            result.add_error(format!("{} 必须是对象", schema_name));
            return Ok(());
        }

        // 检查$schema字段
        if let Some(schema_version) = schema.get("$schema").and_then(|v| v.as_str()) {
            if !schema_version.contains("json-schema.org") {
                result.add_warning(format!(
                    "{} 的$schema字段可能不正确: {}",
                    schema_name, schema_version
                ));
            }
        }

        Ok(())
    }

    /// 验证引用完整性
    fn validate_reference_integrity(
        &self,
        config: &MergedConfig,
        result: &mut ValidationResult,
    ) -> Result<()> {
        let available_types: HashSet<String> = config.types.keys().cloned().collect();

        // 检查类型定义中的引用
        for (type_id, type_def) in &config.types {
            for (field_name, field_def) in &type_def.fields {
                self.check_field_type_references(
                    type_id,
                    field_name,
                    &field_def.field_type,
                    &available_types,
                    result,
                )?;
            }
        }

        Ok(())
    }

    /// 检查字段类型引用
    fn check_field_type_references(
        &self,
        type_id: &str,
        field_name: &str,
        field_type: &FieldType,
        available_types: &HashSet<String>,
        result: &mut ValidationResult,
    ) -> Result<()> {
        match field_type {
            FieldType::Array(element_type) => {
                self.check_field_type_references(
                    type_id,
                    field_name,
                    element_type,
                    available_types,
                    result,
                )?;
            }
            FieldType::Reference(ref_type) => {
                if !available_types.contains(ref_type) {
                    result.add_error(format!(
                        "类型 {} 的字段 {} 引用了不存在的类型: {}",
                        type_id, field_name, ref_type
                    ));
                }
            }
            _ => {} // 其他类型不需要检查引用
        }

        Ok(())
    }

    /// 验证业务规则
    fn validate_business_rules(
        &self,
        config: &MergedConfig,
        result: &mut ValidationResult,
    ) -> Result<()> {
        // 检查规则命名冲突
        let mut rule_names = HashMap::new();
        for (rule_id, rule) in &config.rules {
            if let Some(existing_id) = rule_names.get(&rule.name) {
                result.add_warning(format!(
                    "规则名称冲突: {} 和 {} 都使用名称 '{}'",
                    existing_id, rule_id, rule.name
                ));
            } else {
                rule_names.insert(rule.name.clone(), rule_id.clone());
            }
        }

        // 检查类型命名冲突
        let mut type_names = HashMap::new();
        for (type_id, type_def) in &config.types {
            if let Some(existing_id) = type_names.get(&type_def.name) {
                result.add_warning(format!(
                    "类型名称冲突: {} 和 {} 都使用名称 '{}'",
                    existing_id, type_id, type_def.name
                ));
            } else {
                type_names.insert(type_def.name.clone(), type_id.clone());
            }
        }

        Ok(())
    }

    /// 检查循环引用
    fn check_circular_references(
        &self,
        type_id: &str,
        fields: &HashMap<String, FieldDefinition>,
        result: &mut ValidationResult,
    ) -> Result<()> {
        let mut visited = HashSet::new();
        let mut path = Vec::new();

        self.check_circular_references_recursive(type_id, fields, &mut visited, &mut path, result)?;

        Ok(())
    }

    /// 递归检查循环引用
    fn check_circular_references_recursive(
        &self,
        current_type: &str,
        fields: &HashMap<String, FieldDefinition>,
        visited: &mut HashSet<String>,
        path: &mut Vec<String>,
        result: &mut ValidationResult,
    ) -> Result<()> {
        if path.contains(&current_type.to_string()) {
            result.add_error(format!(
                "检测到循环引用: {} -> {}",
                path.join(" -> "),
                current_type
            ));
            return Ok(());
        }

        if visited.contains(current_type) {
            return Ok(());
        }

        visited.insert(current_type.to_string());
        path.push(current_type.to_string());

        for field_def in fields.values() {
            self.check_field_type_for_circular_reference(
                &field_def.field_type,
                fields,
                visited,
                path,
                result,
            )?;
        }

        path.pop();
        Ok(())
    }

    /// 检查字段类型的循环引用
    fn check_field_type_for_circular_reference(
        &self,
        field_type: &FieldType,
        fields: &HashMap<String, FieldDefinition>,
        visited: &mut HashSet<String>,
        path: &mut Vec<String>,
        result: &mut ValidationResult,
    ) -> Result<()> {
        match field_type {
            FieldType::Array(element_type) => {
                self.check_field_type_for_circular_reference(
                    element_type,
                    fields,
                    visited,
                    path,
                    result,
                )?;
            }
            FieldType::Reference(ref_type) => {
                self.check_circular_references_recursive(ref_type, fields, visited, path, result)?;
            }
            _ => {} // 其他类型不需要检查
        }

        Ok(())
    }

    /// 验证验证规则语法
    fn validate_validation_rule_syntax(
        &self,
        rule: &str,
        result: &mut ValidationResult,
    ) -> Result<()> {
        // 简单的验证规则语法检查
        // 支持的格式: field.property operator value
        // 例如: length > 0, rarity in ["Common", "Rare"]

        if rule.trim().is_empty() {
            result.add_error("验证规则不能为空".to_string());
            return Ok(());
        }

        // 检查是否包含基本操作符
        let operators = [
            "==", "!=", ">", "<", ">=", "<=", "in", "not in", "contains", "matches",
        ];
        let has_operator = operators.iter().any(|op| rule.contains(op));

        if !has_operator {
            result.add_warning(format!("验证规则可能缺少操作符: {}", rule));
        }

        Ok(())
    }

    /// 检查值是否与类型兼容
    fn is_value_compatible_with_type(
        &self,
        value: &ContextValue,
        field_type: &FieldType,
    ) -> Result<bool> {
        let compatible = match (value, field_type) {
            (ContextValue::String(_), FieldType::String) => true,
            (ContextValue::Int(_), FieldType::Integer) => true,
            (ContextValue::Float(_), FieldType::Float) => true,
            (ContextValue::Bool(_), FieldType::Boolean) => true,
            (ContextValue::Array(_), FieldType::Array(_)) => true,
            (ContextValue::Object(_), FieldType::Object) => true,
            (ContextValue::String(s), FieldType::Enum(values)) => values.contains(s),
            (ContextValue::Null, _) => true, // null值通常被允许
            _ => false,
        };

        Ok(compatible)
    }

    /// 检查字段名是否有效
    fn is_valid_field_name(name: &str) -> bool {
        // 字段名必须是有效的标识符
        let regex = regex::Regex::new(r"^[a-zA-Z_][a-zA-Z0-9_]*$").unwrap();
        regex.is_match(name)
    }
}

impl Default for ConfigValidator {
    fn default() -> Self {
        Self::new()
    }
}

/// Schema验证规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchemaRule {
    pub name: String,
    pub description: String,
    pub pattern: Option<String>,
    pub min_length: Option<usize>,
    pub max_length: Option<usize>,
    pub allowed_values: Option<Vec<String>>,
    pub required: bool,
}

/// 自定义验证器trait
pub trait CustomValidator: Send + Sync {
    /// 验证规则定义
    fn validate_rule(&self, rule: &RuleDefinition) -> Result<ValidationResult>;

    /// 验证类型定义
    fn validate_type(&self, type_def: &TypeDefinition) -> Result<ValidationResult> {
        // 默认实现不做任何验证
        Ok(ValidationResult::new())
    }

    /// 获取验证器名称
    fn name(&self) -> &str;
}

/// 验证结果
#[derive(Debug, Clone, Default)]
pub struct ValidationResult {
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub info: Vec<String>,
}

impl ValidationResult {
    pub fn new() -> Self {
        Self {
            errors: Vec::new(),
            warnings: Vec::new(),
            info: Vec::new(),
        }
    }

    pub fn add_error(&mut self, error: String) {
        self.errors.push(error);
    }

    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }

    pub fn add_info(&mut self, info: String) {
        self.info.push(info);
    }

    pub fn merge(&mut self, other: ValidationResult) {
        self.errors.extend(other.errors);
        self.warnings.extend(other.warnings);
        self.info.extend(other.info);
    }

    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }

    pub fn has_warnings(&self) -> bool {
        !self.warnings.is_empty()
    }

    pub fn is_valid(&self) -> bool {
        !self.has_errors()
    }

    pub fn error_count(&self) -> usize {
        self.errors.len()
    }

    pub fn warning_count(&self) -> usize {
        self.warnings.len()
    }

    pub fn total_issues(&self) -> usize {
        self.errors.len() + self.warnings.len()
    }
}

// 示例自定义验证器
pub struct GameRuleValidator;

impl CustomValidator for GameRuleValidator {
    fn validate_rule(&self, rule: &RuleDefinition) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();

        // 检查游戏特定的规则命名约定
        if !rule.id.starts_with("game_")
            && !rule.id.starts_with("material_")
            && !rule.id.starts_with("tool_")
        {
            result.add_warning(format!(
                "规则ID {} 建议使用游戏相关前缀（game_, material_, tool_）",
                rule.id
            ));
        }

        // 检查JDM内容是否包含游戏相关字段
        if let Ok(jdm) = serde_json::from_str::<serde_json::Value>(&rule.jdm_content) {
            let game_fields = [
                "terrain_type",
                "tool_type",
                "material_type",
                "resource_node",
            ];
            let has_game_field = game_fields
                .iter()
                .any(|field| Self::check_jdm_contains_field(&jdm, field));

            if !has_game_field {
                result.add_info(format!("规则 {} 可能不包含游戏特定字段", rule.id));
            }
        }

        Ok(result)
    }

    fn name(&self) -> &str {
        "GameRuleValidator"
    }
}

impl GameRuleValidator {
    fn check_jdm_contains_field(jdm: &serde_json::Value, field: &str) -> bool {
        // 递归检查JDM结构中是否包含指定字段
        match jdm {
            serde_json::Value::Object(obj) => {
                if obj.contains_key(field) {
                    return true;
                }
                for value in obj.values() {
                    if Self::check_jdm_contains_field(value, field) {
                        return true;
                    }
                }
                false
            }
            serde_json::Value::Array(arr) => arr
                .iter()
                .any(|item| Self::check_jdm_contains_field(item, field)),
            _ => false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config_engine::config::RuleDefinition;

    #[test]
    fn test_validator_creation() {
        let validator = ConfigValidator::new();
        assert!(!validator.schema_rules.is_empty());
        assert!(validator.schema_rules.contains_key("rule_id"));
        assert!(validator.schema_rules.contains_key("type_id"));
    }

    #[test]
    fn test_rule_validation_success() {
        let validator = ConfigValidator::new();
        let rule = RuleDefinition::new(
            "test_rule".to_string(),
            "Test Rule".to_string(),
            r#"{"contentType": "application/vnd.gorules.decision", "nodes": [], "edges": []}"#
                .to_string(),
        );

        let result = validator.validate_rule_definition(&rule).unwrap();
        assert!(result.is_valid());
    }

    #[test]
    fn test_rule_validation_failure() {
        let validator = ConfigValidator::new();
        let rule = RuleDefinition::new(
            "".to_string(),             // 无效ID
            "".to_string(),             // 无效名称
            "invalid json".to_string(), // 无效JDM
        );

        let result = validator.validate_rule_definition(&rule).unwrap();
        assert!(!result.is_valid());
        assert!(result.error_count() >= 2);
    }

    #[test]
    fn test_custom_validator() {
        let mut validator = ConfigValidator::new();
        validator.add_custom_validator("game".to_string(), GameRuleValidator);

        let rule = RuleDefinition::new(
            "custom_rule".to_string(),
            "Custom Rule".to_string(),
            r#"{"contentType": "application/vnd.gorules.decision"}"#.to_string(),
        );

        let result = validator.validate_rule_definition(&rule).unwrap();
        assert!(result.is_valid());
        assert!(result.has_warnings()); // 应该有命名约定警告
    }

    #[test]
    fn test_field_type_validation() {
        let validator = ConfigValidator::new();
        let mut result = ValidationResult::new();

        // 测试有效的字段类型
        validator
            .validate_field_type(&FieldType::String, &mut result)
            .unwrap();
        validator
            .validate_field_type(&FieldType::Array(Box::new(FieldType::Integer)), &mut result)
            .unwrap();

        assert!(result.is_valid());

        // 测试无效的字段类型
        validator
            .validate_field_type(&FieldType::Enum(vec![]), &mut result)
            .unwrap();
        assert!(!result.is_valid());
    }

    #[test]
    fn test_schema_rule_application() {
        let validator = ConfigValidator::new();
        let mut result = ValidationResult::new();

        // 测试有效的规则ID
        validator
            .validate_field_with_rule(
                "rule_id",
                &ContextValue::String("valid_rule_id".to_string()),
                &mut result,
            )
            .unwrap();
        assert!(result.is_valid());

        // 测试无效的规则ID
        let mut result2 = ValidationResult::new();
        validator
            .validate_field_with_rule(
                "rule_id",
                &ContextValue::String("123invalid".to_string()),
                &mut result2,
            )
            .unwrap();
        assert!(!result2.is_valid());
    }
}
