//! 怪物结构体定义，支持装备掉落与精英/BOSS判定
use crate::equipment::Equipment;
use crate::loot::{LootPool, LootPoolKind};
use crate::monster_profile::*;
use crate::shared::types::MonsterId;


#[derive(Debug, Clone, PartialEq, Eq)]
pub enum MonsterKind {
    Normal,
    Elite,
    Boss,
} 

pub struct Monster {
    pub id: MonsterId,
    pub profile: MonsterProfile,
    pub hp: f64,
    pub max_hp: f64,
    pub position: crate::world_map::Position,
    pub equipment: Vec<Equipment>,
    pub loot_pools: std::collections::HashMap<LootPoolKind, LootPool>,
    pub skill_bar: crate::skill::skill_bar::SkillBar,
    pub buffs: Vec<crate::skill::buff::Buff>,  // 添加Buff系统支持
}

impl Monster {
    /// 创建新的怪物实例
    pub fn new(id: MonsterId, profile: MonsterProfile, position: crate::world_map::Position) -> Self {
        let max_hp = profile.level as f64 * 20.0 + 50.0; // 基于等级计算血量
        let mut monster = Self {
            id,
            hp: max_hp,
            max_hp,
            position,
            equipment: Vec::new(),
            loot_pools: std::collections::HashMap::new(),
            skill_bar: crate::skill::skill_bar::SkillBar::default(),
            buffs: Vec::new(),  // 初始化空的buffs列表
            profile,
        };
        
        // 根据怪物类型和等级添加技能
        monster.initialize_skills();
        
        monster
    }
    
    /// 根据怪物类型和等级初始化技能
    fn initialize_skills(&mut self) {
        use crate::skill::{Skill, SkillType};
        
        let level = self.profile.level;
        
        // 根据怪物类型添加不同的技能
        match self.profile.kind {
            MonsterKind::Normal => {
                // 普通怪物：简单技能
                if level >= 3 {
                    // 3级以上有一个攻击技能
                    let skill = Skill {
                        id: (level * 2 + 1) as u32, // 奇数ID攻击技能
                        name: "野性撕咬".to_string(),
                        description: "普通怪物的攻击技能".to_string(),
                        skill_types: vec![SkillType::Active],
                        cooldown: 3.0,
                        mana_cost: level as i32,
                        range: 1.5,
                        area: None,
                        cast_condition: None,
                        priority: 1,
                        icon: None,
                    };
                    self.skill_bar.skills.push(skill);
                }
            }
            MonsterKind::Elite => {
                // 精英怪物：攻击技能 + 治疗技能
                // 攻击技能
                let attack_skill = Skill {
                    id: (level * 2 + 1) as u32, // 奇数ID攻击技能
                    name: "精英突击".to_string(),
                    description: "精英怪物的攻击技能".to_string(),
                    skill_types: vec![SkillType::Active],
                    cooldown: 2.0,
                    mana_cost: level as i32,
                    range: 2.0,
                    area: None,
                    cast_condition: None,
                    priority: 1,
                    icon: None,
                };
                self.skill_bar.skills.push(attack_skill);
                
                // 治疗技能
                if level >= 5 {
                    let heal_skill = Skill {
                        id: (level * 2) as u32, // 偶数ID治疗技能
                        name: "自我恢复".to_string(),
                        description: "精英怪物的自愈技能".to_string(),
                        skill_types: vec![SkillType::Active],
                        cooldown: 5.0,
                        mana_cost: level as i32 / 2,
                        range: 0.0, // 自我释放
                        area: None,
                        cast_condition: None,
                        priority: 2,
                        icon: None,
                    };
                    self.skill_bar.skills.push(heal_skill);
                }
            }
            MonsterKind::Boss => {
                // Boss怪物：多个强力技能
                // 强力攻击技能
                let power_attack = Skill {
                    id: (level * 2 + 1) as u32, // 奇数ID攻击技能
                    name: "Boss重击".to_string(),
                    description: "Boss的强力攻击技能".to_string(),
                    skill_types: vec![SkillType::Active],
                    cooldown: 2.5,
                    mana_cost: level as i32,
                    range: 2.5,
                    area: None,
                    cast_condition: None,
                    priority: 1,
                    icon: None,
                };
                self.skill_bar.skills.push(power_attack);
                
                // Boss治疗技能
                let boss_heal = Skill {
                    id: (level * 2) as u32, // 偶数ID治疗技能
                    name: "Boss再生".to_string(),
                    description: "Boss的强力自愈技能".to_string(),
                    skill_types: vec![SkillType::Active],
                    cooldown: 4.0,
                    mana_cost: level as i32 / 2,
                    range: 0.0,
                    area: None,
                    cast_condition: None,
                    priority: 2,
                    icon: None,
                };
                self.skill_bar.skills.push(boss_heal);
                
                // Boss特殊技能（高等级）
                if level >= 10 {
                    let special_skill = Skill {
                        id: (level * 2 + 3) as u32, // 另一个奇数攻击技能
                        name: "Boss怒火".to_string(),
                        description: "Boss的怒火技能".to_string(),
                        skill_types: vec![SkillType::Active],
                        cooldown: 6.0,
                        mana_cost: level as i32 + 5,
                        range: 3.0,
                        area: None,
                        cast_condition: None,
                        priority: 3,
                        icon: None,
                    };
                    self.skill_bar.skills.push(special_skill);
                }
            }
        }
    }
    
    /// 获取怪物的法力值（简化版本）
    pub fn get_mana(&self) -> i32 {
        // 根据等级给怪物一些法力值
        match self.profile.kind {
            MonsterKind::Normal => self.profile.level as i32 * 2 + 10,
            MonsterKind::Elite => self.profile.level as i32 * 3 + 20,
            MonsterKind::Boss => self.profile.level as i32 * 4 + 40,
        }
    }
    
    /// 设置怪物的法力值（简化版本 - 暂时不实际存储）
    pub fn set_mana(&mut self, _mana: i32) {
        // 简化：暂时不实际存储法力值变化
        // 在实际游戏中，可以添加一个mana字段来跟踪法力值
    }
}
