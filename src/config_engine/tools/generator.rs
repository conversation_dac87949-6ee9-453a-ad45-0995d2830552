//! # 配置生成器模块
//!
//! 自动生成配置文件和代码
//!
//! ## 核心功能
//!
//! - 配置文件生成
//! - 代码生成
//! - 模板处理
//! - 批量生成

use crate::config_engine::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 生成模板
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerationTemplate {
    pub name: String,
    pub description: String,
    pub template: String,
    pub variables: HashMap<String, String>,
}

impl GenerationTemplate {
    pub fn new(name: String, description: String, template: String) -> Self {
        Self {
            name,
            description,
            template,
            variables: HashMap::new(),
        }
    }

    pub fn add_variable(&mut self, key: String, value: String) {
        self.variables.insert(key, value);
    }

    pub fn render(&self, data: &serde_json::Value) -> Result<String> {
        let mut result = self.template.clone();

        // 替换变量
        for (key, value) in &self.variables {
            result = result.replace(&format!("{{{{{}}}}}", key), value);
        }

        // 替换数据
        result = result.replace("{{data}}", &serde_json::to_string_pretty(data)?);

        Ok(result)
    }
}

/// 配置生成器
pub struct ConfigGenerator {
    templates: HashMap<String, String>,
}

impl ConfigGenerator {
    pub fn new() -> Self {
        Self {
            templates: HashMap::new(),
        }
    }

    /// 生成配置文件
    pub fn generate_config(&self, template_name: &str, data: &serde_json::Value) -> Result<String> {
        if let Some(template) = self.templates.get(template_name) {
            Ok(self.render_template(template, data))
        } else {
            Ok(format!(
                "# Generated config\n{}",
                serde_json::to_string_pretty(data).unwrap_or_default()
            ))
        }
    }

    /// 渲染模板
    fn render_template(&self, template: &str, data: &serde_json::Value) -> String {
        // 简化的模板渲染
        template.replace(
            "{{data}}",
            &serde_json::to_string_pretty(data).unwrap_or_default(),
        )
    }

    /// 添加模板
    pub fn add_template(&mut self, name: String, template: String) {
        self.templates.insert(name, template);
    }
}

impl Default for ConfigGenerator {
    fn default() -> Self {
        Self::new()
    }
}
