//! # 调试工具模块

use crate::config_engine::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 调试接口
pub trait DebugInterface {
    fn start_session(&mut self, name: String) -> Result<()>;
    fn end_session(&mut self, name: &str) -> Result<DebugSession>;
    fn log_event(&mut self, session: &str, event: String) -> Result<()>;
    fn get_active_sessions(&self) -> Vec<String>;
}

/// 调试会话
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugSession {
    pub name: String,
    pub start_time: std::time::SystemTime,
    pub end_time: Option<std::time::SystemTime>,
    pub events: Vec<String>,
    pub metadata: HashMap<String, String>,
}

impl DebugSession {
    pub fn new(name: String) -> Self {
        Self {
            name,
            start_time: std::time::SystemTime::now(),
            end_time: None,
            events: Vec::new(),
            metadata: HashMap::new(),
        }
    }

    pub fn add_event(&mut self, event: String) {
        self.events.push(event);
    }

    pub fn end(&mut self) {
        self.end_time = Some(std::time::SystemTime::now());
    }

    pub fn add_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
    }
}

/// 调试器
pub struct Debugger {
    active_sessions: HashMap<String, DebugSession>,
}

impl Debugger {
    pub fn new() -> Self {
        Self {
            active_sessions: HashMap::new(),
        }
    }

    pub fn debug_info(&self, data: &serde_json::Value) -> String {
        format!(
            "Debug: {}",
            serde_json::to_string_pretty(data).unwrap_or_default()
        )
    }
}

impl DebugInterface for Debugger {
    fn start_session(&mut self, name: String) -> Result<()> {
        let session = DebugSession::new(name.clone());
        self.active_sessions.insert(name, session);
        Ok(())
    }

    fn end_session(&mut self, name: &str) -> Result<DebugSession> {
        if let Some(mut session) = self.active_sessions.remove(name) {
            session.end();
            Ok(session)
        } else {
            Err(
                crate::config_engine::error::ConfigEngineError::InvalidConfiguration(format!(
                    "Session '{}' not found",
                    name
                )),
            )
        }
    }

    fn log_event(&mut self, session: &str, event: String) -> Result<()> {
        if let Some(s) = self.active_sessions.get_mut(session) {
            s.add_event(event);
            Ok(())
        } else {
            Err(
                crate::config_engine::error::ConfigEngineError::InvalidConfiguration(format!(
                    "Session '{}' not found",
                    session
                )),
            )
        }
    }

    fn get_active_sessions(&self) -> Vec<String> {
        self.active_sessions.keys().cloned().collect()
    }
}

impl Default for Debugger {
    fn default() -> Self {
        Self::new()
    }
}
