//! # 配置管理器
//!
//! 统一的配置管理器，协调多个配置源，提供配置加载、验证、热重载等功能

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{mpsc, RwLock};
use tokio::time::interval;

use crate::config_engine::{
    config::{
        sources::create_config_source, ConfigSource, ConfigValidator, MergedConfig, RuleDefinition,
        TypeDefinition, ValidationResult,
    },
    error::{ConfigEngineError, ConfigurationError},
    ConfigSourceType, Result,
};

/// 配置管理器
///
/// 负责管理多个配置源，提供统一的配置访问接口
pub struct ConfigManager {
    /// 配置源列表（按优先级排序）
    sources: Vec<Box<dyn ConfigSource>>,
    /// 合并后的配置
    merged_config: Arc<RwLock<MergedConfig>>,
    /// 配置验证器
    validator: ConfigValidator,
    /// 是否启用热重载
    hot_reload_enabled: bool,
    /// 热重载检查间隔
    reload_interval: Duration,
    /// 配置变更通知发送器
    change_notifier: Option<mpsc::UnboundedSender<ConfigChangeEvent>>,
    /// 配置状态
    status: Arc<RwLock<ConfigManagerStatus>>,
}

impl ConfigManager {
    /// 创建新的配置管理器
    ///
    /// # 参数
    /// - `source_types`: 配置源类型列表
    /// - `hot_reload`: 是否启用热重载
    ///
    /// # 示例
    ///
    /// ```rust
    /// use game::config_engine::config::ConfigManager;
    /// use game::config_engine::ConfigSourceType;
    ///
    /// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let sources = vec![
    ///     ConfigSourceType::FileSystem { path: "./configs".to_string() },
    ///     ConfigSourceType::Environment { prefix: "GAME".to_string() },
    /// ];
    ///
    /// let manager = ConfigManager::new(sources, true).await?;
    /// # Ok(())
    /// # }
    /// ```
    pub async fn new(source_types: Vec<ConfigSourceType>, hot_reload: bool) -> Result<Self> {
        let mut sources = Vec::new();

        // 创建配置源实例
        for source_type in source_types {
            let source = create_config_source(source_type)?;
            sources.push(source);
        }

        // 按优先级排序（优先级高的在后面，会覆盖前面的配置）
        sources.sort_by_key(|source| source.priority());

        let merged_config = Arc::new(RwLock::new(MergedConfig::new()));
        let validator = ConfigValidator::new();
        let status = Arc::new(RwLock::new(ConfigManagerStatus::new()));

        Ok(Self {
            sources,
            merged_config,
            validator,
            hot_reload_enabled: hot_reload,
            reload_interval: Duration::from_secs(30), // 默认30秒检查一次
            change_notifier: None,
            status,
        })
    }

    /// 设置热重载检查间隔
    pub fn with_reload_interval(mut self, interval: Duration) -> Self {
        self.reload_interval = interval;
        self
    }

    /// 设置配置变更通知
    pub fn with_change_notifier(
        mut self,
        sender: mpsc::UnboundedSender<ConfigChangeEvent>,
    ) -> Self {
        self.change_notifier = Some(sender);
        self
    }

    /// 添加配置源
    pub fn add_source(&mut self, source: Box<dyn ConfigSource>) {
        self.sources.push(source);
        // 重新排序
        self.sources.sort_by_key(|source| source.priority());
    }

    /// 加载初始配置
    ///
    /// 从所有配置源加载配置并进行合并
    pub async fn load_initial_config(&self) -> Result<()> {
        log::info!("开始加载初始配置...");

        let mut status = self.status.write().await;
        status.set_loading(true);

        let result = self.load_and_merge_configs().await;

        match result {
            Ok(()) => {
                status.set_loaded(true);
                status.set_loading(false);
                log::info!("初始配置加载完成");

                // 启动热重载
                if self.hot_reload_enabled {
                    self.start_hot_reload().await;
                }
            }
            Err(e) => {
                status.set_loading(false);
                status.add_error(format!("初始配置加载失败: {}", e));
                log::error!("初始配置加载失败: {}", e);
                return Err(e);
            }
        }

        Ok(())
    }

    /// 从所有配置源加载并合并配置
    async fn load_and_merge_configs(&self) -> Result<()> {
        let mut new_config = MergedConfig::new();
        let mut load_errors = Vec::new();

        // 从每个配置源加载配置
        for source in &self.sources {
            match self
                .load_from_source(source.as_ref(), &mut new_config)
                .await
            {
                Ok(()) => {
                    log::info!("从配置源 {} 加载配置成功", source.name());
                }
                Err(e) => {
                    let error_msg = format!("从配置源 {} 加载配置失败: {}", source.name(), e);
                    log::error!("{}", error_msg);
                    load_errors.push(error_msg);
                }
            }
        }

        // 验证合并后的配置
        let validation_result = self.validator.validate_merged_config(&new_config)?;

        if !validation_result.is_valid() {
            let error_details = validation_result.errors.join("; ");
            return Err(ConfigEngineError::Configuration(
                ConfigurationError::ValidationFailure {
                    errors: validation_result.errors,
                },
            ));
        }

        // 记录验证警告
        if validation_result.has_warnings() {
            for warning in &validation_result.warnings {
                log::warn!("配置验证警告: {}", warning);
            }
        }

        // 更新配置
        {
            let mut merged_config = self.merged_config.write().await;
            *merged_config = new_config;
        }

        // 发送配置变更通知
        if let Some(notifier) = &self.change_notifier {
            let event = ConfigChangeEvent::ConfigReloaded {
                timestamp: chrono::Utc::now(),
                source_count: self.sources.len(),
                validation_result,
                load_errors,
            };

            if let Err(e) = notifier.send(event) {
                log::warn!("发送配置变更通知失败: {}", e);
            }
        }

        // 更新状态
        {
            let mut status = self.status.write().await;
            status.last_reload = Some(chrono::Utc::now());
            status.reload_count += 1;

            if !load_errors.is_empty() {
                for error in load_errors {
                    status.add_error(error);
                }
            }
        }

        Ok(())
    }

    /// 从单个配置源加载配置
    async fn load_from_source(
        &self,
        source: &dyn ConfigSource,
        merged_config: &mut MergedConfig,
    ) -> Result<()> {
        // 加载规则
        let rules = source.load_rules().await?;
        merged_config.merge_rules(source.name().to_string(), rules);

        // 加载类型定义
        let types = source.load_types().await?;
        merged_config.merge_types(source.name().to_string(), types);

        Ok(())
    }

    /// 启动热重载
    async fn start_hot_reload(&self) {
        if !self.hot_reload_enabled {
            return;
        }

        log::info!("启动配置热重载，检查间隔: {:?}", self.reload_interval);

        let sources = self
            .sources
            .iter()
            .map(|s| s.name().to_string())
            .collect::<Vec<_>>();
        let merged_config = Arc::clone(&self.merged_config);
        let validator = self.validator.clone();
        let change_notifier = self.change_notifier.clone();
        let status = Arc::clone(&self.status);
        let reload_interval = self.reload_interval;

        // 启动后台任务
        tokio::spawn(async move {
            let mut interval = interval(reload_interval);

            loop {
                interval.tick().await;

                // 检查是否需要重新加载
                // 这里简化实现，实际项目中需要检查每个配置源的更新
                log::debug!("检查配置更新...");

                // 这里可以实现具体的重载逻辑
                // 由于sources在这里不可用，这部分需要重构
                // 暂时作为占位符
            }
        });
    }

    /// 手动重新加载配置
    pub async fn reload_config(&self) -> Result<()> {
        log::info!("手动重新加载配置");
        self.load_and_merge_configs().await
    }

    /// 重新加载配置的别名方法
    pub async fn reload(&self) -> Result<()> {
        self.reload_config().await
    }

    /// 获取配置值（泛型版本）
    pub async fn get_config<T>(&self, key: &str) -> Option<T>
    where
        T: serde::de::DeserializeOwned,
    {
        // 这里简化实现，实际项目中需要根据具体配置格式处理
        // 临时返回 None，待具体实现
        None
    }

    /// 获取规则定义
    pub async fn get_rule_definition(&self, rule_id: &str) -> Option<RuleDefinition> {
        let config = self.merged_config.read().await;
        config.rules.get(rule_id).cloned()
    }

    /// 获取类型定义
    pub async fn get_type_definition(&self, type_id: &str) -> Option<TypeDefinition> {
        let config = self.merged_config.read().await;
        config.types.get(type_id).cloned()
    }

    /// 获取所有规则ID
    pub async fn get_all_rule_ids(&self) -> Vec<String> {
        let config = self.merged_config.read().await;
        config.rules.keys().cloned().collect()
    }

    /// 获取所有类型ID
    pub async fn get_all_type_ids(&self) -> Vec<String> {
        let config = self.merged_config.read().await;
        config.types.keys().cloned().collect()
    }

    /// 获取按标签过滤的规则
    pub async fn get_rules_by_tag(&self, tag_key: &str, tag_value: &str) -> Vec<RuleDefinition> {
        let config = self.merged_config.read().await;
        config
            .rules
            .values()
            .filter(|rule| {
                rule.tags
                    .get(tag_key)
                    .map(|value| value == tag_value)
                    .unwrap_or(false)
            })
            .cloned()
            .collect()
    }

    /// 获取按标签过滤的类型
    pub async fn get_types_by_tag(&self, tag_key: &str, tag_value: &str) -> Vec<TypeDefinition> {
        let config = self.merged_config.read().await;
        config
            .types
            .values()
            .filter(|type_def| {
                type_def
                    .tags
                    .get(tag_key)
                    .map(|value| value == tag_value)
                    .unwrap_or(false)
            })
            .cloned()
            .collect()
    }

    /// 搜索规则
    pub async fn search_rules(&self, query: &str) -> Vec<RuleDefinition> {
        let config = self.merged_config.read().await;
        let query_lower = query.to_lowercase();

        config
            .rules
            .values()
            .filter(|rule| {
                rule.id.to_lowercase().contains(&query_lower)
                    || rule.name.to_lowercase().contains(&query_lower)
                    || rule
                        .description
                        .as_ref()
                        .map(|desc| desc.to_lowercase().contains(&query_lower))
                        .unwrap_or(false)
            })
            .cloned()
            .collect()
    }

    /// 获取配置统计信息
    pub async fn get_config_stats(&self) -> ConfigStats {
        let config = self.merged_config.read().await;
        let status = self.status.read().await;

        ConfigStats {
            rule_count: config.rules.len(),
            type_count: config.types.len(),
            source_count: self.sources.len(),
            last_reload: status.last_reload,
            reload_count: status.reload_count,
            error_count: status.errors.len(),
            hot_reload_enabled: self.hot_reload_enabled,
            is_loaded: status.is_loaded,
        }
    }

    /// 获取配置管理器状态
    pub async fn get_status(&self) -> ConfigManagerStatus {
        let status = self.status.read().await;
        status.clone()
    }

    /// 获取配置源信息
    pub fn get_source_info(&self) -> Vec<crate::config_engine::config::sources::ConfigSourceInfo> {
        self.sources
            .iter()
            .map(|source| source.get_info())
            .collect()
    }

    /// 验证当前配置
    pub async fn validate_current_config(&self) -> Result<ValidationResult> {
        let config = self.merged_config.read().await;
        self.validator.validate_merged_config(&config)
    }

    /// 获取完整配置快照
    pub async fn get_config_snapshot(&self) -> MergedConfig {
        let config = self.merged_config.read().await;
        config.clone()
    }

    /// 检查配置源更新
    pub async fn check_source_updates(&self) -> Result<HashMap<String, bool>> {
        let mut updates = HashMap::new();

        for source in &self.sources {
            let has_update = source.check_for_updates().await?;
            updates.insert(source.name().to_string(), has_update);
        }

        Ok(updates)
    }

    /// 关闭配置管理器
    pub async fn shutdown(&self) {
        log::info!("配置管理器关闭");

        // 发送关闭通知
        if let Some(notifier) = &self.change_notifier {
            let event = ConfigChangeEvent::ManagerShutdown {
                timestamp: chrono::Utc::now(),
            };

            let _ = notifier.send(event);
        }
    }
}

/// 配置管理器状态
#[derive(Debug, Clone)]
pub struct ConfigManagerStatus {
    pub is_loaded: bool,
    pub is_loading: bool,
    pub last_reload: Option<chrono::DateTime<chrono::Utc>>,
    pub reload_count: u64,
    pub errors: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

impl ConfigManagerStatus {
    pub fn new() -> Self {
        Self {
            is_loaded: false,
            is_loading: false,
            last_reload: None,
            reload_count: 0,
            errors: Vec::new(),
            created_at: chrono::Utc::now(),
        }
    }

    pub fn set_loaded(&mut self, loaded: bool) {
        self.is_loaded = loaded;
    }

    pub fn set_loading(&mut self, loading: bool) {
        self.is_loading = loading;
    }

    pub fn add_error(&mut self, error: String) {
        self.errors.push(error);
    }

    pub fn clear_errors(&mut self) {
        self.errors.clear();
    }
}

impl Default for ConfigManagerStatus {
    fn default() -> Self {
        Self::new()
    }
}

/// 配置统计信息
#[derive(Debug, Clone)]
pub struct ConfigStats {
    pub rule_count: usize,
    pub type_count: usize,
    pub source_count: usize,
    pub last_reload: Option<chrono::DateTime<chrono::Utc>>,
    pub reload_count: u64,
    pub error_count: usize,
    pub hot_reload_enabled: bool,
    pub is_loaded: bool,
}

/// 配置变更事件
#[derive(Debug, Clone)]
pub enum ConfigChangeEvent {
    /// 配置已重新加载
    ConfigReloaded {
        timestamp: chrono::DateTime<chrono::Utc>,
        source_count: usize,
        validation_result: ValidationResult,
        load_errors: Vec<String>,
    },
    /// 配置源更新检测到
    SourceUpdated {
        timestamp: chrono::DateTime<chrono::Utc>,
        source_name: String,
    },
    /// 配置验证失败
    ValidationFailed {
        timestamp: chrono::DateTime<chrono::Utc>,
        errors: Vec<String>,
    },
    /// 管理器关闭
    ManagerShutdown {
        timestamp: chrono::DateTime<chrono::Utc>,
    },
}

/// 配置查询构建器
pub struct ConfigQuery {
    rule_tags: HashMap<String, String>,
    type_tags: HashMap<String, String>,
    enabled_only: bool,
    search_text: Option<String>,
}

impl ConfigQuery {
    pub fn new() -> Self {
        Self {
            rule_tags: HashMap::new(),
            type_tags: HashMap::new(),
            enabled_only: true,
            search_text: None,
        }
    }

    pub fn with_rule_tag(mut self, key: String, value: String) -> Self {
        self.rule_tags.insert(key, value);
        self
    }

    pub fn with_type_tag(mut self, key: String, value: String) -> Self {
        self.type_tags.insert(key, value);
        self
    }

    pub fn include_disabled(mut self) -> Self {
        self.enabled_only = false;
        self
    }

    pub fn with_search(mut self, text: String) -> Self {
        self.search_text = Some(text);
        self
    }

    /// 执行查询
    pub async fn execute(&self, manager: &ConfigManager) -> Result<ConfigQueryResult> {
        let config = manager.merged_config.read().await;

        // 过滤规则
        let mut rules = Vec::new();
        for rule in config.rules.values() {
            if self.enabled_only && !rule.enabled {
                continue;
            }

            // 检查标签匹配
            let mut tag_match = true;
            for (key, value) in &self.rule_tags {
                if rule.tags.get(key) != Some(value) {
                    tag_match = false;
                    break;
                }
            }

            if !tag_match {
                continue;
            }

            // 检查搜索文本
            if let Some(search) = &self.search_text {
                let search_lower = search.to_lowercase();
                let matches = rule.id.to_lowercase().contains(&search_lower)
                    || rule.name.to_lowercase().contains(&search_lower)
                    || rule
                        .description
                        .as_ref()
                        .map(|desc| desc.to_lowercase().contains(&search_lower))
                        .unwrap_or(false);

                if !matches {
                    continue;
                }
            }

            rules.push(rule.clone());
        }

        // 过滤类型
        let mut types = Vec::new();
        for type_def in config.types.values() {
            // 检查标签匹配
            let mut tag_match = true;
            for (key, value) in &self.type_tags {
                if type_def.tags.get(key) != Some(value) {
                    tag_match = false;
                    break;
                }
            }

            if !tag_match {
                continue;
            }

            // 检查搜索文本
            if let Some(search) = &self.search_text {
                let search_lower = search.to_lowercase();
                let matches = type_def.id.to_lowercase().contains(&search_lower)
                    || type_def.name.to_lowercase().contains(&search_lower)
                    || type_def
                        .description
                        .as_ref()
                        .map(|desc| desc.to_lowercase().contains(&search_lower))
                        .unwrap_or(false);

                if !matches {
                    continue;
                }
            }

            types.push(type_def.clone());
        }

        Ok(ConfigQueryResult { rules, types })
    }
}

impl Default for ConfigQuery {
    fn default() -> Self {
        Self::new()
    }
}

/// 配置查询结果
#[derive(Debug, Clone)]
pub struct ConfigQueryResult {
    pub rules: Vec<RuleDefinition>,
    pub types: Vec<TypeDefinition>,
}

impl ConfigQueryResult {
    pub fn rule_count(&self) -> usize {
        self.rules.len()
    }

    pub fn type_count(&self) -> usize {
        self.types.len()
    }

    pub fn is_empty(&self) -> bool {
        self.rules.is_empty() && self.types.is_empty()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config_engine::ConfigSourceType;
    use tempfile::TempDir;
    use tokio::fs;

    #[tokio::test]
    async fn test_config_manager_creation() {
        let sources = vec![ConfigSourceType::Environment {
            prefix: "TEST".to_string(),
        }];

        let manager = ConfigManager::new(sources, false).await.unwrap();
        assert_eq!(manager.sources.len(), 1);
        assert!(!manager.hot_reload_enabled);
    }

    #[tokio::test]
    async fn test_config_loading() {
        // 创建临时配置文件
        let temp_dir = TempDir::new().unwrap();
        let config_content = r#"{
            "rules": [
                {
                    "id": "test_rule",
                    "name": "Test Rule",
                    "jdm_content": "{\"contentType\": \"application/vnd.gorules.decision\"}"
                }
            ]
        }"#;

        let config_file = temp_dir.path().join("rules.json");
        fs::write(&config_file, config_content).await.unwrap();

        let sources = vec![ConfigSourceType::FileSystem {
            path: temp_dir.path().to_string_lossy().to_string(),
        }];

        let manager = ConfigManager::new(sources, false).await.unwrap();
        manager.load_initial_config().await.unwrap();

        let rule = manager.get_rule_definition("test_rule").await;
        assert!(rule.is_some());
        assert_eq!(rule.unwrap().name, "Test Rule");
    }

    #[tokio::test]
    async fn test_config_stats() {
        let sources = vec![ConfigSourceType::Environment {
            prefix: "TEST".to_string(),
        }];

        let manager = ConfigManager::new(sources, true).await.unwrap();
        let stats = manager.get_config_stats().await;

        assert_eq!(stats.source_count, 1);
        assert!(stats.hot_reload_enabled);
        assert!(!stats.is_loaded);
    }

    #[tokio::test]
    async fn test_config_query() {
        let sources = vec![ConfigSourceType::Environment {
            prefix: "TEST".to_string(),
        }];

        let manager = ConfigManager::new(sources, false).await.unwrap();

        let query = ConfigQuery::new()
            .with_rule_tag("category".to_string(), "test".to_string())
            .include_disabled();

        let result = query.execute(&manager).await.unwrap();
        assert!(result.is_empty()); // 没有加载配置，所以为空
    }
}
