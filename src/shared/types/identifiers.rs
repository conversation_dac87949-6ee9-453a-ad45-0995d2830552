/// 标识符类型定义
///
/// 此模块定义了游戏中使用的所有强类型ID，
/// 遵循强类型ID模式以增强类型安全性
use super::primitives::ID;
use serde::{Deserialize, Serialize};

// ============================================================================
// 强类型ID模式 - 增强类型安全性
// ============================================================================

/// 角色ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize, Default)]
pub struct CharacterId(pub ID);

/// 怪物ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize, Default)]
pub struct MonsterId(pub ID);

/// 技能ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize, Default)]
pub struct SkillId(pub ID);

/// BuffId
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize, Default)]
pub struct BuffId(pub ID);

/// 材料ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize, Default)]
pub struct MaterialId(pub ID);

/// 装备ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize, Default)]
pub struct EquipmentId(pub ID);

/// 战斗ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize, Default)]
pub struct BattleId(pub ID);

/// 战斗单位ID（通用）
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum BattleUnitId {
    Character(CharacterId),
    Monster(MonsterId),
}

impl Default for BattleUnitId {
    fn default() -> Self {
        BattleUnitId::Character(CharacterId(0))
    }
}

// ============================================================================
// ID类型的实用方法
// ============================================================================

impl From<u32> for SkillId {
    fn from(id: u32) -> Self {
        SkillId(id)
    }
}

impl From<SkillId> for u32 {
    fn from(skill_id: SkillId) -> Self {
        skill_id.0
    }
}

impl std::fmt::Display for SkillId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<u32> for CharacterId {
    fn from(id: u32) -> Self {
        CharacterId(id)
    }
}

impl From<CharacterId> for u32 {
    fn from(char_id: CharacterId) -> Self {
        char_id.0
    }
}

impl std::fmt::Display for CharacterId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<u32> for MonsterId {
    fn from(id: u32) -> Self {
        MonsterId(id)
    }
}

impl From<MonsterId> for u32 {
    fn from(monster_id: MonsterId) -> Self {
        monster_id.0
    }
}

impl std::fmt::Display for MonsterId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<u32> for BuffId {
    fn from(id: u32) -> Self {
        BuffId(id)
    }
}

impl From<BuffId> for u32 {
    fn from(buff_id: BuffId) -> Self {
        buff_id.0
    }
}

impl std::fmt::Display for BuffId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<u32> for MaterialId {
    fn from(id: u32) -> Self {
        MaterialId(id)
    }
}

impl From<MaterialId> for u32 {
    fn from(material_id: MaterialId) -> Self {
        material_id.0
    }
}

impl std::fmt::Display for MaterialId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<u32> for EquipmentId {
    fn from(id: u32) -> Self {
        EquipmentId(id)
    }
}

impl From<EquipmentId> for u32 {
    fn from(equipment_id: EquipmentId) -> Self {
        equipment_id.0
    }
}

impl std::fmt::Display for EquipmentId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<u32> for BattleId {
    fn from(id: u32) -> Self {
        BattleId(id)
    }
}

impl From<BattleId> for u32 {
    fn from(battle_id: BattleId) -> Self {
        battle_id.0
    }
}

impl std::fmt::Display for BattleId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}
