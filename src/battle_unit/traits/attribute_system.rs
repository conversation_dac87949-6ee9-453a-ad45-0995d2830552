//! # 属性系统 Trait
//!
//! 定义战斗单位的属性管理接口

use crate::attribute::{AttributeType, CoreAttribute, DerivedAttribute};
use crate::shared::GameResult;
use crate::DamageType;

/// 属性系统 Trait
///
/// 提供战斗单位属性管理的统一接口
pub trait AttributeSystem {
    /// 获取核心属性值
    fn get_core_attribute(&self, attr_type: AttributeType) -> i32;

    /// 设置核心属性值
    fn set_core_attribute(&mut self, attr_type: AttributeType, value: i32) -> GameResult<()>;

    /// 获取核心属性对象
    fn get_core_attribute_object(&self, attr_type: AttributeType) -> Option<&CoreAttribute>;

    /// 获取所有核心属性
    fn get_all_core_attributes(&self) -> Vec<(AttributeType, i32)>;

    /// 获取衍生属性值
    fn get_derived_attribute(&self, attr_type: AttributeType) -> i32;

    /// 获取衍生属性对象
    fn get_derived_attribute_object(&self, attr_type: AttributeType) -> Option<&DerivedAttribute>;

    /// 获取所有衍生属性
    fn get_all_derived_attributes(&self) -> Vec<(AttributeType, i32)>;

    /// 计算属性总值（基础值 + 装备加成 + 状态加成）
    fn calculate_total_attribute(&self, attr_type: AttributeType) -> i32;

    /// 获取属性加成
    fn get_attribute_bonus(&self, attr_type: AttributeType) -> i32;

    /// 添加属性加成
    fn add_attribute_bonus(&mut self, attr_type: AttributeType, bonus: i32) -> GameResult<()>;

    /// 移除属性加成
    fn remove_attribute_bonus(&mut self, attr_type: AttributeType, bonus: i32) -> GameResult<()>;

    /// 获取属性修正器
    fn get_attribute_modifier(&self, attr_type: AttributeType) -> f32;

    /// 检查属性是否满足要求
    fn meets_attribute_requirement(&self, attr_type: AttributeType, required_value: i32) -> bool;

    /// 升级属性
    fn upgrade_attribute(&mut self, attr_type: AttributeType, points: i32) -> GameResult<()>;

    /// 重置属性
    fn reset_attributes(&mut self) -> GameResult<()>;

    /// 获取可分配的属性点
    fn get_available_attribute_points(&self) -> i32;

    /// 设置可分配的属性点
    fn set_available_attribute_points(&mut self, points: i32);

    /// 获取属性上限
    fn get_attribute_cap(&self, attr_type: AttributeType) -> Option<i32>;

    /// 设置属性上限
    fn set_attribute_cap(&mut self, attr_type: AttributeType, cap: i32);

    /// 刷新所有衍生属性
    fn refresh_derived_attributes(&mut self);

    /// 验证属性配置的有效性
    fn validate_attributes(&self) -> GameResult<()>;
}

/// 属性系统事件 Trait
///
/// 处理属性变化时的事件回调
pub trait AttributeSystemEvents {
    /// 属性值改变时触发
    fn on_attribute_changed(&mut self, attr_type: AttributeType, old_value: i32, new_value: i32);

    /// 属性升级时触发
    fn on_attribute_upgraded(&mut self, attr_type: AttributeType, points_spent: i32);

    /// 属性达到上限时触发
    fn on_attribute_capped(&mut self, attr_type: AttributeType);

    /// 属性重置时触发
    fn on_attributes_reset(&mut self);
}

/// 属性计算器 Trait
///
/// 提供复杂的属性计算功能
pub trait AttributeCalculator {
    /// 计算攻击力
    fn calculate_attack_power(&self) -> i32;

    /// 计算防御力
    fn calculate_defense(&self) -> i32;

    /// 计算魔法攻击力
    fn calculate_magic_power(&self) -> i32;

    /// 计算魔法抗性
    fn calculate_magic_resistance(&self) -> i32;

    /// 计算命中率
    fn calculate_accuracy(&self) -> f32;

    /// 计算闪避率
    fn calculate_dodge_rate(&self) -> f32;

    /// 计算暴击率
    fn calculate_critical_rate(&self) -> f32;

    /// 计算暴击伤害倍数
    fn calculate_critical_multiplier(&self) -> f32;

    /// 计算移动速度
    fn calculate_movement_speed(&self) -> f32;

    /// 计算攻击速度
    fn calculate_attack_speed(&self) -> f32;

    /// 计算施法速度
    fn calculate_casting_speed(&self) -> f32;

    /// 计算负重能力
    fn calculate_carry_capacity(&self) -> i32;

    /// 计算属性对伤害的加成
    fn calculate_damage_bonus(&self, damage_type: DamageType) -> f32;

    /// 计算属性对防御的加成
    fn calculate_defense_bonus(&self, damage_type: DamageType) -> f32;
}
