use crate::attribute::AttributeType;
use crate::shared::{GameError, GameResult};
use crate::{DamageType, Exp, Level, Position, SkillId};
use serde::{Deserialize, Serialize};
/// 技能系统值对象
///
/// 包含技能系统中的所有值对象：
/// - 技能效果和效果组合
/// - 冷却状态管理
/// - 技能等级和升级
/// - 目标定位信息
/// - 资源消耗
/// - 学习要求
use std::collections::HashMap;

/// 技能效果
///
/// 重新设计的技能效果系统，支持组合和参数化
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SkillEffect {
    /// 效果唯一标识
    pub id: String,
    /// 效果名称
    pub name: String,
    /// 效果类型
    pub effect_type: EffectType,
    /// 基础数值
    pub base_value: f64,
    /// 等级成长系数
    pub scaling_factor: f64,
    /// 持续时间（对于持续效果）
    pub duration: Option<f64>,
    /// 触发条件
    pub trigger: EffectTrigger,
    /// 效果参数
    pub parameters: HashMap<String, EffectParameter>,
}

impl SkillEffect {
    pub fn new(id: String, name: String, effect_type: EffectType, base_value: f64) -> Self {
        Self {
            id,
            name,
            effect_type,
            base_value,
            scaling_factor: 1.0,
            duration: None,
            trigger: EffectTrigger::OnCast,
            parameters: HashMap::new(),
        }
    }

    pub fn id(&self) -> &str {
        &self.id
    }

    pub fn name(&self) -> &str {
        &self.name
    }

    pub fn effect_type(&self) -> &EffectType {
        &self.effect_type
    }

    /// 计算指定等级的效果数值
    pub fn calculate_value(&self, level: Level) -> f64 {
        self.base_value + (level - 1) as f64 * self.scaling_factor
    }

    pub fn with_scaling(mut self, scaling_factor: f64) -> Self {
        self.scaling_factor = scaling_factor;
        self
    }

    pub fn with_duration(mut self, duration: f64) -> Self {
        self.duration = Some(duration);
        self
    }

    pub fn with_trigger(mut self, trigger: EffectTrigger) -> Self {
        self.trigger = trigger;
        self
    }

    pub fn with_parameter(mut self, key: String, value: EffectParameter) -> Self {
        self.parameters.insert(key, value);
        self
    }

    pub fn get_parameter(&self, key: &str) -> Option<&EffectParameter> {
        self.parameters.get(key)
    }

    pub fn duration(&self) -> Option<f64> {
        self.duration
    }

    pub fn trigger(&self) -> &EffectTrigger {
        &self.trigger
    }

    /// 验证效果配置
    pub fn validate(&self) -> GameResult<()> {
        if self.id.trim().is_empty() {
            return Err(GameError::validation_error("effect_id", "效果ID不能为空"));
        }

        if self.name.trim().is_empty() {
            return Err(GameError::validation_error(
                "effect_name",
                "效果名称不能为空",
            ));
        }

        // 验证效果类型特定的规则
        match &self.effect_type {
            EffectType::Damage {
                damage_type: _,
                element: _,
            } => {
                if self.base_value < 0.0 {
                    return Err(GameError::validation_error(
                        "damage_value",
                        "伤害值不能为负",
                    ));
                }
            }
            EffectType::Heal => {
                if self.base_value < 0.0 {
                    return Err(GameError::validation_error("heal_value", "治疗值不能为负"));
                }
            }
            EffectType::StatusEffect { status_type: _ } => {
                if self.duration.is_none() {
                    return Err(GameError::validation_error(
                        "status_duration",
                        "状态效果必须有持续时间",
                    ));
                }
            }
            _ => {} // 其他效果类型的验证可以在这里添加
        }

        Ok(())
    }
}
impl Default for SkillEffect {
    fn default() -> Self {
        Self {
            id: String::new(),
            name: String::new(),
            effect_type: EffectType::Heal,
            base_value: 0.0,
            scaling_factor: 1.0,
            duration: None,
            trigger: EffectTrigger::OnCast,
            parameters: HashMap::new(),
        }
    }
}

/// 效果类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum EffectType {
    /// 伤害效果
    Damage {
        damage_type: DamageType,
        element: Option<ElementType>,
    },
    /// 治疗效果
    Heal,
    /// 状态效果
    StatusEffect { status_type: StatusType },
    /// 资源修改
    ResourceModification { resource_type: ResourceType },
    /// 属性增强
    AttributeBoost { attribute: AttributeType },
    /// 移动效果
    Movement { movement_type: MovementType },
    /// 召唤效果
    Summon { unit_type: String, count: u32 },
    /// 护盾效果
    Shield { shield_type: ShieldType },
    /// 反射效果
    Reflection { reflection_type: ReflectionType },
    /// 复制效果
    Copy { copy_type: CopyType },
    /// 自定义效果
    Custom { effect_name: String },
}

/// 状态类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum StatusType {
    Stun,
    Silence,
    Root,
    Slow,
    Haste,
    Invisible,
    Invulnerable,
    Custom(String),
}

/// 资源类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ResourceType {
    Health,
    Mana,
    Stamina,
    Custom(String),
}

/// 移动类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum MovementType {
    Dash,
    Teleport,
    Pull,
    Push,
    Knockback,
    Knockup,
}

/// 护盾类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ShieldType {
    Absorption,
    Reflection,
    Temporal,
}

/// 反射类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ReflectionType {
    Damage,
    Spell,
    Projectile,
}

/// 复制类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CopyType {
    Skill,
    Buff,
    Stats,
}

/// 效果触发器
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum EffectTrigger {
    /// 施法时触发
    OnCast,
    /// 命中时触发
    OnHit,
    /// 受到伤害时触发
    OnTakeDamage,
    /// 杀死敌人时触发
    OnKill,
    /// 回合开始时触发
    OnTurnStart,
    /// 回合结束时触发
    OnTurnEnd,
    /// 技能冷却完成时触发
    OnCooldownComplete,
    /// 自定义条件触发
    OnCondition(String),
}

/// 效果参数 - 支持多种数据类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum EffectParameter {
    Integer(i32),
    Float(f64),
    String(String),
    Boolean(bool),
    Position(Position),
    Array(Vec<EffectParameter>),
}

/// 技能效果组合
///
/// 管理一个技能的所有效果，支持复合效果和效果间的相互作用
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SkillEffectComposition {
    /// 主要效果（总是先执行）
    pub primary_effects: Vec<SkillEffect>,
    /// 次要效果（在主要效果之后执行）
    pub secondary_effects: Vec<SkillEffect>,
    /// 条件效果（只在特定条件下执行）
    pub conditional_effects: Vec<ConditionalEffect>,
    /// 效果之间的相互作用规则
    pub interaction_rules: Vec<EffectInteraction>,
}

impl SkillEffectComposition {
    pub fn new() -> Self {
        Self {
            primary_effects: Vec::new(),
            secondary_effects: Vec::new(),
            conditional_effects: Vec::new(),
            interaction_rules: Vec::new(),
        }
    }

    /// 添加主要效果
    pub fn add_primary_effect(&mut self, effect: SkillEffect) -> GameResult<()> {
        effect.validate()?;
        self.primary_effects.push(effect);
        Ok(())
    }

    /// 添加次要效果
    pub fn add_secondary_effect(&mut self, effect: SkillEffect) -> GameResult<()> {
        effect.validate()?;
        self.secondary_effects.push(effect);
        Ok(())
    }

    /// 添加条件效果
    pub fn add_conditional_effect(
        &mut self,
        conditional_effect: ConditionalEffect,
    ) -> GameResult<()> {
        conditional_effect.effect.validate()?;
        self.conditional_effects.push(conditional_effect);
        Ok(())
    }

    /// 通用添加效果方法
    pub fn add_effect(&mut self, effect: SkillEffect) -> GameResult<()> {
        self.add_primary_effect(effect)
    }

    /// 移除效果
    pub fn remove_effect(&mut self, effect_id: &str) -> GameResult<bool> {
        // 从主要效果中移除
        if let Some(pos) = self.primary_effects.iter().position(|e| e.id == effect_id) {
            self.primary_effects.remove(pos);
            return Ok(true);
        }

        // 从次要效果中移除
        if let Some(pos) = self
            .secondary_effects
            .iter()
            .position(|e| e.id == effect_id)
        {
            self.secondary_effects.remove(pos);
            return Ok(true);
        }

        // 从条件效果中移除
        if let Some(pos) = self
            .conditional_effects
            .iter()
            .position(|ce| ce.effect.id == effect_id)
        {
            self.conditional_effects.remove(pos);
            return Ok(true);
        }

        Ok(false)
    }

    /// 获取指定效果的威力
    pub fn get_effect_power(&self, effect_id: &str, level: Level) -> f64 {
        // 搜索所有效果
        for effect in &self.primary_effects {
            if effect.id == effect_id {
                return effect.calculate_value(level);
            }
        }

        for effect in &self.secondary_effects {
            if effect.id == effect_id {
                return effect.calculate_value(level);
            }
        }

        for conditional_effect in &self.conditional_effects {
            if conditional_effect.effect.id == effect_id {
                return conditional_effect.effect.calculate_value(level);
            }
        }

        0.0 // 未找到效果
    }

    /// 获取所有效果
    pub fn get_all_effects(&self) -> Vec<&SkillEffect> {
        let mut effects = Vec::new();
        effects.extend(&self.primary_effects);
        effects.extend(&self.secondary_effects);
        effects.extend(self.conditional_effects.iter().map(|ce| &ce.effect));
        effects
    }

    /// 获取效果数量
    pub fn effect_count(&self) -> usize {
        self.primary_effects.len() + self.secondary_effects.len() + self.conditional_effects.len()
    }

    /// 验证效果组合
    pub fn validate(&self) -> GameResult<()> {
        // 验证所有效果
        for effect in &self.primary_effects {
            effect.validate()?;
        }

        for effect in &self.secondary_effects {
            effect.validate()?;
        }

        for conditional_effect in &self.conditional_effects {
            conditional_effect.effect.validate()?;
        }

        // 检查是否有重复的效果ID
        let mut effect_ids = std::collections::HashSet::new();
        for effect in self.get_all_effects() {
            if !effect_ids.insert(effect.id.clone()) {
                return Err(GameError::validation_error(
                    "duplicate_effect",
                    &format!("重复的效果ID: {}", effect.id),
                ));
            }
        }

        Ok(())
    }
}

impl Default for SkillEffectComposition {
    fn default() -> Self {
        Self::new()
    }
}

/// 条件效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ConditionalEffect {
    /// 效果
    pub effect: SkillEffect,
    /// 触发条件
    pub condition: EffectCondition,
    /// 触发概率 (0.0 - 1.0)
    pub probability: f64,
}

/// 效果条件
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum EffectCondition {
    /// 总是触发
    Always,
    /// 暴击时触发
    OnCriticalHit,
    /// 目标生命值低于指定百分比
    TargetHealthBelow(f64),
    /// 施法者生命值低于指定百分比
    CasterHealthBelow(f64),
    /// 目标具有指定状态
    TargetHasStatus(String),
    /// 连击数达到指定值
    ComboCountAbove(u32),
    /// 自定义条件
    Custom(String),
}

/// 效果相互作用
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct EffectInteraction {
    /// 第一个效果ID
    pub effect1_id: String,
    /// 第二个效果ID
    pub effect2_id: String,
    /// 相互作用类型
    pub interaction_type: InteractionType,
    /// 相互作用强度
    pub strength: f64,
}

/// 相互作用类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum InteractionType {
    /// 增强（第二个效果增强第一个效果）
    Amplify,
    /// 抑制（第二个效果减弱第一个效果）
    Suppress,
    /// 取消（两个效果相互抵消）
    Cancel,
    /// 融合（两个效果合并为一个更强的效果）
    Merge,
    /// 连锁（第一个效果触发第二个效果）
    Chain,
}

// ============================================================================
// 技能等级值对象
// ============================================================================

/// 技能等级
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SkillLevel {
    /// 当前等级
    current: Level,
    /// 当前经验值
    experience: Exp,
    /// 升级所需经验值
    experience_per_level: Exp,
}

impl SkillLevel {
    pub fn new(initial_level: Level) -> Self {
        Self {
            current: initial_level,
            experience: 0,
            experience_per_level: 100, // 每级需要100经验
        }
    }

    pub fn current(&self) -> Level {
        self.current
    }

    pub fn experience(&self) -> Exp {
        self.experience
    }

    /// 升级
    pub fn level_up(&mut self) -> GameResult<()> {
        let required_exp = self.experience_to_next_level();
        if self.experience < required_exp {
            return Err(GameError::validation_error(
                "insufficient_experience",
                "经验值不足，无法升级",
            ));
        }

        self.experience -= required_exp;
        self.current += 1;
        Ok(())
    }

    /// 增加经验值
    pub fn gain_experience(&mut self, amount: Exp) -> GameResult<bool> {
        if amount == 0 {
            return Ok(false);
        }

        self.experience += amount;

        // 循环处理升级，直到经验值不再满足升级条件
        let mut leveled_up = false;
        while self.experience >= self.experience_to_next_level() {
            if self.level_up().is_ok() {
                leveled_up = true;
            } else {
                // 如果升级失败（例如已达满级），则停止
                break;
            }
        }
        Ok(leveled_up)
    }

    /// 计算升级所需经验
    pub fn experience_to_next_level(&self) -> Exp {
        // 每级所需经验递增（简单的线性增长）
        self.experience_per_level
            .saturating_mul(self.current as Exp)
    }

    /// 获取经验值进度百分比
    pub fn experience_progress(&self) -> f64 {
        let required = self.experience_to_next_level();
        if required == 0 {
            1.0
        } else {
            (self.experience as f64) / (required as f64)
        }
    }
}

// ============================================================================
// 目标定位值对象
// ============================================================================

/// 目标定位信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TargetingInfo {
    /// 目标类型
    target_type: TargetType,
    /// 作用范围
    range: f32,
    /// 作用区域形状
    area_shape: AreaShape,
    /// 是否可以穿过障碍物
    line_of_sight_required: bool,
}

impl TargetingInfo {
    pub fn new(target_type: TargetType, range: f32) -> Self {
        Self {
            target_type,
            range,
            area_shape: AreaShape::Point,
            line_of_sight_required: true,
        }
    }

    pub fn target_type(&self) -> &TargetType {
        &self.target_type
    }

    pub fn range(&self) -> f32 {
        self.range
    }

    pub fn area_shape(&self) -> &AreaShape {
        &self.area_shape
    }

    pub fn requires_line_of_sight(&self) -> bool {
        self.line_of_sight_required
    }

    pub fn requires_target(&self) -> bool {
        !matches!(self.target_type, TargetType::Self_ | TargetType::Area)
    }

    /// 获取技能影响的位置列表
    pub fn get_affected_positions(
        &self,
        caster_pos: Position,
        target_pos: Position,
    ) -> Vec<Position> {
        match &self.area_shape {
            AreaShape::Point => vec![target_pos],
            AreaShape::Circle { radius } => self.get_circle_positions(target_pos, *radius),
            AreaShape::Rectangle { width, height } => {
                self.get_rectangle_positions(caster_pos, target_pos, *width, *height)
            }
            AreaShape::Line { length } => self.get_line_positions(caster_pos, target_pos, *length),
            AreaShape::Cone { angle, length } => {
                self.get_cone_positions(caster_pos, target_pos, *angle, *length)
            }
        }
    }

    /// 验证目标定位配置
    pub fn validate(&self) -> GameResult<()> {
        if self.range < 0.0 {
            return Err(GameError::validation_error("range", "作用范围不能为负数"));
        }

        // 验证区域形状
        match &self.area_shape {
            AreaShape::Circle { radius } => {
                if *radius < 0.0 {
                    return Err(GameError::validation_error(
                        "circle_radius",
                        "圆形半径不能为负数",
                    ));
                }
            }
            AreaShape::Rectangle { width, height } => {
                if *width < 0.0 || *height < 0.0 {
                    return Err(GameError::validation_error(
                        "rectangle_size",
                        "矩形尺寸不能为负数",
                    ));
                }
            }
            AreaShape::Line { length } => {
                if *length < 0.0 {
                    return Err(GameError::validation_error(
                        "line_length",
                        "直线长度不能为负数",
                    ));
                }
            }
            AreaShape::Cone { angle, length } => {
                if *angle < 0.0 || *angle > 360.0 {
                    return Err(GameError::validation_error(
                        "cone_angle",
                        "锥形角度必须在0-360度之间",
                    ));
                }
                if *length < 0.0 {
                    return Err(GameError::validation_error(
                        "cone_length",
                        "锥形长度不能为负数",
                    ));
                }
            }
            _ => {} // 其他形状无需额外验证
        }

        Ok(())
    }

    // 辅助方法：计算各种形状的影响位置
    fn get_circle_positions(&self, center: Position, radius: f32) -> Vec<Position> {
        let mut positions = Vec::new();
        let radius_i = radius as i32;

        for dx in -radius_i..=radius_i {
            for dy in -radius_i..=radius_i {
                let distance = ((dx * dx + dy * dy) as f32).sqrt();
                if distance <= radius {
                    positions.push(Position::new(center.x + dx as f32, center.y + dy as f32));
                }
            }
        }

        positions
    }

    fn get_rectangle_positions(
        &self,
        caster_pos: Position,
        target_pos: Position,
        width: f32,
        height: f32,
    ) -> Vec<Position> {
        // 简化实现：以目标位置为中心的矩形
        let mut positions = Vec::new();
        let half_width = width / 2.0;
        let half_height = height / 2.0;

        let start_x = (target_pos.x - half_width) as i32;
        let end_x = (target_pos.x + half_width) as i32;
        let start_y = (target_pos.y - half_height) as i32;
        let end_y = (target_pos.y + half_height) as i32;

        for x in start_x..=end_x {
            for y in start_y..=end_y {
                positions.push(Position::new(x as f32, y as f32));
            }
        }

        positions
    }

    fn get_line_positions(&self, start: Position, end: Position, length: f32) -> Vec<Position> {
        // 简化实现：从施法者到目标的直线
        let mut positions = Vec::new();
        let direction = Position::new(end.x - start.x, end.y - start.y).normalized();

        for i in 0..=(length as i32) {
            let pos = Position::new(
                start.x + direction.x * i as f32,
                start.y + direction.y * i as f32,
            );
            positions.push(pos);
        }

        positions
    }

    fn get_cone_positions(
        &self,
        caster_pos: Position,
        target_pos: Position,
        angle: f32,
        length: f32,
    ) -> Vec<Position> {
        // 简化实现：锥形区域
        let mut positions = Vec::new();

        // 计算从施法者到目标的方向
        let direction =
            Position::new(target_pos.x - caster_pos.x, target_pos.y - caster_pos.y).normalized();
        let half_angle = angle.to_radians() / 2.0;

        // 在锥形范围内生成位置
        for i in 0..=(length as i32) {
            let distance = i as f32;
            let width_at_distance = distance * half_angle.tan();
            let width_i = width_at_distance as i32;

            for j in -width_i..=width_i {
                let pos = Position::new(
                    caster_pos.x + direction.x * distance + direction.y * j as f32,
                    caster_pos.y + direction.y * distance - direction.x * j as f32,
                );
                positions.push(pos);
            }
        }

        positions
    }
}

impl Default for TargetingInfo {
    fn default() -> Self {
        Self::new(TargetType::Enemy, 1.0)
    }
}

/// 目标类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TargetType {
    /// 自己
    Self_,
    /// 友方单位
    Ally,
    /// 敌方单位
    Enemy,
    /// 任何单位
    Any,
    /// 指定区域
    Area,
    /// 空地
    Ground,
}

/// 区域形状
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AreaShape {
    /// 点目标
    Point,
    /// 圆形区域
    Circle { radius: f32 },
    /// 矩形区域
    Rectangle { width: f32, height: f32 },
    /// 直线区域
    Line { length: f32 },
    /// 锥形区域
    Cone { angle: f32, length: f32 },
}

// ============================================================================
// 资源消耗值对象
// ============================================================================

// ============================================================================
// 学习要求值对象
// ============================================================================

/// 技能学习要求
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SkillRequirement {
    /// 角色等级要求
    CharacterLevel(Level),
    /// 前置技能要求
    PrerequisiteSkill(SkillId),
    /// 属性要求
    AttributeRequirement {
        attribute: AttributeType,
        minimum_value: i32,
    },
    /// 技能点要求
    SkillPoints(i32),
    /// 完成指定任务
    QuestComplete(String),
    /// 拥有指定物品
    ItemPossession(String),
    /// 组合要求（AND逻辑）
    And(Vec<SkillRequirement>),
    /// 选择要求（OR逻辑）
    Or(Vec<SkillRequirement>),
}

impl SkillRequirement {
    /// 检查是否满足要求
    pub fn check(&self, character_level: Level, learned_skills: &[SkillId]) -> GameResult<()> {
        match self {
            SkillRequirement::CharacterLevel(required_level) => {
                if character_level < *required_level {
                    return Err(GameError::validation_error(
                        "character_level",
                        &format!("需要角色等级{}", required_level),
                    ));
                }
            }
            SkillRequirement::PrerequisiteSkill(skill_id) => {
                if !learned_skills.contains(skill_id) {
                    return Err(GameError::validation_error(
                        "prerequisite_skill",
                        &format!("需要先学习技能{}", skill_id.0),
                    ));
                }
            }
            SkillRequirement::And(requirements) => {
                for req in requirements {
                    req.check(character_level, learned_skills)?;
                }
            }
            SkillRequirement::Or(requirements) => {
                let mut any_satisfied = false;
                for req in requirements {
                    if req.check(character_level, learned_skills).is_ok() {
                        any_satisfied = true;
                        break;
                    }
                }
                if !any_satisfied {
                    return Err(GameError::validation_error(
                        "or_requirement",
                        "不满足任何一个选择性要求",
                    ));
                }
            }
            // 其他要求类型需要额外的上下文信息，这里简化处理
            _ => {
                // TODO: 实现其他要求类型的检查
            }
        }

        Ok(())
    }
}

// ============================================================================
// 元素类型
// ============================================================================

/// 元素类型（从skill_aggregate.rs移动到这里，避免重复定义）
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ElementType {
    Fire,
    Water,
    Earth,
    Air,
    Light,
    Dark,
    Lightning,
    Ice,
    Nature,
    Arcane,
    Chaos,
    Order,
}

pub struct LevelRequirement {}
