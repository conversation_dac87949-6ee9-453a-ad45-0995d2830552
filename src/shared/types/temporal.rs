use crate::shared::GameResult;
use chrono::{DateTime, Utc};
/// 时间相关类型定义
///
/// 此模块定义了游戏中所有与时间、持续时间、冷却等相关的类型
use serde::{Deserialize, Serialize};
use std::ops::Add;
use std::time::Duration;
// ============================================================================
// 游戏时间
// ============================================================================
const DAY_SECONDS: u64 = 86400;
const HOUR_SECONDS: u64 = 3600;
const MINUTE_SECONDS: u64 = 60;

pub fn of_hour(hour: u8) -> Duration {
    let total_seconds = hour as u64 * HOUR_SECONDS;
    Duration::from_secs(total_seconds)
}

pub fn of_minute(minute: u8) -> Duration {
    let total_seconds = minute as u64 * MINUTE_SECONDS;
    Duration::from_secs(total_seconds)
}

pub fn of_seconds(seconds: u8) -> Duration {
    Duration::from_secs(seconds as u64)
}

pub fn of_days(days: u32) -> Duration {
    Duration::from_secs(days as u64 * DAY_SECONDS)
}

/// 游戏时间 - 表示游戏世界中的时间
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct GameTime {
    /// 年份 (修仙历)
    pub year: u32,
    /// 季节
    pub season: Season,
    /// 月份 (1-12, 每季度3个月)
    pub month: u8,
    /// 日期 (1-30, 每月固定30天)
    pub day: u8,
    /// 时辰 (0-23, 对应现实24小时)
    pub hour: u8,
    /// 分钟 (0-59)
    pub minute: u8,
    /// 时间戳
    pub time_stamp: u64,
    /// 时间缩放
    pub time_scale: i32,
}

impl GameTime {
    /// 创建新的游戏时间
    pub fn current() -> Self {
        //从当前时间创建
        let now = Utc::now();
        //TODO: 定义一个全局的时间缩放
        Self::from_real_time(now, 1)
    }

    pub fn new(
        year: u32,
        season: Season,
        month: u8,
        day: u8,
        hour: u8,
        minute: u8,
        time_stamp: u64,
        time_scale: i32,
    ) -> Self {
        Self {
            year,
            season,
            month: month.clamp(1, 12),
            day: day.clamp(1, 30),
            hour: hour.clamp(0, 23),
            minute: minute.clamp(0, 59),
            time_stamp,
            time_scale,
        }
    }

    pub fn add_seconds(&self, seconds: u64) {
        self.add_minutes((seconds / 60) as u32);
    }

    pub fn subtract_seconds(&self, seconds: u64) {
        self.subtract_minutes((seconds / 60) as u32);
    }

    pub fn add_minutes(&self, minutes: u32) -> Self {
        let total_seconds = self.to_total_minutes().as_secs() + (minutes as u64 * 60);
        Self::from_total_minutes(total_seconds / 60)
    }

    pub fn subtract_minutes(&self, minutes: u32) -> Self {
        let current_total = self.to_total_minutes().as_secs() / 60;
        let new_total = current_total.saturating_sub(minutes as u64);
        Self::from_total_minutes(new_total)
    }

    /// 从实际时间创建游戏时间（简化映射）
    pub fn from_real_time(real_time: DateTime<Utc>, time_scale: i32) -> Self {
        // 这里是简化实现，实际可能需要更复杂的时间映射逻辑
        let scaled_timestamp = real_time.timestamp() * time_scale as i64;
        let game_days = (scaled_timestamp / 86400) as u32; // 86400秒 = 1天

        let year = 1000 + (game_days / 360); // 每年360天
        let year_day = game_days % 360;
        let season = Season::from_day_of_year(year_day as u16);
        let month = ((year_day / 30) % 12) as u8 + 1;
        let day = (year_day % 30) as u8 + 1;
        let hour = ((scaled_timestamp / 3600) % 24) as u8;
        let minute = ((scaled_timestamp / 60) % 60) as u8;
        Self::new(
            year,
            season,
            month,
            day,
            hour,
            minute,
            scaled_timestamp as u64,
            time_scale,
        )
    }

    /// 推进指定的分钟数
    pub fn advance_minutes(&mut self, minutes: u32) {
        let duration = Duration::from_secs(minutes as u64 * 60);
        let total_minutes = self.to_total_minutes().add(duration);
        *self = Self::from_total_minutes(total_minutes.as_secs() / 60);
    }

    /// 转换为总分钟数（从某个基准时间开始）
    pub fn to_total_minutes(&self) -> Duration {
        let year_minutes = (self.year as u64) * 360 * 24 * 60;
        let month_minutes = ((self.month - 1) as u64) * 30 * 24 * 60;
        let day_minutes = ((self.day - 1) as u64) * 24 * 60;
        let hour_minutes = (self.hour as u64) * 60;
        let minutes = self.minute as u64;
        Duration::from_secs(
            (year_minutes + month_minutes + day_minutes + hour_minutes + minutes) * 60,
        )
    }

    /// 从总分钟数创建游戏时间
    pub fn from_total_minutes(total_minutes: u64) -> Self {
        let year = (total_minutes / (360 * 24 * 60)) as u32;
        let remaining = total_minutes % (360 * 24 * 60);

        let month = ((remaining / (30 * 24 * 60)) % 12) as u8 + 1;
        let remaining = remaining % (30 * 24 * 60);

        let day = ((remaining / (24 * 60)) % 30) as u8 + 1;
        let remaining = remaining % (24 * 60);

        let hour = ((remaining / 60) % 24) as u8;
        let minute = (remaining % 60) as u8;

        let year_day = ((month - 1) * 30 + (day - 1)) as u16;
        let season = Season::from_day_of_year(year_day);

        Self::new(year, season, month, day, hour, minute, 0, 1)
    }

    /// 获取当前时间的时辰名称（修仙风格）
    pub fn time_period_name(&self) -> &'static str {
        match self.hour {
            23 | 0..=1 => "子时", // 夜半
            2..=3 => "丑时",      // 鸡鸣
            4..=5 => "寅时",      // 平旦
            6..=7 => "卯时",      // 日出
            8..=9 => "辰时",      // 食时
            10..=11 => "巳时",    // 隅中
            12..=13 => "午时",    // 日中
            14..=15 => "未时",    // 日昳
            16..=17 => "申时",    // 晡时
            18..=19 => "酉时",    // 日入
            20..=21 => "戌时",    // 黄昏
            _ => "亥时",          // 人定
        }
    }

    /// 判断是否为白天
    pub fn is_daytime(&self) -> bool {
        self.hour >= 6 && self.hour < 18
    }

    /// 判断是否为修炼的最佳时间
    pub fn is_optimal_cultivation_time(&self) -> bool {
        // 子时（23-1）和午时（11-13）是修炼的最佳时间
        matches!(self.hour, 23 | 0..=1 | 11..=13)
    }
}

impl std::fmt::Display for GameTime {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "修仙历{}年{}月{}日 {}时{}分 ({})",
            self.year,
            self.month,
            self.day,
            self.hour,
            self.minute,
            self.time_period_name()
        )
    }
}

/// 冷却状态
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct CooldownState {
    /// 剩余冷却时间
    remaining_time: f64,
    /// 总冷却时间
    total_cooldown: f64,
    /// 冷却减免百分比
    reduction_percent: f64,
}

impl CooldownState {
    pub fn new(cooldown: f64) -> Self {
        Self {
            remaining_time: 0.0,
            total_cooldown: cooldown,
            reduction_percent: 0.0,
        }
    }

    pub fn ready() -> Self {
        Self {
            remaining_time: 0.0,
            total_cooldown: 0.0,
            reduction_percent: 0.0,
        }
    }

    pub fn is_ready(&self) -> bool {
        self.remaining_time <= 0.0
    }

    pub fn remaining_time(&self) -> f64 {
        self.remaining_time
    }

    pub fn total_cooldown(&self) -> f64 {
        self.total_cooldown
    }

    pub fn progress(&self) -> f64 {
        if self.total_cooldown <= 0.0 {
            1.0
        } else {
            1.0 - (self.remaining_time / self.calculate_effective_cooldown())
                .max(0.0)
                .min(1.0)
        }
    }

    /// 更新冷却时间
    pub fn update(&mut self, delta_time: f32) {
        if self.remaining_time > 0.0 {
            self.remaining_time -= delta_time as f64;
        }
    }

    /// 重置冷却
    pub fn reset(&mut self, new_cooldown: f64) {
        self.total_cooldown = new_cooldown;
        self.remaining_time = self.calculate_effective_cooldown();
    }

    /// 设置冷却减免
    pub fn set_reduction(&mut self, reduction_percent: f64) {
        self.reduction_percent = reduction_percent.max(0.0).min(1.0);
    }

    /// 立即完成冷却
    pub fn complete(&mut self) {
        self.remaining_time = 0.0;
    }

    /// 计算有效冷却时间（考虑减免）
    fn calculate_effective_cooldown(&self) -> f64 {
        self.total_cooldown * (1.0 - self.reduction_percent)
    }
}

impl Default for CooldownState {
    fn default() -> Self {
        Self::ready()
    }
}

// ============================================================================
// 季节
// ============================================================================

/// 季节
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Season {
    /// 春季 - 万物复苏，灵气活跃
    Spring,
    /// 夏季 - 阳气旺盛，火属性增强
    Summer,
    /// 秋季 - 收获季节，金属性增强
    Autumn,
    /// 冬季 - 蛰伏时期，水属性增强
    Winter,
}

impl Season {
    /// 从一年中的第几天计算季节 (0-359)
    pub fn from_day_of_year(day: u16) -> Self {
        match day {
            0..=89 => Season::Spring,    // 春季：1-3月
            90..=179 => Season::Summer,  // 夏季：4-6月
            180..=269 => Season::Autumn, // 秋季：7-9月
            _ => Season::Winter,         // 冬季：10-12月
        }
    }

    /// 获取季节对灵气密度的影响
    pub fn spiritual_density_modifier(&self) -> f32 {
        match self {
            Season::Spring => 1.2, // 春季灵气活跃
            Season::Summer => 1.1, // 夏季阳气旺盛
            Season::Autumn => 1.0, // 秋季平衡
            Season::Winter => 0.9, // 冬季灵气收敛
        }
    }

    /// 获取季节对不同属性的影响
    pub fn elemental_modifiers(&self) -> ElementalModifiers {
        match self {
            Season::Spring => ElementalModifiers {
                wood: 1.3,
                fire: 1.0,
                earth: 1.1,
                metal: 0.9,
                water: 1.0,
            },
            Season::Summer => ElementalModifiers {
                wood: 1.1,
                fire: 1.4,
                earth: 1.0,
                metal: 0.8,
                water: 0.8,
            },
            Season::Autumn => ElementalModifiers {
                wood: 0.9,
                fire: 0.9,
                earth: 1.1,
                metal: 1.3,
                water: 1.0,
            },
            Season::Winter => ElementalModifiers {
                wood: 0.8,
                fire: 0.8,
                earth: 1.0,
                metal: 1.1,
                water: 1.4,
            },
        }
    }

    /// 获取季节适宜的活动
    pub fn seasonal_activities(&self) -> Vec<&'static str> {
        match self {
            Season::Spring => vec!["种植灵草", "采集嫩芽", "修炼木属性功法", "突破瓶颈"],
            Season::Summer => vec![
                "炼制火属性丹药",
                "修炼火属性功法",
                "采集阳性药材",
                "户外修炼",
            ],
            Season::Autumn => vec!["收获灵草", "炼制法器", "修炼金属性功法", "整理资源"],
            Season::Winter => vec!["静修养息", "修炼水属性功法", "研读典籍", "内丹修炼"],
        }
    }

    /// 获取下一个季节
    pub fn next(&self) -> Self {
        match self {
            Season::Spring => Season::Summer,
            Season::Summer => Season::Autumn,
            Season::Autumn => Season::Winter,
            Season::Winter => Season::Spring,
        }
    }
}

impl std::fmt::Display for Season {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let name = match self {
            Season::Spring => "春季",
            Season::Summer => "夏季",
            Season::Autumn => "秋季",
            Season::Winter => "冬季",
        };
        write!(f, "{}", name)
    }
}

/// 五行属性修正值
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ElementalModifiers {
    /// 木属性修正
    pub wood: f32,
    /// 火属性修正
    pub fire: f32,
    /// 土属性修正
    pub earth: f32,
    /// 金属性修正
    pub metal: f32,
    /// 水属性修正
    pub water: f32,
}

// ============================================================================
// 天象事件
// ============================================================================

/// 天象事件 - 影响世界的天文现象
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct CelestialEvent {
    /// 事件ID
    pub event_id: String,
    /// 事件名称
    pub name: String,
    /// 事件类型
    pub event_type: CelestialEventType,
    /// 开始时间
    pub start_time: GameTime,
    /// 持续时间
    pub duration: Duration,
    /// 事件效果
    pub effects: Vec<CelestialEffect>,
    /// 发生概率
    pub probability: f32,
    /// 是否罕见事件
    pub is_rare: bool,
}

/// 天象事件类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CelestialEventType {
    /// 满月 - 增强修炼效果
    FullMoon,
    /// 新月 - 适合内省和突破
    NewMoon,
    /// 日食 - 阴阳失衡，危险但机遇并存
    SolarEclipse,
    /// 月食 - 血月现世，邪气增强
    LunarEclipse,
    /// 流星雨 - 天降异象，可能获得天材地宝
    MeteorShower,
    /// 极光 - 灵气波动剧烈
    Aurora,
    /// 彗星来访 - 千年一遇的大吉大凶之兆
    CometVisit,
    /// 七星连珠 - 极其罕见，威力巨大
    PlanetaryAlignment,
}

/// 天象效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct CelestialEffect {
    /// 效果类型
    pub effect_type: CelestialEffectType,
    /// 效果强度
    pub intensity: f32,
    /// 影响范围
    pub range: Option<f32>,
    /// 持续时间
    pub duration: Option<Duration>,
}

/// 天象效果类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CelestialEffectType {
    /// 修炼速度影响
    CultivationSpeedModifier,
    /// 突破概率影响
    BreakthroughChanceModifier,
    /// 灵气密度影响
    SpiritualDensityModifier,
    /// 特定属性增强
    ElementalBoost,
    /// 危险度增加
    DangerIncrease,
    /// 稀有资源出现概率
    RareResourceChance,
    /// 特殊遭遇概率
    SpecialEncounterChance,
}

// ============================================================================
// 世界时间管理器
// ============================================================================

/// 世界时间管理器 - 管理游戏世界的时间流逝
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct WorldTime {
    /// 当前游戏时间
    pub current_time: GameTime,
    /// 时间流速倍数
    pub time_scale: i32,
    /// 最后更新的真实时间
    pub last_update: DateTime<Utc>,
    /// 活跃的天象事件
    pub active_celestial_events: Vec<CelestialEvent>,
    /// 季节性效果
    pub seasonal_effects: SeasonalEffects,
    /// 是否暂停时间流逝
    pub paused: bool,
}

impl WorldTime {
    /// 创建新的世界时间管理器
    pub fn new(time_scale: i32) -> Self {
        let now = Utc::now();
        Self {
            current_time: GameTime::from_real_time(now, time_scale),
            time_scale,
            last_update: now,
            active_celestial_events: Vec::new(),
            seasonal_effects: SeasonalEffects::default(),
            paused: false,
        }
    }

    /// 更新时间流逝
    pub fn update(&mut self) {
        if self.paused {
            return;
        }

        let now = Utc::now();
        let elapsed = now.signed_duration_since(self.last_update);
        let game_minutes = (elapsed.num_seconds() * self.time_scale as i64 / 60) as u32;

        if game_minutes > 0 {
            self.current_time.advance_minutes(game_minutes);
            self.last_update = now;
            self.update_seasonal_effects();
            self.update_celestial_events();
        }
    }

    /// 更新季节性效果
    fn update_seasonal_effects(&mut self) {
        self.seasonal_effects = SeasonalEffects::from_season(self.current_time.season);
    }

    /// 更新天象事件
    fn update_celestial_events(&mut self) {
        // 移除已结束的事件
        self.active_celestial_events.retain(|event| {
            let event_end = event.start_time.to_total_minutes().add(event.duration);
            self.current_time.to_total_minutes() < event_end
        });

        // 检查是否应该触发新的天象事件
        self.check_for_new_celestial_events();
    }

    /// 检查并触发新的天象事件
    fn check_for_new_celestial_events(&mut self) {
        // 简化实现：基于时间和随机性触发事件
        use rand::Rng;
        let mut rng = rand::thread_rng();

        // 满月和新月事件（月度事件）
        if self.current_time.day == 15 && self.current_time.hour == 0 {
            let eight_hour = Duration::from_secs(8 * 24 * 3600);
            let full_moon = CelestialEvent {
                event_id: format!(
                    "full moon_{}_{}_{}",
                    self.current_time.year, self.current_time.month, self.current_time.day
                ),
                name: "满月之夜".to_string(),
                event_type: CelestialEventType::FullMoon,
                start_time: self.current_time.clone(),
                duration: eight_hour,
                effects: vec![CelestialEffect {
                    effect_type: CelestialEffectType::CultivationSpeedModifier,
                    intensity: 1.5,
                    range: None,
                    duration: Some(eight_hour),
                }],
                probability: 1.0,
                is_rare: false,
            };
            self.active_celestial_events.push(full_moon);
        }

        // 其他稀有事件的随机触发
        if rng.gen::<f32>() < 0.001 {
            // 0.1% 概率
            self.trigger_rare_celestial_event();
        }
    }

    /// 触发稀有天象事件
    fn trigger_rare_celestial_event(&mut self) {
        use rand::Rng;
        let mut rng = rand::thread_rng();

        let event_types = vec![
            CelestialEventType::MeteorShower,
            CelestialEventType::Aurora,
            CelestialEventType::SolarEclipse,
            CelestialEventType::LunarEclipse,
        ];

        let event_type = event_types[rng.gen_range(0..event_types.len())];

        let event = CelestialEvent {
            event_id: format!(
                "rare_{}_{:?}",
                event_type as u8,
                self.current_time.to_total_minutes()
            ),
            name: match event_type {
                CelestialEventType::MeteorShower => "流星雨".to_string(),
                CelestialEventType::Aurora => "极光现象".to_string(),
                CelestialEventType::SolarEclipse => "日食".to_string(),
                CelestialEventType::LunarEclipse => "月食".to_string(),
                _ => "未知天象".to_string(),
            },
            event_type,
            start_time: self.current_time.clone(),
            duration: of_hour(rng.gen_range(2..12)),
            effects: self.generate_event_effects(event_type),
            probability: 0.001,
            is_rare: true,
        };

        self.active_celestial_events.push(event);
    }

    /// 生成事件效果
    fn generate_event_effects(&self, event_type: CelestialEventType) -> Vec<CelestialEffect> {
        match event_type {
            CelestialEventType::MeteorShower => vec![CelestialEffect {
                effect_type: CelestialEffectType::RareResourceChance,
                intensity: 3.0,
                range: None,
                duration: Some(of_hour(6)),
            }],
            CelestialEventType::Aurora => vec![CelestialEffect {
                effect_type: CelestialEffectType::SpiritualDensityModifier,
                intensity: 2.0,
                range: Some(1000.0),
                duration: Some(of_hour(4)),
            }],
            CelestialEventType::SolarEclipse => vec![
                CelestialEffect {
                    effect_type: CelestialEffectType::DangerIncrease,
                    intensity: 1.5,
                    range: None,
                    duration: Some(of_hour(2)),
                },
                CelestialEffect {
                    effect_type: CelestialEffectType::BreakthroughChanceModifier,
                    intensity: 2.0,
                    range: None,
                    duration: Some(of_hour(1)),
                },
            ],
            _ => vec![],
        }
    }
}

/// 季节性效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SeasonalEffects {
    /// 当前季节
    pub current_season: Season,
    /// 灵气密度修正
    pub spiritual_density_modifier: f32,
    /// 五行属性修正
    pub elemental_modifiers: ElementalModifiers,
    /// 资源生长速度修正
    pub resource_growth_modifier: f32,
    /// 修炼速度修正
    pub cultivation_speed_modifier: f32,
}

impl SeasonalEffects {
    /// 从季节创建季节性效果
    pub fn from_season(season: Season) -> Self {
        let elemental_modifiers = season.elemental_modifiers();

        Self {
            current_season: season,
            spiritual_density_modifier: season.spiritual_density_modifier(),
            elemental_modifiers,
            resource_growth_modifier: match season {
                Season::Spring => 1.5,
                Season::Summer => 1.3,
                Season::Autumn => 1.2,
                Season::Winter => 0.8,
            },
            cultivation_speed_modifier: match season {
                Season::Spring => 1.1,
                Season::Summer => 1.0,
                Season::Autumn => 1.0,
                Season::Winter => 1.2, // 冬季适合静修
            },
        }
    }
}

impl Default for SeasonalEffects {
    fn default() -> Self {
        Self::from_season(Season::Spring)
    }
}

/// 时间感知 - 处理时间相关的更新和状态
pub trait TemporalEntity {
    /// 根据时间更新状态
    fn update_by_time(&mut self, delta_seconds: f32) -> GameResult<()>;

    /// 获取创建时间
    fn creation_time(&self) -> GameTime;

    /// 获取存在时长
    fn age(&self, current_time: GameTime) -> f32;

    /// 是否为临时实体
    fn is_temporary(&self) -> bool;

    /// 获取生存时间限制（如果有）
    fn lifetime_limit(&self) -> Option<f32>;
}
