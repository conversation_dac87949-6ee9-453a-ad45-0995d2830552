impl CoreAttribute {
    pub fn all() -> &'static [CoreAttribute] {
        &[CoreAttribute::Metal, CoreAttribute::Wood, CoreAttribute::Water, CoreAttribute::Fire, CoreAttribute::Earth]
    }
}
/// 材料属性体系（DDD/值对象/事件驱动范式）
/// 领域模型、值对象、聚合根、事件接口


use std::collections::HashMap;
use serde::{Serialize, Deserialize};

/// 五行基础属性
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CoreAttribute {
    Metal,  // 金
    Wood,   // 木
    Water,  // 水
    Fire,   // 火
    Earth,  // 土
}

/// 复合属性（由基础属性衍生）
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DerivedAttribute {
    Thunder, // 雷（木+金）
    Ice,     // 冰（水+金）
    Wind,    // 风（木+水）
    Magma,   // 岩浆（火+土）
    Steam,   // 蒸汽（火+水）
    Crystal, // 晶（土+金）
    // 特殊组合属性
    Light,   // 光（火+木）
    Shadow,  // 影（水+土）
}

/// 属性类型（可扩展）
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AttributeType {
    /// 基础属性（如金木水火土）
    Base(CoreAttribute),
    /// 复合属性（如冰雷风）
    Composite(DerivedAttribute),
    // Custom(String), // 未来可扩展
}

/// 属性值对象（不可变）
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Attribute {
    pub attr_type: AttributeType,
    pub value: f64, // 支持浮点，便于加成、百分比等
    // 可扩展：单位、范围、修饰符等
}

impl Attribute {
    pub fn new(attr_type: AttributeType, value: f64) -> Self {
        Self { attr_type, value }
    }
}

/// 材料属性集合（聚合根）
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AttributeSet {
    pub attributes: HashMap<AttributeType, Attribute>,
}

impl AttributeSet {
    pub fn new() -> Self {
        Self { attributes: HashMap::new() }
    }

    pub fn add(&mut self, attr: Attribute) {
        self.attributes.insert(attr.attr_type, attr);
    }

    pub fn get(&self, attr_type: &AttributeType) -> Option<&Attribute> {
        self.attributes.get(attr_type)
    }

    /// 属性叠加（如多材料合成时）
    pub fn merge(&self, other: &AttributeSet) -> AttributeSet {
        let mut merged = self.clone();
        for (k, v) in &other.attributes {
            merged.attributes
                .entry(*k)
                .and_modify(|a| a.value += v.value)
                .or_insert(v.clone());
        }
        merged
    }
}

impl Default for AttributeSet {
    fn default() -> Self {
        Self {
            attributes: std::collections::HashMap::new(),
        }
    }
}

