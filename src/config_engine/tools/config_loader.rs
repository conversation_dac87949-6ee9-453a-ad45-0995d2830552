//! # 配置加载器工具
//!
//! 提供配置文件的高级加载功能

use crate::config_engine::{
    error::{ConfigEngineError, ConfigurationError},
    Result,
};
use serde_json::Value;
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;

/// 配置加载器
pub struct ConfigLoader {
    /// 加载路径列表
    search_paths: Vec<String>,
    /// 文件格式映射
    format_handlers: HashMap<String, Box<dyn FormatHandler>>,
    /// 缓存配置
    cache_enabled: bool,
    /// 缓存
    cache: HashMap<String, (Value, std::time::SystemTime)>,
    /// 缓存TTL（秒）
    cache_ttl: u64,
}

/// 文件格式处理器
pub trait FormatHandler: Send + Sync {
    /// 解析文件内容
    fn parse(&self, content: &str) -> Result<Value>;

    /// 获取支持的文件扩展名
    fn extensions(&self) -> Vec<&'static str>;
}

/// TOML格式处理器
pub struct TomlHandler;

impl FormatHandler for TomlHandler {
    fn parse(&self, content: &str) -> Result<Value> {
        let toml_value: toml::Value = content.parse().map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::ParseError {
                details: format!("TOML解析错误: {}", e),
            })
        })?;

        // 转换TOML值为JSON值
        self.toml_to_json(toml_value)
    }

    fn extensions(&self) -> Vec<&'static str> {
        vec!["toml", "tml"]
    }
}

impl TomlHandler {
    fn toml_to_json(&self, toml_value: toml::Value) -> Result<Value> {
        match toml_value {
            toml::Value::String(s) => Ok(Value::String(s)),
            toml::Value::Integer(i) => Ok(Value::Number(serde_json::Number::from(i))),
            toml::Value::Float(f) => {
                if let Some(num) = serde_json::Number::from_f64(f) {
                    Ok(Value::Number(num))
                } else {
                    Err(ConfigEngineError::Configuration(
                        ConfigurationError::ParseError {
                            details: format!("无效的浮点数: {}", f),
                        },
                    ))
                }
            }
            toml::Value::Boolean(b) => Ok(Value::Bool(b)),
            toml::Value::Array(arr) => {
                let json_arr: Result<Vec<Value>> =
                    arr.into_iter().map(|v| self.toml_to_json(v)).collect();
                Ok(Value::Array(json_arr?))
            }
            toml::Value::Table(table) => {
                let mut json_obj = serde_json::Map::new();
                for (key, value) in table {
                    json_obj.insert(key, self.toml_to_json(value)?);
                }
                Ok(Value::Object(json_obj))
            }
            toml::Value::Datetime(dt) => Ok(Value::String(dt.to_string())),
        }
    }
}

/// JSON格式处理器
pub struct JsonHandler;

impl FormatHandler for JsonHandler {
    fn parse(&self, content: &str) -> Result<Value> {
        serde_json::from_str(content).map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::ParseError {
                details: format!("JSON解析错误: {}", e),
            })
        })
    }

    fn extensions(&self) -> Vec<&'static str> {
        vec!["json"]
    }
}

/// YAML格式处理器
pub struct YamlHandler;

impl FormatHandler for YamlHandler {
    fn parse(&self, content: &str) -> Result<Value> {
        serde_yaml::from_str(content).map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::ParseError {
                details: format!("YAML解析错误: {}", e),
            })
        })
    }

    fn extensions(&self) -> Vec<&'static str> {
        vec!["yaml", "yml"]
    }
}

impl ConfigLoader {
    /// 创建新的配置加载器
    pub fn new() -> Self {
        let mut loader = Self {
            search_paths: vec![".".to_string()],
            format_handlers: HashMap::new(),
            cache_enabled: true,
            cache: HashMap::new(),
            cache_ttl: 300, // 5分钟
        };

        // 注册默认格式处理器
        loader.register_handler(Box::new(TomlHandler));
        loader.register_handler(Box::new(JsonHandler));
        loader.register_handler(Box::new(YamlHandler));

        loader
    }

    /// 注册格式处理器
    pub fn register_handler(&mut self, handler: Box<dyn FormatHandler>) {
        for ext in handler.extensions() {
            // 暂时简化实现，不使用动态注册
            // 实际项目中需要更好的设计
        }
    }

    /// 添加搜索路径
    pub fn add_search_path(&mut self, path: impl Into<String>) {
        self.search_paths.push(path.into());
    }

    /// 启用/禁用缓存
    pub fn set_cache_enabled(&mut self, enabled: bool) {
        self.cache_enabled = enabled;
        if !enabled {
            self.cache.clear();
        }
    }

    /// 设置缓存TTL
    pub fn set_cache_ttl(&mut self, ttl_seconds: u64) {
        self.cache_ttl = ttl_seconds;
    }

    /// 加载配置文件
    pub async fn load_config(&mut self, filename: &str) -> Result<Value> {
        // 检查缓存
        if self.cache_enabled {
            if let Some((value, timestamp)) = self.cache.get(filename) {
                let now = std::time::SystemTime::now();
                if let Ok(duration) = now.duration_since(*timestamp) {
                    if duration.as_secs() < self.cache_ttl {
                        return Ok(value.clone());
                    }
                }
            }
        }

        // 查找文件
        let file_path = self.find_config_file(filename).await?;

        // 读取文件内容
        let content = fs::read_to_string(&file_path).await.map_err(|e| {
            ConfigEngineError::Configuration(ConfigurationError::LoadFailure {
                source: file_path.clone(),
                reason: e.to_string(),
            })
        })?;

        // 确定文件格式
        let extension = Path::new(&file_path)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("toml");

        // 解析内容
        let value = self.parse_content(&content, extension)?;

        // 缓存结果
        if self.cache_enabled {
            self.cache.insert(
                filename.to_string(),
                (value.clone(), std::time::SystemTime::now()),
            );
        }

        Ok(value)
    }

    /// 查找配置文件
    async fn find_config_file(&self, filename: &str) -> Result<String> {
        for search_path in &self.search_paths {
            let full_path = Path::new(search_path).join(filename);
            if full_path.exists() {
                return Ok(full_path.to_string_lossy().to_string());
            }

            // 尝试添加常见扩展名
            for ext in &["toml", "json", "yaml", "yml"] {
                let full_path_with_ext =
                    Path::new(search_path).join(format!("{}.{}", filename, ext));
                if full_path_with_ext.exists() {
                    return Ok(full_path_with_ext.to_string_lossy().to_string());
                }
            }
        }

        Err(ConfigEngineError::Configuration(
            ConfigurationError::FileNotFound {
                file: filename.to_string(),
            },
        ))
    }

    /// 解析文件内容
    fn parse_content(&self, content: &str, extension: &str) -> Result<Value> {
        // 简化实现：直接使用具体的处理器
        match extension {
            "toml" | "tml" => {
                let handler = TomlHandler;
                handler.parse(content)
            }
            "json" => {
                let handler = JsonHandler;
                handler.parse(content)
            }
            "yaml" | "yml" => {
                let handler = YamlHandler;
                handler.parse(content)
            }
            _ => {
                // 默认尝试TOML
                let handler = TomlHandler;
                handler.parse(content)
            }
        }
    }

    /// 合并多个配置文件
    pub async fn load_and_merge_configs(&mut self, filenames: &[&str]) -> Result<Value> {
        let mut merged = Value::Object(serde_json::Map::new());

        for filename in filenames {
            let config = self.load_config(filename).await?;
            merged = self.merge_values(merged, config)?;
        }

        Ok(merged)
    }

    /// 合并两个JSON值
    fn merge_values(&self, mut base: Value, overlay: Value) -> Result<Value> {
        match (&mut base, overlay) {
            (Value::Object(base_map), Value::Object(overlay_map)) => {
                for (key, value) in overlay_map {
                    if let Some(existing) = base_map.get_mut(&key) {
                        *existing = self.merge_values(existing.clone(), value)?;
                    } else {
                        base_map.insert(key, value);
                    }
                }
                Ok(base)
            }
            (_, overlay) => Ok(overlay), // overlay覆盖base
        }
    }

    /// 清除缓存
    pub fn clear_cache(&mut self) {
        self.cache.clear();
    }

    /// 清除过期缓存
    pub fn clear_expired_cache(&mut self) {
        let now = std::time::SystemTime::now();
        self.cache.retain(|_, (_, timestamp)| {
            if let Ok(duration) = now.duration_since(*timestamp) {
                duration.as_secs() < self.cache_ttl
            } else {
                false
            }
        });
    }

    /// 预加载配置文件
    pub async fn preload_configs(&mut self, filenames: &[&str]) -> Result<()> {
        for filename in filenames {
            if let Err(e) = self.load_config(filename).await {
                eprintln!("预加载配置文件 {} 失败: {}", filename, e);
            }
        }
        Ok(())
    }

    /// 热重载配置
    pub async fn hot_reload(&mut self, filename: &str) -> Result<Value> {
        // 清除缓存
        self.cache.remove(filename);

        // 重新加载
        self.load_config(filename).await
    }

    /// 验证配置文件是否存在
    pub async fn validate_config_exists(&self, filename: &str) -> bool {
        self.find_config_file(filename).await.is_ok()
    }

    /// 获取缓存统计信息
    pub fn cache_stats(&self) -> CacheStats {
        CacheStats {
            enabled: self.cache_enabled,
            size: self.cache.len(),
            ttl_seconds: self.cache_ttl,
        }
    }
}

impl Default for ConfigLoader {
    fn default() -> Self {
        Self::new()
    }
}

/// 缓存统计信息
#[derive(Debug, Clone)]
pub struct CacheStats {
    pub enabled: bool,
    pub size: usize,
    pub ttl_seconds: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_config_loader_creation() {
        let loader = ConfigLoader::new();
        assert!(loader.cache_enabled);
        assert_eq!(loader.cache_ttl, 300);
        assert_eq!(loader.search_paths, vec!["."]);
    }

    #[tokio::test]
    async fn test_toml_handler() {
        let handler = TomlHandler;
        let toml_content = r#"
            [server]
            host = "localhost"
            port = 8080
            enabled = true
        "#;

        let result = handler.parse(toml_content).unwrap();
        assert!(result.is_object());

        let server = result.get("server").unwrap();
        assert_eq!(server.get("host").unwrap(), "localhost");
        assert_eq!(server.get("port").unwrap(), 8080);
        assert_eq!(server.get("enabled").unwrap(), true);
    }

    #[tokio::test]
    async fn test_json_handler() {
        let handler = JsonHandler;
        let json_content = r#"
            {
                "server": {
                    "host": "localhost",
                    "port": 8080,
                    "enabled": true
                }
            }
        "#;

        let result = handler.parse(json_content).unwrap();
        assert!(result.is_object());

        let server = result.get("server").unwrap();
        assert_eq!(server.get("host").unwrap(), "localhost");
        assert_eq!(server.get("port").unwrap(), 8080);
        assert_eq!(server.get("enabled").unwrap(), true);
    }

    #[tokio::test]
    async fn test_config_merge() {
        let loader = ConfigLoader::new();

        let base = serde_json::json!({
            "server": {
                "host": "localhost",
                "port": 8080
            },
            "database": {
                "url": "sqlite://db.sqlite"
            }
        });

        let overlay = serde_json::json!({
            "server": {
                "port": 9090,
                "ssl": true
            },
            "cache": {
                "enabled": true
            }
        });

        let merged = loader.merge_values(base, overlay).unwrap();

        let server = merged.get("server").unwrap();
        assert_eq!(server.get("host").unwrap(), "localhost"); // 保持原值
        assert_eq!(server.get("port").unwrap(), 9090); // 被覆盖
        assert_eq!(server.get("ssl").unwrap(), true); // 新添加

        assert!(merged.get("database").is_some()); // 保持原值
        assert!(merged.get("cache").is_some()); // 新添加
    }

    #[tokio::test]
    async fn test_cache_functionality() {
        let mut loader = ConfigLoader::new();

        // 创建临时配置文件
        let mut temp_file = NamedTempFile::new().unwrap();
        writeln!(
            temp_file,
            r#"
            [test]
            value = "cached"
        "#
        )
        .unwrap();

        let temp_path = temp_file.path().to_string_lossy().to_string();
        loader.add_search_path(
            temp_file
                .path()
                .parent()
                .unwrap()
                .to_string_lossy()
                .to_string(),
        );

        // 第一次加载
        let filename = temp_file
            .path()
            .file_name()
            .unwrap()
            .to_string_lossy()
            .to_string();
        let result1 = loader.load_config(&filename).await.unwrap();

        // 检查缓存
        assert_eq!(loader.cache.len(), 1);

        // 第二次加载（应该从缓存获取）
        let result2 = loader.load_config(&filename).await.unwrap();
        assert_eq!(result1, result2);

        // 清除缓存
        loader.clear_cache();
        assert_eq!(loader.cache.len(), 0);
    }
}
