//! # TypedId 模块
//!
//! 实现类型安全的ID系统，替代传统的枚举类型
//!
//! ## 核心概念
//!
//! - `TypedId<T>`: 类型安全的ID包装器，确保不同类型的ID不会被误用
//! - `TypeMarker`: 类型标记trait，用于标识不同的ID类型
//! - 编译时和运行时类型安全保障
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::types::{TypedId, TypeMarker};
//!
//! // 定义材料类型标记
//! struct MaterialType;
//! impl TypeMarker for MaterialType {
//!     const TYPE_NAME: &'static str = "Material";
//! }
//!
//! // 创建类型安全的材料ID
//! let material_id = TypedId::<MaterialType>::new("iron_ore".to_string());
//! assert_eq!(material_id.as_str(), "iron_ore");
//! ```

use serde::{Deserialize, Deserializer, Serialize, Serializer};
use std::fmt::{Debug, Display, Formatter};
use std::hash::{Hash, Hasher};
use std::marker::PhantomData;
use std::str::FromStr;

/// 类型标记trait
///
/// 用于标识不同的ID类型，提供编译时类型安全
pub trait TypeMarker: Send + Sync + 'static {
    /// 类型名称，用于运行时识别和调试
    const TYPE_NAME: &'static str;

    /// 类型描述（可选）
    const TYPE_DESCRIPTION: Option<&'static str> = None;

    /// 类型版本（可选）
    const TYPE_VERSION: Option<&'static str> = None;

    /// 是否允许空ID
    const ALLOW_EMPTY: bool = false;

    /// 验证ID字符串是否有效
    fn validate_id(id: &str) -> Result<(), String> {
        if !Self::ALLOW_EMPTY && id.is_empty() {
            return Err(format!("Empty ID not allowed for type {}", Self::TYPE_NAME));
        }

        // 基本字符验证
        if id.chars().any(|c| c.is_control() || c.is_whitespace()) {
            return Err(format!(
                "ID contains invalid characters for type {}",
                Self::TYPE_NAME
            ));
        }

        Ok(())
    }

    /// 规范化ID字符串（可选的转换）
    fn normalize_id(id: String) -> String {
        id
    }
}

/// 类型安全的ID包装器
///
/// 通过类型系统确保不同类型的ID不会被误用
#[derive(Clone, PartialEq, Eq, PartialOrd, Ord)]
pub struct TypedId<T: TypeMarker> {
    /// 实际的ID字符串
    id: String,
    /// 类型标记（零大小类型）
    _marker: PhantomData<T>,
}

impl<T: TypeMarker> TypedId<T> {
    /// 创建新的TypedId
    ///
    /// # Arguments
    ///
    /// * `id` - ID字符串
    ///
    /// # Returns
    ///
    /// * `Ok(TypedId<T>)` - 成功创建的TypedId
    /// * `Err(String)` - 验证失败的错误信息
    pub fn new(id: String) -> Result<Self, String> {
        T::validate_id(&id)?;
        let normalized_id = T::normalize_id(id);

        Ok(Self {
            id: normalized_id,
            _marker: PhantomData,
        })
    }

    /// 创建TypedId（不进行验证）
    ///
    /// 警告：仅在确信ID有效时使用，跳过验证可能导致运行时错误
    pub fn new_unchecked(id: String) -> Self {
        Self {
            id: T::normalize_id(id),
            _marker: PhantomData,
        }
    }

    /// 尝试从字符串引用创建TypedId
    pub fn try_from_str(id: &str) -> Result<Self, String> {
        Self::new(id.to_string())
    }

    /// 获取ID字符串的引用
    pub fn as_str(&self) -> &str {
        &self.id
    }

    /// 获取ID字符串的拷贝
    pub fn to_string(&self) -> String {
        self.id.clone()
    }

    /// 获取类型名称
    pub fn type_name(&self) -> &'static str {
        T::TYPE_NAME
    }

    /// 获取类型描述
    pub fn type_description(&self) -> Option<&'static str> {
        T::TYPE_DESCRIPTION
    }

    /// 获取类型版本
    pub fn type_version(&self) -> Option<&'static str> {
        T::TYPE_VERSION
    }

    /// 检查ID是否为空
    pub fn is_empty(&self) -> bool {
        self.id.is_empty()
    }

    /// 检查ID长度
    pub fn len(&self) -> usize {
        self.id.len()
    }

    /// 检查是否包含指定的子字符串
    pub fn contains(&self, pattern: &str) -> bool {
        self.id.contains(pattern)
    }

    /// 检查是否以指定字符串开头
    pub fn starts_with(&self, prefix: &str) -> bool {
        self.id.starts_with(prefix)
    }

    /// 检查是否以指定字符串结尾
    pub fn ends_with(&self, suffix: &str) -> bool {
        self.id.ends_with(suffix)
    }

    /// 创建带前缀的新ID
    pub fn with_prefix(&self, prefix: &str) -> Result<Self, String> {
        Self::new(format!("{}{}", prefix, self.id))
    }

    /// 创建带后缀的新ID
    pub fn with_suffix(&self, suffix: &str) -> Result<Self, String> {
        Self::new(format!("{}{}", self.id, suffix))
    }

    /// 将TypedId转换为其他类型的TypedId
    ///
    /// 注意：这会进行类型转换，需要目标类型的验证
    pub fn cast_to<U: TypeMarker>(self) -> Result<TypedId<U>, String> {
        TypedId::<U>::new(self.id)
    }

    /// 安全的类型转换（不消费原对象）
    pub fn try_cast_to<U: TypeMarker>(&self) -> Result<TypedId<U>, String> {
        TypedId::<U>::new(self.id.clone())
    }
}

impl<T: TypeMarker> Debug for TypedId<T> {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("TypedId")
            .field("type", &T::TYPE_NAME)
            .field("id", &self.id)
            .finish()
    }
}

impl<T: TypeMarker> Display for TypedId<T> {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}({})", T::TYPE_NAME, self.id)
    }
}

impl<T: TypeMarker> Hash for TypedId<T> {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.id.hash(state);
        T::TYPE_NAME.hash(state);
    }
}

impl<T: TypeMarker> FromStr for TypedId<T> {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Self::try_from_str(s)
    }
}

impl<T: TypeMarker> From<TypedId<T>> for String {
    fn from(typed_id: TypedId<T>) -> Self {
        typed_id.id
    }
}

impl<T: TypeMarker> AsRef<str> for TypedId<T> {
    fn as_ref(&self) -> &str {
        &self.id
    }
}

impl<T: TypeMarker> Serialize for TypedId<T> {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // 序列化为简单的字符串
        self.id.serialize(serializer)
    }
}

impl<'de, T: TypeMarker> Deserialize<'de> for TypedId<T> {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        let id = String::deserialize(deserializer)?;
        Self::new(id).map_err(serde::de::Error::custom)
    }
}

/// TypedId的构建器，提供更灵活的创建方式
pub struct TypedIdBuilder<T: TypeMarker> {
    id: Option<String>,
    skip_validation: bool,
    _marker: PhantomData<T>,
}

impl<T: TypeMarker> TypedIdBuilder<T> {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            id: None,
            skip_validation: false,
            _marker: PhantomData,
        }
    }

    /// 设置ID字符串
    pub fn with_id(mut self, id: impl Into<String>) -> Self {
        self.id = Some(id.into());
        self
    }

    /// 跳过验证
    pub fn skip_validation(mut self) -> Self {
        self.skip_validation = true;
        self
    }

    /// 构建TypedId
    pub fn build(self) -> Result<TypedId<T>, String> {
        let id = self.id.ok_or_else(|| "ID is required".to_string())?;

        if self.skip_validation {
            Ok(TypedId::new_unchecked(id))
        } else {
            TypedId::new(id)
        }
    }
}

impl<T: TypeMarker> Default for TypedIdBuilder<T> {
    fn default() -> Self {
        Self::new()
    }
}

/// TypedId的工厂，用于批量创建和管理
pub struct TypedIdFactory<T: TypeMarker> {
    _marker: PhantomData<T>,
}

impl<T: TypeMarker> TypedIdFactory<T> {
    /// 创建新的工厂
    pub fn new() -> Self {
        Self {
            _marker: PhantomData,
        }
    }

    /// 批量创建TypedId
    pub fn create_batch(&self, ids: Vec<String>) -> Result<Vec<TypedId<T>>, Vec<String>> {
        let mut results = Vec::new();
        let mut errors = Vec::new();

        for id in ids {
            match TypedId::new(id.clone()) {
                Ok(typed_id) => results.push(typed_id),
                Err(error) => errors.push(format!("ID '{}': {}", id, error)),
            }
        }

        if errors.is_empty() {
            Ok(results)
        } else {
            Err(errors)
        }
    }

    /// 从前缀生成ID序列
    pub fn generate_sequence(&self, prefix: &str, count: usize) -> Result<Vec<TypedId<T>>, String> {
        let mut results = Vec::new();

        for i in 0..count {
            let id = format!("{}_{:04}", prefix, i);
            results.push(TypedId::new(id)?);
        }

        Ok(results)
    }

    /// 验证ID字符串而不创建TypedId
    pub fn validate(&self, id: &str) -> Result<(), String> {
        T::validate_id(id)
    }
}

impl<T: TypeMarker> Default for TypedIdFactory<T> {
    fn default() -> Self {
        Self::new()
    }
}

/// 类型ID集合，提供高效的查找和操作
#[derive(Debug, Clone)]
pub struct TypedIdSet<T: TypeMarker> {
    ids: std::collections::HashSet<TypedId<T>>,
}

impl<T: TypeMarker> TypedIdSet<T> {
    /// 创建新的空集合
    pub fn new() -> Self {
        Self {
            ids: std::collections::HashSet::new(),
        }
    }

    /// 从向量创建集合
    pub fn from_vec(ids: Vec<TypedId<T>>) -> Self {
        Self {
            ids: ids.into_iter().collect(),
        }
    }

    /// 插入ID
    pub fn insert(&mut self, id: TypedId<T>) -> bool {
        self.ids.insert(id)
    }

    /// 检查是否包含指定ID
    pub fn contains(&self, id: &TypedId<T>) -> bool {
        self.ids.contains(id)
    }

    /// 移除ID
    pub fn remove(&mut self, id: &TypedId<T>) -> bool {
        self.ids.remove(id)
    }

    /// 获取集合大小
    pub fn len(&self) -> usize {
        self.ids.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.ids.is_empty()
    }

    /// 清空集合
    pub fn clear(&mut self) {
        self.ids.clear();
    }

    /// 获取所有ID的向量
    pub fn to_vec(&self) -> Vec<TypedId<T>> {
        self.ids.iter().cloned().collect()
    }

    /// 集合并集
    pub fn union(&self, other: &Self) -> Self {
        Self {
            ids: self.ids.union(&other.ids).cloned().collect(),
        }
    }

    /// 集合交集
    pub fn intersection(&self, other: &Self) -> Self {
        Self {
            ids: self.ids.intersection(&other.ids).cloned().collect(),
        }
    }

    /// 集合差集
    pub fn difference(&self, other: &Self) -> Self {
        Self {
            ids: self.ids.difference(&other.ids).cloned().collect(),
        }
    }

    /// 过滤集合
    pub fn filter<F>(&self, predicate: F) -> Self
    where
        F: Fn(&TypedId<T>) -> bool,
    {
        Self {
            ids: self
                .ids
                .iter()
                .filter(|&id| predicate(id))
                .cloned()
                .collect(),
        }
    }

    /// 检查是否满足条件的ID
    pub fn any<F>(&self, predicate: F) -> bool
    where
        F: Fn(&TypedId<T>) -> bool,
    {
        self.ids.iter().any(predicate)
    }

    /// 检查所有ID是否都满足条件
    pub fn all<F>(&self, predicate: F) -> bool
    where
        F: Fn(&TypedId<T>) -> bool,
    {
        self.ids.iter().all(predicate)
    }
}

impl<T: TypeMarker> Default for TypedIdSet<T> {
    fn default() -> Self {
        Self::new()
    }
}

impl<T: TypeMarker> std::iter::IntoIterator for TypedIdSet<T> {
    type Item = TypedId<T>;
    type IntoIter = std::collections::hash_set::IntoIter<TypedId<T>>;

    fn into_iter(self) -> Self::IntoIter {
        self.ids.into_iter()
    }
}

impl<T: TypeMarker> std::iter::FromIterator<TypedId<T>> for TypedIdSet<T> {
    fn from_iter<I: IntoIterator<Item = TypedId<T>>>(iter: I) -> Self {
        Self {
            ids: iter.into_iter().collect(),
        }
    }
}

/// 内置的常用类型标记
pub mod markers {
    use super::TypeMarker;

    /// 通用字符串类型标记
    pub struct GenericString;
    impl TypeMarker for GenericString {
        const TYPE_NAME: &'static str = "String";
        const ALLOW_EMPTY: bool = true;
    }

    /// UUID类型标记
    pub struct UuidType;
    impl TypeMarker for UuidType {
        const TYPE_NAME: &'static str = "UUID";

        fn validate_id(id: &str) -> Result<(), String> {
            // 简单的UUID格式验证
            let uuid_regex = regex::Regex::new(
                r"^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$",
            )
            .unwrap();
            if uuid_regex.is_match(id) {
                Ok(())
            } else {
                Err(format!("Invalid UUID format: {}", id))
            }
        }

        fn normalize_id(id: String) -> String {
            id.to_lowercase()
        }
    }

    /// 数字ID类型标记
    pub struct NumericId;
    impl TypeMarker for NumericId {
        const TYPE_NAME: &'static str = "NumericId";

        fn validate_id(id: &str) -> Result<(), String> {
            if id.parse::<u64>().is_ok() {
                Ok(())
            } else {
                Err(format!("Invalid numeric ID: {}", id))
            }
        }
    }

    /// 标识符类型标记（只允许字母、数字、下划线）
    pub struct Identifier;
    impl TypeMarker for Identifier {
        const TYPE_NAME: &'static str = "Identifier";

        fn validate_id(id: &str) -> Result<(), String> {
            if id.is_empty() {
                return Err("Identifier cannot be empty".to_string());
            }

            if !id.chars().all(|c| c.is_alphanumeric() || c == '_') {
                return Err(format!("Identifier contains invalid characters: {}", id));
            }

            if id.chars().next().unwrap().is_numeric() {
                return Err(format!("Identifier cannot start with a number: {}", id));
            }

            Ok(())
        }

        fn normalize_id(id: String) -> String {
            id.to_lowercase()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::markers::*;
    use super::*;

    struct TestType;
    impl TypeMarker for TestType {
        const TYPE_NAME: &'static str = "Test";
        const TYPE_DESCRIPTION: Some<&'static str> = Some("Test type for unit tests");
    }

    struct StrictType;
    impl TypeMarker for StrictType {
        const TYPE_NAME: &'static str = "Strict";

        fn validate_id(id: &str) -> Result<(), String> {
            if id.len() < 3 {
                return Err("ID must be at least 3 characters".to_string());
            }
            Ok(())
        }
    }

    #[test]
    fn test_typed_id_creation() {
        let id = TypedId::<TestType>::new("test_id".to_string()).unwrap();
        assert_eq!(id.as_str(), "test_id");
        assert_eq!(id.type_name(), "Test");
        assert_eq!(id.type_description(), Some("Test type for unit tests"));
    }

    #[test]
    fn test_typed_id_validation() {
        // 有效ID
        assert!(TypedId::<TestType>::new("valid_id".to_string()).is_ok());

        // 无效ID（包含控制字符）
        assert!(TypedId::<TestType>::new("invalid\nid".to_string()).is_err());

        // 自定义验证
        assert!(TypedId::<StrictType>::new("ab".to_string()).is_err());
        assert!(TypedId::<StrictType>::new("valid".to_string()).is_ok());
    }

    #[test]
    fn test_typed_id_operations() {
        let id = TypedId::<TestType>::new("test_id".to_string()).unwrap();

        assert_eq!(id.len(), 7);
        assert!(!id.is_empty());
        assert!(id.contains("test"));
        assert!(id.starts_with("test"));
        assert!(id.ends_with("id"));

        let prefixed = id.with_prefix("prefix_").unwrap();
        assert_eq!(prefixed.as_str(), "prefix_test_id");

        let suffixed = id.with_suffix("_suffix").unwrap();
        assert_eq!(suffixed.as_str(), "test_id_suffix");
    }

    #[test]
    fn test_typed_id_builder() {
        let id = TypedIdBuilder::<TestType>::new()
            .with_id("builder_test")
            .build()
            .unwrap();
        assert_eq!(id.as_str(), "builder_test");

        let unchecked_id = TypedIdBuilder::<StrictType>::new()
            .with_id("x") // 太短，但跳过验证
            .skip_validation()
            .build()
            .unwrap();
        assert_eq!(unchecked_id.as_str(), "x");
    }

    #[test]
    fn test_typed_id_factory() {
        let factory = TypedIdFactory::<TestType>::new();

        // 批量创建
        let ids = vec!["id1".to_string(), "id2".to_string(), "id3".to_string()];
        let typed_ids = factory.create_batch(ids).unwrap();
        assert_eq!(typed_ids.len(), 3);

        // 生成序列
        let sequence = factory.generate_sequence("item", 3).unwrap();
        assert_eq!(sequence.len(), 3);
        assert_eq!(sequence[0].as_str(), "item_0000");
        assert_eq!(sequence[2].as_str(), "item_0002");

        // 验证
        assert!(factory.validate("valid_id").is_ok());
        assert!(factory.validate("invalid\nid").is_err());
    }

    #[test]
    fn test_typed_id_set() {
        let mut set = TypedIdSet::<TestType>::new();

        let id1 = TypedId::new("id1".to_string()).unwrap();
        let id2 = TypedId::new("id2".to_string()).unwrap();
        let id3 = TypedId::new("id3".to_string()).unwrap();

        assert!(set.insert(id1.clone()));
        assert!(set.insert(id2.clone()));
        assert!(!set.insert(id1.clone())); // 重复插入

        assert!(set.contains(&id1));
        assert!(set.contains(&id2));
        assert!(!set.contains(&id3));

        assert_eq!(set.len(), 2);

        // 过滤操作
        let filtered = set.filter(|id| id.as_str().ends_with("1"));
        assert_eq!(filtered.len(), 1);
        assert!(filtered.contains(&id1));
    }

    #[test]
    fn test_builtin_markers() {
        // 通用字符串
        let str_id = TypedId::<GenericString>::new("".to_string()).unwrap(); // 允许空
        assert_eq!(str_id.as_str(), "");

        // UUID（需要添加regex依赖，这里简化测试）
        // let uuid_id = TypedId::<UuidType>::new("550e8400-e29b-41d4-a716-************".to_string()).unwrap();

        // 数字ID
        let num_id = TypedId::<NumericId>::new("12345".to_string()).unwrap();
        assert_eq!(num_id.as_str(), "12345");
        assert!(TypedId::<NumericId>::new("abc".to_string()).is_err());

        // 标识符
        let ident_id = TypedId::<Identifier>::new("valid_identifier".to_string()).unwrap();
        assert_eq!(ident_id.as_str(), "valid_identifier");
        assert!(TypedId::<Identifier>::new("123invalid".to_string()).is_err());
        assert!(TypedId::<Identifier>::new("invalid-name".to_string()).is_err());
    }

    #[test]
    fn test_serde_serialization() {
        let id = TypedId::<TestType>::new("test_serde".to_string()).unwrap();

        // 序列化
        let json = serde_json::to_string(&id).unwrap();
        assert_eq!(json, "\"test_serde\"");

        // 反序列化
        let deserialized: TypedId<TestType> = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.as_str(), "test_serde");
    }

    #[test]
    fn test_type_safety() {
        let test_id = TypedId::<TestType>::new("test".to_string()).unwrap();
        let strict_id = TypedId::<StrictType>::new("strict".to_string()).unwrap();

        // 类型转换
        let converted = test_id.try_cast_to::<StrictType>().unwrap();
        assert_eq!(converted.type_name(), "Strict");

        // 确保不同类型的ID不相等（即使值相同）
        let test_id2 = TypedId::<TestType>::new("same".to_string()).unwrap();
        let strict_id2 = TypedId::<StrictType>::new("same".to_string()).unwrap();

        // 这里我们通过类型系统确保了类型安全
        // test_id2 != strict_id2 在编译时就会报错，因为类型不同
    }
}
