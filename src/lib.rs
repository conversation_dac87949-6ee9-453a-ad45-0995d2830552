//! # Game Engine
//!
//! 一个基于配置驱动的游戏引擎，支持动态类型系统、规则引擎和高性能缓存

// 配置引擎模块
pub mod config_engine;

// 游戏逻辑模块
pub mod attribute;
pub mod battle_unit;
pub mod equipment;
pub mod experience;
pub mod material;
pub mod shared;
pub mod skill;
pub mod status_panel;
pub mod world_map;
// 重新导出核心类型
pub use config_engine::*;

// 重新导出类型系统
pub use shared::types::*;

use crate::config_engine::{clear_global_engine, initialize_global_engine, ConfigurationEngine};
// 重新导出常用类型
pub use crate::{
    equipment::Equipment,
    material::{Material, MaterialType},
};

/// 游戏引擎结果类型
pub type Result<T> = std::result::Result<T, Box<dyn std::error::Error + Send + Sync>>;

/// 游戏引擎主入口
///
/// 集成配置引擎和游戏逻辑系统的主要接口
pub struct GameEngine {
    /// 配置引擎
    config_engine: std::sync::Arc<ConfigurationEngine>,
    /// 游戏状态
    game_state: GameState,
}

impl GameEngine {
    /// 创建新的游戏引擎
    pub async fn new(config_dir: &str) -> Result<Self> {
        // 创建配置引擎
        let config_engine = config_engine::ConfigEnginePresets::development(config_dir).await?;

        // 启动配置引擎
        config_engine.start().await?;

        // 初始化全局配置引擎
        let config_engine = std::sync::Arc::new(config_engine);
        initialize_global_engine((*config_engine).clone()).await?;

        Ok(Self {
            config_engine,
            game_state: GameState::new(),
        })
    }

    /// 使用自定义配置引擎创建游戏引擎
    pub async fn with_config_engine(config_engine: ConfigurationEngine) -> Result<Self> {
        // 启动配置引擎
        config_engine.start().await?;

        let config_engine = std::sync::Arc::new(config_engine);
        initialize_global_engine((*config_engine).clone()).await?;

        Ok(Self {
            config_engine,
            game_state: GameState::new(),
        })
    }

    /// 获取配置引擎
    pub fn config_engine(&self) -> &std::sync::Arc<ConfigurationEngine> {
        &self.config_engine
    }

    /// 获取游戏状态
    pub fn game_state(&self) -> &GameState {
        &self.game_state
    }

    /// 获取可变游戏状态
    pub fn game_state_mut(&mut self) -> &mut GameState {
        &mut self.game_state
    }

    /// 执行材料发现规则
    pub async fn discover_materials(
        &self,
        terrain_type: &str,
        tool_type: &str,
        player_level: i64,
        location_modifier: f64,
    ) -> Result<Vec<String>> {
        let context = config_engine::rules::RuleExecutionHelper::create_material_discovery_context(
            terrain_type,
            tool_type,
            player_level,
            location_modifier,
        );

        let result = self
            .config_engine
            .rule_engine()
            .execute_rule("material_discovery", &context)
            .await?;

        let materials =
            config_engine::rules::RuleExecutionHelper::extract_materials_from_result(&result);
        Ok(materials)
    }

    /// 获取工具需求
    pub async fn get_tool_requirements(
        &self,
        resource_type: &str,
        rarity_level: &str,
        environment: &str,
    ) -> Result<Vec<String>> {
        let context = config_engine::rules::RuleExecutionHelper::create_tool_requirements_context(
            resource_type,
            rarity_level,
            environment,
        );

        let result = self
            .config_engine
            .rule_engine()
            .execute_rule("tool_requirements", &context)
            .await?;

        let tools = config_engine::rules::RuleExecutionHelper::extract_tools_from_result(&result);
        Ok(tools)
    }

    /// 计算稀有度
    pub async fn calculate_rarity(
        &self,
        base_rarity: &str,
        location_modifier: f64,
        tool_modifier: f64,
        player_luck: f64,
    ) -> Result<String> {
        let context = config_engine::rules::RuleExecutionHelper::create_rarity_calculation_context(
            base_rarity,
            location_modifier,
            tool_modifier,
            player_luck,
        );

        let result = self
            .config_engine
            .rule_engine()
            .execute_rule("rarity_calculation", &context)
            .await?;

        if let Some(config_engine::ContextValue::String(rarity)) = result.get_output("final_rarity")
        {
            Ok(rarity.clone())
        } else {
            Ok("common".to_string())
        }
    }

    /// 重新加载配置
    pub async fn reload_config(&self) -> Result<()> {
        self.config_engine.reload_config().await?;
        log::info!("游戏配置已重新加载");
        Ok(())
    }

    /// 健康检查
    pub async fn health_check(&self) -> config_engine::EngineHealthReport {
        self.config_engine.health_check().await
    }

    /// 预热系统
    pub async fn warmup(&self, rules: Vec<String>) -> Result<()> {
        self.config_engine.warmup(rules).await?;
        Ok(())
    }

    /// 失效缓存
    pub async fn invalidate_cache(&self, pattern: &str) -> Result<u32> {
        let count = self.config_engine.invalidate_cache(pattern).await?;
        Ok(count)
    }

    /// 停止游戏引擎
    pub async fn shutdown(&self) -> Result<()> {
        self.config_engine.stop().await?;
        clear_global_engine().await;
        log::info!("游戏引擎已关闭");
        Ok(())
    }
}

/// 游戏状态
#[derive(Debug, Clone)]
pub struct GameState {
    /// 游戏是否运行中
    pub is_running: bool,
    /// 游戏开始时间
    pub start_time: std::time::Instant,
    /// 当前tick
    pub current_tick: u64,
    /// 玩家数量
    pub player_count: usize,
}

impl GameState {
    fn new() -> Self {
        Self {
            is_running: false,
            start_time: std::time::Instant::now(),
            current_tick: 0,
            player_count: 0,
        }
    }

    /// 启动游戏
    pub fn start(&mut self) {
        self.is_running = true;
        self.start_time = std::time::Instant::now();
        self.current_tick = 0;
        log::info!("游戏已启动");
    }

    /// 停止游戏
    pub fn stop(&mut self) {
        self.is_running = false;
        log::info!("游戏已停止");
    }

    /// 更新tick
    pub fn tick(&mut self) {
        if self.is_running {
            self.current_tick += 1;
        }
    }

    /// 添加玩家
    pub fn add_player(&mut self) {
        self.player_count += 1;
    }

    /// 移除玩家
    pub fn remove_player(&mut self) {
        if self.player_count > 0 {
            self.player_count -= 1;
        }
    }

    /// 获取运行时间
    pub fn uptime(&self) -> std::time::Duration {
        self.start_time.elapsed()
    }
}

/// 游戏引擎构建器
pub struct GameEngineBuilder {
    config_dir: Option<String>,
    config_engine_builder: Option<config_engine::ConfigurationEngineBuilder>,
    enable_debug: bool,
    warmup_rules: Vec<String>,
}

impl GameEngineBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config_dir: None,
            config_engine_builder: None,
            enable_debug: false,
            warmup_rules: Vec::new(),
        }
    }

    /// 设置配置目录
    pub fn with_config_dir<S: Into<String>>(mut self, config_dir: S) -> Self {
        self.config_dir = Some(config_dir.into());
        self
    }

    /// 设置自定义配置引擎构建器
    pub fn with_config_engine_builder(
        mut self,
        builder: config_engine::ConfigurationEngineBuilder,
    ) -> Self {
        self.config_engine_builder = Some(builder);
        self
    }

    /// 启用调试模式
    pub fn enable_debug(mut self) -> Self {
        self.enable_debug = true;
        self
    }

    /// 设置预热规则
    pub fn with_warmup_rules(mut self, rules: Vec<String>) -> Self {
        self.warmup_rules = rules;
        self
    }

    /// 构建游戏引擎
    pub async fn build(self) -> Result<GameEngine> {
        let config_engine = if let Some(builder) = self.config_engine_builder {
            builder.with_warmup_rules(self.warmup_rules).build().await?
        } else if let Some(config_dir) = self.config_dir {
            if self.enable_debug {
                config_engine::ConfigEnginePresets::development(&config_dir).await?
            } else {
                config_engine::ConfigEnginePresets::production(&config_dir).await?
            }
        } else {
            return Err("必须指定配置目录或自定义配置引擎构建器".into());
        };

        GameEngine::with_config_engine(config_engine).await
    }
}

impl Default for GameEngineBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_game_engine_creation() {
        // 注意：这个测试需要有效的配置目录
        // 在实际测试中，你需要设置一个测试配置目录
        /*
        let engine = GameEngine::new("test_config").await.unwrap();
        let health = engine.health_check().await;
        assert!(health.overall_healthy);
        */
    }

    #[test]
    fn test_game_state() {
        let mut state = GameState::new();
        assert!(!state.is_running);
        assert_eq!(state.current_tick, 0);
        assert_eq!(state.player_count, 0);

        state.start();
        assert!(state.is_running);

        state.tick();
        assert_eq!(state.current_tick, 1);

        state.add_player();
        assert_eq!(state.player_count, 1);

        state.remove_player();
        assert_eq!(state.player_count, 0);

        state.stop();
        assert!(!state.is_running);
    }

    #[test]
    fn test_game_engine_builder() {
        let builder = GameEngineBuilder::new()
            .with_config_dir("test_config")
            .enable_debug()
            .with_warmup_rules(vec!["test_rule".to_string()]);

        assert!(builder.config_dir.is_some());
        assert!(builder.enable_debug);
        assert_eq!(builder.warmup_rules.len(), 1);
    }
}
