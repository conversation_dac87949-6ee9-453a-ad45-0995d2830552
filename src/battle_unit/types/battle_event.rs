use crate::status_panel::status::CharacterState;
use crate::{CooldownTime, DamageType, Exp, Health, Mana};

/// 战斗事件枚举，记录战斗中发生的各种事件
#[derive(Debug, Clone)]
pub enum BattleEvent {
    /// 战斗开始事件
    BattleStart {
        participants: Vec<String>, // 参与者名称列表
    },

    /// 回合开始事件
    TurnStart {
        turn_number: u32,
        current_unit: String,
    },

    /// 攻击事件
    Attack {
        attacker: String,
        target: String,
        damage: Health,
        is_critical: bool,
        is_blocked: bool,
    },

    /// 技能释放事件
    SkillCast {
        caster: String,
        skill_name: String,
        targets: Vec<String>,
        mana_cost: Mana,
    },

    /// 伤害事件
    Damage {
        source: String,
        target: String,
        damage_type: DamageType,
        raw_damage: Health,
        final_damage: Health,
        remaining_hp: Health,
    },

    /// 治疗事件
    Heal {
        source: String,
        target: String,
        heal_amount: Health,
        remaining_hp: Health,
    },

    /// Buff应用事件
    BuffApplied {
        target: String,
        buff_name: String,
        duration: f32,
        stack_count: u32,
    },

    /// Buff移除事件
    BuffRemoved {
        target: String,
        buff_name: String,
        reason: BuffRemovalReason,
    },

    /// 状态效果应用事件
    StatusEffectApplied {
        target: String,
        effect: CharacterState,
    },

    /// 状态效果移除事件
    StatusEffectRemoved {
        target: String,
        effect_name: String,
        reason: StatusRemovalReason,
    },

    /// 死亡事件
    Death {
        unit: String,
        killer: Option<String>,
    },

    /// 经验获得事件
    ExpGained { unit: String, amount: Exp },

    /// 战斗结束事件
    BattleEnd {
        winner: BattleResult,
        duration_turns: u32,
    },

    /// 移动事件
    Movement {
        unit: String,
        from: (f32, f32),
        to: (f32, f32),
    },

    /// 冷却时间更新事件
    CooldownUpdate {
        unit: String,
        skill_name: String,
        remaining_cooldown: CooldownTime,
    },

    /// 通用消息事件
    Message(String),
}

/// Buff移除原因
#[derive(Debug, Clone)]
pub enum BuffRemovalReason {
    Expired,     // 自然过期
    Dispelled,   // 被驱散 (移除正面效果)
    Cleansed,    // 被净化 (移除负面效果)
    Overridden,  // 被覆盖
    Death,       // 死亡移除
    BatchClear,  // 批量清除
    TypeClear,   // 类型清除
    TimeExpired, // 时间过期
}

/// 状态移除原因
#[derive(Debug, Clone)]
pub enum StatusRemovalReason {
    Expired,     // 自然过期
    Cleansed,    // 被净化
    Overridden,  // 被覆盖
    Death,       // 死亡移除
    BatchClear,  // 批量清除
    TypeClear,   // 类型清除
    TimeExpired, // 时间过期
    Dispelled,   // 被驱散
}

/// 战斗结果
#[derive(Debug, Clone)]
pub enum BattleResult {
    PlayerVictory,  // 玩家胜利
    MonsterVictory, // 怪物胜利
    TeamAVictory,   // A队胜利
    TeamBVictory,   // B队胜利
    Draw,           // 平局
    Timeout,        // 超时
}

/// 战斗事件日志记录器
#[derive(Debug, Default)]
pub struct BattleLogger {
    events: Vec<BattleEvent>,
    turn_counter: u32,
}

impl BattleLogger {
    /// 创建新的战斗日志记录器
    pub fn new() -> Self {
        Self {
            events: Vec::new(),
            turn_counter: 0,
        }
    }

    /// 记录战斗事件
    pub fn log_event(&mut self, event: BattleEvent) {
        // 打印事件到控制台
        match &event {
            BattleEvent::BattleStart { participants } => {
                println!("🎮 === 战斗开始 ===");
                println!("   参与者: {}", participants.join(", "));
                println!();
            }

            BattleEvent::TurnStart {
                turn_number,
                current_unit,
            } => {
                self.turn_counter = *turn_number;
                println!("⏰ 第{}回合 - {}", turn_number, current_unit);
            }

            BattleEvent::Attack {
                attacker,
                target,
                damage,
                is_critical,
                is_blocked,
            } => {
                let crit_str = if *is_critical { " [暴击!]" } else { "" };
                let block_str = if *is_blocked { " [格挡]" } else { "" };
                println!(
                    "⚔️  {} 攻击 {} 造成 {} 点伤害{}{}",
                    attacker, target, damage, crit_str, block_str
                );
            }

            BattleEvent::SkillCast {
                caster,
                skill_name,
                targets,
                mana_cost,
            } => {
                println!(
                    "✨ {} 释放技能 [{}] (消耗 {} 法力) 目标: {}",
                    caster,
                    skill_name,
                    mana_cost,
                    targets.join(", ")
                );
            }

            BattleEvent::Damage {
                source,
                target,
                damage_type,
                raw_damage,
                final_damage,
                remaining_hp,
            } => {
                println!(
                    "💥 {} 对 {} 造成 {} 点{:?}伤害 (原始:{} 最终:{}) 剩余HP: {}",
                    source,
                    target,
                    final_damage,
                    damage_type,
                    raw_damage,
                    final_damage,
                    remaining_hp
                );
            }

            BattleEvent::Heal {
                source,
                target,
                heal_amount,
                remaining_hp,
            } => {
                println!(
                    "💚 {} 为 {} 恢复 {} 点生命值 剩余HP: {}",
                    source, target, heal_amount, remaining_hp
                );
            }

            BattleEvent::BuffApplied {
                target,
                buff_name,
                duration,
                stack_count,
            } => {
                let stack_str = if *stack_count > 1 {
                    format!(" x{}", stack_count)
                } else {
                    "".to_string()
                };
                println!(
                    "✨ {} 获得Buff [{}]{} 持续{:.1}秒",
                    target, buff_name, stack_str, duration
                );
            }

            BattleEvent::BuffRemoved {
                target,
                buff_name,
                reason,
            } => {
                println!("💨 {} 失去Buff [{}] 原因: {:?}", target, buff_name, reason);
            }

            BattleEvent::StatusEffectApplied { target, effect } => {
                println!("🔮 {} 受到状态效果: {}", target, effect);
            }

            BattleEvent::StatusEffectRemoved {
                target,
                effect_name,
                reason,
            } => {
                println!(
                    "🌟 {} 失去状态效果 [{}] 原因: {:?}",
                    target, effect_name, reason
                );
            }

            BattleEvent::Death { unit, killer } => match killer {
                Some(k) => println!("💀 {} 被 {} 击败", unit, k),
                None => println!("💀 {} 死亡", unit),
            },

            BattleEvent::ExpGained { unit, amount } => {
                println!("📈 {} 获得 {} 点经验值", unit, amount);
            }

            BattleEvent::BattleEnd {
                winner,
                duration_turns,
            } => {
                println!();
                println!("🏆 === 战斗结束 ===");
                println!("   结果: {:?}", winner);
                println!("   持续回合: {}", duration_turns);
                println!();
            }

            BattleEvent::Movement { unit, from, to } => {
                println!(
                    "🏃 {} 从 ({:.1}, {:.1}) 移动到 ({:.1}, {:.1})",
                    unit, from.0, from.1, to.0, to.1
                );
            }

            BattleEvent::CooldownUpdate {
                unit,
                skill_name,
                remaining_cooldown,
            } => {
                println!(
                    "⏳ {} 的技能 [{}] 冷却更新，剩余: {:.1}秒",
                    unit, skill_name, remaining_cooldown
                );
            }

            BattleEvent::Message(msg) => {
                println!("ℹ️ {}", msg);
            }
        }

        // 存储事件
        self.events.push(event);
    }

    /// 获取所有事件记录
    pub fn get_events(&self) -> &Vec<BattleEvent> {
        &self.events
    }

    /// 获取当前回合数
    pub fn get_current_turn(&self) -> u32 {
        self.turn_counter
    }

    /// 清空事件记录
    pub fn clear(&mut self) {
        self.events.clear();
        self.turn_counter = 0;
    }

    /// 获取战斗统计信息
    pub fn get_battle_stats(&self) -> BattleStats {
        let mut total_damage = 0;
        let mut total_healing = 0;
        let mut skill_casts = 0;
        let mut deaths = 0;

        for event in &self.events {
            match event {
                BattleEvent::Damage { final_damage, .. } => total_damage += final_damage,
                BattleEvent::Heal { heal_amount, .. } => total_healing += heal_amount,
                BattleEvent::SkillCast { .. } => skill_casts += 1,
                BattleEvent::Death { .. } => deaths += 1,
                _ => {}
            }
        }

        BattleStats {
            total_turns: self.turn_counter,
            total_damage,
            total_healing,
            skill_casts,
            deaths,
        }
    }
}

/// 战斗统计信息
#[derive(Debug, Clone)]
pub struct BattleStats {
    pub total_turns: u32,
    pub total_damage: Health,
    pub total_healing: Health,
    pub skill_casts: u32,
    pub deaths: u32,
}
