//! Buff/Debuff 系统模块，支持角色、怪物、召唤物等
//!
//! 本模块完全采用时长计时系统（Duration类型，以秒为单位），
//! 替代了之前的回合数计时机制，提供更精确的时间控制。

use crate::attribute::{AttributeSet, AttributeType};
use crate::shared::types::*;
use serde::{Deserialize, Serialize};
use std::ops::Sub;

/// Buff/Debuff 结构体
///
/// 支持基于时间的持续效果系统，包括：
/// - 属性修改
/// - 状态控制
/// - 触发效果
/// - 层数叠加
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Buff {
    /// Buff的唯一标识符
    pub id: ID,
    /// Buff名称
    pub name: String,
    /// 剩余持续时间（秒）
    pub duration: Duration,
    /// Buff类型（增益或减益）
    pub buff_type: BuffType,
    /// 属性加成效果
    pub attributes: Option<AttributeSet>,
    /// 免疫的属性类型
    pub immunities: Vec<AttributeType>,
    /// 易伤的属性类型
    pub vulnerability: Vec<AttributeType>,
    /// 触发条件
    pub trigger_condition: Option<TriggerCondition>,
    /// 触发效果
    pub trigger_effect: Option<TriggerEffect>,
    /// 当前叠加层数
    pub stack_count: u32,
    /// 最大叠加层数
    pub max_stack: u32,
    /// 是否为永久buff
    pub is_permanent: bool,
}

use std::str::FromStr;
use std::time::Duration;

/// Buff类型枚举
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize, Eq, Hash)]
pub enum BuffType {
    /// 增益效果
    Buff,
    /// 减益效果
    Debuff,
}

impl FromStr for BuffType {
    type Err = ();

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "buff" => Ok(BuffType::Buff),
            "debuff" => Ok(BuffType::Debuff),
            _ => Err(()),
        }
    }
}

/// 触发条件枚举
///
/// 定义了各种触发Buff效果的条件
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TriggerCondition {
    /// 回合开始时触发
    OnTurnStart,
    /// 受到伤害时触发
    OnDamageTaken,
    /// 攻击命中时触发
    OnAttackHit,
    /// 施放指定技能时触发
    OnSkillCast(u32),
}

/// 触发效果枚举
///
/// 定义了Buff触发时产生的各种效果，完全基于时长计时系统
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TriggerEffect {
    // === 基础效果 ===
    /// 治疗效果
    Heal(f64),
    /// 伤害效果
    Damage(f64),
    /// 添加Buff
    AddBuff(u32),
    /// 移除指定ID的buff
    RemoveBuff(u32),
    /// 修改指定属性值
    ModifyAttribute(AttributeType, f64),
    /// 重置指定技能的冷却时间
    ResetCooldown(u32),
    /// 获得护盾
    GainShield(f64),
    /// 反弹伤害
    ReflectDamage(f64),
    /// 吸血效果
    StealLife(f64),

    // === 控制状态效果（使用Duration类型的时长计时） ===
    /// 眩晕效果（持续时间）
    Stun(Duration),
    /// 沉默效果（持续时间）
    Silence(Duration),
    /// 定身效果（持续时间）
    Root(Duration),
    /// 嘲讽效果（持续时间）
    Taunt(Duration),
    /// 恐惧效果（持续时间）
    Fear(Duration),
    /// 致盲效果（持续时间）
    Blind(Duration),
    /// 冰冻效果（持续时间）
    Freeze(Duration),
    /// 无敌效果（持续时间）
    Invincible(Duration),
    /// 隐身效果（持续时间）
    Invisible(Duration),
    /// 潜行效果（持续时间）
    Stealth(Duration),
    /// 狂暴效果（持续时间）
    Berserk(Duration),
    /// 踉跄效果（持续时间）
    Stagger(Duration),
    /// 魅惑效果（持续时间）
    Charm(Duration),
    /// 混乱效果（持续时间）
    Confuse(Duration),
    /// 睡眠效果（持续时间）
    Sleep(Duration),
    /// 变形效果（持续时间）
    Transform(Duration),
    /// 变形效果（持续时间）
    Polymorph(Duration),
    /// 诅咒效果（持续时间）
    Curse(Duration),
    /// 诅咒效果（持续时间）
    Hex(Duration),
    /// 放逐效果（持续时间）
    Banish(Duration),
    /// 放逐效果（持续时间）
    Exile(Duration),
    /// 缴械效果（持续时间）
    Disarm(Duration),
    /// 禁用效果（持续时间）
    Disable(Duration),
    /// 封印效果（持续时间）
    Seal(Duration),
    /// 锁定效果（持续时间）
    Lock(Duration),
    /// 石化效果（持续时间）
    Petrify(Duration),
    /// 静止效果（持续时间）
    Stasis(Duration),

    // === 持续伤害效果（伤害值 + 持续时间） ===
    /// 中毒效果（伤害,持续时间）
    Poison(f64, Duration),
    /// 流血效果（伤害,持续时间）
    Bleed(f64, Duration),
    /// 燃烧效果（伤害,持续时间）
    Burn(f64, Duration),
    /// 怒气回复（数值,持续时间）
    Rage(f64, Duration),

    // === 持续回复效果（数值 + 持续时间） ===
    /// 持续回复（数值,持续时间）
    Regen(Mana, Duration),
    /// 法力回复（数值,持续时间）
    ManaRegen(Mana, Duration),
    /// 能量回复（数值,持续时间）
    EnergyRegen(Mana, Duration),

    // === 速度效果（比例修改） ===
    /// 减速效果（减速比例）
    Slow(Percent),
    /// 加速效果（加速比例）
    Haste(Percent),

    // === 计数效果（使用次数的效果） ===
    /// 连击效果（次数）
    Combo(u32),
    /// 反击效果（次数）
    Counter(u32),
    /// 闪避效果（次数）
    Dodge(u32),
    /// 格挡效果（次数）
    Block(u32),
    /// 招架效果（次数）
    Parry(u32),
    /// 反射效果（次数）
    Reflect(u32),
    /// 连锁效果（次数）
    Chain(u32),
    /// 穿透效果（次数）
    Pierce(u32),
    /// 弹射效果（次数）
    Bounce(u32),

    // === 空间距离效果 ===
    /// 溅射效果（范围）
    Splash(Distance),
    /// 爆炸效果（范围）
    Explode(Distance),
    /// 内爆效果（范围）
    Implode(Distance),
    /// 拉拽效果（距离）
    Pull(Distance),
    /// 击退效果（距离）
    Push(Distance),
    /// 击飞效果（高度）
    Knockup(Distance),
    /// 击退效果（距离）
    Knockback(Distance),

    // === 数值效果 ===
    /// 吸收效果（数值）
    Absorb(f64),
    /// 转化效果（比例）
    Convert(Percent),
    /// 增幅效果（比例）
    Amplify(Percent),
    /// 减幅效果（比例）
    Reduce(Percent),
}

impl Buff {
    /// 创建新的Buff
    pub fn new(id: ID, name: String, duration: Duration, buff_type: BuffType) -> Self {
        Self {
            id,
            name,
            duration,
            buff_type,
            attributes: None,
            immunities: Vec::new(),
            vulnerability: Vec::new(),
            trigger_condition: None,
            trigger_effect: None,
            stack_count: 1,
            max_stack: 1,
            is_permanent: false,
        }
    }

    /// 更新Buff的持续时间，如果Buff因此过期则返回true
    pub fn update_duration(&mut self, delta_time: Duration) -> bool {
        if self.is_permanent {
            return false;
        }
        self.duration = self.duration.sub(delta_time);
        self.is_expired()
    }

    /// 检查Buff是否已过期
    pub fn is_expired(&self) -> bool {
        !self.is_permanent && self.duration <= Duration::from_secs(0)
    }

    /// 检查Buff是否是激活状态
    pub fn is_active(&self) -> bool {
        !self.is_expired()
    }

    /// 获取剩余时间的百分比
    pub fn remaining_time_percent(&self, original_duration: Duration) -> f32 {
        if self.is_permanent || original_duration <= Duration::from_secs(0) {
            return 1.0;
        }

        self.duration
            .div_duration_f32(original_duration)
            .max(0.0)
            .min(1.0)
    }

    /// 检查是否可以叠加
    pub fn can_stack(&self) -> bool {
        self.stack_count < self.max_stack
    }

    /// 检查是否为减益效果
    pub fn is_debuff(&self) -> bool {
        matches!(self.buff_type, BuffType::Debuff)
    }

    /// 获取属性修改器
    pub fn attribute_modifiers(&self) -> Option<&AttributeSet> {
        self.attributes.as_ref()
    }

    /// 增加叠加层数
    pub fn add_stack(&mut self, count: u32) -> bool {
        if self.stack_count + count <= self.max_stack {
            self.stack_count += count;
            true
        } else {
            false
        }
    }

    /// 减少叠加层数
    pub fn remove_stack(&mut self, count: u32) -> bool {
        if self.stack_count >= count {
            self.stack_count -= count;
            self.stack_count > 0
        } else {
            self.stack_count = 0;
            false
        }
    }

    /// 刷新Buff持续时间
    pub fn refresh_duration(&mut self, new_duration: Duration) {
        if !self.is_permanent {
            self.duration = new_duration;
        }
    }

    /// 设置为永久Buff
    pub fn make_permanent(&mut self) {
        self.is_permanent = true;
    }
}

impl Default for BuffType {
    fn default() -> Self {
        BuffType::Buff
    }
}

/// Buff管理器
///
/// 负责管理实体身上的所有Buff效果
#[derive(Debug, Clone)]
pub struct BuffManager {
    /// 当前生效的Buff列表
    pub active_buffs: Vec<Buff>,
    /// Buff更新间隔（秒）
    pub update_interval: Duration,
    /// 上次更新时间
    pub last_update: Duration,
}

impl BuffManager {
    /// 创建新的Buff管理器
    pub fn new() -> Self {
        Self {
            active_buffs: Vec::new(),
            update_interval: Duration::from_secs_f64(0.1), // 每0.1秒更新一次
            last_update: Duration::from_secs_f64(0.0),
        }
    }

    /// 添加Buff
    pub fn add_buff(&mut self, buff: Buff) {
        // 检查是否已存在相同ID的Buff
        if let Some(existing) = self.active_buffs.iter_mut().find(|b| b.id == buff.id) {
            // 如果可以叠加，则增加层数
            if existing.add_stack(buff.stack_count) {
                existing.refresh_duration(buff.duration);
            }
        } else {
            // 添加新的Buff
            self.active_buffs.push(buff);
        }
    }

    /// 移除Buff
    pub fn remove_buff(&mut self, buff_id: ID) -> bool {
        if let Some(pos) = self.active_buffs.iter().position(|b| b.id == buff_id) {
            self.active_buffs.remove(pos);
            true
        } else {
            false
        }
    }

    /// 更新所有Buff
    pub fn update(&mut self, current_time: Duration) {
        let delta_time = current_time - self.last_update;

        if delta_time >= self.update_interval {
            // 更新所有Buff的持续时间
            self.active_buffs
                .retain_mut(|buff| buff.update_duration(delta_time));

            self.last_update = current_time;
        }
    }

    /// 获取指定类型的Buff
    pub fn get_buffs_by_type(&self, buff_type: BuffType) -> Vec<&Buff> {
        self.active_buffs
            .iter()
            .filter(|buff| buff.buff_type == buff_type)
            .collect()
    }

    /// 检查是否拥有指定ID的Buff
    pub fn has_buff(&self, buff_id: ID) -> bool {
        self.active_buffs.iter().any(|buff| buff.id == buff_id)
    }

    /// 清除所有Buff
    pub fn clear_all(&mut self) {
        self.active_buffs.clear();
    }

    /// 清除指定类型的Buff
    pub fn clear_buffs_by_type(&mut self, buff_type: BuffType) {
        self.active_buffs.retain(|buff| buff.buff_type != buff_type);
    }
}

impl Default for BuffManager {
    fn default() -> Self {
        Self::new()
    }
}
