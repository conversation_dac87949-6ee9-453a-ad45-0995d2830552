# 材料定义 (material_definition) 模块功能规划

本模块负责定义材料的核心属性、命名规则、品阶、形态、来源以及描述文本等基础设定。

## 1. 属性体系 (attribute_system)

*   **核心功能:**
    *   定义基础五行属性（金、木、水、火、土）。
    *   定义衍生/变异属性（如冰、雷、风、光、暗等，根据 `材料系统设计备忘录.md` 确认具体包含哪些）。
    *   提供属性的结构体或枚举表示。
    *   (后续扩展) 实现属性之间的生克关系逻辑（可能需要与 `material_interaction` 模块联动）。
    *   (后续扩展) 属性值的表示与计算方式。

## 2. 命名规范 (naming_convention_system)

*   **核心功能:**
    *   依据 `材料命名词库.md` 和 `材料系统设计备忘录.md` 中的规则，实现材料名称的程序化生成或校验。
    *   管理不同属性、品阶、形态等对应的命名词缀。
    *   处理多属性材料的命名逻辑。
    *   区分规则化命名与特殊材料的独特命名。
    *   提供接口供其他模块（如材料生成、展示时）调用以获取或验证材料名称。

## 3. 品阶与年份体系 (grade_and_age_system)

*   **核心功能:**
    *   定义材料的品阶划分（如凡品、灵品、仙品、神品等，参考 `材料系统设计备忘录.md`）。
    *   实现品阶的层级关系和表示方法。
    *   定义材料的年份体系，包括关键年份阈值。
    *   实现年份对材料属性、效果可能产生的影响逻辑（“阶段性质变”和“阶段内曲线增强”）。
    *   品阶与年份的关联机制，以及它们如何影响材料的稀有度和价值。
    *   提供接口查询材料的品阶和年份信息。

## 4. 形态与来源分类 (form_and_source_categorization)

*   **核心功能:**
    *   定义材料的物理形态（如矿石、液体、粉末、气体、植物、骨骼、精华、能量体等）。
    *   定义材料的来源分类（如怪物掉落、矿物采集、草药采摘、天地灵物、人工合成等）。
    *   提供结构体或枚举来表示不同的形态和来源。
    *   形态和来源如何影响材料的堆叠、存储和部分基础属性。
    *   允许根据形态和来源对材料进行筛选和分类。

## 5. 描述文本管理 (descriptive_text_management)

*   **核心功能:**
    *   根据 `材料系统设计备忘录.md` 中关于材料描述的指导原则，管理材料的描述文本。
    *   可能需要一个模板化或结构化的方式来存储和生成描述，确保风格统一。
    *   描述文本应能反映材料的属性、品阶、年份、来源和一些感官特征或潜在用途的暗示。
    *   提供接口根据材料ID或具体实例获取其描述文本。
    *   考虑多语言支持的可能性（如果未来有此需求）。
