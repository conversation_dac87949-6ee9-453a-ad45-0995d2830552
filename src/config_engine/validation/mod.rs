//! # 验证模块
//!
//! 提供全面的配置验证、数据完整性检查和业务规则验证
//!
//! ## 核心组件
//!
//! - `ValidationEngine`: 验证引擎，协调各种验证器
//! - `SchemaValidator`: Schema验证器，验证数据结构
//! - `BusinessRuleValidator`: 业务规则验证器
//! - `IntegrityChecker`: 完整性检查器，验证引用关系
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::validation::{ValidationEngine, ValidationRequest};
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let engine = ValidationEngine::new().await?;
//!
//! let request = ValidationRequest::new()
//!     .with_rule_validation(true)
//!     .with_type_validation(true)
//!     .with_integrity_check(true);
//!
//! let result = engine.validate(&config, &request).await?;
//!
//! if !result.is_valid() {
//!     for error in result.errors {
//!         println!("验证错误: {}", error);
//!     }
//! }
//! # Ok(())
//! # }
//! ```

// 核心验证模块
pub mod business;
pub mod engine;
pub mod integrity;
pub mod reporter;
pub mod schema;

// 插件化验证器模块
pub mod plugin;

// 重新导出主要类型
pub use business::{BusinessRule, BusinessRuleValidator};
pub use engine::{ValidationEngine, ValidationRequest};
pub use integrity::{IntegrityChecker, ReferenceCheck};
pub use reporter::{ValidationReport, ValidationReporter};
pub use schema::{SchemaRule, SchemaValidator};

// 重新导出插件化验证器类型
pub use plugin::{
    DirectoryPluginDiscoverer, PluginConfig, PluginDiscoverer, PluginHealthStatus,
    PluginManagerConfig, PluginStatistics, ValidatorCapability, ValidatorInfo, ValidatorPlugin,
    ValidatorPluginManager,
};

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    /// 是否验证通过
    pub is_valid: bool,
    /// 错误信息
    pub errors: Vec<ValidationError>,
    /// 警告信息
    pub warnings: Vec<ValidationWarning>,
    /// 信息提示
    pub info: Vec<ValidationInfo>,
    /// 验证统计
    pub stats: ValidationStats,
    /// 验证时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl ValidationResult {
    pub fn new() -> Self {
        Self {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
            info: Vec::new(),
            stats: ValidationStats::new(),
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn success() -> Self {
        Self::new()
    }

    pub fn with_error(mut self, error: ValidationError) -> Self {
        self.is_valid = false;
        self.errors.push(error);
        self
    }

    pub fn with_warning(mut self, warning: ValidationWarning) -> Self {
        self.warnings.push(warning);
        self
    }

    pub fn with_info(mut self, info: ValidationInfo) -> Self {
        self.info.push(info);
        self
    }

    pub fn add_error(&mut self, error: ValidationError) {
        self.is_valid = false;
        self.errors.push(error);
    }

    pub fn add_warning(&mut self, warning: ValidationWarning) {
        self.warnings.push(warning);
    }

    pub fn add_info(&mut self, info: ValidationInfo) {
        self.info.push(info);
    }

    pub fn merge(&mut self, other: ValidationResult) {
        if !other.is_valid {
            self.is_valid = false;
        }
        self.errors.extend(other.errors);
        self.warnings.extend(other.warnings);
        self.info.extend(other.info);
        self.stats.merge(other.stats);
    }

    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }

    pub fn has_warnings(&self) -> bool {
        !self.warnings.is_empty()
    }

    pub fn error_count(&self) -> usize {
        self.errors.len()
    }

    pub fn warning_count(&self) -> usize {
        self.warnings.len()
    }

    pub fn total_issues(&self) -> usize {
        self.errors.len() + self.warnings.len()
    }
}

impl Default for ValidationResult {
    fn default() -> Self {
        Self::new()
    }
}

/// 验证错误
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationError {
    /// 错误代码
    pub code: String,
    /// 错误消息
    pub message: String,
    /// 错误路径
    pub path: Option<String>,
    /// 错误来源
    pub source: ValidationSource,
    /// 错误严重性
    pub severity: ErrorSeverity,
    /// 错误上下文
    pub context: HashMap<String, String>,
}

impl ValidationError {
    pub fn new(code: String, message: String, source: ValidationSource) -> Self {
        Self {
            code,
            message,
            path: None,
            source,
            severity: ErrorSeverity::Error,
            context: HashMap::new(),
        }
    }

    pub fn with_path(mut self, path: String) -> Self {
        self.path = Some(path);
        self
    }

    pub fn with_severity(mut self, severity: ErrorSeverity) -> Self {
        self.severity = severity;
        self
    }

    pub fn with_context(mut self, key: String, value: String) -> Self {
        self.context.insert(key, value);
        self
    }
}

/// 验证警告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationWarning {
    /// 警告代码
    pub code: String,
    /// 警告消息
    pub message: String,
    /// 警告路径
    pub path: Option<String>,
    /// 警告来源
    pub source: ValidationSource,
    /// 警告严重性
    pub severity: ErrorSeverity,
    /// 建议操作
    pub suggestion: Option<String>,
}

impl ValidationWarning {
    pub fn new(code: String, message: String, source: ValidationSource) -> Self {
        Self {
            code,
            message,
            path: None,
            source,
            severity: ErrorSeverity::Warning,
            suggestion: None,
        }
    }

    pub fn with_path(mut self, path: String) -> Self {
        self.path = Some(path);
        self
    }

    pub fn with_suggestion(mut self, suggestion: String) -> Self {
        self.suggestion = Some(suggestion);
        self
    }
}

/// 验证信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationInfo {
    /// 信息代码
    pub code: String,
    /// 信息消息
    pub message: String,
    /// 信息路径
    pub path: Option<String>,
    /// 信息来源
    pub source: ValidationSource,
}

impl ValidationInfo {
    pub fn new(code: String, message: String, source: ValidationSource) -> Self {
        Self {
            code,
            message,
            path: None,
            source,
        }
    }

    pub fn with_path(mut self, path: String) -> Self {
        self.path = Some(path);
        self
    }
}

/// 验证来源
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ValidationSource {
    Schema,
    BusinessRule,
    Integrity,
    Type,
    Rule,
    Configuration,
    Custom(String),
}

/// 错误严重性
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum ErrorSeverity {
    Info = 1,
    Warning = 2,
    Error = 3,
    Critical = 4,
}

/// 验证统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationStats {
    /// 验证的规则数量
    pub rules_validated: usize,
    /// 验证的类型数量
    pub types_validated: usize,
    /// 验证的字段数量
    pub fields_validated: usize,
    /// 检查的引用数量
    pub references_checked: usize,
    /// 验证耗时（毫秒）
    pub validation_time_ms: u64,
    /// Schema验证耗时
    pub schema_validation_time_ms: u64,
    /// 业务规则验证耗时
    pub business_validation_time_ms: u64,
    /// 完整性检查耗时
    pub integrity_check_time_ms: u64,
}

impl ValidationStats {
    pub fn new() -> Self {
        Self {
            rules_validated: 0,
            types_validated: 0,
            fields_validated: 0,
            references_checked: 0,
            validation_time_ms: 0,
            schema_validation_time_ms: 0,
            business_validation_time_ms: 0,
            integrity_check_time_ms: 0,
        }
    }

    pub fn merge(&mut self, other: ValidationStats) {
        self.rules_validated += other.rules_validated;
        self.types_validated += other.types_validated;
        self.fields_validated += other.fields_validated;
        self.references_checked += other.references_checked;
        self.validation_time_ms += other.validation_time_ms;
        self.schema_validation_time_ms += other.schema_validation_time_ms;
        self.business_validation_time_ms += other.business_validation_time_ms;
        self.integrity_check_time_ms += other.integrity_check_time_ms;
    }
}

impl Default for ValidationStats {
    fn default() -> Self {
        Self::new()
    }
}

/// 验证选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationOptions {
    /// 启用Schema验证
    pub enable_schema_validation: bool,
    /// 启用业务规则验证
    pub enable_business_validation: bool,
    /// 启用完整性检查
    pub enable_integrity_check: bool,
    /// 启用类型验证
    pub enable_type_validation: bool,
    /// 停止于第一个错误
    pub fail_fast: bool,
    /// 最大错误数量
    pub max_errors: Option<usize>,
    /// 验证超时（毫秒）
    pub timeout_ms: Option<u64>,
    /// 并行验证
    pub parallel: bool,
    /// 详细模式
    pub verbose: bool,
}

impl Default for ValidationOptions {
    fn default() -> Self {
        Self {
            enable_schema_validation: true,
            enable_business_validation: true,
            enable_integrity_check: true,
            enable_type_validation: true,
            fail_fast: false,
            max_errors: None,
            timeout_ms: Some(30000), // 30秒
            parallel: true,
            verbose: false,
        }
    }
}

impl ValidationOptions {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn schema_only() -> Self {
        Self {
            enable_schema_validation: true,
            enable_business_validation: false,
            enable_integrity_check: false,
            enable_type_validation: false,
            ..Default::default()
        }
    }

    pub fn business_only() -> Self {
        Self {
            enable_schema_validation: false,
            enable_business_validation: true,
            enable_integrity_check: false,
            enable_type_validation: false,
            ..Default::default()
        }
    }

    pub fn integrity_only() -> Self {
        Self {
            enable_schema_validation: false,
            enable_business_validation: false,
            enable_integrity_check: true,
            enable_type_validation: false,
            ..Default::default()
        }
    }

    pub fn fail_fast(mut self) -> Self {
        self.fail_fast = true;
        self
    }

    pub fn with_max_errors(mut self, max: usize) -> Self {
        self.max_errors = Some(max);
        self
    }

    pub fn with_timeout(mut self, timeout_ms: u64) -> Self {
        self.timeout_ms = Some(timeout_ms);
        self
    }

    pub fn sequential(mut self) -> Self {
        self.parallel = false;
        self
    }

    pub fn verbose(mut self) -> Self {
        self.verbose = true;
        self
    }
}

/// 验证上下文
pub struct ValidationContext {
    /// 当前路径
    pub current_path: Vec<String>,
    /// 验证选项
    pub options: ValidationOptions,
    /// 累积的验证结果
    pub result: ValidationResult,
    /// 开始时间
    pub start_time: std::time::Instant,
}

impl ValidationContext {
    pub fn new(options: ValidationOptions) -> Self {
        Self {
            current_path: Vec::new(),
            options,
            result: ValidationResult::new(),
            start_time: std::time::Instant::now(),
        }
    }

    pub fn push_path(&mut self, path: String) {
        self.current_path.push(path);
    }

    pub fn pop_path(&mut self) {
        self.current_path.pop();
    }

    pub fn current_path_string(&self) -> String {
        self.current_path.join(".")
    }

    pub fn should_continue(&self) -> bool {
        if self.options.fail_fast && self.result.has_errors() {
            return false;
        }

        if let Some(max_errors) = self.options.max_errors {
            if self.result.error_count() >= max_errors {
                return false;
            }
        }

        if let Some(timeout_ms) = self.options.timeout_ms {
            let elapsed = self.start_time.elapsed();
            if elapsed.as_millis() > timeout_ms as u128 {
                return false;
            }
        }

        true
    }

    pub fn add_error(&mut self, error: ValidationError) {
        let mut error = error;
        if error.path.is_none() && !self.current_path.is_empty() {
            error.path = Some(self.current_path_string());
        }
        self.result.add_error(error);
    }

    pub fn add_warning(&mut self, warning: ValidationWarning) {
        let mut warning = warning;
        if warning.path.is_none() && !self.current_path.is_empty() {
            warning.path = Some(self.current_path_string());
        }
        self.result.add_warning(warning);
    }

    pub fn add_info(&mut self, info: ValidationInfo) {
        let mut info = info;
        if info.path.is_none() && !self.current_path.is_empty() {
            info.path = Some(self.current_path_string());
        }
        self.result.add_info(info);
    }
}

/// 验证器trait
pub trait Validator: Send + Sync {
    /// 验证类型名称
    fn validator_name(&self) -> &str;

    /// 验证数据
    fn validate(
        &self,
        context: &mut ValidationContext,
        data: &dyn ValidatableData,
    ) -> crate::config_engine::Result<()>;

    /// 验证器优先级
    fn priority(&self) -> i32 {
        0
    }

    /// 是否支持该数据类型
    fn supports(&self, data_type: &str) -> bool {
        true
    }
}

/// 可验证数据trait
pub trait ValidatableData: Send + Sync {
    /// 获取数据类型
    fn data_type(&self) -> String;

    /// 获取数据ID
    fn data_id(&self) -> String;

    /// 序列化为JSON值用于验证
    fn to_json_value(&self) -> serde_json::Value;

    /// 获取相关联的数据
    fn get_related_data(&self, relation: &str) -> Option<Box<dyn ValidatableData>>;
}

/// 验证规则定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationRuleDefinition {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: Option<String>,
    /// 适用的数据类型
    pub target_types: Vec<String>,
    /// 验证表达式
    pub expression: String,
    /// 错误消息模板
    pub error_message: String,
    /// 警告消息模板
    pub warning_message: Option<String>,
    /// 规则严重性
    pub severity: ErrorSeverity,
    /// 是否启用
    pub enabled: bool,
    /// 规则标签
    pub tags: HashMap<String, String>,
}

impl ValidationRuleDefinition {
    pub fn new(id: String, name: String, expression: String, error_message: String) -> Self {
        Self {
            id,
            name,
            description: None,
            target_types: Vec::new(),
            expression,
            error_message,
            warning_message: None,
            severity: ErrorSeverity::Error,
            enabled: true,
            tags: HashMap::new(),
        }
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn with_target_type(mut self, target_type: String) -> Self {
        self.target_types.push(target_type);
        self
    }

    pub fn with_warning_message(mut self, message: String) -> Self {
        self.warning_message = Some(message);
        self
    }

    pub fn with_severity(mut self, severity: ErrorSeverity) -> Self {
        self.severity = severity;
        self
    }

    pub fn disabled(mut self) -> Self {
        self.enabled = false;
        self
    }

    pub fn with_tag(mut self, key: String, value: String) -> Self {
        self.tags.insert(key, value);
        self
    }

    pub fn applies_to(&self, data_type: &str) -> bool {
        self.enabled
            && (self.target_types.is_empty() || self.target_types.contains(&data_type.to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validation_result_creation() {
        let result = ValidationResult::new();
        assert!(result.is_valid);
        assert!(result.errors.is_empty());
        assert!(result.warnings.is_empty());
    }

    #[test]
    fn test_validation_result_with_error() {
        let error = ValidationError::new(
            "E001".to_string(),
            "Test error".to_string(),
            ValidationSource::Schema,
        );

        let result = ValidationResult::new().with_error(error);
        assert!(!result.is_valid);
        assert_eq!(result.errors.len(), 1);
    }

    #[test]
    fn test_validation_result_merge() {
        let mut result1 = ValidationResult::new();
        let error = ValidationError::new(
            "E001".to_string(),
            "Error 1".to_string(),
            ValidationSource::Schema,
        );
        result1.add_error(error);

        let mut result2 = ValidationResult::new();
        let warning = ValidationWarning::new(
            "W001".to_string(),
            "Warning 1".to_string(),
            ValidationSource::BusinessRule,
        );
        result2.add_warning(warning);

        result1.merge(result2);
        assert!(!result1.is_valid);
        assert_eq!(result1.errors.len(), 1);
        assert_eq!(result1.warnings.len(), 1);
    }

    #[test]
    fn test_validation_options() {
        let options = ValidationOptions::schema_only();
        assert!(options.enable_schema_validation);
        assert!(!options.enable_business_validation);
        assert!(!options.enable_integrity_check);

        let options = ValidationOptions::new()
            .fail_fast()
            .with_max_errors(10)
            .verbose();
        assert!(options.fail_fast);
        assert_eq!(options.max_errors, Some(10));
        assert!(options.verbose);
    }

    #[test]
    fn test_validation_context() {
        let options = ValidationOptions::new().with_max_errors(5);
        let mut context = ValidationContext::new(options);

        context.push_path("rules".to_string());
        context.push_path("rule1".to_string());
        assert_eq!(context.current_path_string(), "rules.rule1");

        context.pop_path();
        assert_eq!(context.current_path_string(), "rules");

        // 测试错误计数
        for i in 0..3 {
            let error = ValidationError::new(
                format!("E{:03}", i),
                format!("Error {}", i),
                ValidationSource::Schema,
            );
            context.add_error(error);
        }

        assert!(context.should_continue());
        assert_eq!(context.result.error_count(), 3);
    }

    #[test]
    fn test_validation_rule_definition() {
        let rule = ValidationRuleDefinition::new(
            "R001".to_string(),
            "Non-empty ID".to_string(),
            "id.length > 0".to_string(),
            "ID cannot be empty".to_string(),
        )
        .with_description("Validates that ID is not empty".to_string())
        .with_target_type("Rule".to_string())
        .with_target_type("Type".to_string())
        .with_severity(ErrorSeverity::Critical);

        assert_eq!(rule.id, "R001");
        assert!(rule.applies_to("Rule"));
        assert!(rule.applies_to("Type"));
        assert!(!rule.applies_to("Other"));
        assert_eq!(rule.severity, ErrorSeverity::Critical);
    }

    #[test]
    fn test_error_severity_ordering() {
        assert!(ErrorSeverity::Critical > ErrorSeverity::Error);
        assert!(ErrorSeverity::Error > ErrorSeverity::Warning);
        assert!(ErrorSeverity::Warning > ErrorSeverity::Info);
    }

    #[test]
    fn test_validation_stats() {
        let mut stats = ValidationStats::new();
        stats.rules_validated = 10;
        stats.types_validated = 5;

        let other_stats = ValidationStats {
            rules_validated: 3,
            types_validated: 2,
            validation_time_ms: 100,
            ..ValidationStats::new()
        };

        stats.merge(other_stats);
        assert_eq!(stats.rules_validated, 13);
        assert_eq!(stats.types_validated, 7);
        assert_eq!(stats.validation_time_ms, 100);
    }
}
