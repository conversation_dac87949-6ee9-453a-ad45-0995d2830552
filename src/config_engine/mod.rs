//! # 配置引擎
//!
//! 提供完整的配置驱动开发解决方案，包括多源配置管理、规则引擎、缓存系统等功能

pub mod api;
pub mod cache;
pub mod config;
pub mod core;
pub mod error;
pub mod rules;
pub mod tools;
pub mod types;
pub mod validation;

// 重新导出核心类型
pub use api::{ConfigEngine, ConfigEngineBuilder};
pub use core::*;
pub use error::{ConfigEngineError, Result};

// 重新导出主要模块的关键类型
pub use config::{
    ConfigLoader, ConfigManager, ConfigSource, ConfigStats as ConfigStatus, ConfigValidator,
    RuleDefinition, TypeDefinition,
};

pub use rules::{
    ContextValue, ExecutionContext, ExecutionResult, ExecutionStatus, RuleEngine,
    RuleEngineBuilder, RuleExecutionFactory, RuleExecutionHelper, RuleExecutionRequest,
    RuleExecutionResponse,
};

pub use cache::{
    CacheBuilder, CacheConfig, CacheFactory, CacheHealthStatus, CacheManager, CachePresets,
    CacheResult, CacheStats,
};

use std::sync::Arc;
use tokio::sync::RwLock;

/// 配置引擎主入口
///
/// 提供统一的配置引擎访问接口，集成所有子系统
#[derive(Clone)]
pub struct ConfigurationEngine {
    /// 配置管理器
    config_manager: Arc<ConfigManager>,
    /// 规则引擎
    rule_engine: Arc<RuleEngine>,
    /// 缓存管理器
    cache_manager: Arc<CacheManager>,
    /// 引擎状态
    status: Arc<RwLock<EngineStatus>>,
}

impl ConfigurationEngine {
    /// 创建新的配置引擎实例
    pub async fn new(
        config_sources: Vec<Box<dyn ConfigSource>>,
        enable_hot_reload: bool,
    ) -> Result<Self> {
        // 创建缓存管理器
        let cache_manager = Arc::new(CacheManager::new(CacheConfig::default()).await?);

        // 创建配置管理器
        let config_manager = Arc::new(ConfigManager::new(config_sources, enable_hot_reload).await?);

        // 创建规则引擎
        let rule_engine = Arc::new(
            RuleEngine::new(
                Arc::clone(&config_manager),
                Arc::clone(&cache_manager),
                rules::RuleEngineConfig::default(),
            )
            .await?,
        );

        Ok(Self {
            config_manager,
            rule_engine,
            cache_manager,
            status: Arc::new(RwLock::new(EngineStatus::new())),
        })
    }

    /// 使用构建器创建配置引擎
    pub fn builder() -> ConfigurationEngineBuilder {
        ConfigurationEngineBuilder::new()
    }

    /// 获取配置管理器
    pub fn config_manager(&self) -> &Arc<ConfigManager> {
        &self.config_manager
    }

    /// 获取规则引擎
    pub fn rule_engine(&self) -> &Arc<RuleEngine> {
        &self.rule_engine
    }

    /// 获取缓存管理器
    pub fn cache_manager(&self) -> &Arc<CacheManager> {
        &self.cache_manager
    }

    /// 获取规则执行工厂
    pub fn rule_factory(&self) -> rules::RuleExecutionFactory {
        rules::RuleExecutionFactory::new(Arc::clone(&self.rule_engine))
    }

    /// 执行规则（快捷方法）
    pub async fn execute_rule(
        &self,
        rule_id: &str,
        input_data: std::collections::HashMap<String, ContextValue>,
    ) -> Result<ExecutionResult> {
        self.rule_factory().execute_quick(rule_id, input_data).await
    }

    /// 获取配置值（快捷方法）
    pub async fn get_config<T>(&self, key: &str) -> Option<T>
    where
        T: serde::de::DeserializeOwned,
    {
        self.config_manager.get_config(key).await
    }

    /// 获取类型定义（快捷方法）
    pub async fn get_type_definition(&self, type_id: &str) -> Option<TypeDefinition> {
        self.config_manager.get_type_definition(type_id).await
    }

    /// 获取规则定义（快捷方法）
    pub async fn get_rule_definition(&self, rule_id: &str) -> Option<RuleDefinition> {
        self.config_manager.get_rule_definition(rule_id).await
    }

    /// 失效缓存
    pub async fn invalidate_cache(&self, pattern: &str) -> Result<u32> {
        self.cache_manager.invalidate(pattern).await
    }

    /// 重新加载配置
    pub async fn reload_config(&self) -> Result<()> {
        self.config_manager.reload().await
    }

    /// 获取引擎状态
    pub async fn get_status(&self) -> EngineStatus {
        self.status.read().await.clone()
    }

    /// 健康检查
    pub async fn health_check(&self) -> EngineHealthReport {
        let config_status = self.config_manager.get_status().await;
        let rule_health = self.rule_engine.health_check().await;
        let cache_health = self.cache_manager.health_check().await;

        let overall_healthy =
            config_status.is_loaded && rule_health.healthy && cache_health.healthy;

        EngineHealthReport {
            overall_healthy,
            config_manager_status: config_status,
            rule_engine_health: rule_health,
            cache_manager_health: cache_health,
            uptime: self.get_uptime().await,
        }
    }

    /// 获取运行时间
    async fn get_uptime(&self) -> std::time::Duration {
        let status = self.status.read().await;
        status.start_time.elapsed()
    }

    /// 启动引擎
    pub async fn start(&self) -> Result<()> {
        let mut status = self.status.write().await;
        status.is_running = true;
        status.start_time = std::time::Instant::now();

        log::info!("配置引擎已启动");
        Ok(())
    }

    /// 停止引擎
    pub async fn stop(&self) -> Result<()> {
        let mut status = self.status.write().await;
        status.is_running = false;

        log::info!("配置引擎已停止");
        Ok(())
    }

    /// 预热系统
    pub async fn warmup(&self, warmup_rules: Vec<String>) -> Result<()> {
        log::info!("开始系统预热");

        // 预热缓存
        self.cache_manager.warmup(warmup_rules.clone()).await?;

        // 预热规则引擎
        self.rule_engine.warmup_rules(&warmup_rules).await?;

        log::info!("系统预热完成");
        Ok(())
    }
}

/// 配置引擎构建器
pub struct ConfigurationEngineBuilder {
    config_sources: Vec<Box<dyn ConfigSource>>,
    enable_hot_reload: bool,
    cache_config: Option<CacheConfig>,
    rule_engine_config: Option<rules::RuleEngineConfig>,
    warmup_rules: Vec<String>,
}

impl ConfigurationEngineBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config_sources: Vec::new(),
            enable_hot_reload: false,
            cache_config: None,
            rule_engine_config: None,
            warmup_rules: Vec::new(),
        }
    }

    /// 添加配置源
    pub fn add_config_source<S>(mut self, source: S) -> Self
    where
        S: ConfigSource + 'static,
    {
        self.config_sources.push(Box::new(source));
        self
    }

    /// 启用热重载
    pub fn enable_hot_reload(mut self) -> Self {
        self.enable_hot_reload = true;
        self
    }

    /// 设置缓存配置
    pub fn with_cache_config(mut self, config: CacheConfig) -> Self {
        self.cache_config = Some(config);
        self
    }

    /// 设置规则引擎配置
    pub fn with_rule_engine_config(mut self, config: rules::RuleEngineConfig) -> Self {
        self.rule_engine_config = Some(config);
        self
    }

    /// 设置预热规则
    pub fn with_warmup_rules(mut self, rules: Vec<String>) -> Self {
        self.warmup_rules = rules;
        self
    }

    /// 构建配置引擎
    pub async fn build(self) -> Result<ConfigurationEngine> {
        // 创建缓存管理器
        let cache_config = self.cache_config.unwrap_or_default();
        let cache_manager = Arc::new(CacheManager::new(cache_config).await?);

        // 创建配置管理器
        let config_manager =
            Arc::new(ConfigManager::new(self.config_sources, self.enable_hot_reload).await?);

        // 创建规则引擎
        let rule_engine_config = self.rule_engine_config.unwrap_or_default();
        let rule_engine = Arc::new(
            RuleEngine::new(
                Arc::clone(&config_manager),
                Arc::clone(&cache_manager),
                rule_engine_config,
            )
            .await?,
        );

        let engine = ConfigurationEngine {
            config_manager,
            rule_engine,
            cache_manager,
            status: Arc::new(RwLock::new(EngineStatus::new())),
        };

        // 预热系统
        if !self.warmup_rules.is_empty() {
            engine.warmup(self.warmup_rules).await?;
        }

        Ok(engine)
    }
}

impl Default for ConfigurationEngineBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 引擎状态
#[derive(Debug, Clone)]
pub struct EngineStatus {
    /// 是否运行中
    pub is_running: bool,
    /// 启动时间
    pub start_time: std::time::Instant,
    /// 版本信息
    pub version: String,
}

impl EngineStatus {
    fn new() -> Self {
        Self {
            is_running: false,
            start_time: std::time::Instant::now(),
            version: env!("CARGO_PKG_VERSION").to_string(),
        }
    }
}

/// 引擎健康报告
#[derive(Debug, Clone)]
pub struct EngineHealthReport {
    /// 整体健康状态
    pub overall_healthy: bool,
    /// 配置管理器状态
    pub config_manager_status: ConfigStatus,
    /// 规则引擎健康状态
    pub rule_engine_health: rules::RuleEngineHealth,
    /// 缓存管理器健康状态
    pub cache_manager_health: CacheHealthStatus,
    /// 运行时间
    pub uptime: std::time::Duration,
}

/// 便捷的预设配置
pub struct ConfigEnginePresets;

impl ConfigEnginePresets {
    /// 开发环境配置
    pub async fn development(config_dir: &str) -> Result<ConfigurationEngine> {
        ConfigurationEngine::builder()
            .add_config_source(config::FileSystemConfigSource::new(
                config_dir.to_string(),
                true,
            ))
            .add_config_source(config::EnvironmentConfigSource::new("GAME_"))
            .enable_hot_reload()
            .with_cache_config(CachePresets::development())
            .build()
            .await
    }

    /// 生产环境配置
    pub async fn production(config_dir: &str) -> Result<ConfigurationEngine> {
        ConfigurationEngine::builder()
            .add_config_source(config::FileSystemConfigSource::new(config_dir, false))
            .add_config_source(config::EnvironmentConfigSource::new("GAME_"))
            .with_cache_config(CachePresets::production())
            .build()
            .await
    }

    /// 高性能配置
    pub async fn high_performance(config_dir: &str) -> Result<ConfigurationEngine> {
        let rule_config = rules::RuleEngineConfig {
            max_concurrent_executions: 500,
            enable_tracing: false,
            ..Default::default()
        };

        ConfigurationEngine::builder()
            .add_config_source(config::FileSystemConfigSource::new(config_dir, false))
            .with_cache_config(CachePresets::high_performance())
            .with_rule_engine_config(rule_config)
            .build()
            .await
    }
}

/// 全局配置引擎实例管理
pub struct GlobalConfigEngine {
    instance: Arc<RwLock<Option<Arc<ConfigurationEngine>>>>,
}

impl GlobalConfigEngine {
    /// 创建全局实例管理器
    pub fn new() -> Self {
        Self {
            instance: Arc::new(RwLock::new(None)),
        }
    }

    /// 初始化全局实例
    pub async fn initialize(&self, engine: ConfigurationEngine) -> Result<()> {
        let mut instance = self.instance.write().await;
        *instance = Some(Arc::new(engine));
        Ok(())
    }

    /// 获取全局实例
    pub async fn get(&self) -> Option<Arc<ConfigurationEngine>> {
        self.instance.read().await.clone()
    }

    /// 检查是否已初始化
    pub async fn is_initialized(&self) -> bool {
        self.instance.read().await.is_some()
    }

    /// 清除全局实例
    pub async fn clear(&self) {
        let mut instance = self.instance.write().await;
        *instance = None;
    }
}

// 全局单例实例
lazy_static::lazy_static! {
    static ref GLOBAL_ENGINE: GlobalConfigEngine = GlobalConfigEngine::new();
}

/// 初始化全局配置引擎
pub async fn initialize_global_engine(engine: ConfigurationEngine) -> Result<()> {
    GLOBAL_ENGINE.initialize(engine).await
}

/// 获取全局配置引擎
pub async fn get_global_engine() -> Option<Arc<ConfigurationEngine>> {
    GLOBAL_ENGINE.get().await
}

/// 清除全局配置引擎
pub async fn clear_global_engine() {
    GLOBAL_ENGINE.clear().await
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_configuration_engine_creation() {
        let sources: Vec<Box<dyn ConfigSource>> = vec![];
        let engine = ConfigurationEngine::new(sources, false).await.unwrap();

        let status = engine.get_status().await;
        assert!(!status.is_running);
        assert!(!status.version.is_empty());
    }

    #[tokio::test]
    async fn test_configuration_engine_builder() {
        let engine = ConfigurationEngineBuilder::new()
            .enable_hot_reload()
            .with_cache_config(CachePresets::development())
            .build()
            .await
            .unwrap();

        let health = engine.health_check().await;
        assert!(health.cache_manager_health.healthy);
    }

    #[tokio::test]
    async fn test_configuration_engine_lifecycle() {
        let engine = ConfigurationEngineBuilder::new().build().await.unwrap();

        // 启动引擎
        engine.start().await.unwrap();
        let status = engine.get_status().await;
        assert!(status.is_running);

        // 停止引擎
        engine.stop().await.unwrap();
        let status = engine.get_status().await;
        assert!(!status.is_running);
    }

    #[tokio::test]
    async fn test_configuration_engine_health_check() {
        let engine = ConfigurationEngineBuilder::new().build().await.unwrap();

        let health = engine.health_check().await;
        // 新引擎应该是健康的
        assert!(health.cache_manager_health.healthy);
        assert!(health.rule_engine_health.healthy);
    }

    #[tokio::test]
    async fn test_global_engine_management() {
        // 确保开始时未初始化
        assert!(!GLOBAL_ENGINE.is_initialized().await);

        // 初始化全局引擎
        let engine = ConfigurationEngineBuilder::new().build().await.unwrap();
        initialize_global_engine(engine).await.unwrap();

        // 检查是否已初始化
        assert!(GLOBAL_ENGINE.is_initialized().await);

        // 获取全局引擎
        let global_engine = get_global_engine().await;
        assert!(global_engine.is_some());

        // 清除全局引擎
        clear_global_engine().await;
        assert!(!GLOBAL_ENGINE.is_initialized().await);
    }

    #[test]
    fn test_engine_status() {
        let status = EngineStatus::new();
        assert!(!status.is_running);
        assert!(!status.version.is_empty());
    }
}
