//! # 类型转换器模块
//!
//! 提供强大的类型转换功能，支持自定义转换规则和双向转换
//!
//! ## 核心功能
//!
//! - 自动类型转换
//! - 自定义转换规则
//! - 双向转换支持
//! - 转换链和管道
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::types::{TypeConverter, ConversionRule};
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let converter = TypeConverter::new();
//!
//! // 添加转换规则
//! let rule = ConversionRule::new()
//!     .with_name("material_to_string")
//!     .with_source_type("material")
//!     .with_target_type("string")
//!     .with_converter_function("to_lowercase");
//!
//! converter.add_rule(rule).await?;
//!
//! // 执行转换
//! let result = converter.convert("material", "IRON_ORE", "string").await?;
//! assert_eq!(result, "iron_ore");
//! # Ok(())
//! # }
//! ```

use crate::config_engine::{error::ConfigEngineError, Result};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fmt::Debug;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 转换规则定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: Option<String>,
    /// 源类型
    pub source_type: String,
    /// 目标类型
    pub target_type: String,
    /// 转换器函数名称
    pub converter_function: String,
    /// 转换优先级（数字越大优先级越高）
    pub priority: i32,
    /// 是否支持双向转换
    pub bidirectional: bool,
    /// 转换参数
    pub parameters: HashMap<String, String>,
    /// 是否启用
    pub enabled: bool,
    /// 转换成本（用于路径优化）
    pub cost: f64,
    /// 质量等级（转换结果的可信度）
    pub quality: ConversionQuality,
    /// 前置条件
    pub preconditions: Vec<String>,
    /// 后置处理
    pub post_processors: Vec<String>,
}

impl ConversionRule {
    /// 创建新的转换规则
    pub fn new() -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            name: String::new(),
            description: None,
            source_type: String::new(),
            target_type: String::new(),
            converter_function: String::new(),
            priority: 0,
            bidirectional: false,
            parameters: HashMap::new(),
            enabled: true,
            cost: 1.0,
            quality: ConversionQuality::High,
            preconditions: Vec::new(),
            post_processors: Vec::new(),
        }
    }

    /// 设置规则名称
    pub fn with_name(mut self, name: impl Into<String>) -> Self {
        self.name = name.into();
        self
    }

    /// 设置描述
    pub fn with_description(mut self, description: impl Into<String>) -> Self {
        self.description = Some(description.into());
        self
    }

    /// 设置源类型
    pub fn with_source_type(mut self, source_type: impl Into<String>) -> Self {
        self.source_type = source_type.into();
        self
    }

    /// 设置目标类型
    pub fn with_target_type(mut self, target_type: impl Into<String>) -> Self {
        self.target_type = target_type.into();
        self
    }

    /// 设置转换器函数
    pub fn with_converter_function(mut self, function: impl Into<String>) -> Self {
        self.converter_function = function.into();
        self
    }

    /// 设置优先级
    pub fn with_priority(mut self, priority: i32) -> Self {
        self.priority = priority;
        self
    }

    /// 启用双向转换
    pub fn bidirectional(mut self) -> Self {
        self.bidirectional = true;
        self
    }

    /// 添加参数
    pub fn with_parameter(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.parameters.insert(key.into(), value.into());
        self
    }

    /// 设置转换成本
    pub fn with_cost(mut self, cost: f64) -> Self {
        self.cost = cost;
        self
    }

    /// 设置质量等级
    pub fn with_quality(mut self, quality: ConversionQuality) -> Self {
        self.quality = quality;
        self
    }

    /// 添加前置条件
    pub fn with_precondition(mut self, condition: impl Into<String>) -> Self {
        self.preconditions.push(condition.into());
        self
    }

    /// 添加后置处理器
    pub fn with_post_processor(mut self, processor: impl Into<String>) -> Self {
        self.post_processors.push(processor.into());
        self
    }

    /// 禁用规则
    pub fn disabled(mut self) -> Self {
        self.enabled = false;
        self
    }

    /// 检查规则是否适用于指定的转换
    pub fn applies_to(&self, source_type: &str, target_type: &str) -> bool {
        if !self.enabled {
            return false;
        }

        // 直接匹配
        if self.source_type == source_type && self.target_type == target_type {
            return true;
        }

        // 双向转换匹配
        if self.bidirectional && self.source_type == target_type && self.target_type == source_type
        {
            return true;
        }

        false
    }

    /// 获取转换方向
    pub fn get_direction(
        &self,
        source_type: &str,
        target_type: &str,
    ) -> Option<ConversionDirection> {
        if self.source_type == source_type && self.target_type == target_type {
            Some(ConversionDirection::Forward)
        } else if self.bidirectional
            && self.source_type == target_type
            && self.target_type == source_type
        {
            Some(ConversionDirection::Backward)
        } else {
            None
        }
    }

    /// 验证规则的有效性
    pub fn validate(&self) -> Result<()> {
        if self.name.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: "Rule name cannot be empty".to_string(),
                details: Some(self.id.clone()),
            });
        }

        if self.source_type.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: "Source type cannot be empty".to_string(),
                details: Some(self.id.clone()),
            });
        }

        if self.target_type.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: "Target type cannot be empty".to_string(),
                details: Some(self.id.clone()),
            });
        }

        if self.converter_function.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: "Converter function cannot be empty".to_string(),
                details: Some(self.id.clone()),
            });
        }

        if self.source_type == self.target_type {
            return Err(ConfigEngineError::ValidationError {
                message: "Source and target types cannot be the same".to_string(),
                details: Some(format!("Type: {}", self.source_type)),
            });
        }

        if self.cost < 0.0 {
            return Err(ConfigEngineError::ValidationError {
                message: "Conversion cost cannot be negative".to_string(),
                details: Some(self.cost.to_string()),
            });
        }

        Ok(())
    }
}

impl Default for ConversionRule {
    fn default() -> Self {
        Self::new()
    }
}

/// 转换方向
#[derive(Debug, Clone, PartialEq)]
pub enum ConversionDirection {
    Forward,
    Backward,
}

/// 转换质量等级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum ConversionQuality {
    Low = 1,
    Medium = 2,
    High = 3,
    Lossless = 4,
}

/// 转换上下文
#[derive(Debug, Clone)]
pub struct ConversionContext {
    /// 源值
    pub source_value: String,
    /// 源类型
    pub source_type: String,
    /// 目标类型
    pub target_type: String,
    /// 转换参数
    pub parameters: HashMap<String, String>,
    /// 转换路径（用于链式转换）
    pub conversion_path: Vec<String>,
    /// 质量要求
    pub quality_requirement: Option<ConversionQuality>,
    /// 最大成本
    pub max_cost: Option<f64>,
}

impl ConversionContext {
    pub fn new(source_value: String, source_type: String, target_type: String) -> Self {
        Self {
            source_value,
            source_type,
            target_type,
            parameters: HashMap::new(),
            conversion_path: Vec::new(),
            quality_requirement: None,
            max_cost: None,
        }
    }

    pub fn with_parameter(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.parameters.insert(key.into(), value.into());
        self
    }

    pub fn with_quality_requirement(mut self, quality: ConversionQuality) -> Self {
        self.quality_requirement = Some(quality);
        self
    }

    pub fn with_max_cost(mut self, max_cost: f64) -> Self {
        self.max_cost = Some(max_cost);
        self
    }

    pub fn add_to_path(&mut self, step: String) {
        self.conversion_path.push(step);
    }

    pub fn get_parameter(&self, key: &str) -> Option<&String> {
        self.parameters.get(key)
    }
}

/// 转换结果
#[derive(Debug, Clone)]
pub struct ConversionResult {
    /// 转换后的值
    pub value: String,
    /// 实际使用的转换路径
    pub conversion_path: Vec<String>,
    /// 总转换成本
    pub total_cost: f64,
    /// 最低质量等级
    pub quality: ConversionQuality,
    /// 转换警告信息
    pub warnings: Vec<String>,
    /// 转换元数据
    pub metadata: HashMap<String, String>,
}

impl ConversionResult {
    pub fn new(value: String) -> Self {
        Self {
            value,
            conversion_path: Vec::new(),
            total_cost: 0.0,
            quality: ConversionQuality::Lossless,
            warnings: Vec::new(),
            metadata: HashMap::new(),
        }
    }

    pub fn with_path(mut self, path: Vec<String>) -> Self {
        self.conversion_path = path;
        self
    }

    pub fn with_cost(mut self, cost: f64) -> Self {
        self.total_cost = cost;
        self
    }

    pub fn with_quality(mut self, quality: ConversionQuality) -> Self {
        self.quality = quality;
        self
    }

    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }

    pub fn add_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
    }
}

/// 转换器函数接口
#[async_trait]
pub trait ConverterFunction: Send + Sync + Debug {
    /// 函数名称
    fn name(&self) -> &str;

    /// 执行转换
    async fn convert(
        &self,
        context: &ConversionContext,
        direction: ConversionDirection,
    ) -> Result<String>;

    /// 检查是否支持指定的转换
    fn supports(&self, source_type: &str, target_type: &str) -> bool;

    /// 获取转换的估计成本
    fn estimate_cost(&self, source_type: &str, target_type: &str) -> f64 {
        1.0
    }

    /// 获取转换质量
    fn get_quality(&self) -> ConversionQuality {
        ConversionQuality::High
    }
}

/// 字符串转换器函数
#[derive(Debug)]
pub struct StringConverterFunction;

#[async_trait]
impl ConverterFunction for StringConverterFunction {
    fn name(&self) -> &str {
        "string_converter"
    }

    async fn convert(
        &self,
        context: &ConversionContext,
        _direction: ConversionDirection,
    ) -> Result<String> {
        // 简单的字符串转换实现
        match context.target_type.as_str() {
            "lowercase" => Ok(context.source_value.to_lowercase()),
            "uppercase" => Ok(context.source_value.to_uppercase()),
            "trim" => Ok(context.source_value.trim().to_string()),
            "string" => Ok(context.source_value.clone()),
            _ => Ok(context.source_value.clone()),
        }
    }

    fn supports(&self, _source_type: &str, target_type: &str) -> bool {
        matches!(target_type, "string" | "lowercase" | "uppercase" | "trim")
    }

    fn estimate_cost(&self, _source_type: &str, _target_type: &str) -> f64 {
        0.1 // 字符串转换成本很低
    }

    fn get_quality(&self) -> ConversionQuality {
        ConversionQuality::Lossless
    }
}

/// 数字转换器函数
#[derive(Debug)]
pub struct NumberConverterFunction;

#[async_trait]
impl ConverterFunction for NumberConverterFunction {
    fn name(&self) -> &str {
        "number_converter"
    }

    async fn convert(
        &self,
        context: &ConversionContext,
        _direction: ConversionDirection,
    ) -> Result<String> {
        match context.target_type.as_str() {
            "integer" => {
                let num: f64 = context.source_value.parse().map_err(|_| {
                    ConfigEngineError::ConversionError {
                        message: format!("Cannot parse '{}' as number", context.source_value),
                        details: None,
                    }
                })?;
                Ok((num as i64).to_string())
            }
            "float" => {
                let num: f64 = context.source_value.parse().map_err(|_| {
                    ConfigEngineError::ConversionError {
                        message: format!("Cannot parse '{}' as number", context.source_value),
                        details: None,
                    }
                })?;
                Ok(num.to_string())
            }
            "string" => Ok(context.source_value.clone()),
            _ => Err(ConfigEngineError::ConversionError {
                message: format!("Unsupported conversion to type: {}", context.target_type),
                details: None,
            }),
        }
    }

    fn supports(&self, source_type: &str, target_type: &str) -> bool {
        (source_type == "number" || source_type == "integer" || source_type == "float")
            && (target_type == "integer" || target_type == "float" || target_type == "string")
    }

    fn estimate_cost(&self, _source_type: &str, target_type: &str) -> f64 {
        match target_type {
            "string" => 0.1,
            "integer" => 0.5,
            "float" => 0.3,
            _ => 1.0,
        }
    }

    fn get_quality(&self) -> ConversionQuality {
        ConversionQuality::High
    }
}

/// 类型转换器
pub struct TypeConverter {
    /// 转换规则
    rules: Arc<RwLock<Vec<ConversionRule>>>,
    /// 转换器函数
    functions: Arc<RwLock<HashMap<String, Box<dyn ConverterFunction>>>>,
    /// 转换缓存
    cache: Arc<RwLock<HashMap<String, ConversionResult>>>,
    /// 类型图（用于路径查找）
    type_graph: Arc<RwLock<HashMap<String, Vec<String>>>>,
}

impl TypeConverter {
    /// 创建新的类型转换器
    pub fn new() -> Self {
        let mut functions: HashMap<String, Box<dyn ConverterFunction>> = HashMap::new();
        functions.insert(
            "string_converter".to_string(),
            Box::new(StringConverterFunction),
        );
        functions.insert(
            "number_converter".to_string(),
            Box::new(NumberConverterFunction),
        );

        Self {
            rules: Arc::new(RwLock::new(Vec::new())),
            functions: Arc::new(RwLock::new(functions)),
            cache: Arc::new(RwLock::new(HashMap::new())),
            type_graph: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 添加转换规则
    pub async fn add_rule(&self, rule: ConversionRule) -> Result<()> {
        // 验证规则
        rule.validate()?;

        // 添加到规则列表
        {
            let mut rules = self.rules.write().await;
            rules.push(rule.clone());
            // 按优先级排序
            rules.sort_by(|a, b| b.priority.cmp(&a.priority));
        }

        // 更新类型图
        self.update_type_graph(&rule).await;

        // 清除相关缓存
        self.clear_cache_for_types(&rule.source_type, &rule.target_type)
            .await;

        Ok(())
    }

    /// 移除转换规则
    pub async fn remove_rule(&self, rule_id: &str) -> Result<bool> {
        let mut rules = self.rules.write().await;
        let initial_len = rules.len();
        rules.retain(|rule| rule.id != rule_id);

        if rules.len() < initial_len {
            // 重新构建类型图
            drop(rules);
            self.rebuild_type_graph().await;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 获取所有转换规则
    pub async fn get_rules(&self) -> Vec<ConversionRule> {
        let rules = self.rules.read().await;
        rules.clone()
    }

    /// 注册转换器函数
    pub async fn register_function(&self, function: Box<dyn ConverterFunction>) {
        let mut functions = self.functions.write().await;
        functions.insert(function.name().to_string(), function);
    }

    /// 执行单步转换
    pub async fn convert(
        &self,
        source_type: &str,
        source_value: &str,
        target_type: &str,
    ) -> Result<String> {
        let context = ConversionContext::new(
            source_value.to_string(),
            source_type.to_string(),
            target_type.to_string(),
        );

        let result = self.convert_with_context(&context).await?;
        Ok(result.value)
    }

    /// 使用上下文执行转换
    pub async fn convert_with_context(
        &self,
        context: &ConversionContext,
    ) -> Result<ConversionResult> {
        // 检查缓存
        let cache_key = format!(
            "{}:{}:{}",
            context.source_type, context.source_value, context.target_type
        );
        if let Some(cached_result) = self.get_cached_result(&cache_key).await {
            return Ok(cached_result);
        }

        // 尝试直接转换
        if let Some(result) = self.try_direct_conversion(context).await? {
            self.cache_result(cache_key, result.clone()).await;
            return Ok(result);
        }

        // 尝试路径转换
        if let Some(result) = self.try_path_conversion(context).await? {
            self.cache_result(cache_key, result.clone()).await;
            return Ok(result);
        }

        Err(ConfigEngineError::ConversionError {
            message: format!(
                "No conversion path found from '{}' to '{}'",
                context.source_type, context.target_type
            ),
            details: None,
        })
    }

    /// 批量转换
    pub async fn convert_batch(
        &self,
        conversions: Vec<ConversionContext>,
    ) -> Result<Vec<ConversionResult>> {
        let mut results = Vec::new();

        // 并行执行转换（这里简化为顺序执行）
        for context in conversions {
            let result = self.convert_with_context(&context).await?;
            results.push(result);
        }

        Ok(results)
    }

    /// 查找转换路径
    pub async fn find_conversion_path(
        &self,
        source_type: &str,
        target_type: &str,
    ) -> Option<Vec<String>> {
        if source_type == target_type {
            return Some(vec![source_type.to_string()]);
        }

        // 使用广度优先搜索找到最短路径
        let type_graph = self.type_graph.read().await;
        let mut queue = std::collections::VecDeque::new();
        let mut visited = std::collections::HashSet::new();
        let mut parent = std::collections::HashMap::new();

        queue.push_back(source_type.to_string());
        visited.insert(source_type.to_string());

        while let Some(current) = queue.pop_front() {
            if current == target_type {
                // 重建路径
                let mut path = Vec::new();
                let mut node = target_type.to_string();

                while let Some(prev) = parent.get(&node) {
                    path.push(node.clone());
                    node = prev.clone();
                }
                path.push(source_type.to_string());
                path.reverse();

                return Some(path);
            }

            if let Some(neighbors) = type_graph.get(&current) {
                for neighbor in neighbors {
                    if !visited.contains(neighbor) {
                        visited.insert(neighbor.clone());
                        parent.insert(neighbor.clone(), current.clone());
                        queue.push_back(neighbor.clone());
                    }
                }
            }
        }

        None
    }

    /// 获取支持的转换类型
    pub async fn get_supported_conversions(&self) -> HashMap<String, Vec<String>> {
        let type_graph = self.type_graph.read().await;
        type_graph.clone()
    }

    /// 验证转换是否可能
    pub async fn can_convert(&self, source_type: &str, target_type: &str) -> bool {
        self.find_conversion_path(source_type, target_type)
            .await
            .is_some()
    }

    /// 获取转换统计信息
    pub async fn get_statistics(&self) -> ConversionStatistics {
        let rules = self.rules.read().await;
        let functions = self.functions.read().await;
        let cache = self.cache.read().await;

        let total_rules = rules.len();
        let enabled_rules = rules.iter().filter(|r| r.enabled).count();
        let bidirectional_rules = rules.iter().filter(|r| r.bidirectional).count();

        ConversionStatistics {
            total_rules,
            enabled_rules,
            disabled_rules: total_rules - enabled_rules,
            bidirectional_rules,
            total_functions: functions.len(),
            cached_conversions: cache.len(),
        }
    }

    /// 清除所有缓存
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
    }

    /// 尝试直接转换
    async fn try_direct_conversion(
        &self,
        context: &ConversionContext,
    ) -> Result<Option<ConversionResult>> {
        let rules = self.rules.read().await;

        for rule in rules.iter() {
            if rule.applies_to(&context.source_type, &context.target_type) {
                if let Some(result) = self.execute_rule(rule, context).await? {
                    return Ok(Some(result));
                }
            }
        }

        Ok(None)
    }

    /// 尝试路径转换
    async fn try_path_conversion(
        &self,
        context: &ConversionContext,
    ) -> Result<Option<ConversionResult>> {
        if let Some(path) = self
            .find_conversion_path(&context.source_type, &context.target_type)
            .await
        {
            if path.len() <= 2 {
                return Ok(None); // 直接转换已经尝试过了
            }

            let mut current_value = context.source_value.clone();
            let mut total_cost = 0.0;
            let mut min_quality = ConversionQuality::Lossless;
            let mut warnings = Vec::new();

            // 逐步转换
            for window in path.windows(2) {
                let source_type = &window[0];
                let target_type = &window[1];

                let step_context = ConversionContext::new(
                    current_value.clone(),
                    source_type.clone(),
                    target_type.clone(),
                );

                if let Some(step_result) = self.try_direct_conversion(&step_context).await? {
                    current_value = step_result.value;
                    total_cost += step_result.total_cost;
                    if step_result.quality < min_quality {
                        min_quality = step_result.quality;
                    }
                    warnings.extend(step_result.warnings);
                } else {
                    return Ok(None); // 转换链中断
                }
            }

            let mut result = ConversionResult::new(current_value)
                .with_path(path)
                .with_cost(total_cost)
                .with_quality(min_quality);

            for warning in warnings {
                result.add_warning(warning);
            }

            return Ok(Some(result));
        }

        Ok(None)
    }

    /// 执行转换规则
    async fn execute_rule(
        &self,
        rule: &ConversionRule,
        context: &ConversionContext,
    ) -> Result<Option<ConversionResult>> {
        if !rule.enabled {
            return Ok(None);
        }

        // 检查前置条件
        if !self.check_preconditions(rule, context).await? {
            return Ok(None);
        }

        // 检查质量要求
        if let Some(required_quality) = &context.quality_requirement {
            if rule.quality < *required_quality {
                return Ok(None);
            }
        }

        // 检查成本限制
        if let Some(max_cost) = context.max_cost {
            if rule.cost > max_cost {
                return Ok(None);
            }
        }

        // 获取转换函数
        let functions = self.functions.read().await;
        if let Some(function) = functions.get(&rule.converter_function) {
            let direction = rule
                .get_direction(&context.source_type, &context.target_type)
                .ok_or_else(|| ConfigEngineError::ConversionError {
                    message: "Rule does not apply to this conversion".to_string(),
                    details: None,
                })?;

            let converted_value = function.convert(context, direction).await?;

            let mut result = ConversionResult::new(converted_value)
                .with_path(vec![
                    context.source_type.clone(),
                    context.target_type.clone(),
                ])
                .with_cost(rule.cost)
                .with_quality(rule.quality.clone());

            // 执行后置处理
            self.apply_post_processors(rule, &mut result).await?;

            Ok(Some(result))
        } else {
            Err(ConfigEngineError::ConversionError {
                message: format!("Converter function '{}' not found", rule.converter_function),
                details: Some(rule.id.clone()),
            })
        }
    }

    /// 检查前置条件
    async fn check_preconditions(
        &self,
        _rule: &ConversionRule,
        _context: &ConversionContext,
    ) -> Result<bool> {
        // 简化实现：总是返回true
        // 实际实现应该评估前置条件表达式
        Ok(true)
    }

    /// 应用后置处理器
    async fn apply_post_processors(
        &self,
        _rule: &ConversionRule,
        _result: &mut ConversionResult,
    ) -> Result<()> {
        // 简化实现：什么都不做
        // 实际实现应该执行后置处理逻辑
        Ok(())
    }

    /// 更新类型图
    async fn update_type_graph(&self, rule: &ConversionRule) {
        let mut type_graph = self.type_graph.write().await;

        // 添加正向连接
        type_graph
            .entry(rule.source_type.clone())
            .or_insert_with(Vec::new)
            .push(rule.target_type.clone());

        // 如果是双向转换，添加反向连接
        if rule.bidirectional {
            type_graph
                .entry(rule.target_type.clone())
                .or_insert_with(Vec::new)
                .push(rule.source_type.clone());
        }
    }

    /// 重新构建类型图
    async fn rebuild_type_graph(&self) {
        let mut type_graph = self.type_graph.write().await;
        type_graph.clear();

        let rules = self.rules.read().await;
        for rule in rules.iter() {
            if rule.enabled {
                // 添加正向连接
                type_graph
                    .entry(rule.source_type.clone())
                    .or_insert_with(Vec::new)
                    .push(rule.target_type.clone());

                // 如果是双向转换，添加反向连接
                if rule.bidirectional {
                    type_graph
                        .entry(rule.target_type.clone())
                        .or_insert_with(Vec::new)
                        .push(rule.source_type.clone());
                }
            }
        }
    }

    /// 获取缓存的转换结果
    async fn get_cached_result(&self, cache_key: &str) -> Option<ConversionResult> {
        let cache = self.cache.read().await;
        cache.get(cache_key).cloned()
    }

    /// 缓存转换结果
    async fn cache_result(&self, cache_key: String, result: ConversionResult) {
        let mut cache = self.cache.write().await;
        cache.insert(cache_key, result);

        // 简单的缓存清理策略
        if cache.len() > 1000 {
            // 清除一半的缓存项
            let keys: Vec<String> = cache.keys().take(500).cloned().collect();
            for key in keys {
                cache.remove(&key);
            }
        }
    }

    /// 清除指定类型的缓存
    async fn clear_cache_for_types(&self, source_type: &str, target_type: &str) {
        let mut cache = self.cache.write().await;
        cache.retain(|key, _| {
            !key.starts_with(&format!("{}:", source_type))
                && !key.ends_with(&format!(":{}", target_type))
        });
    }
}

impl Default for TypeConverter {
    fn default() -> Self {
        Self::new()
    }
}

/// 转换统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionStatistics {
    pub total_rules: usize,
    pub enabled_rules: usize,
    pub disabled_rules: usize,
    pub bidirectional_rules: usize,
    pub total_functions: usize,
    pub cached_conversions: usize,
}

/// 类型转换器构建器
pub struct TypeConverterBuilder {
    rules: Vec<ConversionRule>,
    functions: Vec<Box<dyn ConverterFunction>>,
}

impl TypeConverterBuilder {
    pub fn new() -> Self {
        Self {
            rules: Vec::new(),
            functions: Vec::new(),
        }
    }

    pub fn with_rule(mut self, rule: ConversionRule) -> Self {
        self.rules.push(rule);
        self
    }

    pub fn with_function(mut self, function: Box<dyn ConverterFunction>) -> Self {
        self.functions.push(function);
        self
    }

    pub async fn build(self) -> Result<TypeConverter> {
        let converter = TypeConverter::new();

        // 注册转换器函数
        for function in self.functions {
            converter.register_function(function).await;
        }

        // 添加转换规则
        for rule in self.rules {
            converter.add_rule(rule).await?;
        }

        Ok(converter)
    }
}

impl Default for TypeConverterBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_conversion_rule_creation() {
        let rule = ConversionRule::new()
            .with_name("test_rule")
            .with_source_type("string")
            .with_target_type("uppercase")
            .with_converter_function("string_converter")
            .with_priority(10)
            .bidirectional();

        assert_eq!(rule.name, "test_rule");
        assert_eq!(rule.source_type, "string");
        assert_eq!(rule.target_type, "uppercase");
        assert!(rule.bidirectional);
        assert!(rule.applies_to("string", "uppercase"));
        assert!(rule.applies_to("uppercase", "string"));
        assert!(rule.validate().is_ok());
    }

    #[tokio::test]
    async fn test_string_converter_function() {
        let function = StringConverterFunction;
        let context = ConversionContext::new(
            "Hello World".to_string(),
            "string".to_string(),
            "lowercase".to_string(),
        );

        let result = function
            .convert(&context, ConversionDirection::Forward)
            .await
            .unwrap();
        assert_eq!(result, "hello world");

        let context = ConversionContext::new(
            "hello world".to_string(),
            "string".to_string(),
            "uppercase".to_string(),
        );

        let result = function
            .convert(&context, ConversionDirection::Forward)
            .await
            .unwrap();
        assert_eq!(result, "HELLO WORLD");
    }

    #[tokio::test]
    async fn test_number_converter_function() {
        let function = NumberConverterFunction;
        let context = ConversionContext::new(
            "123.456".to_string(),
            "float".to_string(),
            "integer".to_string(),
        );

        let result = function
            .convert(&context, ConversionDirection::Forward)
            .await
            .unwrap();
        assert_eq!(result, "123");

        let context = ConversionContext::new(
            "123".to_string(),
            "integer".to_string(),
            "string".to_string(),
        );

        let result = function
            .convert(&context, ConversionDirection::Forward)
            .await
            .unwrap();
        assert_eq!(result, "123");
    }

    #[tokio::test]
    async fn test_type_converter() {
        let converter = TypeConverter::new();

        let rule = ConversionRule::new()
            .with_name("string_to_lowercase")
            .with_source_type("string")
            .with_target_type("lowercase")
            .with_converter_function("string_converter");

        converter.add_rule(rule).await.unwrap();

        let result = converter
            .convert("string", "Hello World", "lowercase")
            .await
            .unwrap();
        assert_eq!(result, "hello world");
    }

    #[tokio::test]
    async fn test_conversion_with_context() {
        let converter = TypeConverter::new();

        let rule = ConversionRule::new()
            .with_source_type("string")
            .with_target_type("uppercase")
            .with_converter_function("string_converter")
            .with_cost(0.5)
            .with_quality(ConversionQuality::Lossless);

        converter.add_rule(rule).await.unwrap();

        let context = ConversionContext::new(
            "hello".to_string(),
            "string".to_string(),
            "uppercase".to_string(),
        )
        .with_quality_requirement(ConversionQuality::High);

        let result = converter.convert_with_context(&context).await.unwrap();
        assert_eq!(result.value, "HELLO");
        assert_eq!(result.total_cost, 0.5);
        assert_eq!(result.quality, ConversionQuality::Lossless);
    }

    #[tokio::test]
    async fn test_path_conversion() {
        let converter = TypeConverter::new();

        // 添加转换链：A -> B -> C
        let rule1 = ConversionRule::new()
            .with_source_type("A")
            .with_target_type("B")
            .with_converter_function("string_converter");

        let rule2 = ConversionRule::new()
            .with_source_type("B")
            .with_target_type("C")
            .with_converter_function("string_converter");

        converter.add_rule(rule1).await.unwrap();
        converter.add_rule(rule2).await.unwrap();

        // 查找转换路径
        let path = converter.find_conversion_path("A", "C").await.unwrap();
        assert_eq!(path, vec!["A", "B", "C"]);

        // 检查转换能力
        assert!(converter.can_convert("A", "C").await);
        assert!(!converter.can_convert("C", "A").await);
    }

    #[tokio::test]
    async fn test_bidirectional_conversion() {
        let converter = TypeConverter::new();

        let rule = ConversionRule::new()
            .with_source_type("string")
            .with_target_type("uppercase")
            .with_converter_function("string_converter")
            .bidirectional();

        converter.add_rule(rule).await.unwrap();

        // 正向转换
        let result = converter
            .convert("string", "hello", "uppercase")
            .await
            .unwrap();
        assert_eq!(result, "HELLO");

        // 反向转换
        let result = converter
            .convert("uppercase", "HELLO", "string")
            .await
            .unwrap();
        assert_eq!(result, "HELLO"); // 这里简化实现，实际应该有专门的反向逻辑
    }

    #[tokio::test]
    async fn test_batch_conversion() {
        let converter = TypeConverter::new();

        let rule = ConversionRule::new()
            .with_source_type("string")
            .with_target_type("lowercase")
            .with_converter_function("string_converter");

        converter.add_rule(rule).await.unwrap();

        let conversions = vec![
            ConversionContext::new(
                "HELLO".to_string(),
                "string".to_string(),
                "lowercase".to_string(),
            ),
            ConversionContext::new(
                "WORLD".to_string(),
                "string".to_string(),
                "lowercase".to_string(),
            ),
        ];

        let results = converter.convert_batch(conversions).await.unwrap();
        assert_eq!(results.len(), 2);
        assert_eq!(results[0].value, "hello");
        assert_eq!(results[1].value, "world");
    }

    #[tokio::test]
    async fn test_conversion_statistics() {
        let converter = TypeConverter::new();

        let rule1 = ConversionRule::new()
            .with_source_type("A")
            .with_target_type("B")
            .with_converter_function("string_converter");

        let rule2 = ConversionRule::new()
            .with_source_type("B")
            .with_target_type("C")
            .with_converter_function("string_converter")
            .bidirectional()
            .disabled();

        converter.add_rule(rule1).await.unwrap();
        converter.add_rule(rule2).await.unwrap();

        let stats = converter.get_statistics().await;
        assert_eq!(stats.total_rules, 2);
        assert_eq!(stats.enabled_rules, 1);
        assert_eq!(stats.disabled_rules, 1);
        assert_eq!(stats.bidirectional_rules, 1);
    }

    #[tokio::test]
    async fn test_converter_builder() {
        let rule = ConversionRule::new()
            .with_source_type("test")
            .with_target_type("result")
            .with_converter_function("string_converter");

        let converter = TypeConverterBuilder::new()
            .with_rule(rule)
            .build()
            .await
            .unwrap();

        assert!(converter.can_convert("test", "result").await);
    }

    #[tokio::test]
    async fn test_conversion_caching() {
        let converter = TypeConverter::new();

        let rule = ConversionRule::new()
            .with_source_type("string")
            .with_target_type("lowercase")
            .with_converter_function("string_converter");

        converter.add_rule(rule).await.unwrap();

        // 第一次转换
        let result1 = converter
            .convert("string", "HELLO", "lowercase")
            .await
            .unwrap();
        assert_eq!(result1, "hello");

        // 第二次转换（应该从缓存返回）
        let result2 = converter
            .convert("string", "HELLO", "lowercase")
            .await
            .unwrap();
        assert_eq!(result2, "hello");

        // 验证缓存工作
        let stats = converter.get_statistics().await;
        assert!(stats.cached_conversions > 0);
    }
}
