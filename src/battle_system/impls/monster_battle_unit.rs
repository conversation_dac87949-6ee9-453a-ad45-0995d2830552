
use crate::battle_system::simplified_battle_traits::*;
use crate::monster::Monster;
use crate::shared::types::{self as shared_types, *};
use crate::shared::GameResult;
use crate::skill::buff::Buff;
use crate::world_map::position::Position as WorldMapPosition;
use crate::world_map::domain::spatial::WorldLayer;

/// 包装器结构体，用于将 `Monster` 适配到简化的战斗trait。
pub struct SimplifiedMonster {
    pub monster: Monster,
}

impl SimplifiedMonster {
    pub fn new(monster: Monster) -> Self {
        Self { monster }
    }
}

fn to_shared_pos(pos: WorldMapPosition) -> shared_types::Position {
    shared_types::Position { x: pos.x as f32, y: pos.y as f32 }
}

fn to_world_pos(pos: shared_types::Position) -> WorldMapPosition {
    WorldMapPosition::new_2d(pos.x as i32, pos.y as i32, WorldLayer::default())
}


impl BattleEntity for SimplifiedMonster {
    fn entity_id(&self) -> BattleUnitId {
        BattleUnitId::Monster(self.monster.id)
    }

    fn display_name(&self) -> &str {
        &self.monster.profile.name
    }

    fn level(&self) -> Level {
        self.monster.profile.level
    }

    fn position(&self) -> shared_types::Position {
        to_shared_pos(self.monster.position)
    }

    fn set_position(&mut self, position: shared_types::Position) -> GameResult<()> {
        self.monster.position = to_world_pos(position);
        Ok(())
    }
}

impl LifeForce for SimplifiedMonster {
    fn current_health(&self) -> Health {
        self.monster.hp as Health
    }

    fn max_health(&self) -> Health {
        self.monster.max_hp as Health
    }

    fn take_damage(&mut self, damage: Health) -> GameResult<DamageResult> {
        let old_hp = self.monster.hp;
        self.monster.hp = (self.monster.hp - damage as f64).max(0.0);
        let actual_damage = (old_hp - self.monster.hp) as Health;
        Ok(DamageResult::new(actual_damage, self.monster.hp as Health, false, false))
    }

    fn heal(&mut self, amount: Health) -> GameResult<HealResult> {
        let old_hp = self.monster.hp;
        self.monster.hp = (self.monster.hp + amount as f64).min(self.monster.max_hp);
        let actual_heal = (self.monster.hp - old_hp) as Health;
        Ok(HealResult::new(actual_heal, false))
    }
}

impl CombatCapable for SimplifiedMonster {
    fn attack_power(&self) -> Attack {
        self.monster.profile.level as Attack * 2 + 10
    }

    fn defense_power(&self) -> Defense {
        self.monster.profile.level as Defense + 5
    }

    fn movement_speed(&self) -> Speed {
        5.0 // 默认速度
    }

    fn attack_range(&self) -> Range {
        1.0 // 默认近战范围
    }
}

impl SkillUser for SimplifiedMonster {
    fn learned_skills(&self) -> Vec<SkillId> {
        self.monster.skill_bar.skills.iter().map(|s| SkillId(s.id)).collect()
    }

    fn skill_cooldown(&self, skill_id: &SkillId) -> f64 {
        self.monster.skill_bar.skill_cooldowns.get(&skill_id.0).cloned().unwrap_or(0.0)
    }
    
    fn use_skill(&mut self, skill_id: &SkillId) -> GameResult<()> {
        if self.monster.skill_bar.skill_cooldowns.contains_key(&skill_id.0) {
            return Err(crate::shared::GameError::SkillOnCooldown {
                skill_id: skill_id.0.to_string(),
                remaining_time: 0.0,
            });
        }
        Ok(())
    }

    fn set_skill_cooldown(&mut self, skill_id: SkillId, cooldown: f64) -> GameResult<()> {
        self.monster.skill_bar.skill_cooldowns.insert(skill_id.0, cooldown);
        Ok(())
    }

    fn update_skill_cooldowns(&mut self, delta_time: f32) {
        // Monsters currently have a simplified cooldown logic within their skill_bar
        for cooldown in self.monster.skill_bar.skill_cooldowns.values_mut() {
            *cooldown = (*cooldown - delta_time as f64).max(0.0);
        }
    }

    fn add_buff(&mut self, buff: Buff) -> GameResult<()> {
        self.monster.buffs.push(buff);
        Ok(())
    }

    fn get_buffs(&self) -> Vec<Buff> {
        self.monster.buffs.clone()
    }
}

impl TimeAware for SimplifiedMonster {
    fn update(&mut self, delta_seconds: f32) {
        // Monster does not have a top-level update function yet.
        // We can update its components here if needed, e.g., buffs and cooldowns.
        self.update_skill_cooldowns(delta_seconds);
        
        // Simplified buff update for monster
        self.monster.buffs.retain_mut(|buff| {
            if !buff.is_permanent {
                buff.duration -= delta_seconds;
            }
            buff.duration > 0.0 || buff.is_permanent
        });
    }
}

impl FullBattleUnit for SimplifiedMonster {}
