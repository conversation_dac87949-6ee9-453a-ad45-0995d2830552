use game::character::domain::character_aggregate::{Character, CharacterBuilder};
use game::character::domain::value_objects::BasicAttributes;
use game::shared::{CharacterId, GameError, GameResult, Position, SkillId};

fn main() -> GameResult<()> {
    println!("🧪 === 技能系统核心功能测试 ===");
    println!();

    test_skill_cooldown_query()?;
    println!();

    test_skill_availability_check()?;
    println!();

    test_skill_cast_result()?;
    println!();

    println!("✅ 技能系统核心功能测试完成！");
    Ok(())
}

fn test_skill_cooldown_query() -> GameResult<()> {
    println!("🔍 === 测试1: 技能冷却时间查询 ===");

    let mut character = create_test_character()?;

    // 设置一些技能冷却时间
    character
        .skill_cooldowns_mut()
        .set_cooldown(SkillId(1), 5.0)?;
    character
        .skill_cooldowns_mut()
        .set_cooldown(SkillId(2), 0.0)?; // 无冷却
    character
        .skill_cooldowns_mut()
        .set_cooldown(SkillId(3), 2.5)?;

    println!("📋 角色技能冷却状态:");
    for skill_id in [1, 2, 3, 999] {
        let cooldown = character.skill_cooldowns().get_cooldown(&SkillId(skill_id));
        println!("   技能{}: 剩余冷却时间 {:.1}秒", skill_id, cooldown);
    }

    println!("✅ 技能冷却时间查询测试通过");
    Ok(())
}

fn test_skill_availability_check() -> GameResult<()> {
    println!("⚡ === 测试2: 技能可用性检查 ===");

    let mut character = create_test_character()?;

    // 设置技能冷却时间
    character
        .skill_cooldowns_mut()
        .set_cooldown(SkillId(1), 3.0)?;

    println!("🎯 技能可用性检查 (基于冷却):");
    for skill_id in [1, 2, 999] {
        // 999 is a skill the character hasn't learned
        let is_ready = character
            .skill_cooldowns()
            .is_skill_ready(&SkillId(skill_id));
        let status = if is_ready {
            "✅ 就绪"
        } else {
            "❌ 冷却中"
        };
        println!("   技能{}: {}", skill_id, status);
    }

    println!("✅ 技能可用性检查测试通过");
    Ok(())
}

fn test_skill_cast_result() -> GameResult<()> {
    println!("🎲 === 测试3: 技能释放结果检查 ===");

    let mut character = create_test_character()?;

    // -- 技能信息 --
    let skill1 = SkillId(1);
    let skill1_mana_cost = 15;
    let skill1_cooldown = 3.0;

    let skill2 = SkillId(2);
    let skill2_mana_cost = 10;
    let skill2_cooldown = 2.0;

    let unlearned_skill = SkillId(999);

    println!("🔬 技能释放结果分析:");

    // 1. 测试冷却中的技能
    println!("\n   --- 场景1: 技能冷却中 ---");
    character
        .skill_cooldowns_mut()
        .set_cooldown(skill1, skill1_cooldown)?;
    let result = character.use_skill(skill1, skill1_mana_cost, skill1_cooldown);
    println!("   尝试使用技能1 (冷却中): {:?}", result);
    assert!(matches!(result, Err(GameError::SkillOnCooldown { .. })));
    println!("     ✅ 返回了预期的 '技能冷却中' 错误");
    character.skill_cooldowns_mut().clear_all_cooldowns(); // 重置状态

    // 2. 测试法力不足
    println!("\n   --- 场景2: 法力不足 ---");
    let mut broke_character = create_test_character()?;
    broke_character.consume_mana(broke_character.current_mana() - 5)?; // 只留5点法力
    println!("   角色当前法力: {}", broke_character.current_mana());
    let result = broke_character.use_skill(skill2, skill2_mana_cost, skill2_cooldown);
    println!(
        "   尝试使用技能2 (需要 {} 法力): {:?}",
        skill2_mana_cost, result
    );
    assert!(matches!(result, Err(GameError::InsufficientMana { .. })));
    println!("     ✅ 返回了预期的 '法力不足' 错误");

    // 3. 测试未学习的技能
    println!("\n   --- 场景3: 技能未学习 ---");
    let result = character.use_skill(unlearned_skill, 0, 0.0);
    println!("   尝试使用技能999 (未学习): {:?}", result);
    assert!(matches!(result, Err(GameError::SkillNotFound { .. })));
    println!("     ✅ 返回了预期的 '技能未找到' 错误");

    // 4. 测试成功释放
    println!("\n   --- 场景4: 成功释放 ---");
    let mana_before_cast = character.current_mana();
    println!(
        "   角色当前法力: {}, 技能2冷却状态: {}",
        mana_before_cast,
        character.skill_cooldowns().is_skill_ready(&skill2)
    );
    let result = character.use_skill(skill2, skill2_mana_cost, skill2_cooldown);
    println!("   尝试使用技能2 (条件满足): {:?}", result);
    assert!(result.is_ok());
    println!("     ✅ 技能成功释放");
    let mana_after_cast = character.current_mana();
    println!("   释放后法力: {}", mana_after_cast);
    println!(
        "   释放后技能2冷却: {:.1}s",
        character.skill_cooldowns().get_cooldown(&skill2)
    );
    assert_eq!(mana_after_cast, mana_before_cast - skill2_mana_cost);
    assert!(!character.skill_cooldowns().is_skill_ready(&skill2));

    println!("\n✅ 技能释放结果检查测试通过");
    Ok(())
}

fn create_test_character() -> GameResult<Character> {
    let attributes = BasicAttributes::new(25, 20, 30);

    let mut character = CharacterBuilder::new()
        .with_id(CharacterId(1))
        .with_name("测试法师".to_string())
        .with_position(Position::new(0.0, 0.0))
        .with_attributes(attributes)
        .build()?;

    // 手动设置法力值，因为builder会根据属性计算
    character.restore_mana(80)?;

    // 学习技能
    character.learn_skill(SkillId(1))?;
    character.learn_skill(SkillId(2))?;

    Ok(character)
}
