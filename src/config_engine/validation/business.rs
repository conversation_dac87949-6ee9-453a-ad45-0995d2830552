//! # 业务规则验证器模块
//!
//! 实现游戏特定的业务逻辑验证
//!
//! ## 核心功能
//!
//! - 游戏规则验证
//! - 业务逻辑一致性检查
//! - 自定义业务规则
//! - 跨实体关系验证

use crate::config_engine::validation::{
    ErrorSeverity, ValidatableData, ValidationContext, ValidationError, ValidationResult,
    ValidationSource, ValidationWarning, Validator,
};
use crate::config_engine::Result;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 业务规则类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BusinessRuleType {
    /// 约束规则（硬约束）
    Constraint,
    /// 建议规则（软约束）
    Recommendation,
    /// 一致性规则
    Consistency,
    /// 完整性规则
    Integrity,
    /// 平衡性规则
    Balance,
}

/// 业务规则定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BusinessRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 规则类型
    pub rule_type: BusinessRuleType,
    /// 规则表达式
    pub expression: String,
    /// 适用的实体类型
    pub target_entities: Vec<String>,
    /// 错误消息模板
    pub error_message: String,
    /// 是否启用
    pub enabled: bool,
    /// 严重性级别
    pub severity: ErrorSeverity,
    /// 规则参数
    pub parameters: HashMap<String, serde_json::Value>,
    /// 依赖的其他规则
    pub dependencies: Vec<String>,
}

impl BusinessRule {
    pub fn new(id: String, name: String, expression: String) -> Self {
        Self {
            id,
            name,
            description: String::new(),
            rule_type: BusinessRuleType::Constraint,
            expression,
            target_entities: Vec::new(),
            error_message: "Business rule violation".to_string(),
            enabled: true,
            severity: ErrorSeverity::Error,
            parameters: HashMap::new(),
            dependencies: Vec::new(),
        }
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = description;
        self
    }

    pub fn with_rule_type(mut self, rule_type: BusinessRuleType) -> Self {
        self.rule_type = rule_type;
        self
    }

    pub fn with_target_entity(mut self, entity: String) -> Self {
        self.target_entities.push(entity);
        self
    }

    pub fn with_error_message(mut self, message: String) -> Self {
        self.error_message = message;
        self
    }

    pub fn with_severity(mut self, severity: ErrorSeverity) -> Self {
        self.severity = severity;
        self
    }

    pub fn with_parameter(mut self, key: String, value: serde_json::Value) -> Self {
        self.parameters.insert(key, value);
        self
    }

    pub fn applies_to(&self, entity_type: &str) -> bool {
        self.enabled
            && (self.target_entities.is_empty()
                || self.target_entities.contains(&entity_type.to_string()))
    }
}

/// 业务规则验证器
pub struct BusinessRuleValidator {
    /// 业务规则集合
    rules: HashMap<String, BusinessRule>,
    /// 规则执行统计
    execution_stats: HashMap<String, RuleExecutionStats>,
}

impl BusinessRuleValidator {
    pub fn new() -> Self {
        let mut validator = Self {
            rules: HashMap::new(),
            execution_stats: HashMap::new(),
        };

        // 初始化默认的游戏业务规则
        validator.initialize_default_rules();
        validator
    }

    /// 添加业务规则
    pub fn add_rule(&mut self, rule: BusinessRule) {
        self.rules.insert(rule.id.clone(), rule);
    }

    /// 移除业务规则
    pub fn remove_rule(&mut self, rule_id: &str) -> bool {
        self.rules.remove(rule_id).is_some()
    }

    /// 获取业务规则
    pub fn get_rule(&self, rule_id: &str) -> Option<&BusinessRule> {
        self.rules.get(rule_id)
    }

    /// 验证游戏数据
    pub fn validate_game_data(
        &mut self,
        data: &serde_json::Value,
        entity_type: &str,
    ) -> ValidationResult {
        let mut result = ValidationResult::new();

        for rule in self.rules.values() {
            if rule.applies_to(entity_type) {
                let rule_result = self.validate_against_rule(data, rule);
                result.merge(rule_result);

                // 更新执行统计
                self.update_execution_stats(&rule.id, &rule_result);
            }
        }

        result
    }

    /// 验证游戏配置的完整性
    pub fn validate_game_config_integrity(&self, config: &serde_json::Value) -> ValidationResult {
        let mut result = ValidationResult::new();

        // 验证材料系统
        if let Some(materials) = config.get("materials") {
            let material_result = self.validate_materials(materials);
            result.merge(material_result);
        }

        // 验证工具系统
        if let Some(tools) = config.get("tools") {
            let tool_result = self.validate_tools(tools);
            result.merge(tool_result);
        }

        // 验证技能系统
        if let Some(skills) = config.get("skills") {
            let skill_result = self.validate_skills(skills);
            result.merge(skill_result);
        }

        // 验证世界地图
        if let Some(world_map) = config.get("world_map") {
            let world_result = self.validate_world_map(world_map);
            result.merge(world_result);
        }

        // 验证跨系统一致性
        let consistency_result = self.validate_cross_system_consistency(config);
        result.merge(consistency_result);

        result
    }

    /// 获取执行统计
    pub fn get_execution_statistics(&self) -> &HashMap<String, RuleExecutionStats> {
        &self.execution_stats
    }

    /// 清除执行统计
    pub fn clear_execution_statistics(&mut self) {
        self.execution_stats.clear();
    }

    /// 针对特定规则验证
    fn validate_against_rule(
        &self,
        data: &serde_json::Value,
        rule: &BusinessRule,
    ) -> ValidationResult {
        let mut result = ValidationResult::new();

        match self.evaluate_rule_expression(&rule.expression, data, &rule.parameters) {
            Ok(is_valid) => {
                if !is_valid {
                    let severity = if rule.rule_type == BusinessRuleType::Recommendation {
                        ErrorSeverity::Warning
                    } else {
                        rule.severity.clone()
                    };

                    if severity == ErrorSeverity::Warning {
                        result.add_warning(ValidationWarning::new(
                            rule.id.clone(),
                            rule.error_message.clone(),
                            ValidationSource::BusinessRule,
                        ));
                    } else {
                        result.add_error(
                            ValidationError::new(
                                rule.id.clone(),
                                rule.error_message.clone(),
                                ValidationSource::BusinessRule,
                            )
                            .with_severity(severity),
                        );
                    }
                }
            }
            Err(e) => {
                result.add_error(ValidationError::new(
                    format!("{}_evaluation_error", rule.id),
                    format!("Failed to evaluate rule '{}': {}", rule.name, e),
                    ValidationSource::BusinessRule,
                ));
            }
        }

        result
    }

    /// 评估规则表达式
    fn evaluate_rule_expression(
        &self,
        expression: &str,
        data: &serde_json::Value,
        parameters: &HashMap<String, serde_json::Value>,
    ) -> Result<bool, String> {
        // 简化的规则表达式评估
        // 实际应该使用更强大的表达式引擎
        match expression {
            // 材料相关规则
            "material_id_format" => self.validate_material_id_format(data),
            "material_rarity_consistency" => self.validate_material_rarity_consistency(data),
            "material_tool_requirements" => self.validate_material_tool_requirements(data),

            // 工具相关规则
            "tool_durability_range" => self.validate_tool_durability_range(data, parameters),
            "tool_efficiency_balance" => self.validate_tool_efficiency_balance(data),

            // 技能相关规则
            "skill_level_progression" => self.validate_skill_level_progression(data),
            "skill_effect_balance" => self.validate_skill_effect_balance(data),

            // 世界地图相关规则
            "world_layer_consistency" => self.validate_world_layer_consistency(data),
            "resource_distribution" => self.validate_resource_distribution(data),

            // 通用规则
            "positive_value" => self.validate_positive_value(data, parameters),
            "required_fields" => self.validate_required_fields(data, parameters),
            "unique_id" => self.validate_unique_id(data, parameters),

            _ => Err(format!("Unknown rule expression: {}", expression)),
        }
    }

    /// 验证材料ID格式
    fn validate_material_id_format(&self, data: &serde_json::Value) -> Result<bool, String> {
        if let Some(id) = data.get("id") {
            if let Some(id_str) = id.as_str() {
                // 材料ID应该是小写字母和下划线
                let is_valid =
                    id_str.chars().all(|c| c.is_lowercase() || c == '_') && !id_str.is_empty();
                Ok(is_valid)
            } else {
                Err("Material ID must be a string".to_string())
            }
        } else {
            Err("Material ID is required".to_string())
        }
    }

    /// 验证材料稀有度一致性
    fn validate_material_rarity_consistency(
        &self,
        data: &serde_json::Value,
    ) -> Result<bool, String> {
        if let Some(rarity) = data.get("rarity") {
            if let Some(rarity_str) = rarity.as_str() {
                let valid_rarities = vec!["common", "uncommon", "rare", "epic", "legendary"];
                Ok(valid_rarities.contains(&rarity_str))
            } else {
                Err("Rarity must be a string".to_string())
            }
        } else {
            Ok(true) // 稀有度是可选的
        }
    }

    /// 验证材料工具需求
    fn validate_material_tool_requirements(
        &self,
        data: &serde_json::Value,
    ) -> Result<bool, String> {
        if let Some(tool_requirements) = data.get("required_tools") {
            if let Some(tools_array) = tool_requirements.as_array() {
                // 检查工具名称格式
                for tool in tools_array {
                    if let Some(tool_str) = tool.as_str() {
                        if tool_str.is_empty() {
                            return Ok(false);
                        }
                    } else {
                        return Err("Tool requirement must be a string".to_string());
                    }
                }
                Ok(true)
            } else {
                Err("Required tools must be an array".to_string())
            }
        } else {
            Ok(true) // 工具需求是可选的
        }
    }

    /// 验证工具耐久度范围
    fn validate_tool_durability_range(
        &self,
        data: &serde_json::Value,
        parameters: &HashMap<String, serde_json::Value>,
    ) -> Result<bool, String> {
        if let Some(durability) = data.get("durability") {
            if let Some(durability_num) = durability.as_f64() {
                let min_durability = parameters
                    .get("min_durability")
                    .and_then(|v| v.as_f64())
                    .unwrap_or(0.0);
                let max_durability = parameters
                    .get("max_durability")
                    .and_then(|v| v.as_f64())
                    .unwrap_or(1000.0);

                Ok(durability_num >= min_durability && durability_num <= max_durability)
            } else {
                Err("Durability must be a number".to_string())
            }
        } else {
            Ok(true) // 耐久度是可选的
        }
    }

    /// 验证工具效率平衡
    fn validate_tool_efficiency_balance(&self, data: &serde_json::Value) -> Result<bool, String> {
        if let Some(efficiency) = data.get("efficiency") {
            if let Some(efficiency_num) = efficiency.as_f64() {
                // 效率应该在合理范围内
                Ok(efficiency_num >= 0.1 && efficiency_num <= 10.0)
            } else {
                Err("Efficiency must be a number".to_string())
            }
        } else {
            Ok(true) // 效率是可选的
        }
    }

    /// 验证技能等级进度
    fn validate_skill_level_progression(&self, data: &serde_json::Value) -> Result<bool, String> {
        if let Some(levels) = data.get("levels") {
            if let Some(levels_array) = levels.as_array() {
                // 检查等级是否递增
                let mut prev_level = 0;
                for level in levels_array {
                    if let Some(level_obj) = level.as_object() {
                        if let Some(level_num) = level_obj.get("level").and_then(|v| v.as_u64()) {
                            if level_num as i32 <= prev_level {
                                return Ok(false);
                            }
                            prev_level = level_num as i32;
                        }
                    }
                }
                Ok(true)
            } else {
                Err("Levels must be an array".to_string())
            }
        } else {
            Ok(true) // 等级是可选的
        }
    }

    /// 验证技能效果平衡
    fn validate_skill_effect_balance(&self, data: &serde_json::Value) -> Result<bool, String> {
        if let Some(effects) = data.get("effects") {
            if let Some(effects_array) = effects.as_array() {
                // 检查效果值是否在合理范围内
                for effect in effects_array {
                    if let Some(effect_obj) = effect.as_object() {
                        if let Some(value) = effect_obj.get("value").and_then(|v| v.as_f64()) {
                            if value < -100.0 || value > 1000.0 {
                                return Ok(false);
                            }
                        }
                    }
                }
                Ok(true)
            } else {
                Err("Effects must be an array".to_string())
            }
        } else {
            Ok(true) // 效果是可选的
        }
    }

    /// 验证世界层一致性
    fn validate_world_layer_consistency(&self, data: &serde_json::Value) -> Result<bool, String> {
        if let Some(layers) = data.get("layers") {
            if let Some(layers_obj) = layers.as_object() {
                // 检查基础层是否存在
                if !layers_obj.contains_key("terrain") {
                    return Ok(false);
                }
                Ok(true)
            } else {
                Err("Layers must be an object".to_string())
            }
        } else {
            Ok(true) // 层是可选的
        }
    }

    /// 验证资源分布
    fn validate_resource_distribution(&self, data: &serde_json::Value) -> Result<bool, String> {
        if let Some(resources) = data.get("resources") {
            if let Some(resources_obj) = resources.as_object() {
                // 检查资源分布的总概率不超过100%
                let mut total_probability = 0.0;
                for (_, resource) in resources_obj {
                    if let Some(prob) = resource.get("probability").and_then(|v| v.as_f64()) {
                        total_probability += prob;
                    }
                }
                Ok(total_probability <= 1.0)
            } else {
                Err("Resources must be an object".to_string())
            }
        } else {
            Ok(true) // 资源是可选的
        }
    }

    /// 验证正值
    fn validate_positive_value(
        &self,
        data: &serde_json::Value,
        parameters: &HashMap<String, serde_json::Value>,
    ) -> Result<bool, String> {
        if let Some(field_name) = parameters.get("field").and_then(|v| v.as_str()) {
            if let Some(value) = data.get(field_name) {
                if let Some(num_value) = value.as_f64() {
                    Ok(num_value > 0.0)
                } else {
                    Err(format!("Field '{}' must be a number", field_name))
                }
            } else {
                Err(format!("Field '{}' is required", field_name))
            }
        } else {
            Err("Field parameter is required".to_string())
        }
    }

    /// 验证必需字段
    fn validate_required_fields(
        &self,
        data: &serde_json::Value,
        parameters: &HashMap<String, serde_json::Value>,
    ) -> Result<bool, String> {
        if let Some(fields) = parameters.get("fields").and_then(|v| v.as_array()) {
            if let Some(data_obj) = data.as_object() {
                for field in fields {
                    if let Some(field_name) = field.as_str() {
                        if !data_obj.contains_key(field_name) {
                            return Ok(false);
                        }
                    }
                }
                Ok(true)
            } else {
                Err("Data must be an object".to_string())
            }
        } else {
            Err("Fields parameter is required".to_string())
        }
    }

    /// 验证唯一ID
    fn validate_unique_id(
        &self,
        data: &serde_json::Value,
        _parameters: &HashMap<String, serde_json::Value>,
    ) -> Result<bool, String> {
        if let Some(id) = data.get("id") {
            if let Some(id_str) = id.as_str() {
                Ok(!id_str.is_empty())
            } else {
                Err("ID must be a string".to_string())
            }
        } else {
            Err("ID is required".to_string())
        }
    }

    /// 验证材料系统
    fn validate_materials(&self, materials: &serde_json::Value) -> ValidationResult {
        let mut result = ValidationResult::new();

        if let Some(materials_obj) = materials.as_object() {
            for (_material_id, material_data) in materials_obj {
                // 创建临时验证器来验证
                let mut temp_validator = BusinessRuleValidator::new();
                for (id, rule) in &self.rules {
                    temp_validator.add_rule(rule.clone());
                }
                let material_result = temp_validator.validate_game_data(material_data, "material");
                result.merge(material_result);
            }
        }

        result
    }

    /// 验证工具系统
    fn validate_tools(&self, tools: &serde_json::Value) -> ValidationResult {
        let mut result = ValidationResult::new();

        if let Some(tools_obj) = tools.as_object() {
            for (_tool_id, tool_data) in tools_obj {
                // 创建临时验证器来验证
                let mut temp_validator = BusinessRuleValidator::new();
                for (id, rule) in &self.rules {
                    temp_validator.add_rule(rule.clone());
                }
                let tool_result = temp_validator.validate_game_data(tool_data, "tool");
                result.merge(tool_result);
            }
        }

        result
    }

    /// 验证技能系统
    fn validate_skills(&self, skills: &serde_json::Value) -> ValidationResult {
        let mut result = ValidationResult::new();

        if let Some(skills_obj) = skills.as_object() {
            for (_skill_id, skill_data) in skills_obj {
                // 创建临时验证器来验证
                let mut temp_validator = BusinessRuleValidator::new();
                for (id, rule) in &self.rules {
                    temp_validator.add_rule(rule.clone());
                }
                let skill_result = temp_validator.validate_game_data(skill_data, "skill");
                result.merge(skill_result);
            }
        }

        result
    }

    /// 验证世界地图
    fn validate_world_map(&self, world_map: &serde_json::Value) -> ValidationResult {
        let mut result = ValidationResult::new();

        // 创建临时验证器来验证
        let mut temp_validator = BusinessRuleValidator::new();
        for (id, rule) in &self.rules {
            temp_validator.add_rule(rule.clone());
        }
        let world_result = temp_validator.validate_game_data(world_map, "world_map");
        result.merge(world_result);

        result
    }

    /// 验证跨系统一致性
    fn validate_cross_system_consistency(&self, config: &serde_json::Value) -> ValidationResult {
        let mut result = ValidationResult::new();

        // 验证材料和工具的一致性
        if let (Some(materials), Some(tools)) = (config.get("materials"), config.get("tools")) {
            let consistency_result = self.validate_material_tool_consistency(materials, tools);
            result.merge(consistency_result);
        }

        result
    }

    /// 验证材料和工具的一致性
    fn validate_material_tool_consistency(
        &self,
        materials: &serde_json::Value,
        tools: &serde_json::Value,
    ) -> ValidationResult {
        let mut result = ValidationResult::new();

        // 检查材料中引用的工具是否存在
        if let (Some(materials_obj), Some(tools_obj)) = (materials.as_object(), tools.as_object()) {
            for (material_id, material_data) in materials_obj {
                if let Some(required_tools) = material_data
                    .get("required_tools")
                    .and_then(|v| v.as_array())
                {
                    for tool in required_tools {
                        if let Some(tool_name) = tool.as_str() {
                            if !tools_obj.contains_key(tool_name) {
                                result.add_error(ValidationError::new(
                                    "material_tool_reference".to_string(),
                                    format!(
                                        "Material '{}' references non-existent tool '{}'",
                                        material_id, tool_name
                                    ),
                                    ValidationSource::BusinessRule,
                                ));
                            }
                        }
                    }
                }
            }
        }

        result
    }

    /// 初始化默认的游戏业务规则
    fn initialize_default_rules(&mut self) {
        // 材料相关规则
        let material_id_rule = BusinessRule::new(
            "material_id_format".to_string(),
            "Material ID Format".to_string(),
            "material_id_format".to_string(),
        )
        .with_description("Material IDs must be lowercase with underscores".to_string())
        .with_target_entity("material".to_string())
        .with_error_message(
            "Material ID must be lowercase letters and underscores only".to_string(),
        );

        let material_rarity_rule = BusinessRule::new(
            "material_rarity_consistency".to_string(),
            "Material Rarity Consistency".to_string(),
            "material_rarity_consistency".to_string(),
        )
        .with_description("Material rarity must be a valid value".to_string())
        .with_target_entity("material".to_string())
        .with_error_message(
            "Material rarity must be one of: common, uncommon, rare, epic, legendary".to_string(),
        );

        // 工具相关规则
        let tool_durability_rule = BusinessRule::new(
            "tool_durability_range".to_string(),
            "Tool Durability Range".to_string(),
            "tool_durability_range".to_string(),
        )
        .with_description("Tool durability must be within valid range".to_string())
        .with_target_entity("tool".to_string())
        .with_error_message("Tool durability must be between 0 and 1000".to_string())
        .with_parameter(
            "min_durability".to_string(),
            serde_json::Value::Number(serde_json::Number::from(0)),
        )
        .with_parameter(
            "max_durability".to_string(),
            serde_json::Value::Number(serde_json::Number::from(1000)),
        );

        // 添加规则到验证器
        self.add_rule(material_id_rule);
        self.add_rule(material_rarity_rule);
        self.add_rule(tool_durability_rule);
    }

    /// 更新执行统计
    fn update_execution_stats(&mut self, rule_id: &str, result: &ValidationResult) {
        let stats = self
            .execution_stats
            .entry(rule_id.to_string())
            .or_insert_with(RuleExecutionStats::new);
        stats.execution_count += 1;
        if !result.is_valid {
            stats.failure_count += 1;
        }
    }
}

impl Default for BusinessRuleValidator {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Validator for BusinessRuleValidator {
    fn validator_name(&self) -> &str {
        "BusinessRuleValidator"
    }

    fn validate(&self, context: &mut ValidationContext, data: &dyn ValidatableData) -> Result<()> {
        let json_data = data.to_json_value();
        let data_type = data.data_type();

        // 这里需要创建一个可变引用的副本来调用validate_game_data
        // 由于trait方法限制，我们使用不可变的版本
        let mut temp_validator = BusinessRuleValidator::new();
        // 复制规则到临时验证器
        for (id, rule) in &self.rules {
            temp_validator.add_rule(rule.clone());
        }

        let validation_result = temp_validator.validate_game_data(&json_data, &data_type);
        context.result.merge(validation_result);

        Ok(())
    }

    fn supports(&self, data_type: &str) -> bool {
        // 业务规则验证器支持游戏相关的数据类型
        matches!(
            data_type,
            "material" | "tool" | "skill" | "world_map" | "game_config"
        )
    }
}

/// 规则执行统计
#[derive(Debug, Clone)]
pub struct RuleExecutionStats {
    pub execution_count: u64,
    pub failure_count: u64,
}

impl RuleExecutionStats {
    pub fn new() -> Self {
        Self {
            execution_count: 0,
            failure_count: 0,
        }
    }

    pub fn success_rate(&self) -> f64 {
        if self.execution_count == 0 {
            0.0
        } else {
            1.0 - (self.failure_count as f64 / self.execution_count as f64)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_business_rule_creation() {
        let rule = BusinessRule::new(
            "test_rule".to_string(),
            "Test Rule".to_string(),
            "test_expression".to_string(),
        )
        .with_rule_type(BusinessRuleType::Constraint)
        .with_target_entity("material".to_string())
        .with_error_message("Test error message".to_string());

        assert_eq!(rule.id, "test_rule");
        assert_eq!(rule.rule_type, BusinessRuleType::Constraint);
        assert!(rule.applies_to("material"));
        assert!(!rule.applies_to("tool"));
    }

    #[test]
    fn test_material_id_format_validation() {
        let validator = BusinessRuleValidator::new();

        // 有效的材料ID
        let valid_material = serde_json::json!({
            "id": "iron_ore",
            "name": "Iron Ore"
        });
        assert!(validator
            .validate_material_id_format(&valid_material)
            .unwrap());

        // 无效的材料ID（包含大写字母）
        let invalid_material = serde_json::json!({
            "id": "Iron_Ore",
            "name": "Iron Ore"
        });
        assert!(!validator
            .validate_material_id_format(&invalid_material)
            .unwrap());

        // 无效的材料ID（包含特殊字符）
        let invalid_material2 = serde_json::json!({
            "id": "iron-ore",
            "name": "Iron Ore"
        });
        assert!(!validator
            .validate_material_id_format(&invalid_material2)
            .unwrap());
    }

    #[test]
    fn test_material_rarity_validation() {
        let validator = BusinessRuleValidator::new();

        // 有效的稀有度
        let valid_material = serde_json::json!({
            "id": "iron_ore",
            "rarity": "common"
        });
        assert!(validator
            .validate_material_rarity_consistency(&valid_material)
            .unwrap());

        // 无效的稀有度
        let invalid_material = serde_json::json!({
            "id": "iron_ore",
            "rarity": "invalid_rarity"
        });
        assert!(!validator
            .validate_material_rarity_consistency(&invalid_material)
            .unwrap());

        // 没有稀有度字段（应该是有效的）
        let no_rarity_material = serde_json::json!({
            "id": "iron_ore"
        });
        assert!(validator
            .validate_material_rarity_consistency(&no_rarity_material)
            .unwrap());
    }

    #[test]
    fn test_tool_durability_validation() {
        let validator = BusinessRuleValidator::new();
        let mut parameters = HashMap::new();
        parameters.insert(
            "min_durability".to_string(),
            serde_json::Value::Number(serde_json::Number::from(0)),
        );
        parameters.insert(
            "max_durability".to_string(),
            serde_json::Value::Number(serde_json::Number::from(1000)),
        );

        // 有效的耐久度
        let valid_tool = serde_json::json!({
            "id": "iron_pickaxe",
            "durability": 500
        });
        assert!(validator
            .validate_tool_durability_range(&valid_tool, &parameters)
            .unwrap());

        // 超出范围的耐久度
        let invalid_tool = serde_json::json!({
            "id": "iron_pickaxe",
            "durability": 1500
        });
        assert!(!validator
            .validate_tool_durability_range(&invalid_tool, &parameters)
            .unwrap());

        // 负数耐久度
        let negative_tool = serde_json::json!({
            "id": "iron_pickaxe",
            "durability": -10
        });
        assert!(!validator
            .validate_tool_durability_range(&negative_tool, &parameters)
            .unwrap());
    }

    #[test]
    fn test_business_rule_validator() {
        let mut validator = BusinessRuleValidator::new();

        // 测试材料验证
        let material_data = serde_json::json!({
            "id": "iron_ore",
            "name": "Iron Ore",
            "rarity": "common"
        });

        let result = validator.validate_game_data(&material_data, "material");
        assert!(result.is_valid);

        // 测试无效材料
        let invalid_material = serde_json::json!({
            "id": "Iron-Ore",
            "name": "Iron Ore",
            "rarity": "invalid"
        });

        let result = validator.validate_game_data(&invalid_material, "material");
        assert!(!result.is_valid);
        assert!(result.errors.len() > 0);
    }

    #[test]
    fn test_cross_system_consistency() {
        let validator = BusinessRuleValidator::new();

        let config = serde_json::json!({
            "materials": {
                "iron_ore": {
                    "id": "iron_ore",
                    "required_tools": ["pickaxe", "shovel"]
                }
            },
            "tools": {
                "pickaxe": {
                    "id": "pickaxe",
                    "durability": 500
                }
            }
        });

        let result = validator.validate_game_config_integrity(&config);
        // 应该有一个错误，因为"shovel"工具不存在
        assert!(!result.is_valid);
        assert!(result.errors.iter().any(|e| e.message.contains("shovel")));
    }

    #[test]
    fn test_rule_execution_stats() {
        let mut stats = RuleExecutionStats::new();

        assert_eq!(stats.execution_count, 0);
        assert_eq!(stats.failure_count, 0);
        assert_eq!(stats.success_rate(), 0.0);

        stats.execution_count = 10;
        stats.failure_count = 3;
        assert_eq!(stats.success_rate(), 0.7);
    }
}
