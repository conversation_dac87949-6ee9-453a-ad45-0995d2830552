use crate::shared::types::*;
/// 等级系统 - 处理角色等级提升和属性成长
/// 基于修仙世界观的等级设计，包含境界和修为等概念
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// ============================================================================
// 等级系统核心
// ============================================================================

/// 等级系统管理器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LevelSystem {
    /// 等级经验值需求表
    pub level_exp_requirements: HashMap<Level, Exp>,
    /// 属性成长配置
    pub attribute_growth_config: AttributeGrowthConfig,
    /// 境界系统配置
    pub realm_config: RealmConfig,
    /// 技能点奖励配置
    pub skill_point_config: SkillPointConfig,
}

/// 修仙境界
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum CultivationRealm {
    /// 凡人境界 (1-10级)
    Mortal,
    /// 炼气期 (11-20级)
    QiCondensation,
    /// 筑基期 (21-30级)
    FoundationEstablishment,
    /// 金丹期 (31-40级)
    GoldenCore,
    /// 元婴期 (41-50级)
    NascentSoul,
    /// 化神期 (51-60级)
    SpiritTransformation,
    /// 炼虚期 (61-70级)
    VoidRefinement,
    /// 合体期 (71-80级)
    BodyIntegration,
    /// 大乘期 (81-90级)
    Mahayana,
    /// 渡劫期 (91-100级)
    Tribulation,
    /// 仙人境 (101级+)
    Immortal,
}

impl CultivationRealm {
    /// 获取境界的中文名称
    pub fn chinese_name(&self) -> &'static str {
        match self {
            CultivationRealm::Mortal => "凡人",
            CultivationRealm::QiCondensation => "炼气期",
            CultivationRealm::FoundationEstablishment => "筑基期",
            CultivationRealm::GoldenCore => "金丹期",
            CultivationRealm::NascentSoul => "元婴期",
            CultivationRealm::SpiritTransformation => "化神期",
            CultivationRealm::VoidRefinement => "炼虚期",
            CultivationRealm::BodyIntegration => "合体期",
            CultivationRealm::Mahayana => "大乘期",
            CultivationRealm::Tribulation => "渡劫期",
            CultivationRealm::Immortal => "仙人境",
        }
    }

    /// 从等级获取境界
    pub fn from_level(level: Level) -> Self {
        match level {
            1..=10 => CultivationRealm::Mortal,
            11..=20 => CultivationRealm::QiCondensation,
            21..=30 => CultivationRealm::FoundationEstablishment,
            31..=40 => CultivationRealm::GoldenCore,
            41..=50 => CultivationRealm::NascentSoul,
            51..=60 => CultivationRealm::SpiritTransformation,
            61..=70 => CultivationRealm::VoidRefinement,
            71..=80 => CultivationRealm::BodyIntegration,
            81..=90 => CultivationRealm::Mahayana,
            91..=100 => CultivationRealm::Tribulation,
            _ => CultivationRealm::Immortal,
        }
    }

    /// 获取境界的等级范围
    pub fn level_range(&self) -> (Level, Level) {
        match self {
            CultivationRealm::Mortal => (1, 10),
            CultivationRealm::QiCondensation => (11, 20),
            CultivationRealm::FoundationEstablishment => (21, 30),
            CultivationRealm::GoldenCore => (31, 40),
            CultivationRealm::NascentSoul => (41, 50),
            CultivationRealm::SpiritTransformation => (51, 60),
            CultivationRealm::VoidRefinement => (61, 70),
            CultivationRealm::BodyIntegration => (71, 80),
            CultivationRealm::Mahayana => (81, 90),
            CultivationRealm::Tribulation => (91, 100),
            CultivationRealm::Immortal => (101, u32::MAX),
        }
    }

    /// 获取境界突破奖励
    pub fn breakthrough_bonus(&self) -> AttributeBonus {
        match self {
            CultivationRealm::Mortal => AttributeBonus::new(0, 0, 0, 0, 0),
            CultivationRealm::QiCondensation => AttributeBonus::new(50, 100, 10, 10, 5),
            CultivationRealm::FoundationEstablishment => AttributeBonus::new(100, 200, 20, 20, 10),
            CultivationRealm::GoldenCore => AttributeBonus::new(200, 400, 40, 40, 20),
            CultivationRealm::NascentSoul => AttributeBonus::new(400, 800, 80, 80, 40),
            CultivationRealm::SpiritTransformation => AttributeBonus::new(800, 1600, 160, 160, 80),
            CultivationRealm::VoidRefinement => AttributeBonus::new(1600, 3200, 320, 320, 160),
            CultivationRealm::BodyIntegration => AttributeBonus::new(3200, 6400, 640, 640, 320),
            CultivationRealm::Mahayana => AttributeBonus::new(6400, 12800, 1280, 1280, 640),
            CultivationRealm::Tribulation => AttributeBonus::new(12800, 25600, 2560, 2560, 1280),
            CultivationRealm::Immortal => AttributeBonus::new(25600, 51200, 5120, 5120, 2560),
        }
    }
}

/// 属性成长配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttributeGrowthConfig {
    /// 每级基础生命值增长
    pub hp_per_level: Health,
    /// 每级基础法力值增长
    pub mana_per_level: Mana,
    /// 每级基础攻击力增长
    pub attack_per_level: Attack,
    /// 每级基础防御力增长
    pub defense_per_level: Defense,
    /// 每级基础速度增长
    pub speed_per_level: Speed,
    /// 属性成长变化率（不同境界有不同成长率）
    pub growth_multipliers: HashMap<CultivationRealm, f32>,
}

/// 境界配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealmConfig {
    /// 境界突破所需额外经验
    pub breakthrough_exp_cost: HashMap<CultivationRealm, Exp>,
    /// 境界突破条件
    pub breakthrough_requirements: HashMap<CultivationRealm, BreakthroughRequirement>,
}

/// 境界突破要求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BreakthroughRequirement {
    /// 所需特殊材料
    pub required_materials: Vec<(String, u32)>,
    /// 所需完成的任务
    pub required_quests: Vec<String>,
    /// 最低属性需求
    pub minimum_attributes: AttributeRequirement,
    /// 特殊条件描述
    pub special_conditions: Vec<String>,
}

/// 属性需求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttributeRequirement {
    pub min_constitution: Attr,
    pub min_strength: Attr,
    pub min_essence: u64,
    pub min_total_essence: u64,
}

/// 技能点配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillPointConfig {
    /// 每级获得的技能点
    pub skill_points_per_level: u32,
    /// 境界突破额外技能点
    pub breakthrough_skill_points: HashMap<CultivationRealm, u32>,
}

/// 属性奖励
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct AttributeBonus {
    pub hp_bonus: Health,
    pub mana_bonus: Mana,
    pub attack_bonus: Attack,
    pub defense_bonus: Defense,
    pub speed_bonus: i32,
}

impl AttributeBonus {
    pub fn new(hp: Health, mana: Mana, attack: Attack, defense: Defense, speed: i32) -> Self {
        Self {
            hp_bonus: hp,
            mana_bonus: mana,
            attack_bonus: attack,
            defense_bonus: defense,
            speed_bonus: speed,
        }
    }

    pub fn zero() -> Self {
        Self::new(0, 0, 0, 0, 0)
    }
}

/// 等级提升结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LevelUpResult {
    /// 新等级
    pub new_level: Level,
    /// 新境界
    pub new_realm: CultivationRealm,
    /// 是否境界突破
    pub realm_breakthrough: bool,
    /// 属性增长
    pub attribute_growth: AttributeBonus,
    /// 境界突破奖励
    pub breakthrough_bonus: AttributeBonus,
    /// 获得的技能点
    pub skill_points_gained: u32,
    /// 升级说明
    pub level_up_messages: Vec<String>,
    /// 剩余经验值（溢出到下一级的经验）
    pub remaining_exp: Exp,
}

impl LevelSystem {
    /// 创建默认等级系统
    pub fn new() -> Self {
        Self {
            level_exp_requirements: Self::create_exp_requirements(),
            attribute_growth_config: Self::create_attribute_growth_config(),
            realm_config: Self::create_realm_config(),
            skill_point_config: Self::create_skill_point_config(),
        }
    }

    /// 创建经验值需求表
    fn create_exp_requirements() -> HashMap<Level, Exp> {
        let mut requirements = HashMap::new();

        for level in 1..=200 {
            let base_exp = match level {
                1..=10 => 100,      // 凡人境界
                11..=20 => 300,     // 炼气期
                21..=30 => 800,     // 筑基期
                31..=40 => 2000,    // 金丹期
                41..=50 => 5000,    // 元婴期
                51..=60 => 12000,   // 化神期
                61..=70 => 25000,   // 炼虚期
                71..=80 => 50000,   // 合体期
                81..=90 => 100000,  // 大乘期
                91..=100 => 200000, // 渡劫期
                _ => 500000,        // 仙人境
            };

            // 每级递增
            let level_multiplier = ((level - 1) % 10 + 1) as f32;
            let required_exp = (base_exp as f32 * level_multiplier) as Exp;

            requirements.insert(level, required_exp);
        }

        requirements
    }

    /// 创建属性成长配置
    fn create_attribute_growth_config() -> AttributeGrowthConfig {
        let mut growth_multipliers = HashMap::new();
        growth_multipliers.insert(CultivationRealm::Mortal, 1.0);
        growth_multipliers.insert(CultivationRealm::QiCondensation, 1.2);
        growth_multipliers.insert(CultivationRealm::FoundationEstablishment, 1.5);
        growth_multipliers.insert(CultivationRealm::GoldenCore, 2.0);
        growth_multipliers.insert(CultivationRealm::NascentSoul, 2.5);
        growth_multipliers.insert(CultivationRealm::SpiritTransformation, 3.0);
        growth_multipliers.insert(CultivationRealm::VoidRefinement, 4.0);
        growth_multipliers.insert(CultivationRealm::BodyIntegration, 5.0);
        growth_multipliers.insert(CultivationRealm::Mahayana, 6.0);
        growth_multipliers.insert(CultivationRealm::Tribulation, 8.0);
        growth_multipliers.insert(CultivationRealm::Immortal, 10.0);

        AttributeGrowthConfig {
            hp_per_level: 50,
            mana_per_level: 30,
            attack_per_level: 5,
            defense_per_level: 3,
            speed_per_level: 0.1,
            growth_multipliers,
        }
    }

    /// 创建境界配置
    fn create_realm_config() -> RealmConfig {
        let mut breakthrough_exp_cost = HashMap::new();
        breakthrough_exp_cost.insert(CultivationRealm::QiCondensation, 1000);
        breakthrough_exp_cost.insert(CultivationRealm::FoundationEstablishment, 5000);
        breakthrough_exp_cost.insert(CultivationRealm::GoldenCore, 20000);
        breakthrough_exp_cost.insert(CultivationRealm::NascentSoul, 80000);
        breakthrough_exp_cost.insert(CultivationRealm::SpiritTransformation, 300000);
        breakthrough_exp_cost.insert(CultivationRealm::VoidRefinement, 1000000);
        breakthrough_exp_cost.insert(CultivationRealm::BodyIntegration, 3000000);
        breakthrough_exp_cost.insert(CultivationRealm::Mahayana, 10000000);
        breakthrough_exp_cost.insert(CultivationRealm::Tribulation, 30000000);
        breakthrough_exp_cost.insert(CultivationRealm::Immortal, 100000000);

        // 简化版本 - 实际项目中应该有更复杂的突破要求
        let breakthrough_requirements = HashMap::new();

        RealmConfig {
            breakthrough_exp_cost,
            breakthrough_requirements,
        }
    }

    /// 创建技能点配置
    fn create_skill_point_config() -> SkillPointConfig {
        let mut breakthrough_skill_points = HashMap::new();
        breakthrough_skill_points.insert(CultivationRealm::QiCondensation, 5);
        breakthrough_skill_points.insert(CultivationRealm::FoundationEstablishment, 10);
        breakthrough_skill_points.insert(CultivationRealm::GoldenCore, 15);
        breakthrough_skill_points.insert(CultivationRealm::NascentSoul, 20);
        breakthrough_skill_points.insert(CultivationRealm::SpiritTransformation, 25);
        breakthrough_skill_points.insert(CultivationRealm::VoidRefinement, 30);
        breakthrough_skill_points.insert(CultivationRealm::BodyIntegration, 35);
        breakthrough_skill_points.insert(CultivationRealm::Mahayana, 40);
        breakthrough_skill_points.insert(CultivationRealm::Tribulation, 50);
        breakthrough_skill_points.insert(CultivationRealm::Immortal, 100);

        SkillPointConfig {
            skill_points_per_level: 2,
            breakthrough_skill_points,
        }
    }

    /// 计算等级提升
    pub fn calculate_level_up(
        &self,
        current_level: Level,
        current_exp: Exp,
        gained_exp: Exp,
    ) -> Option<LevelUpResult> {
        let mut new_level = current_level;
        let mut total_exp = current_exp + gained_exp;
        let mut messages = Vec::new();

        // 检查是否可以升级
        while let Some(&required_exp) = self.level_exp_requirements.get(&(new_level + 1)) {
            if total_exp >= required_exp {
                total_exp = total_exp.saturating_sub(required_exp);
                new_level += 1;

                // 检查是否境界突破
                let old_realm = CultivationRealm::from_level(current_level);
                let new_realm = CultivationRealm::from_level(new_level);
                let realm_breakthrough = old_realm != new_realm;

                // 计算属性成长
                let attribute_growth = self.calculate_attribute_growth(new_level, new_realm);

                // 计算境界突破奖励
                let breakthrough_bonus = if realm_breakthrough {
                    messages.push(format!("恭喜！突破至{}！", new_realm.chinese_name()));
                    new_realm.breakthrough_bonus()
                } else {
                    AttributeBonus::zero()
                };

                // 计算技能点奖励
                let skill_points_gained =
                    self.calculate_skill_points(new_level, realm_breakthrough, new_realm);

                messages.push(format!("等级提升至{}级！", new_level));

                return Some(LevelUpResult {
                    new_level,
                    new_realm,
                    realm_breakthrough,
                    attribute_growth,
                    breakthrough_bonus,
                    skill_points_gained,
                    level_up_messages: messages,
                    remaining_exp: total_exp, // 使用剩余经验值，修复未读警告
                });
            } else {
                break;
            }
        }

        None
    }

    /// 计算属性成长
    fn calculate_attribute_growth(&self, _level: Level, realm: CultivationRealm) -> AttributeBonus {
        let multiplier = self
            .attribute_growth_config
            .growth_multipliers
            .get(&realm)
            .copied()
            .unwrap_or(1.0);

        let base_hp = self.attribute_growth_config.hp_per_level;
        let base_mana = self.attribute_growth_config.mana_per_level;
        let base_attack = self.attribute_growth_config.attack_per_level;
        let base_defense = self.attribute_growth_config.defense_per_level;
        let base_speed = self.attribute_growth_config.speed_per_level;

        AttributeBonus {
            hp_bonus: (base_hp as f32 * multiplier) as Health,
            mana_bonus: (base_mana as f32 * multiplier) as Mana,
            attack_bonus: (base_attack as f32 * multiplier) as Attack,
            defense_bonus: (base_defense as f32 * multiplier) as Defense,
            speed_bonus: (base_speed * multiplier) as i32,
        }
    }

    /// 计算技能点奖励
    fn calculate_skill_points(
        &self,
        _level: Level,
        realm_breakthrough: bool,
        realm: CultivationRealm,
    ) -> u32 {
        let mut points = self.skill_point_config.skill_points_per_level;

        if realm_breakthrough {
            if let Some(&breakthrough_points) = self
                .skill_point_config
                .breakthrough_skill_points
                .get(&realm)
            {
                points += breakthrough_points;
            }
        }

        points
    }

    /// 获取等级经验值需求
    pub fn get_exp_requirement(&self, level: Level) -> Option<Exp> {
        self.level_exp_requirements.get(&level).copied()
    }

    /// 获取境界信息
    pub fn get_realm_info(&self, level: Level) -> (CultivationRealm, String) {
        let realm = CultivationRealm::from_level(level);
        let (min_level, max_level) = realm.level_range();
        let description = if max_level == u32::MAX {
            format!("{}境界 ({}级以上)", realm.chinese_name(), min_level)
        } else {
            format!(
                "{}境界 ({}-{}级)",
                realm.chinese_name(),
                min_level,
                max_level
            )
        };

        (realm, description)
    }

    /// 计算到下一级所需经验值
    pub fn calculate_exp_to_next_level(
        &self,
        current_level: Level,
        current_exp: Exp,
    ) -> Option<Exp> {
        if let Some(required_exp) = self.get_exp_requirement(current_level + 1) {
            Some(required_exp.saturating_sub(current_exp))
        } else {
            None
        }
    }

    /// 计算到下一境界所需经验值
    pub fn calculate_exp_to_next_realm(
        &self,
        current_level: Level,
        current_exp: Exp,
    ) -> Option<(CultivationRealm, Exp)> {
        let current_realm = CultivationRealm::from_level(current_level);
        let (_, max_level) = current_realm.level_range();

        if max_level == u32::MAX {
            return None; // 已经是最高境界
        }

        let next_realm_level = max_level + 1;
        let next_realm = CultivationRealm::from_level(next_realm_level);

        // 计算到下一境界需要的总经验
        let mut total_exp_needed = 0;
        for level in (current_level + 1)..=next_realm_level {
            if let Some(exp) = self.get_exp_requirement(level) {
                total_exp_needed += exp;
            }
        }

        let current_level_exp_needed = self
            .get_exp_requirement(current_level + 1)
            .map(|req| req.saturating_sub(current_exp))
            .unwrap_or(0);

        total_exp_needed = total_exp_needed
            - self.get_exp_requirement(current_level + 1).unwrap_or(0)
            + current_level_exp_needed;

        Some((next_realm, total_exp_needed))
    }
}

impl Default for LevelSystem {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 便利函数
// ============================================================================

/// 创建测试用的等级提升结果
pub fn create_test_level_up(from_level: Level, to_level: Level) -> LevelUpResult {
    let new_realm = CultivationRealm::from_level(to_level);
    let old_realm = CultivationRealm::from_level(from_level);
    let realm_breakthrough = old_realm != new_realm;

    LevelUpResult {
        new_level: to_level,
        new_realm,
        realm_breakthrough,
        attribute_growth: AttributeBonus::new(50, 30, 5, 3, 1),
        breakthrough_bonus: if realm_breakthrough {
            new_realm.breakthrough_bonus()
        } else {
            AttributeBonus::zero()
        },
        skill_points_gained: if realm_breakthrough { 10 } else { 2 },
        level_up_messages: vec![
            format!("等级提升至{}级！", to_level),
            if realm_breakthrough {
                format!("恭喜！突破至{}！", new_realm.chinese_name())
            } else {
                String::new()
            },
        ]
        .into_iter()
        .filter(|s| !s.is_empty())
        .collect(),
        remaining_exp: 0, // 测试用例，无剩余经验
    }
}
