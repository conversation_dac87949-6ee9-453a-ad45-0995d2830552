//! 装备对象定义
use crate::attribute::attribute::AttributeSet;
use crate::shared::types::MaterialGrade;
use std::collections::HashMap;

/// 装备类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum EquipmentType {
    Weapon,    // 武器：剑、刀、枪等
    Armor,     // 防具：铠甲、护甲等
    Accessory, // 饰品：戒指、项链等
    Helmet,    // 头盔：提供头部防护
    Boots,     // 靴子：提供移动速度和脚部防护
    Shield,    // 盾牌：提供额外防御
    Gloves,    // 手套：提供手部防护和特殊效果
    Belt,      // 腰带：提供腰部防护和储物空间
}

/// 装备品质等级
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub enum EquipmentQuality {
    Common,    // 凡品 - 白色
    Uncommon,  // 不凡 - 绿色
    Rare,      // 稀有 - 蓝色
    Epic,      // 史诗 - 紫色
    Legendary, // 传说 - 橙色
    Artifact,  // 神器 - 红色
}

/// 装备强化等级信息
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct EnhancementInfo {
    /// 当前强化等级 (0-20)
    pub level: u8,
    /// 强化成功率 (0.0-1.0)
    pub success_rate: f32,
    /// 下次强化所需材料
    pub required_materials: Vec<(u32, u32)>, // (材料ID, 数量)
    /// 强化失败惩罚
    pub failure_penalty: FailurePenalty,
}

/// 强化失败惩罚类型
#[derive(Debug, Clone, PartialEq)]
pub enum FailurePenalty {
    None,           // 无惩罚
    LevelDown(u8),  // 等级下降
    Destruction,    // 装备销毁
}

/// 套装效果定义
#[derive(Debug, Clone, PartialEq)]
pub struct SetEffect {
    /// 套装名称
    pub set_name: String,
    /// 激活所需件数
    pub required_pieces: u8,
    /// 套装效果属性加成
    pub bonus_attributes: AttributeSet,
    /// 套装特殊效果描述
    pub special_effects: Vec<String>,
}

/// 装备核心结构体
#[derive(Debug, Clone)]
pub struct Equipment {
    /// 装备唯一标识符
    pub id: u32,
    /// 装备名称
    pub name: String,
    /// 装备类型（武器、防具等）
    pub eq_type: EquipmentType,
    /// 装备品质
    pub quality: EquipmentQuality,
    /// 装备等级要求
    pub level_requirement: u32,
    /// 装备基础属性集合
    pub base_attributes: AttributeSet,
    /// 强化信息
    pub enhancement: EnhancementInfo,
    /// 套装ID（如果属于套装）
    pub set_id: Option<u32>,
    /// 是否已装备
    pub is_equipped: bool,
    /// 装备描述
    pub description: String,
    /// 装备制作材料等级
    pub material_grade: MaterialGrade,
}

/// 装备栏结构体
#[derive(Debug, Clone)]
pub struct EquipmentBar {
    /// 已装备的装备（按装备类型索引）
    pub equipped: HashMap<EquipmentType, Equipment>,
    /// 背包中的装备
    pub inventory: Vec<Equipment>,
    /// 最大背包容量
    pub max_capacity: usize,
}

/// 套装管理器
#[derive(Debug, Clone)]
pub struct SetManager {
    /// 所有套装定义
    pub sets: HashMap<u32, SetEffect>,
    /// 当前激活的套装效果
    pub active_sets: HashMap<u32, u8>, // 套装ID -> 已装备件数
}

// ============================================================================
// 实现装备核心功能方法
// ============================================================================

impl Equipment {
    /// 创建新装备
    pub fn new(
        id: u32, 
        name: String, 
        eq_type: EquipmentType, 
        quality: EquipmentQuality,
        level_requirement: u32,
        base_attributes: AttributeSet,
        material_grade: MaterialGrade,
        description: String,
    ) -> Self {
        Self {
            id,
            name,
            eq_type,
            quality,
            level_requirement,
            base_attributes,
            enhancement: EnhancementInfo::new(),
            set_id: None,
            is_equipped: false,
            description,
            material_grade,
        }
    }
    
    /// 获取装备的总属性值（基础属性 + 强化加成）
    pub fn get_total_attributes(&self) -> AttributeSet {
        let mut total = self.base_attributes.clone();
        
        // 应用强化等级的属性加成
        let enhancement_multiplier = 1.0 + (self.enhancement.level as f64 * 0.1);
        for (_, attr) in total.attributes.iter_mut() {
            attr.value *= enhancement_multiplier;
        }
        
        total
    }
    
    /// 检查是否可以装备（等级要求）
    pub fn can_equip(&self, character_level: u32) -> bool {
        character_level >= self.level_requirement
    }
    
    /// 获取装备评分（用于装备比较）
    pub fn get_equipment_score(&self) -> f64 {
        let base_score = self.base_attributes.attributes.values()
            .map(|attr| attr.value)
            .sum::<f64>();
            
        let quality_multiplier = match self.quality {
            EquipmentQuality::Common => 1.0,
            EquipmentQuality::Uncommon => 1.2,
            EquipmentQuality::Rare => 1.5,
            EquipmentQuality::Epic => 2.0,
            EquipmentQuality::Legendary => 3.0,
            EquipmentQuality::Artifact => 5.0,
        };
        
        let enhancement_multiplier = 1.0 + (self.enhancement.level as f64 * 0.15);
        
        base_score * quality_multiplier * enhancement_multiplier
    }
}

impl EnhancementInfo {
    /// 创建新的强化信息
    pub fn new() -> Self {
        Self {
            level: 0,
            success_rate: 1.0,
            required_materials: Vec::new(),
            failure_penalty: FailurePenalty::None,
        }
    }
    
    /// 计算下次强化的成功率
    pub fn calculate_success_rate(&self) -> f32 {
        match self.level {
            0..=5 => 0.9,      // 90% 成功率
            6..=10 => 0.7,     // 70% 成功率
            11..=15 => 0.5,    // 50% 成功率
            16..=19 => 0.3,    // 30% 成功率
            20 => 0.0,         // 最高等级，无法继续强化
            _ => 0.0,
        }
    }
    
    /// 获取强化失败惩罚
    pub fn get_failure_penalty(&self) -> FailurePenalty {
        match self.level {
            0..=10 => FailurePenalty::None,
            11..=15 => FailurePenalty::LevelDown(1),
            16..=19 => FailurePenalty::LevelDown(2),
            20 => FailurePenalty::Destruction,
            _ => FailurePenalty::None,
        }
    }
}

impl EquipmentBar {
    /// 创建新的装备栏
    pub fn new(max_capacity: usize) -> Self {
        Self {
            equipped: HashMap::new(),
            inventory: Vec::new(),
            max_capacity,
        }
    }
    
    /// 装备一件装备
    pub fn equip(&mut self, mut equipment: Equipment) -> Result<Option<Equipment>, String> {
        equipment.is_equipped = true;
        let eq_type = equipment.eq_type.clone();
        
        // 如果该位置已有装备，先卸下
        let old_equipment = if let Some(mut old) = self.equipped.remove(&eq_type) {
            old.is_equipped = false;
            Some(old)
        } else {
            None
        };
        
        // 装备新装备
        self.equipped.insert(eq_type, equipment);
        
        Ok(old_equipment)
    }
    
    /// 卸下装备
    pub fn unequip(&mut self, eq_type: &EquipmentType) -> Option<Equipment> {
        if let Some(mut equipment) = self.equipped.remove(eq_type) {
            equipment.is_equipped = false;
            Some(equipment)
        } else {
            None
        }
    }
    
    /// 获取指定类型的已装备装备
    pub fn get_equipped(&self, eq_type: &EquipmentType) -> Option<&Equipment> {
        self.equipped.get(eq_type)
    }
    
    /// 获取所有已装备装备的总属性加成
    pub fn get_total_equipment_attributes(&self) -> AttributeSet {
        let mut total = AttributeSet::new();
        
        for equipment in self.equipped.values() {
            total = total.merge(&equipment.get_total_attributes());
        }
        
        total
    }
    
    /// 添加装备到背包
    pub fn add_to_inventory(&mut self, equipment: Equipment) -> Result<(), String> {
        if self.inventory.len() >= self.max_capacity {
            return Err("背包已满".to_string());
        }
        
        self.inventory.push(equipment);
        Ok(())
    }
    
    /// 从背包移除装备
    pub fn remove_from_inventory(&mut self, equipment_id: u32) -> Option<Equipment> {
        if let Some(index) = self.inventory.iter().position(|eq| eq.id == equipment_id) {
            Some(self.inventory.remove(index))
        } else {
            None
        }
    }
    
    /// 按品质排序背包装备
    pub fn sort_inventory_by_quality(&mut self) {
        self.inventory.sort_by(|a, b| b.quality.cmp(&a.quality));
    }
    
    /// 按评分排序背包装备
    pub fn sort_inventory_by_score(&mut self) {
        self.inventory.sort_by(|a, b| {
            b.get_equipment_score().partial_cmp(&a.get_equipment_score()).unwrap()
        });
    }
}

impl SetManager {
    /// 创建新的套装管理器
    pub fn new() -> Self {
        Self {
            sets: HashMap::new(),
            active_sets: HashMap::new(),
        }
    }
    
    /// 添加套装定义
    pub fn add_set(&mut self, set_id: u32, set_effect: SetEffect) {
        self.sets.insert(set_id, set_effect);
    }
    
    /// 更新激活的套装状态
    pub fn update_active_sets(&mut self, equipped: &HashMap<EquipmentType, Equipment>) {
        self.active_sets.clear();
        
        // 统计每个套装的装备件数
        for equipment in equipped.values() {
            if let Some(set_id) = equipment.set_id {
                *self.active_sets.entry(set_id).or_insert(0) += 1;
            }
        }
    }
    
    /// 获取当前激活的套装效果
    pub fn get_active_set_effects(&self) -> Vec<&SetEffect> {
        let mut effects = Vec::new();
        
        for (&set_id, &count) in &self.active_sets {
            if let Some(set_effect) = self.sets.get(&set_id) {
                if count >= set_effect.required_pieces {
                    effects.push(set_effect);
                }
            }
        }
        
        effects
    }
    
    /// 获取套装效果的总属性加成
    pub fn get_total_set_attributes(&self) -> AttributeSet {
        let mut total = AttributeSet::new();
        
        for set_effect in self.get_active_set_effects() {
            total = total.merge(&set_effect.bonus_attributes);
        }
        
        total
    }
}

// ============================================================================
// 装备品质和类型的显示实现
// ============================================================================

impl std::fmt::Display for EquipmentQuality {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let (name, color) = match self {
            EquipmentQuality::Common => ("凡品", "白色"),
            EquipmentQuality::Uncommon => ("不凡", "绿色"),
            EquipmentQuality::Rare => ("稀有", "蓝色"),
            EquipmentQuality::Epic => ("史诗", "紫色"),
            EquipmentQuality::Legendary => ("传说", "橙色"),
            EquipmentQuality::Artifact => ("神器", "红色"),
        };
        write!(f, "{}({})", name, color)
    }
}

impl std::fmt::Display for EquipmentType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let name = match self {
            EquipmentType::Weapon => "武器",
            EquipmentType::Armor => "防具",
            EquipmentType::Accessory => "饰品",
            EquipmentType::Helmet => "头盔",
            EquipmentType::Boots => "靴子",
            EquipmentType::Shield => "盾牌",
            EquipmentType::Gloves => "手套",
            EquipmentType::Belt => "腰带",
        };
        write!(f, "{}", name)
    }
}

// ============================================================================
// 默认实现
// ============================================================================

impl Default for EquipmentBar {
    fn default() -> Self {
        Self::new(50) // 默认背包容量50
    }
}

impl Default for SetManager {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for EnhancementInfo {
    fn default() -> Self {
        Self::new()
    }
}