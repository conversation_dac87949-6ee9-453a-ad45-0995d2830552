//! # 验证引擎模块
//!
//! 统一的验证引擎，协调各种验证器和验证流程
//!
//! ## 核心功能
//!
//! - 多阶段验证流程
//! - 验证器管理和调度
//! - 验证结果聚合
//! - 性能监控和优化
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::validation::{ValidationEngine, ValidationRequest};
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let engine = ValidationEngine::new().await?;
//!
//! let request = ValidationRequest::new()
//!     .with_schema_validation(true)
//!     .with_business_validation(true)
//!     .with_integrity_check(true);
//!
//! let config = serde_json::json!({
//!     "rules": [
//!         {"id": "rule1", "name": "Test Rule"}
//!     ]
//! });
//!
//! let result = engine.validate(&config, &request).await?;
//!
//! if !result.is_valid {
//!     for error in result.errors {
//!         println!("验证错误: {}", error.message);
//!     }
//! }
//! # Ok(())
//! # }
//! ```

use crate::config_engine::validation::{
    ValidatableData, ValidationError, ValidationOptions, ValidationResult, ValidationSource,
    Validator,
};
use crate::config_engine::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

/// 验证请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationRequest {
    /// 数据源标识
    pub source_id: String,
    /// 验证选项
    pub options: ValidationOptions,
    /// 自定义参数
    pub parameters: HashMap<String, String>,
    /// 验证范围
    pub scope: ValidationScope,
    /// 请求优先级
    pub priority: ValidationPriority,
    /// 验证标签（用于分组和过滤）
    pub tags: HashMap<String, String>,
}

impl ValidationRequest {
    /// 创建新的验证请求
    pub fn new() -> Self {
        Self {
            source_id: uuid::Uuid::new_v4().to_string(),
            options: ValidationOptions::default(),
            parameters: HashMap::new(),
            scope: ValidationScope::Full,
            priority: ValidationPriority::Normal,
            tags: HashMap::new(),
        }
    }

    /// 设置源ID
    pub fn with_source_id(mut self, source_id: impl Into<String>) -> Self {
        self.source_id = source_id.into();
        self
    }

    /// 设置验证选项
    pub fn with_options(mut self, options: ValidationOptions) -> Self {
        self.options = options;
        self
    }

    /// 启用Schema验证
    pub fn with_schema_validation(mut self, enabled: bool) -> Self {
        self.options.enable_schema_validation = enabled;
        self
    }

    /// 启用业务规则验证
    pub fn with_business_validation(mut self, enabled: bool) -> Self {
        self.options.enable_business_validation = enabled;
        self
    }

    /// 启用完整性检查
    pub fn with_integrity_check(mut self, enabled: bool) -> Self {
        self.options.enable_integrity_check = enabled;
        self
    }

    /// 启用类型验证
    pub fn with_type_validation(mut self, enabled: bool) -> Self {
        self.options.enable_type_validation = enabled;
        self
    }

    /// 设置快速失败
    pub fn with_fail_fast(mut self, fail_fast: bool) -> Self {
        self.options.fail_fast = fail_fast;
        self
    }

    /// 设置最大错误数
    pub fn with_max_errors(mut self, max_errors: usize) -> Self {
        self.options.max_errors = Some(max_errors);
        self
    }

    /// 设置超时时间
    pub fn with_timeout(mut self, timeout_ms: u64) -> Self {
        self.options.timeout_ms = Some(timeout_ms);
        self
    }

    /// 添加参数
    pub fn with_parameter(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.parameters.insert(key.into(), value.into());
        self
    }

    /// 设置验证范围
    pub fn with_scope(mut self, scope: ValidationScope) -> Self {
        self.scope = scope;
        self
    }

    /// 设置优先级
    pub fn with_priority(mut self, priority: ValidationPriority) -> Self {
        self.priority = priority;
        self
    }

    /// 添加标签
    pub fn with_tag(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.tags.insert(key.into(), value.into());
        self
    }

    /// 获取参数
    pub fn get_parameter(&self, key: &str) -> Option<&String> {
        self.parameters.get(key)
    }

    /// 获取标签
    pub fn get_tag(&self, key: &str) -> Option<&String> {
        self.tags.get(key)
    }
}

impl Default for ValidationRequest {
    fn default() -> Self {
        Self::new()
    }
}

/// 验证范围
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ValidationScope {
    /// 完整验证
    Full,
    /// 仅验证指定的类型
    TypesOnly(Vec<String>),
    /// 仅验证指定的规则
    RulesOnly(Vec<String>),
    /// 增量验证（仅验证更改的部分）
    Incremental,
    /// 自定义范围
    Custom(String),
}

/// 验证优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum ValidationPriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4,
}

/// 验证阶段
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum ValidationPhase {
    /// 预处理阶段
    PreProcessing,
    /// Schema验证阶段
    SchemaValidation,
    /// 类型验证阶段
    TypeValidation,
    /// 业务规则验证阶段
    BusinessValidation,
    /// 完整性检查阶段
    IntegrityCheck,
    /// 后处理阶段
    PostProcessing,
}

/// 验证执行计划
#[derive(Debug, Clone)]
pub struct ValidationPlan {
    /// 执行阶段
    pub phases: Vec<ValidationPhase>,
    /// 每个阶段的验证器
    pub phase_validators: HashMap<ValidationPhase, Vec<String>>,
    /// 并行执行配置
    pub parallel_config: ParallelConfig,
    /// 预估执行时间
    pub estimated_duration: Option<Duration>,
}

impl ValidationPlan {
    pub fn new() -> Self {
        Self {
            phases: vec![
                ValidationPhase::PreProcessing,
                ValidationPhase::SchemaValidation,
                ValidationPhase::TypeValidation,
                ValidationPhase::BusinessValidation,
                ValidationPhase::IntegrityCheck,
                ValidationPhase::PostProcessing,
            ],
            phase_validators: HashMap::new(),
            parallel_config: ParallelConfig::default(),
            estimated_duration: None,
        }
    }

    /// 添加验证器到指定阶段
    pub fn add_validator_to_phase(&mut self, phase: ValidationPhase, validator_name: String) {
        self.phase_validators
            .entry(phase)
            .or_insert_with(Vec::new)
            .push(validator_name);
    }

    /// 设置并行配置
    pub fn with_parallel_config(mut self, config: ParallelConfig) -> Self {
        self.parallel_config = config;
        self
    }

    /// 设置预估执行时间
    pub fn with_estimated_duration(mut self, duration: Duration) -> Self {
        self.estimated_duration = Some(duration);
        self
    }
}

impl Default for ValidationPlan {
    fn default() -> Self {
        Self::new()
    }
}

/// 并行执行配置
#[derive(Debug, Clone)]
pub struct ParallelConfig {
    /// 是否启用并行执行
    pub enabled: bool,
    /// 最大并行任务数
    pub max_concurrent_tasks: usize,
    /// 任务分批大小
    pub batch_size: usize,
    /// 阶段间并行
    pub inter_phase_parallel: bool,
}

impl Default for ParallelConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_concurrent_tasks: num_cpus::get(),
            batch_size: 100,
            inter_phase_parallel: false,
        }
    }
}

/// 验证引擎
pub struct ValidationEngine {
    /// 注册的验证器
    validators: Arc<RwLock<HashMap<String, Box<dyn Validator>>>>,
    /// 验证器优先级
    validator_priorities: Arc<RwLock<HashMap<String, i32>>>,
    /// 验证历史
    validation_history: Arc<RwLock<Vec<ValidationSession>>>,
    /// 性能统计
    performance_stats: Arc<RwLock<ValidationPerformanceStats>>,
    /// 配置选项
    config: ValidationEngineConfig,
}

impl ValidationEngine {
    /// 创建新的验证引擎
    pub async fn new() -> Result<Self> {
        Self::with_config(ValidationEngineConfig::default()).await
    }

    /// 使用配置创建验证引擎
    pub async fn with_config(config: ValidationEngineConfig) -> Result<Self> {
        let engine = Self {
            validators: Arc::new(RwLock::new(HashMap::new())),
            validator_priorities: Arc::new(RwLock::new(HashMap::new())),
            validation_history: Arc::new(RwLock::new(Vec::new())),
            performance_stats: Arc::new(RwLock::new(ValidationPerformanceStats::new())),
            config,
        };

        // 初始化默认验证器
        engine.initialize_default_validators().await?;

        Ok(engine)
    }

    /// 注册验证器
    pub async fn register_validator(
        &self,
        name: String,
        validator: Box<dyn Validator>,
        priority: i32,
    ) -> Result<()> {
        {
            let mut validators = self.validators.write().await;
            validators.insert(name.clone(), validator);
        }

        {
            let mut priorities = self.validator_priorities.write().await;
            priorities.insert(name, priority);
        }

        Ok(())
    }

    /// 注销验证器
    pub async fn unregister_validator(&self, name: &str) -> Result<bool> {
        let removed = {
            let mut validators = self.validators.write().await;
            validators.remove(name).is_some()
        };

        if removed {
            let mut priorities = self.validator_priorities.write().await;
            priorities.remove(name);
        }

        Ok(removed)
    }

    /// 获取已注册的验证器列表
    pub async fn list_validators(&self) -> Vec<String> {
        let validators = self.validators.read().await;
        validators.keys().cloned().collect()
    }

    /// 执行验证
    pub async fn validate(
        &self,
        data: &serde_json::Value,
        request: &ValidationRequest,
    ) -> Result<ValidationResult> {
        let session = ValidationSession::new(request.clone());
        let session_id = session.id.clone();

        // 记录验证开始
        {
            let mut history = self.validation_history.write().await;
            history.push(session);
        }

        let start_time = Instant::now();
        let mut result = ValidationResult::new();

        // 创建验证执行计划
        let plan = self.create_validation_plan(request).await?;

        // 执行验证计划
        match self.execute_validation_plan(&plan, data, request).await {
            Ok(validation_result) => {
                result = validation_result;
            }
            Err(e) => {
                result.add_error(ValidationError::new(
                    "validation_engine_error".to_string(),
                    format!("Validation engine error: {}", e),
                    ValidationSource::Configuration,
                ));
            }
        }

        // 更新性能统计
        let duration = start_time.elapsed();
        self.update_performance_stats(&result, duration).await;

        // 更新验证历史
        self.update_validation_session(&session_id, &result, duration)
            .await;

        Ok(result)
    }

    /// 批量验证
    pub async fn validate_batch(
        &self,
        data_items: Vec<(&serde_json::Value, &ValidationRequest)>,
    ) -> Result<Vec<ValidationResult>> {
        if self.config.enable_batch_optimization {
            self.validate_batch_optimized(data_items).await
        } else {
            self.validate_batch_sequential(data_items).await
        }
    }

    /// 验证单个数据对象
    pub async fn validate_object(
        &self,
        data: &dyn ValidatableData,
        request: &ValidationRequest,
    ) -> Result<ValidationResult> {
        let json_value = data.to_json_value();
        self.validate(&json_value, request).await
    }

    /// 获取验证统计信息
    pub async fn get_statistics(&self) -> ValidationEngineStatistics {
        let validators = self.validators.read().await;
        let history = self.validation_history.read().await;
        let perf_stats = self.performance_stats.read().await;

        ValidationEngineStatistics {
            total_validators: validators.len(),
            total_validations: history.len(),
            average_validation_time: perf_stats.average_validation_time,
            total_errors: perf_stats.total_errors,
            total_warnings: perf_stats.total_warnings,
            cache_hit_rate: perf_stats.cache_hit_rate,
        }
    }

    /// 获取验证历史
    pub async fn get_validation_history(&self, limit: Option<usize>) -> Vec<ValidationSession> {
        let history = self.validation_history.read().await;
        match limit {
            Some(n) => history.iter().rev().take(n).cloned().collect(),
            None => history.clone(),
        }
    }

    /// 清除验证历史
    pub async fn clear_validation_history(&self) {
        let mut history = self.validation_history.write().await;
        history.clear();
    }

    /// 健康检查
    pub async fn health_check(&self) -> ValidationEngineHealth {
        let validators = self.validators.read().await;
        let perf_stats = self.performance_stats.read().await;

        let is_healthy = validators.len() > 0
            && perf_stats.average_validation_time < Duration::from_secs(30)
            && perf_stats.error_rate < 0.5;

        ValidationEngineHealth {
            is_healthy,
            validator_count: validators.len(),
            average_response_time: perf_stats.average_validation_time,
            error_rate: perf_stats.error_rate,
            memory_usage: self.estimate_memory_usage().await,
        }
    }

    /// 创建验证执行计划
    async fn create_validation_plan(&self, request: &ValidationRequest) -> Result<ValidationPlan> {
        let mut plan = ValidationPlan::new();

        // 根据请求配置调整执行计划
        if !request.options.enable_schema_validation {
            plan.phases
                .retain(|p| *p != ValidationPhase::SchemaValidation);
        }

        if !request.options.enable_business_validation {
            plan.phases
                .retain(|p| *p != ValidationPhase::BusinessValidation);
        }

        if !request.options.enable_integrity_check {
            plan.phases
                .retain(|p| *p != ValidationPhase::IntegrityCheck);
        }

        if !request.options.enable_type_validation {
            plan.phases
                .retain(|p| *p != ValidationPhase::TypeValidation);
        }

        // 分配验证器到各个阶段
        let validators = self.validators.read().await;
        let priorities = self.validator_priorities.read().await;

        for (validator_name, validator) in validators.iter() {
            let priority = priorities.get(validator_name).unwrap_or(&0);

            // 根据验证器类型分配到相应阶段
            if validator_name.contains("schema") {
                plan.add_validator_to_phase(
                    ValidationPhase::SchemaValidation,
                    validator_name.clone(),
                );
            } else if validator_name.contains("business") {
                plan.add_validator_to_phase(
                    ValidationPhase::BusinessValidation,
                    validator_name.clone(),
                );
            } else if validator_name.contains("integrity") {
                plan.add_validator_to_phase(
                    ValidationPhase::IntegrityCheck,
                    validator_name.clone(),
                );
            } else if validator_name.contains("type") {
                plan.add_validator_to_phase(
                    ValidationPhase::TypeValidation,
                    validator_name.clone(),
                );
            }
        }

        // 配置并行执行
        if request.options.parallel {
            plan.parallel_config.enabled = true;
        }

        Ok(plan)
    }

    /// 执行验证计划
    async fn execute_validation_plan(
        &self,
        plan: &ValidationPlan,
        data: &serde_json::Value,
        request: &ValidationRequest,
    ) -> Result<ValidationResult> {
        let mut final_result = ValidationResult::new();
        let mut context =
            crate::config_engine::validation::ValidationContext::new(request.options.clone());

        for phase in &plan.phases {
            if !context.should_continue() {
                break;
            }

            let phase_result = self
                .execute_validation_phase(phase, plan, data, &mut context)
                .await?;
            final_result.merge(phase_result);
        }

        // 更新执行时间
        final_result.stats.validation_time_ms = context.start_time.elapsed().as_millis() as u64;

        Ok(final_result)
    }

    /// 执行验证阶段
    async fn execute_validation_phase(
        &self,
        phase: &ValidationPhase,
        plan: &ValidationPlan,
        data: &serde_json::Value,
        context: &mut crate::config_engine::validation::ValidationContext,
    ) -> Result<ValidationResult> {
        let mut phase_result = ValidationResult::new();

        if let Some(validator_names) = plan.phase_validators.get(phase) {
            let validators = self.validators.read().await;

            if plan.parallel_config.enabled && validator_names.len() > 1 {
                // 并行执行验证器
                let mut tasks = Vec::new();

                for validator_name in validator_names {
                    if let Some(validator) = validators.get(validator_name) {
                        let validator_name = validator_name.clone();
                        let data_clone = data.clone();
                        let mut context_clone = context.clone();

                        // 这里应该使用实际的异步任务，简化实现
                        let validatable_data = JsonValidatableData::new(data_clone);
                        match validator.validate(&mut context_clone, &validatable_data) {
                            Ok(_) => {
                                phase_result.merge(context_clone.result.clone());
                            }
                            Err(e) => {
                                phase_result.add_error(ValidationError::new(
                                    format!("validator_error_{}", validator_name),
                                    format!("Validator '{}' error: {}", validator_name, e),
                                    ValidationSource::Configuration,
                                ));
                            }
                        }
                    }
                }
            } else {
                // 顺序执行验证器
                for validator_name in validator_names {
                    if !context.should_continue() {
                        break;
                    }

                    if let Some(validator) = validators.get(validator_name) {
                        let validatable_data = JsonValidatableData::new(data.clone());
                        match validator.validate(context, &validatable_data) {
                            Ok(_) => {
                                // 验证结果已经在context中
                            }
                            Err(e) => {
                                context.add_error(ValidationError::new(
                                    format!("validator_error_{}", validator_name),
                                    format!("Validator '{}' error: {}", validator_name, e),
                                    ValidationSource::Configuration,
                                ));
                            }
                        }
                    }
                }

                phase_result.merge(context.result.clone());
            }
        }

        Ok(phase_result)
    }

    /// 批量验证（优化版本）
    async fn validate_batch_optimized(
        &self,
        data_items: Vec<(&serde_json::Value, &ValidationRequest)>,
    ) -> Result<Vec<ValidationResult>> {
        // 简化实现：实际应该进行批量优化
        self.validate_batch_sequential(data_items).await
    }

    /// 批量验证（顺序版本）
    async fn validate_batch_sequential(
        &self,
        data_items: Vec<(&serde_json::Value, &ValidationRequest)>,
    ) -> Result<Vec<ValidationResult>> {
        let mut results = Vec::new();

        for (data, request) in data_items {
            let result = self.validate(data, request).await?;
            results.push(result);
        }

        Ok(results)
    }

    /// 初始化默认验证器
    async fn initialize_default_validators(&self) -> Result<()> {
        // 这里应该注册默认的验证器
        // 简化实现：暂时不注册任何验证器
        Ok(())
    }

    /// 更新性能统计
    async fn update_performance_stats(&self, result: &ValidationResult, duration: Duration) {
        let mut stats = self.performance_stats.write().await;
        stats.total_validations += 1;
        stats.total_errors += result.error_count();
        stats.total_warnings += result.warning_count();

        // 更新平均验证时间
        let total_time = stats.average_validation_time.as_millis() as u64
            * (stats.total_validations - 1)
            + duration.as_millis() as u64;
        stats.average_validation_time =
            Duration::from_millis(total_time / stats.total_validations as u64);

        // 更新错误率
        stats.error_rate = stats.total_errors as f64 / stats.total_validations as f64;
    }

    /// 更新验证会话
    async fn update_validation_session(
        &self,
        session_id: &str,
        result: &ValidationResult,
        duration: Duration,
    ) {
        let mut history = self.validation_history.write().await;

        if let Some(session) = history.iter_mut().find(|s| s.id == session_id) {
            session.end_time = Some(chrono::Utc::now());
            session.duration = Some(duration);
            session.result = Some(result.clone());
        }
    }

    /// 估算内存使用量
    async fn estimate_memory_usage(&self) -> usize {
        // 简化实现：返回固定值
        // 实际应该计算验证器、历史记录等的内存使用量
        1024 * 1024 // 1MB
    }
}

/// 验证引擎配置
#[derive(Debug, Clone)]
pub struct ValidationEngineConfig {
    /// 最大并发验证数
    pub max_concurrent_validations: usize,
    /// 启用缓存
    pub enable_caching: bool,
    /// 缓存大小限制
    pub cache_size_limit: usize,
    /// 启用批量优化
    pub enable_batch_optimization: bool,
    /// 最大验证历史记录数
    pub max_history_size: usize,
    /// 性能监控间隔
    pub performance_monitoring_interval: Duration,
}

impl Default for ValidationEngineConfig {
    fn default() -> Self {
        Self {
            max_concurrent_validations: 100,
            enable_caching: true,
            cache_size_limit: 10000,
            enable_batch_optimization: true,
            max_history_size: 1000,
            performance_monitoring_interval: Duration::from_secs(60),
        }
    }
}

/// 验证会话
#[derive(Debug, Clone)]
pub struct ValidationSession {
    /// 会话ID
    pub id: String,
    /// 验证请求
    pub request: ValidationRequest,
    /// 开始时间
    pub start_time: chrono::DateTime<chrono::Utc>,
    /// 结束时间
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    /// 执行时长
    pub duration: Option<Duration>,
    /// 验证结果
    pub result: Option<ValidationResult>,
}

impl ValidationSession {
    pub fn new(request: ValidationRequest) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            request,
            start_time: chrono::Utc::now(),
            end_time: None,
            duration: None,
            result: None,
        }
    }
}

/// 验证性能统计
#[derive(Debug, Clone)]
pub struct ValidationPerformanceStats {
    pub total_validations: usize,
    pub total_errors: usize,
    pub total_warnings: usize,
    pub average_validation_time: Duration,
    pub error_rate: f64,
    pub cache_hit_rate: f64,
}

impl ValidationPerformanceStats {
    pub fn new() -> Self {
        Self {
            total_validations: 0,
            total_errors: 0,
            total_warnings: 0,
            average_validation_time: Duration::from_millis(0),
            error_rate: 0.0,
            cache_hit_rate: 0.0,
        }
    }
}

/// 验证引擎统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationEngineStatistics {
    pub total_validators: usize,
    pub total_validations: usize,
    pub average_validation_time: Duration,
    pub total_errors: usize,
    pub total_warnings: usize,
    pub cache_hit_rate: f64,
}

/// 验证引擎健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationEngineHealth {
    pub is_healthy: bool,
    pub validator_count: usize,
    pub average_response_time: Duration,
    pub error_rate: f64,
    pub memory_usage: usize,
}

/// JSON数据的可验证数据实现
struct JsonValidatableData {
    data: serde_json::Value,
}

impl JsonValidatableData {
    fn new(data: serde_json::Value) -> Self {
        Self { data }
    }
}

impl ValidatableData for JsonValidatableData {
    fn data_type(&self) -> String {
        match &self.data {
            serde_json::Value::Object(_) => "object".to_string(),
            serde_json::Value::Array(_) => "array".to_string(),
            serde_json::Value::String(_) => "string".to_string(),
            serde_json::Value::Number(_) => "number".to_string(),
            serde_json::Value::Bool(_) => "boolean".to_string(),
            serde_json::Value::Null => "null".to_string(),
        }
    }

    fn data_id(&self) -> String {
        // 尝试从数据中提取ID
        if let Some(obj) = self.data.as_object() {
            if let Some(id) = obj.get("id") {
                if let Some(id_str) = id.as_str() {
                    return id_str.to_string();
                }
            }
        }
        uuid::Uuid::new_v4().to_string()
    }

    fn to_json_value(&self) -> serde_json::Value {
        self.data.clone()
    }

    fn get_related_data(&self, _relation: &str) -> Option<Box<dyn ValidatableData>> {
        // 简化实现：返回None
        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_validation_request_creation() {
        let request = ValidationRequest::new()
            .with_source_id("test_source")
            .with_schema_validation(true)
            .with_business_validation(true)
            .with_parameter("param1", "value1")
            .with_tag("category", "test");

        assert_eq!(request.source_id, "test_source");
        assert!(request.options.enable_schema_validation);
        assert!(request.options.enable_business_validation);
        assert_eq!(request.get_parameter("param1"), Some(&"value1".to_string()));
        assert_eq!(request.get_tag("category"), Some(&"test".to_string()));
    }

    #[tokio::test]
    async fn test_validation_plan_creation() {
        let mut plan = ValidationPlan::new();
        plan.add_validator_to_phase(
            ValidationPhase::SchemaValidation,
            "schema_validator".to_string(),
        );
        plan.add_validator_to_phase(
            ValidationPhase::BusinessValidation,
            "business_validator".to_string(),
        );

        assert!(plan.phases.contains(&ValidationPhase::SchemaValidation));
        assert!(plan.phases.contains(&ValidationPhase::BusinessValidation));

        let schema_validators = plan
            .phase_validators
            .get(&ValidationPhase::SchemaValidation)
            .unwrap();
        assert!(schema_validators.contains(&"schema_validator".to_string()));
    }

    #[tokio::test]
    async fn test_validation_engine_creation() {
        let engine = ValidationEngine::new().await.unwrap();
        let validators = engine.list_validators().await;

        // 初始状态应该没有验证器
        assert_eq!(validators.len(), 0);
    }

    #[tokio::test]
    async fn test_validator_registration() {
        let engine = ValidationEngine::new().await.unwrap();

        // 这里需要一个测试验证器实现
        // 由于Validator trait的复杂性，这里简化测试
        let initial_count = engine.list_validators().await.len();

        // 验证初始状态
        assert_eq!(initial_count, 0);
    }

    #[tokio::test]
    async fn test_validation_session() {
        let request = ValidationRequest::new().with_source_id("test");
        let session = ValidationSession::new(request);

        assert_eq!(session.request.source_id, "test");
        assert!(session.end_time.is_none());
        assert!(session.duration.is_none());
        assert!(session.result.is_none());
    }

    #[tokio::test]
    async fn test_validation_engine_statistics() {
        let engine = ValidationEngine::new().await.unwrap();
        let stats = engine.get_statistics().await;

        assert_eq!(stats.total_validators, 0);
        assert_eq!(stats.total_validations, 0);
        assert_eq!(stats.total_errors, 0);
        assert_eq!(stats.total_warnings, 0);
    }

    #[tokio::test]
    async fn test_validation_engine_health() {
        let engine = ValidationEngine::new().await.unwrap();
        let health = engine.health_check().await;

        // 没有验证器时应该是不健康的
        assert!(!health.is_healthy);
        assert_eq!(health.validator_count, 0);
    }

    #[tokio::test]
    async fn test_json_validatable_data() {
        let data = serde_json::json!({
            "id": "test_id",
            "name": "test_name",
            "value": 123
        });

        let validatable = JsonValidatableData::new(data.clone());

        assert_eq!(validatable.data_type(), "object");
        assert_eq!(validatable.data_id(), "test_id");
        assert_eq!(validatable.to_json_value(), data);
    }

    #[tokio::test]
    async fn test_validation_engine_config() {
        let config = ValidationEngineConfig {
            max_concurrent_validations: 50,
            enable_caching: false,
            cache_size_limit: 5000,
            ..Default::default()
        };

        assert_eq!(config.max_concurrent_validations, 50);
        assert!(!config.enable_caching);
        assert_eq!(config.cache_size_limit, 5000);
    }

    #[tokio::test]
    async fn test_validation_scope() {
        let scope_full = ValidationScope::Full;
        let scope_types =
            ValidationScope::TypesOnly(vec!["Material".to_string(), "Tool".to_string()]);
        let scope_rules = ValidationScope::RulesOnly(vec!["rule1".to_string()]);

        assert_eq!(scope_full, ValidationScope::Full);

        if let ValidationScope::TypesOnly(types) = scope_types {
            assert_eq!(types.len(), 2);
            assert!(types.contains(&"Material".to_string()));
        }

        if let ValidationScope::RulesOnly(rules) = scope_rules {
            assert_eq!(rules.len(), 1);
            assert!(rules.contains(&"rule1".to_string()));
        }
    }

    #[tokio::test]
    async fn test_validation_priority() {
        assert!(ValidationPriority::Critical > ValidationPriority::High);
        assert!(ValidationPriority::High > ValidationPriority::Normal);
        assert!(ValidationPriority::Normal > ValidationPriority::Low);
    }
}
