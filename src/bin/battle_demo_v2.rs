/// 战斗演示程序 V2 - 使用BattleView架构
///
/// 这是使用方案二（分离战斗视图和聚合根）的演示程序
/// 展示了新的优化战斗引擎的能力，同时解决了生命周期和所有权问题
use game::battle_system::performance::battle_engine::{
    BattleEndStatus, EngineOptimization, OptimizedBattleEngine,
};
use game::battle_system::BattleView;
use game::character::domain::character_aggregate::CharacterBuilder;
use game::character::domain::value_objects::BasicAttributes;
use game::character::Character;
use game::monster::Monster;
use game::monster_profile::MonsterProfile;
use game::shared::{CharacterId, GameResult, MonsterId, Position};
use game::world_map::Position as WorldMapPosition;

fn main() -> GameResult<()> {
    println!("🎮 === 高性能战斗引擎演示 V2 ===");
    println!("🔄 使用BattleView架构，解决生命周期问题");
    println!();

    // 演示不同优化级别的战斗引擎
    demo_basic_battle()?;
    println!();

    demo_optimized_battle()?;
    println!();

    demo_high_performance_battle()?;
    println!();

    demo_performance_comparison()?;

    Ok(())
}

fn demo_basic_battle() -> GameResult<()> {
    println!("⚡ === 1. 基础战斗演示 ===");
    println!("🔄 特点：最小开销，无优化组件");
    println!();

    let config = EngineOptimization::basic().to_config();
    let mut engine = OptimizedBattleEngine::new(config);

    // 创建参与者
    let participants = create_test_participants()?;

    // 显示参与者信息
    display_participants(&participants);

    // 初始化战斗
    let battle_id = engine.initialize_battle(participants)?;
    println!("✅ 战斗初始化完成，ID: {:?}", battle_id);

    // 模拟几个回合
    simulate_turns(&mut engine, battle_id, 3)?;

    Ok(())
}

fn demo_optimized_battle() -> GameResult<()> {
    println!("⚡ === 2. 优化战斗演示 ===");
    println!("🔄 特点：标准优化配置，平衡性能和资源使用");
    println!();

    let config = EngineOptimization::standard().to_config();
    let mut engine = OptimizedBattleEngine::new(config);

    // 创建更强的参与者
    let mut participants = create_test_participants()?;

    // 增强参与者属性
    for participant in &mut participants {
        participant.attack_power += 20;
        participant.defense_power += 10;
        participant.max_health += 50;
        participant.current_health = participant.max_health;
    }

    display_participants(&participants);

    let battle_id = engine.initialize_battle(participants)?;
    println!("✅ 优化战斗初始化完成，ID: {:?}", battle_id);

    // 模拟更多回合
    simulate_turns(&mut engine, battle_id, 5)?;

    // 显示性能统计
    let stats = engine.get_performance_snapshot();
    display_performance_stats(&stats);

    Ok(())
}

fn demo_high_performance_battle() -> GameResult<()> {
    println!("⚡ === 3. 高性能战斗演示 ===");
    println!("🔄 特点：最大化性能优化，适用于大规模战斗");
    println!();

    let config = EngineOptimization::high_performance().to_config();
    let mut engine = OptimizedBattleEngine::new(config);

    // 创建更多参与者
    let mut participants = Vec::new();

    // 创建玩家队伍
    for i in 1..=2 {
        let character = create_balanced_character(i, &format!("战士{}", i), 25, 20, 18)?;
        participants.push(BattleView::from_character(&character));
    }

    // 创建敌人队伍
    for i in 1..=3 {
        let monster = create_balanced_monster(i, &format!("哥布林{}", i))?;
        participants.push(BattleView::from_monster(&monster));
    }

    display_participants(&participants);

    let battle_id = engine.initialize_battle(participants)?;
    println!("✅ 高性能战斗初始化完成，ID: {:?}", battle_id);

    // 模拟长时间战斗
    simulate_turns(&mut engine, battle_id, 8)?;

    // 显示详细性能统计
    let stats = engine.get_performance_snapshot();
    display_detailed_performance_stats(&stats);

    Ok(())
}

fn demo_performance_comparison() -> GameResult<()> {
    println!("⚡ === 4. 性能对比演示 ===");
    println!("🔄 对比不同优化级别的性能差异");
    println!();

    use game::battle_system::performance::battle_engine::BenchmarkSuite;

    println!("📊 运行基准测试...");
    let comparison_results = BenchmarkSuite::compare_optimization_levels();

    for (level, results) in comparison_results {
        println!("\n🎯 优化级别: {:?}", level);
        println!(
            "   ⚔️ 技能执行: {:.2}μs平均, {:.0} ops/秒",
            results.skill_execution.avg_execution_time_us(),
            results.skill_execution.throughput_ops_per_sec
        );
        println!(
            "   🧠 AI决策: {:.2}μs平均, {:.0} ops/秒",
            results.ai_decision.avg_execution_time_us(),
            results.ai_decision.throughput_ops_per_sec
        );
        println!(
            "   🏊 对象池: {:.2}μs平均, {:.0} ops/秒",
            results.object_pooling.avg_execution_time_us(),
            results.object_pooling.throughput_ops_per_sec
        );
        println!(
            "   💾 内存效率: {:.1}分",
            results.memory_usage.memory_efficiency_score()
        );
    }

    Ok(())
}

/// 创建测试参与者
fn create_test_participants() -> GameResult<Vec<BattleView>> {
    let mut participants = Vec::new();

    // 创建玩家角色
    let player_char = create_balanced_character(1, "勇敢骑士", 20, 15, 12)?;
    participants.push(BattleView::from_character(&player_char));

    // 创建敌人
    let enemy_monster = create_balanced_monster(1, "森林狼")?;
    participants.push(BattleView::from_monster(&enemy_monster));

    Ok(participants)
}

/// 显示参与者信息
fn display_participants(participants: &[BattleView]) {
    println!("⚔️ 战斗参与者:");
    for (i, participant) in participants.iter().enumerate() {
        let icon = match participant.id {
            game::shared::BattleUnitId::Character(_) => "🦾",
            game::shared::BattleUnitId::Monster(_) => "👹",
        };

        println!(
            "   {} {}. {}: 生命值 {}/{}, 攻击力 {}, 防御力 {}",
            icon,
            i + 1,
            participant.name,
            participant.current_health,
            participant.max_health,
            participant.attack_power,
            participant.defense_power
        );
    }
}

/// 模拟战斗回合
fn simulate_turns(
    engine: &mut OptimizedBattleEngine,
    battle_id: game::battle_system::performance::battle_engine::BattleId,
    max_turns: u32,
) -> GameResult<()> {
    println!("\n🎮 开始战斗模拟:");

    for turn in 1..=max_turns {
        println!("\n--- 第{}回合 ---", turn);

        let turn_result = engine.execute_turn(battle_id)?;

        println!("⚡ 行动者: {:?}", turn_result.actor_id);
        println!("📊 行动结果: {:?}", turn_result.action_result);

        match turn_result.battle_status {
            BattleEndStatus::Victory(winner_id) => {
                println!("🏆 战斗结束！胜利者: {:?}", winner_id);
                break;
            }
            BattleEndStatus::Draw => {
                println!("🤝 战斗平局!");
                break;
            }
            BattleEndStatus::Defeat => {
                println!("💀 全军覆没!");
                break;
            }
            BattleEndStatus::Ongoing => {
                println!("⏳ 战斗继续...");
            }
        }
    }

    Ok(())
}

/// 显示性能统计
fn display_performance_stats(
    stats: &game::battle_system::performance::battle_engine::PerformanceSnapshot,
) {
    println!("\n📊 性能统计:");
    println!("   🧠 AI缓存命中率: {:.1}%", stats.cache_hit_rate * 100.0);
    println!("   💾 内存使用: {}KB", stats.memory_usage / 1024);
    println!("   🏊 对象池状态: {} 个池", stats.pool_stats.len());
}

/// 显示详细性能统计
fn display_detailed_performance_stats(
    stats: &game::battle_system::performance::battle_engine::PerformanceSnapshot,
) {
    display_performance_stats(stats);

    println!("\n📈 详细指标:");

    // 显示对象池统计
    for (pool_name, pool_stat) in &stats.pool_stats {
        println!(
            "   🏊 {}: 已用 {}/{}, 命中率 {:.1}%",
            pool_name,
            pool_stat.capacity - pool_stat.available,
            pool_stat.capacity,
            pool_stat.hit_rate * 100.0
        );
    }

    // 显示AI统计
    println!("   🧠 AI决策总数: {}", stats.ai_stats.total_decisions);
    println!("   🧠 AI缓存命中: {}", stats.ai_stats.cache_hits);
    println!(
        "   🧠 决策时间: {:.2}ms",
        stats.ai_stats.total_decision_time.as_millis() as f64
    );
}

/// 创建平衡的角色
fn create_balanced_character(
    id: u32,
    name: &str,
    _level: u32,
    str_val: u32,
    def_val: u32,
) -> GameResult<Character> {
    let basic_attrs = BasicAttributes::new(
        def_val, // 体质
        str_val, // 力量
        12,      // 精神
    );

    let position = Position::new(0.0, 0.0);

    CharacterBuilder::new()
        .with_id(CharacterId(id))
        .with_name(name.to_string())
        .with_attributes(basic_attrs)
        .with_position(position)
        .build()
}

/// 创建平衡的怪物
fn create_balanced_monster(id: u32, name: &str) -> GameResult<Monster> {
    let profile = MonsterProfile {
        kind: game::monster::MonsterKind::Normal,
        name: name.to_string(),
        level: 15,
        main_attribute: game::attribute::attribute::AttributeType::Base(
            game::attribute::attribute::CoreAttribute::Earth,
        ),
        grade: "Common".to_string(),
    };

    let position = WorldMapPosition {
        x: 1,
        y: 1,
        z: Some(0),
        layer: game::world_map::domain::spatial::WorldLayer::Mortal,
        region_id: Some(game::world_map::RegionId(0)),
    };

    Ok(Monster::new(MonsterId(id), profile, position))
}
