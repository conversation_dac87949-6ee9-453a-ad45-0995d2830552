/// 地形和环境相关的类型定义
/// 处理世界中的地形、气候和环境效果
use serde::{Deserialize, Serialize};

// use std::collections::HashMap; // 暂时未使用

// ============================================================================
// 地形类型
// ============================================================================

/// 地形类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TerrainType {
    /// 平原 - 基础地形，适合居住和建设
    Plains,
    /// 森林 - 富含木材和药草资源
    Forest,
    /// 山脉 - 富含矿物资源，地势险峻
    Mountains,
    /// 沙漠 - 干旱地区，资源稀少但可能有特殊矿物
    Desert,
    /// 水域 - 湖泊、河流等水体
    Water,
    /// 沼泽 - 潮湿环境，可能有特殊药材
    Swamp,
    /// 冰原 - 寒冷地区，有独特的冰系资源
    Tundra,
    /// 火山 - 活跃火山区域，有火系特殊材料
    Volcanic,
    /// 灵脉 - 灵气浓郁的特殊地形
    SpiritualVein,
    /// 虚空 - 混沌或破碎的空间
    Void,
}

impl TerrainType {
    /// 获取地形的基础灵气密度修正
    pub fn spiritual_density_modifier(&self) -> f32 {
        match self {
            TerrainType::SpiritualVein => 2.5,
            TerrainType::Mountains => 1.3,
            TerrainType::Forest => 1.2,
            TerrainType::Water => 1.1,
            TerrainType::Plains => 1.0,
            TerrainType::Swamp => 0.9,
            TerrainType::Desert => 0.7,
            TerrainType::Tundra => 0.8,
            TerrainType::Volcanic => 1.4,
            TerrainType::Void => 0.1,
        }
    }

    /// 获取地形的移动成本倍数
    pub fn movement_cost_multiplier(&self) -> f32 {
        match self {
            TerrainType::Plains => 1.0,
            TerrainType::Forest => 1.5,
            TerrainType::Mountains => 2.5,
            TerrainType::Desert => 1.8,
            TerrainType::Water => 3.0, // 需要游泳或船只
            TerrainType::Swamp => 2.0,
            TerrainType::Tundra => 1.6,
            TerrainType::Volcanic => 2.2,
            TerrainType::SpiritualVein => 1.2,
            TerrainType::Void => 5.0, // 极其危险和困难
        }
    }

    /// 获取地形适合的建筑类型
    pub fn suitable_buildings(&self) -> Vec<crate::world_map::domain::buildings::BuildingType> {
        use crate::world_map::domain::buildings::BuildingType;

        match self {
            TerrainType::Plains => vec![
                BuildingType::CaveAbode,
                BuildingType::SpiritualFarm,
                BuildingType::AlchemyLab,
                BuildingType::CraftingWorkshop,
                BuildingType::BeastPen,
                BuildingType::Library,
            ],
            TerrainType::Forest => vec![
                BuildingType::CaveAbode,
                BuildingType::AlchemyLab,
                BuildingType::BeastPen,
                BuildingType::Library,
            ],
            TerrainType::Mountains => vec![
                BuildingType::CaveAbode,
                BuildingType::CraftingWorkshop,
                BuildingType::Observatory,
                BuildingType::FormationPlatform,
            ],
            TerrainType::Desert => vec![BuildingType::CaveAbode, BuildingType::Observatory],
            TerrainType::Water => vec![
                // 水上建筑限制较多
                BuildingType::FormationPlatform,
            ],
            TerrainType::Swamp => vec![BuildingType::AlchemyLab, BuildingType::BeastPen],
            TerrainType::Tundra => vec![BuildingType::CaveAbode, BuildingType::CraftingWorkshop],
            TerrainType::Volcanic => vec![
                BuildingType::CraftingWorkshop,
                BuildingType::FormationPlatform,
            ],
            TerrainType::SpiritualVein => vec![
                BuildingType::CaveAbode,
                BuildingType::AlchemyLab,
                BuildingType::FormationPlatform,
                BuildingType::Observatory,
                BuildingType::TeleportationArray,
            ],
            TerrainType::Void => vec![
                // 虚空中建筑极其困难
                BuildingType::FormationPlatform,
            ],
        }
    }

    /// 获取地形的主要资源类型
    pub fn primary_resources(&self) -> Vec<crate::world_map::domain::resources::ResourceType> {
        use crate::world_map::domain::resources::ResourceType;

        match self {
            TerrainType::Plains => vec![ResourceType::Herbs, ResourceType::CraftingMaterial],
            TerrainType::Forest => vec![
                ResourceType::Herbs,
                ResourceType::BeastMaterial,
                ResourceType::CraftingMaterial,
            ],
            TerrainType::Mountains => vec![
                ResourceType::MetalOre,
                ResourceType::SpiritStone,
                ResourceType::CraftingMaterial,
            ],
            TerrainType::Desert => vec![ResourceType::SpiritStone, ResourceType::CraftingMaterial],
            TerrainType::Water => vec![ResourceType::Herbs, ResourceType::BeastMaterial],
            TerrainType::Swamp => vec![ResourceType::Herbs, ResourceType::BeastMaterial],
            TerrainType::Tundra => vec![ResourceType::MetalOre, ResourceType::CraftingMaterial],
            TerrainType::Volcanic => vec![
                ResourceType::MetalOre,
                ResourceType::SpiritStone,
                ResourceType::FormationMaterial,
            ],
            TerrainType::SpiritualVein => {
                vec![ResourceType::SpiritStone, ResourceType::FormationMaterial]
            }
            TerrainType::Void => vec![ResourceType::FormationMaterial], // 特殊的虚空材料
        }
    }
}

impl Default for TerrainType {
    fn default() -> Self {
        TerrainType::Plains
    }
}

// ============================================================================
// 气候类型
// ============================================================================

/// 气候类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ClimateType {
    /// 温带 - 四季分明，适宜居住
    Temperate,
    /// 热带 - 炎热潮湿，植物繁茂
    Tropical,
    /// 寒带 - 寒冷干燥，资源稀少
    Arctic,
    /// 干旱 - 少雨干燥，昼夜温差大
    Arid,
    /// 灵气充沛 - 特殊的修炼环境
    SpirituallyRich,
    /// 混沌气候 - 不稳定的环境条件
    Chaotic,
}

impl ClimateType {
    /// 获取气候对各种活动的影响修正
    pub fn activity_modifiers(&self) -> ClimateModifiers {
        match self {
            ClimateType::Temperate => ClimateModifiers {
                cultivation_bonus: 1.0,
                construction_speed: 1.0,
                resource_growth: 1.0,
                travel_comfort: 1.0,
                energy_consumption: 1.0,
            },
            ClimateType::Tropical => ClimateModifiers {
                cultivation_bonus: 1.1,
                construction_speed: 0.8,
                resource_growth: 1.5,
                travel_comfort: 0.7,
                energy_consumption: 1.2,
            },
            ClimateType::Arctic => ClimateModifiers {
                cultivation_bonus: 0.7,
                construction_speed: 0.6,
                resource_growth: 0.5,
                travel_comfort: 0.5,
                energy_consumption: 1.5,
            },
            ClimateType::Arid => ClimateModifiers {
                cultivation_bonus: 0.8,
                construction_speed: 0.9,
                resource_growth: 0.6,
                travel_comfort: 0.6,
                energy_consumption: 1.3,
            },
            ClimateType::SpirituallyRich => ClimateModifiers {
                cultivation_bonus: 2.0,
                construction_speed: 1.2,
                resource_growth: 1.8,
                travel_comfort: 1.2,
                energy_consumption: 0.8,
            },
            ClimateType::Chaotic => ClimateModifiers {
                cultivation_bonus: 0.5,
                construction_speed: 0.4,
                resource_growth: 0.3,
                travel_comfort: 0.2,
                energy_consumption: 2.0,
            },
        }
    }

    /// 获取气候的季节性变化强度
    pub fn seasonal_variation_intensity(&self) -> f32 {
        match self {
            ClimateType::Temperate => 1.0,
            ClimateType::Tropical => 0.3,
            ClimateType::Arctic => 1.5,
            ClimateType::Arid => 0.8,
            ClimateType::SpirituallyRich => 0.6,
            ClimateType::Chaotic => 2.0,
        }
    }
}

impl Default for ClimateType {
    fn default() -> Self {
        ClimateType::Temperate
    }
}

/// 气候修正值
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ClimateModifiers {
    /// 修炼效果加成
    pub cultivation_bonus: f32,
    /// 建造速度修正
    pub construction_speed: f32,
    /// 资源生长速度修正
    pub resource_growth: f32,
    /// 旅行舒适度修正
    pub travel_comfort: f32,
    /// 能量消耗修正
    pub energy_consumption: f32,
}

// ============================================================================
// 危险等级
// ============================================================================

/// 危险等级
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum DangerLevel {
    /// 安全 - 新手区域，基本无危险
    Safe,
    /// 低危 - 偶有低级妖兽出没
    Low,
    /// 中等 - 有一定危险，需要小心
    Medium,
    /// 高危 - 危险较高，需要足够实力
    High,
    /// 极危 - 极其危险，高手才能涉足
    Extreme,
    /// 绝境 - 生死一线，顶级强者的试炼场
    Lethal,
}

impl DangerLevel {
    /// 获取危险等级对应的数值 (0-5)
    pub fn to_numeric(&self) -> u8 {
        match self {
            DangerLevel::Safe => 0,
            DangerLevel::Low => 1,
            DangerLevel::Medium => 2,
            DangerLevel::High => 3,
            DangerLevel::Extreme => 4,
            DangerLevel::Lethal => 5,
        }
    }

    /// 从数值创建危险等级
    pub fn from_numeric(value: u8) -> Self {
        match value {
            0 => DangerLevel::Safe,
            1 => DangerLevel::Low,
            2 => DangerLevel::Medium,
            3 => DangerLevel::High,
            4 => DangerLevel::Extreme,
            _ => DangerLevel::Lethal,
        }
    }

    /// 获取建议的最低修为等级
    pub fn recommended_level(&self) -> u8 {
        match self {
            DangerLevel::Safe => 1,
            DangerLevel::Low => 5,
            DangerLevel::Medium => 15,
            DangerLevel::High => 30,
            DangerLevel::Extreme => 50,
            DangerLevel::Lethal => 80,
        }
    }

    /// 获取遭遇强敌的概率
    pub fn elite_encounter_chance(&self) -> f32 {
        match self {
            DangerLevel::Safe => 0.0,
            DangerLevel::Low => 0.05,
            DangerLevel::Medium => 0.15,
            DangerLevel::High => 0.3,
            DangerLevel::Extreme => 0.5,
            DangerLevel::Lethal => 0.8,
        }
    }
}

impl Default for DangerLevel {
    fn default() -> Self {
        DangerLevel::Safe
    }
}

// ============================================================================
// 资源丰富度
// ============================================================================

/// 资源丰富度
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum ResourceRichness {
    /// 贫瘠 - 几乎没有资源
    Barren,
    /// 稀少 - 资源很少
    Sparse,
    /// 普通 - 正常的资源水平
    Common,
    /// 丰富 - 资源较为丰富
    Rich,
    /// 极丰富 - 资源非常丰富
    Abundant,
    /// 传说级丰富 - 传说中的宝地
    Legendary,
}

impl ResourceRichness {
    /// 获取资源丰富度的倍数修正
    pub fn multiplier(&self) -> f32 {
        match self {
            ResourceRichness::Barren => 0.1,
            ResourceRichness::Sparse => 0.5,
            ResourceRichness::Common => 1.0,
            ResourceRichness::Rich => 1.5,
            ResourceRichness::Abundant => 2.0,
            ResourceRichness::Legendary => 3.0,
        }
    }

    /// 获取稀有资源出现概率修正
    pub fn rare_resource_chance(&self) -> f32 {
        match self {
            ResourceRichness::Barren => 0.0,
            ResourceRichness::Sparse => 0.01,
            ResourceRichness::Common => 0.05,
            ResourceRichness::Rich => 0.15,
            ResourceRichness::Abundant => 0.3,
            ResourceRichness::Legendary => 0.6,
        }
    }

    /// 获取资源节点的最大数量修正
    pub fn max_nodes_modifier(&self) -> f32 {
        match self {
            ResourceRichness::Barren => 0.2,
            ResourceRichness::Sparse => 0.6,
            ResourceRichness::Common => 1.0,
            ResourceRichness::Rich => 1.5,
            ResourceRichness::Abundant => 2.2,
            ResourceRichness::Legendary => 3.5,
        }
    }
}

impl Default for ResourceRichness {
    fn default() -> Self {
        ResourceRichness::Common
    }
}

// ============================================================================
// 遭遇概率配置
// ============================================================================

/// 遭遇概率配置 - 定义区域内各种遭遇的发生概率
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct EncounterRates {
    /// 基础遭遇概率（每次移动）
    pub base_encounter_rate: f32,
    /// 野兽遭遇概率
    pub beast_encounter_rate: f32,
    /// 妖怪遭遇概率
    pub demon_encounter_rate: f32,
    /// 其他修士遭遇概率
    pub cultivator_encounter_rate: f32,
    /// 宝藏发现概率
    pub treasure_discovery_rate: f32,
    /// 特殊事件概率
    pub special_event_rate: f32,
}

impl EncounterRates {
    /// 创建基于危险等级的默认遭遇概率
    pub fn from_danger_level(danger: DangerLevel) -> Self {
        let base_rate = match danger {
            DangerLevel::Safe => 0.05,
            DangerLevel::Low => 0.15,
            DangerLevel::Medium => 0.25,
            DangerLevel::High => 0.4,
            DangerLevel::Extreme => 0.6,
            DangerLevel::Lethal => 0.8,
        };

        Self {
            base_encounter_rate: base_rate,
            beast_encounter_rate: base_rate * 0.6,
            demon_encounter_rate: base_rate * 0.3,
            cultivator_encounter_rate: base_rate * 0.1,
            treasure_discovery_rate: base_rate * 0.05,
            special_event_rate: base_rate * 0.02,
        }
    }

    /// 应用地形修正
    pub fn apply_terrain_modifier(&mut self, terrain: TerrainType) {
        let modifier = match terrain {
            TerrainType::Forest => (1.2, 1.5, 0.8, 0.9, 1.1, 1.0),
            TerrainType::Mountains => (1.0, 0.8, 1.3, 0.7, 1.3, 1.2),
            TerrainType::Desert => (0.8, 0.6, 1.1, 0.5, 0.8, 1.1),
            TerrainType::Water => (0.6, 1.2, 0.7, 0.3, 0.9, 0.8),
            TerrainType::Swamp => (1.1, 1.0, 1.4, 0.4, 0.7, 1.3),
            TerrainType::SpiritualVein => (1.3, 0.7, 1.6, 1.2, 1.8, 2.0),
            TerrainType::Void => (2.0, 0.3, 2.5, 0.1, 0.5, 3.0),
            _ => (1.0, 1.0, 1.0, 1.0, 1.0, 1.0),
        };

        self.base_encounter_rate *= modifier.0;
        self.beast_encounter_rate *= modifier.1;
        self.demon_encounter_rate *= modifier.2;
        self.cultivator_encounter_rate *= modifier.3;
        self.treasure_discovery_rate *= modifier.4;
        self.special_event_rate *= modifier.5;
    }
}

impl Default for EncounterRates {
    fn default() -> Self {
        Self::from_danger_level(DangerLevel::Safe)
    }
}
