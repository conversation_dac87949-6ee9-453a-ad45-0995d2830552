/// 空间和世界层次相关的类型定义
/// 处理修仙世界的多层次空间结构

use serde::{Deserialize, Serialize};
use std::fmt;

// ============================================================================
// 世界层次枚举
// ============================================================================

/// 世界层次 - 修仙世界的多层次空间结构
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum WorldLayer {
    /// 凡间 - 普通修士生活的世界
    Mortal,
    /// 灵界 - 灵气充沛的修炼世界
    Spirit,
    /// 仙界 - 仙人居住的高级世界
    Immortal,
    /// 混沌界 - 最高层次的混沌空间
    Chaos,
    /// 秘境 - 独立的小世界空间
    SecretRealm(u32),
}

impl WorldLayer {
    /// 获取层级的灵气密度基础倍数
    pub fn spiritual_density_base(&self) -> f32 {
        match self {
            WorldLayer::Mortal => 1.0,
            WorldLayer::Spirit => 3.0,
            WorldLayer::Immortal => 8.0,
            WorldLayer::Chaos => 20.0,
            WorldLayer::SecretRealm(_) => 5.0, // 秘境灵气浓度较高但不如仙界
        }
    }
    
    /// 获取层级的危险系数
    pub fn danger_multiplier(&self) -> f32 {
        match self {
            WorldLayer::Mortal => 1.0,
            WorldLayer::Spirit => 2.0,
            WorldLayer::Immortal => 5.0,
            WorldLayer::Chaos => 10.0,
            WorldLayer::SecretRealm(_) => 3.0,
        }
    }
    
    /// 判断是否可以从当前层级传送到目标层级
    pub fn can_teleport_to(&self, target: &WorldLayer) -> bool {
        match (self, target) {
            // 凡间可以去灵界和秘境
            (WorldLayer::Mortal, WorldLayer::Spirit) => true,
            (WorldLayer::Mortal, WorldLayer::SecretRealm(_)) => true,
            
            // 灵界可以去凡间、仙界和秘境
            (WorldLayer::Spirit, WorldLayer::Mortal) => true,
            (WorldLayer::Spirit, WorldLayer::Immortal) => true,
            (WorldLayer::Spirit, WorldLayer::SecretRealm(_)) => true,
            
            // 仙界可以去所有层级
            (WorldLayer::Immortal, _) => true,
            
            // 混沌界可以去所有层级
            (WorldLayer::Chaos, _) => true,
            
            // 秘境可以回到原来的层级（简化处理）
            (WorldLayer::SecretRealm(_), WorldLayer::Mortal) => true,
            (WorldLayer::SecretRealm(_), WorldLayer::Spirit) => true,
            
            // 同层级内传送总是允许的
            (a, b) if a == b => true,
            
            _ => false,
        }
    }
}

impl fmt::Display for WorldLayer {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            WorldLayer::Mortal => write!(f, "凡间"),
            WorldLayer::Spirit => write!(f, "灵界"),
            WorldLayer::Immortal => write!(f, "仙界"),
            WorldLayer::Chaos => write!(f, "混沌界"),
            WorldLayer::SecretRealm(id) => write!(f, "秘境-{}", id),
        }
    }
}

impl Default for WorldLayer {
    fn default() -> Self {
        WorldLayer::Mortal
    }
}

// ============================================================================
// 区域边界定义
// ============================================================================

/// 区域边界 - 定义一个区域的空间范围
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct RegionBoundaries {
    /// 最小X坐标
    pub min_x: i32,
    /// 最大X坐标
    pub max_x: i32,
    /// 最小Y坐标
    pub min_y: i32,
    /// 最大Y坐标
    pub max_y: i32,
    /// 最小Z坐标（可选，用于3D世界）
    pub min_z: Option<i32>,
    /// 最大Z坐标（可选，用于3D世界）
    pub max_z: Option<i32>,
}

impl RegionBoundaries {
    /// 创建2D矩形边界
    pub fn new_2d(min_x: i32, max_x: i32, min_y: i32, max_y: i32) -> Self {
        Self {
            min_x,
            max_x,
            min_y,
            max_y,
            min_z: None,
            max_z: None,
        }
    }
    
    /// 创建3D立方体边界
    pub fn new_3d(min_x: i32, max_x: i32, min_y: i32, max_y: i32, min_z: i32, max_z: i32) -> Self {
        Self {
            min_x,
            max_x,
            min_y,
            max_y,
            min_z: Some(min_z),
            max_z: Some(max_z),
        }
    }
    
    /// 检查坐标是否在边界内
    pub fn contains(&self, x: i32, y: i32, z: Option<i32>) -> bool {
        if x < self.min_x || x > self.max_x || y < self.min_y || y > self.max_y {
            return false;
        }
        
        match (self.min_z, self.max_z, z) {
            (Some(min_z), Some(max_z), Some(z)) => z >= min_z && z <= max_z,
            (None, None, None) => true, // 2D情况
            _ => false, // 维度不匹配
        }
    }
    
    /// 计算区域面积（2D）或体积（3D）
    pub fn area_or_volume(&self) -> u64 {
        let width = (self.max_x - self.min_x + 1) as u64;
        let height = (self.max_y - self.min_y + 1) as u64;
        
        match (self.min_z, self.max_z) {
            (Some(min_z), Some(max_z)) => {
                let depth = (max_z - min_z + 1) as u64;
                width * height * depth
            }
            _ => width * height,
        }
    }
    
    /// 获取边界的中心点
    pub fn center(&self) -> (i32, i32, Option<i32>) {
        let center_x = (self.min_x + self.max_x) / 2;
        let center_y = (self.min_y + self.max_y) / 2;
        
        let center_z = match (self.min_z, self.max_z) {
            (Some(min_z), Some(max_z)) => Some((min_z + max_z) / 2),
            _ => None,
        };
        
        (center_x, center_y, center_z)
    }
    
    /// 检查两个区域是否重叠
    pub fn overlaps(&self, other: &RegionBoundaries) -> bool {
        // 检查X和Y轴是否重叠
        let x_overlap = self.min_x <= other.max_x && self.max_x >= other.min_x;
        let y_overlap = self.min_y <= other.max_y && self.max_y >= other.min_y;
        
        if !x_overlap || !y_overlap {
            return false;
        }
        
        // 检查Z轴是否重叠（如果存在）
        match (self.min_z, self.max_z, other.min_z, other.max_z) {
            (Some(self_min_z), Some(self_max_z), Some(other_min_z), Some(other_max_z)) => {
                self_min_z <= other_max_z && self_max_z >= other_min_z
            }
            (None, None, None, None) => true, // 都是2D，已经检查了XY重叠
            _ => false, // 维度不匹配
        }
    }
}

// ============================================================================
// 建筑占地面积
// ============================================================================

/// 建筑占地面积 - 定义建筑在世界中占用的空间
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct BuildingFootprint {
    /// 建筑的形状类型
    pub shape: FootprintShape,
    /// 建筑的方向（角度，以度为单位）
    pub orientation: f32,
}

/// 建筑占地形状
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum FootprintShape {
    /// 矩形占地
    Rectangle { width: u32, height: u32 },
    /// 圆形占地
    Circle { radius: f32 },
    /// 多边形占地
    Polygon { vertices: Vec<(i32, i32)> },
}

impl BuildingFootprint {
    /// 创建矩形占地
    pub fn rectangle(width: u32, height: u32) -> Self {
        Self {
            shape: FootprintShape::Rectangle { width, height },
            orientation: 0.0,
        }
    }
    
    /// 创建圆形占地
    pub fn circle(radius: f32) -> Self {
        Self {
            shape: FootprintShape::Circle { radius },
            orientation: 0.0,
        }
    }
    
    /// 检查指定位置是否在建筑占地范围内
    pub fn contains_point(&self, building_center: (i32, i32), point: (i32, i32)) -> bool {
        let (cx, cy) = building_center;
        let (px, py) = point;
        
        match &self.shape {
            FootprintShape::Rectangle { width, height } => {
                let half_width = *width as i32 / 2;
                let half_height = *height as i32 / 2;
                
                px >= cx - half_width && px <= cx + half_width &&
                py >= cy - half_height && py <= cy + half_height
            }
            FootprintShape::Circle { radius } => {
                let dx = (px - cx) as f32;
                let dy = (py - cy) as f32;
                let distance = (dx * dx + dy * dy).sqrt();
                distance <= *radius
            }
            FootprintShape::Polygon { vertices } => {
                // 使用射线投射算法检查点是否在多边形内
                self.point_in_polygon(building_center, point, vertices)
            }
        }
    }
    
    /// 点在多边形内的判断（射线投射算法）
    fn point_in_polygon(&self, building_center: (i32, i32), point: (i32, i32), vertices: &[(i32, i32)]) -> bool {
        let (cx, cy) = building_center;
        let (px, py) = point;
        
        // 将多边形顶点相对于建筑中心进行偏移
        let offset_vertices: Vec<(i32, i32)> = vertices.iter()
            .map(|(vx, vy)| (cx + vx, cy + vy))
            .collect();
        
        let mut inside = false;
        let n = offset_vertices.len();
        
        for i in 0..n {
            let j = (i + n - 1) % n;
            let (xi, yi) = offset_vertices[i];
            let (xj, yj) = offset_vertices[j];
            
            if ((yi > py) != (yj > py)) && 
               (px < (xj - xi) * (py - yi) / (yj - yi) + xi) {
                inside = !inside;
            }
        }
        
        inside
    }
    
    /// 计算建筑占地面积
    pub fn area(&self) -> f32 {
        match &self.shape {
            FootprintShape::Rectangle { width, height } => {
                (*width * *height) as f32
            }
            FootprintShape::Circle { radius } => {
                std::f32::consts::PI * radius * radius
            }
            FootprintShape::Polygon { vertices } => {
                // 使用鞋带公式计算多边形面积
                if vertices.len() < 3 {
                    return 0.0;
                }
                
                let mut area = 0.0;
                let n = vertices.len();
                
                for i in 0..n {
                    let j = (i + 1) % n;
                    let (xi, yi) = vertices[i];
                    let (xj, yj) = vertices[j];
                    area += (xi * yj - xj * yi) as f32;
                }
                
                (area / 2.0).abs()
            }
        }
    }
}