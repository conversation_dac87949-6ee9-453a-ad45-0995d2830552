/// 战斗视图模块
/// 
/// 实现方案二：分离战斗视图和聚合根
/// 
/// BattleView是专门用于战斗的纯数据结构，它从聚合根中提取必要的战斗数据，
/// 避免了生命周期复杂性，同时保持了聚合根的完整性。
/// 
/// 设计原则：
/// 1. 纯数据结构，无业务逻辑
/// 2. 支持Clone，便于在战斗引擎中传递
/// 3. 包含所有战斗必需的信息
/// 4. 支持从聚合根转换而来

use crate::shared::*;
use crate::skill::buff::Buff;
use crate::battle_system::simplified_battle_traits::{
    BattleEntity, LifeForce, CombatCapable, SkillUser, TimeAware,
    BasicBattleUnit, FullBattleUnit, BattleStatus, BasicBattleStatus,
    DamageResult, HealResult
};
use crate::battle_system::simplified_battle_traits;
use crate::battle_system::combat_calculator::*;
use crate::battle_system::buff_adapter::SimplifiedBuff;

/// 战斗视图 - 战斗中使用的纯数据表示
/// 
/// 这个结构体包含了战斗所需的所有数据，避免了trait object的复杂性
#[derive(Debug, Clone)]
pub struct BattleView {
    // === 基础身份信息 ===
    pub id: BattleUnitId,
    pub name: String,
    pub level: Level,
    pub position: Position,

    // === 生命力数据 ===
    pub current_health: Health,
    pub max_health: Health,
    pub current_mana: Mana,
    pub max_mana: Mana,

    // === 战斗属性 ===
    pub attack_power: Attack,
    pub defense_power: Defense,
    pub movement_speed: Speed,
    pub attack_range: Range,

    // === 技能系统 ===
    pub learned_skills: Vec<SkillId>,
    pub skill_cooldowns: std::collections::HashMap<SkillId, f64>,
    pub buffs: Vec<SimplifiedBuff>,

    // === 状态标记 ===
    pub is_alive: bool,
    pub can_move: bool,
    pub can_attack: bool,
    pub can_cast_skills: bool,

    // === 其他状态 ===
    pub active_statuses: Vec<String>,
}

impl BattleView {
    /// 创建一个新的战斗视图
    pub fn new(
        id: BattleUnitId,
        name: String,
        level: Level,
        position: Position,
    ) -> Self {
        Self {
            id,
            name,
            level,
            position,
            current_health: 100,
            max_health: 100,
            current_mana: 0,
            max_mana: 0,
            attack_power: 10,
            defense_power: 5,
            movement_speed: 5.0,
            attack_range: 1.0,
            learned_skills: Vec::new(),
            skill_cooldowns: std::collections::HashMap::new(),
            buffs: Vec::new(),
            is_alive: true,
            can_move: true,
            can_attack: true,
            can_cast_skills: true,
            active_statuses: Vec::new(),
        }
    }

    /// 检查技能是否已冷却
    pub fn is_skill_ready(&self, skill_id: &SkillId) -> bool {
        self.skill_cooldowns.get(skill_id).copied().unwrap_or(0.0) <= 0.0
    }

    /// 获取技能冷却时间
    pub fn skill_cooldown(&self, skill_id: &SkillId) -> f64 {
        self.skill_cooldowns.get(skill_id).copied().unwrap_or(0.0)
    }

    /// 更新技能冷却时间
    pub fn update_skill_cooldowns(&mut self, delta_time: f32) {
        for cooldown in self.skill_cooldowns.values_mut() {
            *cooldown = (*cooldown - delta_time as f64).max(0.0);
        }
    }

    /// 设置技能冷却
    pub fn set_skill_cooldown(&mut self, skill_id: SkillId, cooldown: f64) {
        self.skill_cooldowns.insert(skill_id, cooldown);
    }

    /// 受到伤害
    pub fn take_damage(&mut self, damage: Health) -> DamageResult {
        // 创建一个虚拟攻击者进行计算 (在实际使用中应该传入真实攻击者)
        let dummy_attacker = Self::create_test_character(999, "DummyAttacker", self.level);
        let damage_result = CombatCalculator::calculate_damage(&dummy_attacker, self, damage.max(0) as u32);
        
        // 应用伤害
        self.current_health = self.current_health.saturating_sub(damage_result.actual_damage);
        self.is_alive = self.current_health > 0;
        
        // 直接返回统一的 DamageResult
        damage_result
    }
    
    /// 受到来自特定攻击者的伤害
    pub fn take_damage_from(&mut self, attacker: &BattleView, damage: Health) -> DamageResult {
        let damage_result = CombatCalculator::calculate_damage(attacker, self, damage.max(0) as u32);
        
        // 应用伤害
        self.current_health = self.current_health.saturating_sub(damage_result.actual_damage);
        self.is_alive = self.current_health > 0;
        
        // 直接返回统一的 DamageResult
        damage_result
    }

    /// 恢复生命值
    pub fn heal(&mut self, amount: Health) -> HealResult {
        // 创建一个虚拟治疗者进行计算 (在实际使用中应该传入真实治疗者)
        let dummy_healer = Self::create_test_character(998, "DummyHealer", self.level);
        let heal_result = CombatCalculator::calculate_heal(&dummy_healer, self, amount.max(0) as u32);
        
        // 应用治疗
        self.current_health = (self.current_health + heal_result.actual_heal).min(self.max_health);
        
        heal_result
    }
    
    /// 受到来自特定治疗者的治疗
    pub fn heal_from(&mut self, healer: &BattleView, amount: Health) -> HealResult {
        let heal_result = CombatCalculator::calculate_heal(healer, self, amount.max(0) as u32);
        
        // 应用治疗
        self.current_health = (self.current_health + heal_result.actual_heal).min(self.max_health);
        
        heal_result
    }

    /// 消耗法力
    pub fn consume_mana(&mut self, amount: Mana) -> bool {
        if self.current_mana >= amount {
            self.current_mana -= amount;
            true
        } else {
            false
        }
    }

    /// 恢复法力
    pub fn restore_mana(&mut self, amount: Mana) {
        self.current_mana = (self.current_mana + amount).min(self.max_mana);
    }

    /// 获取生命值百分比
    pub fn health_percentage(&self) -> f32 {
        if self.max_health == 0 {
            0.0
        } else {
            (self.current_health as f32) / (self.max_health as f32)
        }
    }

    /// 获取战斗力评分
    pub fn combat_rating(&self) -> u32 {
        let health_score = (self.max_health as u32) / 10;
        let attack_score = self.attack_power as u32;
        let defense_score = self.defense_power as u32;
        let level_score = self.level * 10;
        
        health_score + attack_score + defense_score + level_score
    }

    /// 检查是否能到达目标
    pub fn can_reach(&self, target: &BattleView) -> bool {
        self.position.distance_to(&target.position) <= self.attack_range
    }

    /// 计算与另一个单位的距离
    pub fn distance_to(&self, other: &BattleView) -> f32 {
        self.position.distance_to(&other.position)
    }
    
    /// 更新攻击范围（从装备和技能计算）
    pub fn update_attack_range(&mut self) {
        self.attack_range = BonusCalculator::calculate_attack_range(self);
    }
    
    /// 更新状态效果列表
    pub fn update_active_statuses(&mut self) {
        self.active_statuses = BonusCalculator::calculate_active_statuses(self);
    }
    
    /// 全面更新所有计算值
    pub fn refresh_calculated_values(&mut self) {
        self.update_attack_range();
        self.update_active_statuses();
    }
    
    /// 计算技能需求（冷却时间和法力消耗）
    pub fn calculate_skill_requirements(&self, skill_id: &SkillId) -> (f64, Mana) {
        // 基础技能需求计算
        let base_cooldown = match skill_id.0 {
            1..=10 => 3.0,      // 基础攻击技能
            11..=20 => 5.0,     // 高级攻击技能
            21..=30 => 8.0,     // 特殊技能
            31..=40 => 2.0,     // 辅助技能
            _ => 5.0,           // 默认
        };
        
        let base_mana = match skill_id.0 {
            1..=10 => 10,       // 低法力消耗
            11..=20 => 25,      // 中等法力消耗
            21..=30 => 50,      // 高法力消耗
            31..=40 => 15,      // 辅助技能
            _ => 20,            // 默认
        };
        
        // 等级影响（高等级减少冷却时间）
        let level_reduction = (self.level as f64) * 0.02; // 每级2%减少
        let final_cooldown = (base_cooldown - level_reduction).max(1.0);
        
        // Buff影响
        let cooldown_reduction = self.buffs.iter()
            .filter(|buff| buff.skill_type == "cooldown_reduction")
            .map(|buff| buff.effect_value as f64 / 100.0)
            .sum::<f64>();
        
        let mana_reduction = self.buffs.iter()
            .filter(|buff| buff.skill_type == "mana_efficiency")
            .map(|buff| buff.effect_value)
            .sum::<f32>() / 100.0;
        
        let adjusted_cooldown = final_cooldown * (1.0 - cooldown_reduction).max(0.1f64);
        let adjusted_mana = (base_mana as f32 * (1.0 - mana_reduction)).max(0.0) as Mana;
        
        (adjusted_cooldown, adjusted_mana)
    }
    
    /// 更新时间相关的效果
    pub fn update_time_based_effects(&mut self, delta_seconds: f32) {
        // 处理持续伤害效果
        for buff in &self.buffs {
            match buff.skill_type.as_str() {
                "poison" => {
                    if buff.effect_value > 0.0 {
                        let poison_damage = (buff.effect_value * delta_seconds) as Health;
                        self.current_health = self.current_health.saturating_sub(poison_damage);
                        if self.current_health == 0 {
                            self.is_alive = false;
                        }
                    }
                },
                "regeneration" => {
                    if buff.effect_value > 0.0 {
                        let heal_amount = (buff.effect_value * delta_seconds) as Health;
                        self.current_health = (self.current_health + heal_amount).min(self.max_health);
                    }
                },
                "mana_regen" => {
                    if buff.effect_value > 0.0 {
                        let mana_amount = (buff.effect_value * delta_seconds) as Mana;
                        self.current_mana = (self.current_mana + mana_amount).min(self.max_mana);
                    }
                },
                _ => {} // 其他效果不在这里处理
            }
        }
    }
}

// ============================================================================
// 从聚合根到BattleView的转换
// ============================================================================

/// 从Character聚合根转换为BattleView
impl From<&crate::character::Character> for BattleView {
    fn from(character: &crate::character::Character) -> Self {
        // 收集技能冷却时间
        let mut skill_cooldowns = std::collections::HashMap::new();
        for skill_id in character.skill_ref().get_learned_skills() {
            let cooldown = character.skill_cooldowns().get_cooldown(skill_id);
            if cooldown > 0.0 {
                skill_cooldowns.insert(*skill_id, cooldown);
            }
        }

        let mut battle_view = Self {
            id: BattleUnitId::Character(character.id()),
            name: character.name().to_string(),
            level: character.level(),
            position: character.position(),
            current_health: character.current_health(),
            max_health: character.max_health(),
            current_mana: character.current_mana(),
            max_mana: character.max_mana(),
            attack_power: character.calculate_attack(),
            defense_power: character.calculate_defense(),
            movement_speed: character.calculate_speed(),
            attack_range: 1.0, // 将被重新计算
            learned_skills: character.skill_ref().get_learned_skills().to_vec(),
            skill_cooldowns,
            buffs: crate::battle_system::buff_adapter::BuffConverter::to_simplified(
                &character.buff_manager().get_buffs()
            ),
            is_alive: character.is_alive(),
            can_move: true,
            can_attack: true,
            can_cast_skills: true,
            active_statuses: Vec::new(), // 将被重新计算
        };
        
        // 更新计算值
        battle_view.refresh_calculated_values();
        battle_view
    }
}

/// 从Monster转换为BattleView
impl From<&crate::monster::Monster> for BattleView {
    fn from(monster: &crate::monster::Monster) -> Self {
        // 收集技能列表
        let learned_skills: Vec<SkillId> = monster.skill_bar.skills
            .iter()
            .map(|s| SkillId(s.id))
            .collect();

        // 收集技能冷却时间
        let mut skill_cooldowns = std::collections::HashMap::new();
        for (skill_id, cooldown) in &monster.skill_bar.skill_cooldowns {
            if *cooldown > 0.0 {
                skill_cooldowns.insert(SkillId(*skill_id), *cooldown);
            }
        }

        let mut battle_view = Self {
            id: BattleUnitId::Monster(monster.id),
            name: monster.profile.name.clone(),
            level: monster.profile.level,
            position: Position::new(monster.position.x as f32, monster.position.y as f32),
            current_health: monster.hp as Health,
            max_health: monster.max_hp as Health,
            current_mana: 0, // Monster通常没有法力值
            max_mana: 0,
            attack_power: monster.profile.level as Attack * 2 + 10,
            defense_power: monster.profile.level as Defense + 5,
            movement_speed: 5.0, // 默认速度
            attack_range: 1.0,   // 将被重新计算
            learned_skills,
            skill_cooldowns,
            buffs: crate::battle_system::buff_adapter::BuffConverter::to_simplified(
                &monster.buffs
            ),
            is_alive: monster.hp > 0.0,
            can_move: true,
            can_attack: true,
            can_cast_skills: !monster.skill_bar.skills.is_empty(),
            active_statuses: Vec::new(), // 将被重新计算
        };
        
        // 更新计算值
        battle_view.refresh_calculated_values();
        battle_view
    }
}

// ============================================================================
// BattleView实现所有战斗trait（用于兼容性）
// ============================================================================

impl BattleEntity for BattleView {
    fn entity_id(&self) -> BattleUnitId {
        self.id
    }

    fn display_name(&self) -> &str {
        &self.name
    }

    fn level(&self) -> Level {
        self.level
    }

    fn position(&self) -> Position {
        self.position
    }

    fn set_position(&mut self, position: Position) -> GameResult<()> {
        if self.can_move {
            self.position = position;
            Ok(())
        } else {
            Err(GameError::validation_error("movement", "此单位不能移动"))
        }
    }
}

impl LifeForce for BattleView {
    fn current_health(&self) -> Health {
        self.current_health
    }

    fn max_health(&self) -> Health {
        self.max_health
    }

    fn current_mana(&self) -> Mana {
        self.current_mana
    }

    fn max_mana(&self) -> Mana {
        self.max_mana
    }

    fn is_alive(&self) -> bool {
        self.is_alive
    }

    fn take_damage(&mut self, damage: Health) -> GameResult<DamageResult> {
        Ok(self.take_damage(damage))
    }

    fn heal(&mut self, amount: Health) -> GameResult<HealResult> {
        Ok(self.heal(amount))
    }

    fn consume_mana(&mut self, amount: Mana) -> GameResult<()> {
        if self.consume_mana(amount) {
            Ok(())
        } else {
            Err(GameError::validation_error("mana", "法力不足"))
        }
    }

    fn restore_mana(&mut self, amount: Mana) -> GameResult<()> {
        self.restore_mana(amount);
        Ok(())
    }
}

impl CombatCapable for BattleView {
    fn attack_power(&self) -> Attack {
        self.attack_power
    }

    fn defense_power(&self) -> Defense {
        self.defense_power
    }

    fn movement_speed(&self) -> Speed {
        self.movement_speed
    }

    fn attack_range(&self) -> Range {
        self.attack_range
    }

    fn can_move(&self) -> bool {
        self.can_move
    }

    fn can_attack(&self) -> bool {
        self.can_attack
    }

    fn can_cast_skills(&self) -> bool {
        self.can_cast_skills
    }
}

impl SkillUser for BattleView {
    fn learned_skills(&self) -> Vec<SkillId> {
        self.learned_skills.clone()
    }

    fn skill_cooldown(&self, skill_id: &SkillId) -> f64 {
        self.skill_cooldown(skill_id)
    }

    fn is_skill_ready(&self, skill_id: &SkillId) -> bool {
        self.is_skill_ready(skill_id)
    }

    fn use_skill(&mut self, skill_id: &SkillId) -> GameResult<()> {
        if !self.learned_skills.contains(skill_id) {
            return Err(GameError::validation_error("skill", "技能未学习"));
        }
        if !self.is_skill_ready(skill_id) {
            return Err(GameError::validation_error("skill", "技能冷却中"));
        }
        
        // 根据技能定义设置冷却时间和消耗法力
        let (cooldown, mana_cost) = self.calculate_skill_requirements(skill_id);
        
        // 检查法力是否足够
        if !self.consume_mana(mana_cost) {
            return Err(GameError::validation_error("mana", "法力不足"));
        }
        
        // 设置冷却时间
        self.set_skill_cooldown(*skill_id, cooldown);
        Ok(())
    }

    fn set_skill_cooldown(&mut self, skill_id: SkillId, cooldown: f64) -> GameResult<()> {
        self.set_skill_cooldown(skill_id, cooldown);
        Ok(())
    }

    fn update_skill_cooldowns(&mut self, delta_time: f32) {
        self.update_skill_cooldowns(delta_time);
    }

    fn add_buff(&mut self, buff: Buff) -> GameResult<()> {
        let simplified_buff = SimplifiedBuff::from_official_buff(&buff);
        self.buffs.push(simplified_buff);
        Ok(())
    }

    fn get_buffs(&self) -> Vec<Buff> {
        self.buffs.iter().map(|sb| sb.to_official_buff()).collect()
    }

    fn active_statuses(&self) -> Vec<String> {
        self.active_statuses.clone()
    }
}

impl TimeAware for BattleView {
    fn update(&mut self, delta_seconds: f32) {
        // 更新技能冷却
        self.update_skill_cooldowns(delta_seconds);
        
        // 更新Buff
        self.buffs.retain_mut(|buff| {
            if !buff.is_permanent {
                buff.duration -= delta_seconds;
            }
            buff.duration > 0.0 || buff.is_permanent
        });
        
        // 更新其他时间相关的状态
        self.update_time_based_effects(delta_seconds);
        
        // 刷新计算值（因为Buff可能影响属性）
        self.update_active_statuses();
    }
}

impl FullBattleUnit for BattleView {}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl BattleView {
    /// 从Character创建BattleView
    pub fn from_character(character: &crate::character::Character) -> Self {
        character.into()
    }
    
    /// 从Monster创建BattleView
    pub fn from_monster(monster: &crate::monster::Monster) -> Self {
        monster.into()
    }
    
    /// 创建测试用的BattleView
    pub fn create_test_character(id: u32, name: &str, level: Level) -> Self {
        Self::new(
            BattleUnitId::Character(CharacterId(id)),
            name.to_string(),
            level,
            Position::new(0.0, 0.0),
        )
    }
    
    /// 创建测试用的Monster BattleView
    pub fn create_test_monster(id: u32, name: &str, level: Level) -> Self {
        Self::new(
            BattleUnitId::Monster(MonsterId(id)),
            name.to_string(),
            level,
            Position::new(0.0, 0.0),
        )
    }
}