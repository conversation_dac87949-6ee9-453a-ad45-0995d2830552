use crate::battle_unit::BuffEffects;
/// Buff管理trait
/// 
/// 管理Buff系统，包括Buff的叠加、更新和效果计算

use crate::shared::*;
use crate::skill::buff::Buff;
use crate::skill_modules::BuffType;

/// Buff管理系统
pub trait BuffManager {
    /// 获取所有Buff
    fn get_buffs(&self) -> Vec<Buff>;
    
    /// 添加Buff
    fn add_buff(&mut self, buff: Buff) -> GameResult<()>;
    
    /// 移除Buff
    fn remove_buff(&mut self, buff_id: BuffId) -> GameResult<()>;
    
    /// 获取Buff数量
    fn buff_count(&self) -> usize;
    
    /// 获取指定类型的Buff
    fn get_buffs_by_type(&self, buff_type: BuffType) -> Vec<Buff>;
    
    /// 更新Buff持续时间
    fn update_buffs(&mut self, delta_time: f32);
    
    /// 检查Buff是否可叠加
    fn can_stack_buff(&self, buff: &Buff) -> bool;
    
    /// 叠加Buff
    fn stack_buff(&mut self, buff: Buff) -> GameResult<()>;
    
    /// 获取Buff效果总和
    fn calculate_buff_effects(&self) -> BuffEffects;
}