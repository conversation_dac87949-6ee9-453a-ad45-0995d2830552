use serde::{Deserialize, Serialize};

/// 角色状态枚举，用于表示角色的各种状态效果
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CharacterState {
    /// 眩晕状态 - 无法行动
    #[serde(rename = "眩晕")]
    Stunned(u32), // 持续时间
    /// 减速状态 - 移动速度降低
    #[serde(rename = "减速")]
    Slowed(f32, u32), // 减速比例, 持续时间
    /// 沉默状态 - 无法使用技能
    #[serde(rename = "沉默")]
    Silenced(u32), // 持续时间
    /// 定身状态 - 无法移动
    #[serde(rename = "定身")]
    Rooted(u32), // 持续时间
    /// 无敌状态 - 免疫所有伤害
    #[serde(rename = "无敌")]
    Invincible(u32), // 持续时间
    /// 隐身状态 - 无法被选中
    #[serde(rename = "隐身")]
    Invisible(u32), // 持续时间
    /// 狂暴状态 - 伤害提升
    #[serde(rename = "狂暴")]
    Berserk(f32, u32), // 伤害提升比例, 持续时间
    /// 虚弱状态 - 伤害降低
    #[serde(rename = "虚弱")]
    Weakened(f32, u32), // 伤害降低比例, 持续时间
}

impl std::fmt::Display for CharacterState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CharacterState::Stunned(duration) => write!(f, "眩晕状态 (剩余{}秒)", duration),
            CharacterState::Slowed(ratio, duration) => {
                write!(f, "减速状态 (减速{}%, 剩余{}秒)", ratio * 100.0, duration)
            }
            CharacterState::Silenced(duration) => write!(f, "沉默状态 (剩余{}秒)", duration),
            CharacterState::Rooted(duration) => write!(f, "定身状态 (剩余{}秒)", duration),
            CharacterState::Invincible(duration) => write!(f, "无敌状态 (剩余{}秒)", duration),
            CharacterState::Invisible(duration) => write!(f, "隐身状态 (剩余{}秒)", duration),
            CharacterState::Berserk(ratio, duration) => write!(
                f,
                "狂暴状态 (伤害提升{}%, 剩余{}秒)",
                ratio * 100.0,
                duration
            ),
            CharacterState::Weakened(ratio, duration) => write!(
                f,
                "虚弱状态 (伤害降低{}%, 剩余{}秒)",
                ratio * 100.0,
                duration
            ),
        }
    }
}
