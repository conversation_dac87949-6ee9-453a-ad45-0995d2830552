//! 装备系统服务层 - 装备管理核心业务逻辑

use crate::equipment::{Equipment, EquipmentBar, SetManager, EquipmentType, EquipmentQuality, FailurePenalty};
use crate::attribute::attribute::{AttributeSet, Attribute, AttributeType};
use crate::shared::types::MaterialGrade;
use std::collections::HashMap;
use rand::Rng;

/// 装备系统服务
#[derive(Debug, Clone)]
pub struct EquipmentService {
    /// 套装管理器
    pub set_manager: SetManager,
    /// 装备模板库
    pub equipment_templates: HashMap<u32, EquipmentTemplate>,
    /// 强化材料配置
    pub enhancement_materials: HashMap<u8, Vec<(u32, u32)>>, // 强化等级 -> (材料ID, 数量)
}

/// 装备模板定义
#[derive(Debug, Clone)]
pub struct EquipmentTemplate {
    pub name: String,
    pub eq_type: EquipmentType,
    pub quality: EquipmentQuality,
    pub level_requirement: u32,
    pub base_attributes: AttributeSet,
    pub material_grade: MaterialGrade,
    pub description: String,
    pub set_id: Option<u32>,
}

/// 强化结果
#[derive(Debug, Clone, PartialEq)]
pub enum EnhancementResult {
    Success(u8),           // 成功，新等级
    Failure,               // 失败，无惩罚
    LevelDown(u8),         // 失败，等级下降，新等级
    Destroyed,             // 失败，装备被销毁
}

/// 装备比较结果
#[derive(Debug, Clone)]
pub struct EquipmentComparison {
    pub current_score: f64,
    pub new_score: f64,
    pub score_diff: f64,
    pub is_upgrade: bool,
    pub attribute_changes: Vec<AttributeChange>,
}

/// 属性变化描述
#[derive(Debug, Clone)]
pub struct AttributeChange {
    pub attr_type: AttributeType,
    pub old_value: f64,
    pub new_value: f64,
    pub change: f64,
}

impl EquipmentService {
    /// 创建新的装备服务
    pub fn new() -> Self {
        let mut service = Self {
            set_manager: SetManager::new(),
            equipment_templates: HashMap::new(),
            enhancement_materials: HashMap::new(),
        };
        
        // 初始化默认配置
        service.init_default_templates();
        service.init_enhancement_materials();
        
        service
    }
    
    /// 根据模板创建装备
    pub fn create_equipment_from_template(&self, template_id: u32, equipment_id: u32) -> Option<Equipment> {
        if let Some(template) = self.equipment_templates.get(&template_id) {
            let mut equipment = Equipment::new(
                equipment_id,
                template.name.clone(),
                template.eq_type.clone(),
                template.quality,
                template.level_requirement,
                template.base_attributes.clone(),
                template.material_grade,
                template.description.clone(),
            );
            
            // 设置套装ID
            equipment.set_id = template.set_id;
            
            Some(equipment)
        } else {
            None
        }
    }
    
    /// 强化装备
    pub fn enhance_equipment(&self, equipment: &mut Equipment) -> EnhancementResult {
        if equipment.enhancement.level >= 20 {
            return EnhancementResult::Failure; // 已达最高等级
        }
        
        let mut rng = rand::thread_rng();
        let success_rate = equipment.enhancement.calculate_success_rate();
        let roll = rng.gen::<f32>();
        
        if roll <= success_rate {
            // 强化成功
            equipment.enhancement.level += 1;
            equipment.enhancement.success_rate = equipment.enhancement.calculate_success_rate();
            equipment.enhancement.failure_penalty = equipment.enhancement.get_failure_penalty();
            
            EnhancementResult::Success(equipment.enhancement.level)
        } else {
            // 强化失败
            match equipment.enhancement.get_failure_penalty() {
                FailurePenalty::None => EnhancementResult::Failure,
                FailurePenalty::LevelDown(levels) => {
                    let new_level = equipment.enhancement.level.saturating_sub(levels);
                    equipment.enhancement.level = new_level;
                    equipment.enhancement.success_rate = equipment.enhancement.calculate_success_rate();
                    equipment.enhancement.failure_penalty = equipment.enhancement.get_failure_penalty();
                    EnhancementResult::LevelDown(new_level)
                }
                FailurePenalty::Destruction => EnhancementResult::Destroyed,
            }
        }
    }
    
    /// 装备装备到角色
    pub fn equip_to_character(&mut self, equipment_bar: &mut EquipmentBar, equipment: Equipment) -> Result<Option<Equipment>, String> {
        // 更新套装状态
        equipment_bar.equip(equipment)?;
        self.set_manager.update_active_sets(&equipment_bar.equipped);
        
        Ok(None)
    }
    
    /// 卸下装备
    pub fn unequip_from_character(&mut self, equipment_bar: &mut EquipmentBar, eq_type: &EquipmentType) -> Option<Equipment> {
        let unequipped = equipment_bar.unequip(eq_type);
        
        // 更新套装状态
        self.set_manager.update_active_sets(&equipment_bar.equipped);
        
        unequipped
    }
    
    /// 比较两件装备
    pub fn compare_equipment(&self, current: Option<&Equipment>, new: &Equipment) -> EquipmentComparison {
        let new_score = new.get_equipment_score();
        let new_attributes = new.get_total_attributes();
        
        if let Some(current_eq) = current {
            let current_score = current_eq.get_equipment_score();
            let current_attributes = current_eq.get_total_attributes();
            let score_diff = new_score - current_score;
            
            // 计算属性变化
            let mut attribute_changes = Vec::new();
            
            // 获取所有可能的属性类型
            let mut all_attr_types = std::collections::HashSet::new();
            for attr_type in current_attributes.attributes.keys() {
                all_attr_types.insert(attr_type);
            }
            for attr_type in new_attributes.attributes.keys() {
                all_attr_types.insert(attr_type);
            }
            
            for attr_type in all_attr_types {
                let old_value = current_attributes.get(attr_type).map(|a| a.value).unwrap_or(0.0);
                let new_value = new_attributes.get(attr_type).map(|a| a.value).unwrap_or(0.0);
                let change = new_value - old_value;
                
                if change.abs() > 0.01 { // 忽略微小变化
                    attribute_changes.push(AttributeChange {
                        attr_type: *attr_type,
                        old_value,
                        new_value,
                        change,
                    });
                }
            }
            
            EquipmentComparison {
                current_score,
                new_score,
                score_diff,
                is_upgrade: score_diff > 0.0,
                attribute_changes,
            }
        } else {
            // 没有当前装备，新装备就是提升
            EquipmentComparison {
                current_score: 0.0,
                new_score,
                score_diff: new_score,
                is_upgrade: true,
                attribute_changes: new_attributes.attributes.iter().map(|(attr_type, attr)| {
                    AttributeChange {
                        attr_type: *attr_type,
                        old_value: 0.0,
                        new_value: attr.value,
                        change: attr.value,
                    }
                }).collect(),
            }
        }
    }
    
    /// 获取角色的总属性加成（装备 + 套装）
    pub fn get_total_character_attributes(&self, equipment_bar: &EquipmentBar) -> AttributeSet {
        let equipment_attrs = equipment_bar.get_total_equipment_attributes();
        let set_attrs = self.set_manager.get_total_set_attributes();
        
        equipment_attrs.merge(&set_attrs)
    }
    
    /// 自动推荐装备升级
    pub fn recommend_equipment_upgrades(&self, equipment_bar: &EquipmentBar) -> Vec<(EquipmentType, u32)> {
        let mut recommendations = Vec::new();
        
        for template_id in self.equipment_templates.keys() {
            if let Some(template) = self.equipment_templates.get(template_id) {
                let current_equipment = equipment_bar.get_equipped(&template.eq_type);
                
                if let Some(test_equipment) = self.create_equipment_from_template(*template_id, 0) {
                    let comparison = self.compare_equipment(current_equipment, &test_equipment);
                    
                    if comparison.is_upgrade && comparison.score_diff > 10.0 { // 显著提升
                        recommendations.push((template.eq_type.clone(), *template_id));
                    }
                }
            }
        }
        
        recommendations
    }
    
    /// 计算强化费用
    pub fn calculate_enhancement_cost(&self, equipment: &Equipment) -> Vec<(u32, u32)> {
        let next_level = equipment.enhancement.level + 1;
        self.enhancement_materials.get(&next_level).cloned().unwrap_or_default()
    }
    
    /// 检查是否有足够材料进行强化
    pub fn can_enhance(&self, equipment: &Equipment, materials: &HashMap<u32, u32>) -> bool {
        let required = self.calculate_enhancement_cost(equipment);
        
        for (material_id, required_count) in required {
            if let Some(&available_count) = materials.get(&material_id) {
                if available_count < required_count {
                    return false;
                }
            } else {
                return false;
            }
        }
        
        true
    }
    
    /// 获取装备的详细信息
    pub fn get_equipment_details(&self, equipment: &Equipment) -> String {
        let mut details = String::new();
        
        details.push_str(&format!("装备名称: {}\n", equipment.name));
        details.push_str(&format!("装备类型: {}\n", equipment.eq_type));
        details.push_str(&format!("装备品质: {}\n", equipment.quality));
        details.push_str(&format!("等级要求: {}\n", equipment.level_requirement));
        details.push_str(&format!("强化等级: +{}\n", equipment.enhancement.level));
        details.push_str(&format!("装备评分: {:.1}\n", equipment.get_equipment_score()));
        
        details.push_str("\n基础属性:\n");
        for (attr_type, attr) in &equipment.base_attributes.attributes {
            details.push_str(&format!("  {:?}: {:.1}\n", attr_type, attr.value));
        }
        
        if equipment.enhancement.level > 0 {
            details.push_str("\n总属性(含强化):\n");
            let total_attrs = equipment.get_total_attributes();
            for (attr_type, attr) in &total_attrs.attributes {
                details.push_str(&format!("  {:?}: {:.1}\n", attr_type, attr.value));
            }
        }
        
        if let Some(set_id) = equipment.set_id {
            details.push_str(&format!("\n套装ID: {}\n", set_id));
        }
        
        details.push_str(&format!("\n描述: {}\n", equipment.description));
        
        details
    }
    
    // ====================================================================
    // 私有初始化方法
    // ====================================================================
    
    /// 初始化默认装备模板
    fn init_default_templates(&mut self) {
        use crate::attribute::{AttributeType, CoreAttribute, DerivedAttribute};
        
        // 创建一些基础装备模板
        
        // 1. 基础剑类武器
        let mut sword_attrs = AttributeSet::new();
        sword_attrs.add(Attribute::new(AttributeType::Base(CoreAttribute::Fire), 25.0));
        sword_attrs.add(Attribute::new(AttributeType::Base(CoreAttribute::Metal), 15.0));
        
        let sword_template = EquipmentTemplate {
            name: "烈焰剑".to_string(),
            eq_type: EquipmentType::Weapon,
            quality: EquipmentQuality::Common,
            level_requirement: 1,
            base_attributes: sword_attrs,
            material_grade: MaterialGrade::Mortal,
            description: "一把散发着火焰气息的长剑，适合初学者使用。".to_string(),
            set_id: None,
        };
        self.equipment_templates.insert(1001, sword_template);
        
        // 2. 基础防具
        let mut armor_attrs = AttributeSet::new();
        armor_attrs.add(Attribute::new(AttributeType::Base(CoreAttribute::Earth), 30.0));
        armor_attrs.add(Attribute::new(AttributeType::Base(CoreAttribute::Water), 20.0));
        
        let armor_template = EquipmentTemplate {
            name: "大地护甲".to_string(),
            eq_type: EquipmentType::Armor,
            quality: EquipmentQuality::Common,
            level_requirement: 1,
            base_attributes: armor_attrs,
            material_grade: MaterialGrade::Mortal,
            description: "由坚硬岩石制成的护甲，提供基础防护。".to_string(),
            set_id: None,
        };
        self.equipment_templates.insert(1002, armor_template);
        
        // 3. 高级套装 - 龙鳞套装
        let mut dragonscale_weapon_attrs = AttributeSet::new();
        dragonscale_weapon_attrs.add(Attribute::new(AttributeType::Base(CoreAttribute::Fire), 80.0));
        dragonscale_weapon_attrs.add(Attribute::new(AttributeType::Composite(DerivedAttribute::Thunder), 45.0));
        
        let dragonscale_weapon = EquipmentTemplate {
            name: "龙鳞剑".to_string(),
            eq_type: EquipmentType::Weapon,
            quality: EquipmentQuality::Epic,
            level_requirement: 25,
            base_attributes: dragonscale_weapon_attrs,
            material_grade: MaterialGrade::Spiritual,
            description: "传说中的龙鳞制成的武器，蕴含强大的火焰与雷电之力。".to_string(),
            set_id: Some(2001),
        };
        self.equipment_templates.insert(2001, dragonscale_weapon);
        
        let mut dragonscale_armor_attrs = AttributeSet::new();
        dragonscale_armor_attrs.add(Attribute::new(AttributeType::Base(CoreAttribute::Earth), 100.0));
        dragonscale_armor_attrs.add(Attribute::new(AttributeType::Base(CoreAttribute::Fire), 60.0));
        
        let dragonscale_armor = EquipmentTemplate {
            name: "龙鳞甲".to_string(),
            eq_type: EquipmentType::Armor,
            quality: EquipmentQuality::Epic,
            level_requirement: 25,
            base_attributes: dragonscale_armor_attrs,
            material_grade: MaterialGrade::Spiritual,
            description: "龙鳞制成的护甲，坚不可摧且散发威严。".to_string(),
            set_id: Some(2001),
        };
        self.equipment_templates.insert(2002, dragonscale_armor);
        
        // 初始化龙鳞套装效果
        let mut set_bonus = AttributeSet::new();
        set_bonus.add(Attribute::new(AttributeType::Base(CoreAttribute::Fire), 50.0));
        set_bonus.add(Attribute::new(AttributeType::Composite(DerivedAttribute::Thunder), 30.0));
        
        let dragonscale_set = crate::equipment::SetEffect {
            set_name: "龙鳞套装".to_string(),
            required_pieces: 2,
            bonus_attributes: set_bonus,
            special_effects: vec![
                "火焰免疫".to_string(),
                "雷电抗性+50%".to_string(),
                "攻击时有20%几率触发龙息".to_string(),
            ],
        };
        self.set_manager.add_set(2001, dragonscale_set);
    }
    
    /// 初始化强化材料配置
    fn init_enhancement_materials(&mut self) {
        // 强化等级 1-5: 基础材料
        for level in 1..=5 {
            self.enhancement_materials.insert(level, vec![
                (3001, level as u32), // 基础强化石
                (3002, 1),            // 稳定剂
            ]);
        }
        
        // 强化等级 6-10: 中级材料
        for level in 6..=10 {
            self.enhancement_materials.insert(level, vec![
                (3003, (level - 5) as u32), // 中级强化石
                (3004, 1),                  // 高级稳定剂
                (3001, level as u32 * 2),   // 额外基础强化石
            ]);
        }
        
        // 强化等级 11-15: 高级材料
        for level in 11..=15 {
            self.enhancement_materials.insert(level, vec![
                (3005, (level - 10) as u32), // 高级强化石
                (3006, 1),                   // 大师级稳定剂
                (3007, 1),                   // 祝福水晶
            ]);
        }
        
        // 强化等级 16-20: 传说材料
        for level in 16..=20 {
            self.enhancement_materials.insert(level, vec![
                (3008, (level - 15) as u32), // 传说强化石
                (3009, 1),                   // 神圣稳定剂
                (3010, 1),                   // 奇迹水晶
                (3011, 1),                   // 龙族祝福
            ]);
        }
    }
}

// ============================================================================
// 默认实现
// ============================================================================

impl Default for EquipmentService {
    fn default() -> Self {
        Self::new()
    }
}

impl std::fmt::Display for EnhancementResult {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            EnhancementResult::Success(level) => write!(f, "强化成功！新等级: +{}", level),
            EnhancementResult::Failure => write!(f, "强化失败，装备未受损"),
            EnhancementResult::LevelDown(level) => write!(f, "强化失败，等级下降至: +{}", level),
            EnhancementResult::Destroyed => write!(f, "强化失败，装备被摧毁"),
        }
    }
}

impl std::fmt::Display for AttributeChange {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let direction = if self.change > 0.0 { "↗" } else { "↘" };
        write!(f, "{:?}: {:.1} {} {:.1} ({}{:.1})", 
               self.attr_type, self.old_value, direction, self.new_value, 
               if self.change > 0.0 { "+" } else { "" }, self.change)
    }
}
