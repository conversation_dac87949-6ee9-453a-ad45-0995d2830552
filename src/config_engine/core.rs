//! # 核心数据结构和执行上下文
//!
//! 定义配置引擎的核心数据结构，包括执行上下文、结果和选项等

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant};

use crate::config_engine::{error::ConfigEngineError, Result};
use crate::config_engine::rules::ContextValue;

/// 配置源类型
///
/// 定义不同类型的配置源
#[derive(Debug, Clone)]
pub enum ConfigSourceType {
    /// 文件系统配置源
    FileSystem {
        /// 配置文件路径
        path: String,
    },
    /// 环境变量配置源
    Environment {
        /// 环境变量前缀
        prefix: String,
    },
    /// HTTP配置源
    Http {
        /// 配置服务URL
        url: String,
    },
    /// 数据库配置源
    Database {
        /// 数据库连接字符串
        connection_string: String,
    },
}

/// 规则执行上下文
///
/// 包含规则执行所需的所有输入参数和环境信息
///
/// # 示例
///
/// ```rust
/// use game::config_engine::RuleContext;
///
/// let context = RuleContext::new()
///     .set("player_level", 50)
///     .set("terrain_type", "forest")
///     .set("skill_bonuses", vec!["herbalism", "exploration"])
///     .set_metadata("session_id", "abc123");
/// ```
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleContext {
    /// 规则输入参数
    parameters: HashMap<String, ContextValue>,
    /// 元数据（不参与规则计算，但可用于调试和审计）
    metadata: HashMap<String, String>,
    /// 上下文创建时间
    created_at: DateTime<Utc>,
    /// 上下文ID（用于追踪）
    context_id: String,
}

impl RuleContext {
    /// 创建新的规则上下文
    pub fn new() -> Self {
        Self {
            parameters: HashMap::new(),
            metadata: HashMap::new(),
            created_at: Utc::now(),
            context_id: uuid::Uuid::new_v4().to_string(),
        }
    }

    /// 设置参数值
    pub fn set(mut self, key: impl Into<String>, value: impl Into<ContextValue>) -> Self {
        self.parameters.insert(key.into(), value.into());
        self
    }

    /// 设置元数据
    pub fn set_metadata(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.metadata.insert(key.into(), value.into());
        self
    }

    /// 获取参数值
    pub fn get(&self, key: &str) -> Option<&ContextValue> {
        self.parameters.get(key)
    }

    /// 获取强类型参数值
    pub fn get_typed<T>(&self, key: &str) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        match self.parameters.get(key) {
            Some(value) => value.deserialize(),
            None => Err(ConfigEngineError::RuleExecution(
                crate::config_engine::error::RuleExecutionError::InvalidInput {
                    rule_name: "unknown".to_string(),
                    parameter: key.to_string(),
                    reason: "参数不存在".to_string(),
                },
            )),
        }
    }

    /// 获取元数据
    pub fn get_metadata(&self, key: &str) -> Option<&String> {
        self.metadata.get(key)
    }

    /// 获取所有参数
    pub fn parameters(&self) -> &HashMap<String, ContextValue> {
        &self.parameters
    }

    /// 获取所有元数据
    pub fn metadata(&self) -> &HashMap<String, String> {
        &self.metadata
    }

    /// 获取上下文ID
    pub fn context_id(&self) -> &str {
        &self.context_id
    }

    /// 获取创建时间
    pub fn created_at(&self) -> DateTime<Utc> {
        self.created_at
    }

    /// 转换为JSON值（用于传递给规则引擎）
    pub fn to_json(&self) -> Result<serde_json::Value> {
        Ok(serde_json::to_value(&self.parameters)?)
    }

    /// 合并另一个上下文
    pub fn merge(mut self, other: &RuleContext) -> Self {
        for (key, value) in &other.parameters {
            self.parameters.insert(key.clone(), value.clone());
        }
        for (key, value) in &other.metadata {
            self.metadata.insert(key.clone(), value.clone());
        }
        self
    }

    /// 验证必需参数是否存在
    pub fn validate_required_parameters(&self, required: &[&str]) -> Result<()> {
        for param in required {
            if !self.parameters.contains_key(*param) {
                return Err(ConfigEngineError::Validation(
                    crate::config_engine::error::ValidationError::RequiredFieldMissing {
                        field_name: param.to_string(),
                    },
                ));
            }
        }
        Ok(())
    }

    /// 计算上下文的哈希值（用于缓存键）
    pub fn hash(&self) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();

        // 按键排序以确保一致的哈希
        let mut sorted_keys: Vec<_> = self.parameters.keys().collect();
        sorted_keys.sort();

        for key in sorted_keys {
            key.hash(&mut hasher);
            if let Some(value) = self.parameters.get(key) {
                // 将 ContextValue 转换为字符串进行哈希
                format!("{:?}", value).hash(&mut hasher);
            }
        }

        format!("{:x}", hasher.finish())
    }
}

impl Default for RuleContext {
    fn default() -> Self {
        Self::new()
    }
}

/// 规则执行结果
///
/// 包含规则执行的输出数据和执行元信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleResult {
    /// 规则输出数据
    data: HashMap<String, ContextValue>,
    /// 执行元信息
    metadata: ExecutionMetadata,
    /// 结果创建时间
    created_at: DateTime<Utc>,
}

impl RuleResult {
    /// 创建新的规则结果
    pub fn new(data: HashMap<String, ContextValue>, metadata: ExecutionMetadata) -> Self {
        Self {
            data,
            metadata,
            created_at: Utc::now(),
        }
    }

    /// 获取输出值
    pub fn get(&self, key: &str) -> Option<&ContextValue> {
        self.data.get(key)
    }

    /// 获取强类型输出值
    pub fn get_typed<T>(&self, key: &str) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        match self.data.get(key) {
            Some(value) => value.deserialize(),
            None => Err(ConfigEngineError::RuleExecution(
                crate::config_engine::error::RuleExecutionError::LogicError {
                    rule_name: self.metadata.rule_name.clone(),
                    details: format!("输出字段 '{}' 不存在", key),
                },
            )),
        }
    }

    /// 获取数组类型输出
    pub fn get_array<T>(&self, key: &str) -> Result<Vec<T>>
    where
        T: for<'de> Deserialize<'de>,
    {
        match self.data.get(key) {
            Some(ContextValue::Array(arr)) => {
                let mut result = Vec::new();
                for item in arr {
                    result.push(item.deserialize()?);
                }
                Ok(result)
            }
            Some(_) => Err(ConfigEngineError::TypeSystem(
                crate::config_engine::error::TypeSystemError::ConversionFailure {
                    from_type: self.data.get(key).unwrap().type_name().to_string(),
                    to_type: "array".to_string(),
                },
            )),
            None => Err(ConfigEngineError::RuleExecution(
                crate::config_engine::error::RuleExecutionError::LogicError {
                    rule_name: self.metadata.rule_name.clone(),
                    details: format!("输出字段 '{}' 不存在", key),
                },
            )),
        }
    }

    /// 获取所有输出数据
    pub fn data(&self) -> &HashMap<String, ContextValue> {
        &self.data
    }

    /// 获取执行元信息
    pub fn metadata(&self) -> &ExecutionMetadata {
        &self.metadata
    }

    /// 获取创建时间
    pub fn created_at(&self) -> DateTime<Utc> {
        self.created_at
    }

    /// 转换为JSON值
    pub fn to_json(&self) -> Result<serde_json::Value> {
        Ok(serde_json::to_value(&self.data)?)
    }

    /// 检查执行是否成功
    pub fn is_success(&self) -> bool {
        self.metadata.error.is_none()
    }

    /// 获取执行时间
    pub fn execution_duration(&self) -> Duration {
        self.metadata.execution_duration
    }
}

/// 执行元信息
///
/// 记录规则执行的详细信息，用于调试和监控
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionMetadata {
    /// 规则名称
    pub rule_name: String,
    /// 执行时间
    pub execution_duration: Duration,
    /// 是否缓存命中
    pub cache_hit: bool,
    /// 执行ID（用于追踪）
    pub execution_id: String,
    /// 输入上下文ID
    pub context_id: String,
    /// 执行开始时间
    pub started_at: DateTime<Utc>,
    /// 执行结束时间
    pub completed_at: DateTime<Utc>,
    /// 错误信息（如果有）
    pub error: Option<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 执行统计
    pub stats: ExecutionStats,
}

/// 执行统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionStats {
    /// 处理的输入参数数量
    pub input_parameters_count: usize,
    /// 生成的输出字段数量
    pub output_fields_count: usize,
    /// 执行的规则步骤数量
    pub rule_steps_executed: usize,
    /// 内存使用量（字节）
    pub memory_used_bytes: usize,
    /// CPU时间（微秒）
    pub cpu_time_micros: u64,
}

impl Default for ExecutionStats {
    fn default() -> Self {
        Self {
            input_parameters_count: 0,
            output_fields_count: 0,
            rule_steps_executed: 0,
            memory_used_bytes: 0,
            cpu_time_micros: 0,
        }
    }
}

/// 规则执行选项
///
/// 控制规则执行的各种参数和行为
#[derive(Debug, Clone)]
pub struct ExecutionOptions {
    /// 是否使用缓存
    pub use_cache: bool,
    /// 执行超时时间
    pub timeout: Option<Duration>,
    /// 是否启用调试模式
    pub debug_mode: bool,
    /// 是否收集详细统计信息
    pub collect_stats: bool,
    /// 最大内存使用限制（字节）
    pub max_memory_bytes: Option<usize>,
    /// 自定义标签（用于分类和过滤）
    pub tags: HashMap<String, String>,
    /// 重试选项
    pub retry_options: Option<RetryOptions>,
}

impl Default for ExecutionOptions {
    fn default() -> Self {
        Self {
            use_cache: true,
            timeout: Some(Duration::from_secs(30)),
            debug_mode: false,
            collect_stats: true,
            max_memory_bytes: None,
            tags: HashMap::new(),
            retry_options: None,
        }
    }
}

impl ExecutionOptions {
    /// 创建新的执行选项
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置缓存使用
    pub fn with_cache(mut self, use_cache: bool) -> Self {
        self.use_cache = use_cache;
        self
    }

    /// 设置超时时间
    pub fn with_timeout(mut self, timeout: Duration) -> Self {
        self.timeout = Some(timeout);
        self
    }

    /// 启用调试模式
    pub fn with_debug(mut self, debug: bool) -> Self {
        self.debug_mode = debug;
        self
    }

    /// 设置内存限制
    pub fn with_memory_limit(mut self, max_bytes: usize) -> Self {
        self.max_memory_bytes = Some(max_bytes);
        self
    }

    /// 添加标签
    pub fn with_tag(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.tags.insert(key.into(), value.into());
        self
    }

    /// 设置重试选项
    pub fn with_retry(mut self, retry_options: RetryOptions) -> Self {
        self.retry_options = Some(retry_options);
        self
    }
}

/// 重试选项
#[derive(Debug, Clone)]
pub struct RetryOptions {
    /// 最大重试次数
    pub max_attempts: usize,
    /// 重试间隔
    pub retry_delay: Duration,
    /// 是否使用指数退避
    pub exponential_backoff: bool,
    /// 最大退避时间
    pub max_backoff: Duration,
}

impl Default for RetryOptions {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            retry_delay: Duration::from_millis(100),
            exponential_backoff: true,
            max_backoff: Duration::from_secs(10),
        }
    }
}

/// 执行追踪器
///
/// 用于记录和追踪规则执行过程
#[derive(Debug)]
pub struct ExecutionTracker {
    execution_id: String,
    rule_name: String,
    context_id: String,
    started_at: Instant,
    start_time: DateTime<Utc>,
    warnings: Vec<String>,
    stats: ExecutionStats,
}

impl ExecutionTracker {
    /// 创建新的执行追踪器
    pub fn new(rule_name: String, context_id: String) -> Self {
        Self {
            execution_id: uuid::Uuid::new_v4().to_string(),
            rule_name,
            context_id,
            started_at: Instant::now(),
            start_time: Utc::now(),
            warnings: Vec::new(),
            stats: ExecutionStats::default(),
        }
    }

    /// 添加警告信息
    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }

    /// 更新统计信息
    pub fn update_stats(&mut self, stats: ExecutionStats) {
        self.stats = stats;
    }

    /// 完成追踪，生成元信息
    pub fn finish(self, cache_hit: bool, error: Option<String>) -> ExecutionMetadata {
        let execution_duration = self.started_at.elapsed();

        ExecutionMetadata {
            rule_name: self.rule_name,
            execution_duration,
            cache_hit,
            execution_id: self.execution_id,
            context_id: self.context_id,
            started_at: self.start_time,
            completed_at: Utc::now(),
            error,
            warnings: self.warnings,
            stats: self.stats,
        }
    }
}

#[cfg(test)]
mod tests {
    use crate::ContextValue;
    use super::*;

    #[test]
    fn test_rule_context_creation() {
        let context = RuleContext::new()
            .set("param1", "value1")
            .set("param2", 42)
            .set_metadata("session", "test_session");

        assert_eq!(context.get("param1").unwrap().as_string(), Some("value1"));
        assert_eq!(context.get("param2").unwrap().as_int(), Some(42));
        assert_eq!(
            context.get_metadata("session"),
            Some(&"test_session".to_string())
        );
    }

    #[test]
    fn test_config_value_conversions() {
        let bool_val: ContextValue = true.into();
        assert_eq!(bool_val.as_bool(), Some(true));

        let int_val: ContextValue = 42.into();
        assert_eq!(int_val.as_int(), Some(42));

        let string_val: ContextValue = "test".into();
        assert_eq!(string_val.as_string(), Some("test"));

        let array_val: ContextValue = vec![1, 2, 3].into();
        assert!(array_val.as_array().is_some());
    }

    #[test]
    fn test_execution_options() {
        let options = ExecutionOptions::new()
            .with_cache(false)
            .with_timeout(Duration::from_secs(10))
            .with_debug(true)
            .with_tag("environment", "test");

        assert!(!options.use_cache);
        assert_eq!(options.timeout, Some(Duration::from_secs(10)));
        assert!(options.debug_mode);
        assert_eq!(options.tags.get("environment"), Some(&"test".to_string()));
    }

    #[test]
    fn test_execution_tracker() {
        let mut tracker = ExecutionTracker::new("test_rule".to_string(), "ctx123".to_string());
        tracker.add_warning("Test warning".to_string());

        let metadata = tracker.finish(false, None);
        assert_eq!(metadata.rule_name, "test_rule");
        assert!(!metadata.cache_hit);
        assert_eq!(metadata.warnings.len(), 1);
        assert!(metadata.error.is_none());
    }
}
