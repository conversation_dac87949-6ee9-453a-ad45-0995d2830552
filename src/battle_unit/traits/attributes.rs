use std::collections::HashMap;
use crate::attribute::AttributeType;
use crate::battle_unit::AttributeValue;

/// 基础属性 - 力量、敏捷、智力等基础属性
pub trait BaseAttributes {
    /// 获取力量
    fn strength(&self) -> AttributeValue;
    
    /// 获取敏捷
    fn dexterity(&self) -> AttributeValue;
    
    /// 获取智力
    fn intelligence(&self) -> AttributeValue;
    
    /// 获取体质
    fn constitution(&self) -> AttributeValue;
    
    /// 获取魅力
    fn charisma(&self) -> AttributeValue;
    
    /// 获取感知
    fn perception(&self) -> AttributeValue;
    
    /// 获取幸运
    fn luck(&self) -> AttributeValue;
    
    /// 获取属性总和
    fn total_attributes(&self) -> AttributeValue;
    
    /// 获取属性加成映射
    fn attribute_bonuses(&self) -> HashMap<AttributeType, AttributeValue>;
    
    /// 计算属性修正值
    fn attribute_modifier(&self, attribute: AttributeType) -> i32;
}