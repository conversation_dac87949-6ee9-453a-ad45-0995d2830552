/// Character模块 - 基于DDD的角色聚合
/// 
/// 重构后的Character模块采用DDD分层架构：
/// - domain: 领域层（聚合根、实体、值对象、领域服务）
/// - application: 应用层（应用服务、命令处理）
/// - infrastructure: 基础设施层（仓储实现）

pub mod domain;

// 未来可以添加的层次
// pub mod application;
// pub mod infrastructure;

// 重新导出主要类型，保持向后兼容
pub use domain::Character;
pub use domain::CharacterBuilder;
pub use domain::*;

// ============================================================================
// 向后兼容性支持
// ============================================================================