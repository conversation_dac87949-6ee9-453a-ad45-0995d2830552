/// 成长系统 - 处理角色成长、属性分配和天赋觉醒
/// 结合修仙世界观，包含天赋、悟性、根骨等概念

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::shared::types::*;
use crate::experience::level_system::AttributeBonus;

// ============================================================================
// 成长系统核心
// ============================================================================

/// 角色成长管理器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrowthSystem {
    /// 天赋系统配置
    pub talent_config: TalentConfig,
    /// 属性分配配置
    pub attribute_allocation_config: AttributeAllocationConfig,
    /// 悟性系统配置
    pub comprehension_config: ComprehensionConfig,
    /// 根骨系统配置
    pub spiritual_root_config: SpiritualRootConfig,
}

/// 角色天赋
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Talent {
    /// 五行天赋
    ElementalAffinity(ElementalType),
    /// 战斗天赋
    CombatGenius,
    /// 炼丹天赋
    AlchemyGenius,
    /// 炼器天赋
    CraftingGenius,
    /// 修炼天赋
    CultivationGenius,
    /// 领悟天赋
    ComprehensionGenius,
    /// 速成天赋
    FastLearner,
    /// 持久天赋
    Endurance,
    /// 幸运天赋
    Lucky,
    /// 洞察天赋
    Insight,
    /// 魅力天赋
    Charisma,
    /// 恢复天赋
    Regeneration,
}

/// 五行元素类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ElementalType {
    /// 金系
    Metal,
    /// 木系
    Wood,
    /// 水系
    Water,
    /// 火系
    Fire,
    /// 土系
    Earth,
}

impl ElementalType {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            ElementalType::Metal => "金系",
            ElementalType::Wood => "木系",
            ElementalType::Water => "水系",
            ElementalType::Fire => "火系",
            ElementalType::Earth => "土系",
        }
    }
}

impl Talent {
    /// 获取天赋的中文名称
    pub fn chinese_name(&self) -> String {
        match self {
            Talent::ElementalAffinity(element) => format!("{}天赋", element.chinese_name()),
            Talent::CombatGenius => "战斗天才".to_string(),
            Talent::AlchemyGenius => "炼丹天才".to_string(),
            Talent::CraftingGenius => "炼器天才".to_string(),
            Talent::CultivationGenius => "修炼天才".to_string(),
            Talent::ComprehensionGenius => "悟性超凡".to_string(),
            Talent::FastLearner => "学习天赋".to_string(),
            Talent::Endurance => "持久天赋".to_string(),
            Talent::Lucky => "天生幸运".to_string(),
            Talent::Insight => "洞察天赋".to_string(),
            Talent::Charisma => "魅力天赋".to_string(),
            Talent::Regeneration => "恢复天赋".to_string(),
        }
    }
    
    /// 获取天赋描述
    pub fn description(&self) -> String {
        match self {
            Talent::ElementalAffinity(element) => {
                format!("对{}元素的亲和力极高，{}系技能效果提升20%", 
                    element.chinese_name(), element.chinese_name())
            },
            Talent::CombatGenius => "天生的战士，战斗经验获得提升30%，技能冷却时间减少10%".to_string(),
            Talent::AlchemyGenius => "炼丹成功率提升25%，有概率炼制出品质更高的丹药".to_string(),
            Talent::CraftingGenius => "炼器成功率提升25%，装备品质有概率额外提升".to_string(),
            Talent::CultivationGenius => "修炼速度提升50%，境界突破成功率提升20%".to_string(),
            Talent::ComprehensionGenius => "技能学习速度提升40%，顿悟概率提升".to_string(),
            Talent::FastLearner => "所有经验获得提升20%".to_string(),
            Talent::Endurance => "生命值和法力值上限提升15%，状态恢复速度提升".to_string(),
            Talent::Lucky => "掉落概率提升，随机事件更容易获得好结果".to_string(),
            Talent::Insight => "能够洞察敌人弱点，暴击率和暴击伤害提升".to_string(),
            Talent::Charisma => "NPC好感度提升速度翻倍，交易价格更优惠".to_string(),
            Talent::Regeneration => "战斗中每回合恢复最大生命值和法力值的5%".to_string(),
        }
    }
    
    /// 获取天赋稀有度（1-5星）
    pub fn rarity(&self) -> u8 {
        match self {
            Talent::ElementalAffinity(_) => 3,
            Talent::CombatGenius => 4,
            Talent::AlchemyGenius => 4,
            Talent::CraftingGenius => 4,
            Talent::CultivationGenius => 5,
            Talent::ComprehensionGenius => 5,
            Talent::FastLearner => 3,
            Talent::Endurance => 2,
            Talent::Lucky => 4,
            Talent::Insight => 3,
            Talent::Charisma => 2,
            Talent::Regeneration => 3,
        }
    }
}

/// 天赋配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TalentConfig {
    /// 初始天赋数量
    pub initial_talent_count: u32,
    /// 天赋觉醒等级要求
    pub talent_awakening_levels: Vec<Level>,
    /// 天赋重置费用
    pub talent_reset_cost: HashMap<u32, Exp>, // 重置次数 -> 费用
}

/// 灵根类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SpiritualRootType {
    /// 废柴灵根（1星）
    Trash,
    /// 普通灵根（2星）
    Common,
    /// 良好灵根（3星）
    Good,
    /// 优秀灵根（4星）
    Excellent,
    /// 天才灵根（5星）
    Genius,
    /// 绝世灵根（6星）
    Legendary,
    /// 混沌灵根（7星）
    Chaos,
}

impl SpiritualRootType {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            SpiritualRootType::Trash => "废柴灵根",
            SpiritualRootType::Common => "普通灵根",
            SpiritualRootType::Good => "良好灵根",
            SpiritualRootType::Excellent => "优秀灵根",
            SpiritualRootType::Genius => "天才灵根",
            SpiritualRootType::Legendary => "绝世灵根",
            SpiritualRootType::Chaos => "混沌灵根",
        }
    }
    
    /// 获取修炼速度倍数
    pub fn cultivation_speed_multiplier(&self) -> f32 {
        match self {
            SpiritualRootType::Trash => 0.5,
            SpiritualRootType::Common => 1.0,
            SpiritualRootType::Good => 1.5,
            SpiritualRootType::Excellent => 2.0,
            SpiritualRootType::Genius => 3.0,
            SpiritualRootType::Legendary => 5.0,
            SpiritualRootType::Chaos => 10.0,
        }
    }
    
    /// 获取星级
    pub fn star_rating(&self) -> u8 {
        match self {
            SpiritualRootType::Trash => 1,
            SpiritualRootType::Common => 2,
            SpiritualRootType::Good => 3,
            SpiritualRootType::Excellent => 4,
            SpiritualRootType::Genius => 5,
            SpiritualRootType::Legendary => 6,
            SpiritualRootType::Chaos => 7,
        }
    }
}

/// 灵根配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpiritualRoot {
    /// 灵根类型
    pub root_type: SpiritualRootType,
    /// 元素亲和
    pub elemental_affinities: HashMap<ElementalType, f32>,
    /// 特殊属性
    pub special_properties: Vec<String>,
}

/// 属性分配配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttributeAllocationConfig {
    /// 每级获得的自由属性点
    pub free_points_per_level: u32,
    /// 属性点分配限制
    pub allocation_limits: HashMap<String, u32>,
    /// 属性转换比例
    pub conversion_rates: HashMap<String, f32>,
}

/// 悟性系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComprehensionConfig {
    /// 顿悟基础概率
    pub base_enlightenment_chance: f32,
    /// 顿悟奖励倍数
    pub enlightenment_multiplier: f32,
    /// 悟性影响因子
    pub comprehension_factors: HashMap<String, f32>,
}

/// 灵根系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpiritualRootConfig {
    /// 灵根觉醒等级
    pub awakening_levels: Vec<Level>,
    /// 灵根进化材料需求
    pub evolution_requirements: HashMap<SpiritualRootType, Vec<(String, u32)>>,
}

/// 角色成长数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterGrowth {
    /// 角色ID
    pub character_id: ID,
    /// 拥有的天赋
    pub talents: Vec<Talent>,
    /// 灵根信息
    pub spiritual_root: SpiritualRoot,
    /// 悟性值
    pub comprehension: u32,
    /// 根骨值
    pub constitution_base: u32,
    /// 可分配属性点
    pub free_attribute_points: u32,
    /// 天赋点
    pub talent_points: u32,
    /// 成长历史
    pub growth_history: Vec<GrowthEvent>,
}

/// 成长事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrowthEvent {
    /// 事件类型
    pub event_type: GrowthEventType,
    /// 发生等级
    pub level: Level,
    /// 事件描述
    pub description: String,
    /// 获得的奖励
    pub rewards: Vec<String>,
    /// 事件时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// 成长事件类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum GrowthEventType {
    /// 天赋觉醒
    TalentAwakening,
    /// 境界突破
    RealmBreakthrough,
    /// 顿悟
    Enlightenment,
    /// 灵根进化
    SpiritualRootEvolution,
    /// 特殊奖励
    SpecialReward,
}

/// 成长结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrowthResult {
    /// 新觉醒的天赋
    pub new_talents: Vec<Talent>,
    /// 属性奖励
    pub attribute_bonus: AttributeBonus,
    /// 特殊奖励
    pub special_rewards: Vec<String>,
    /// 成长消息
    pub growth_messages: Vec<String>,
}

impl GrowthSystem {
    /// 创建默认成长系统
    pub fn new() -> Self {
        Self {
            talent_config: TalentConfig {
                initial_talent_count: 1,
                talent_awakening_levels: vec![10, 20, 30, 50, 70, 100],
                talent_reset_cost: {
                    let mut cost = HashMap::new();
                    cost.insert(1, 10000);
                    cost.insert(2, 50000);
                    cost.insert(3, 200000);
                    cost.insert(4, 1000000);
                    cost
                },
            },
            attribute_allocation_config: AttributeAllocationConfig {
                free_points_per_level: 5,
                allocation_limits: HashMap::new(),
                conversion_rates: HashMap::new(),
            },
            comprehension_config: ComprehensionConfig {
                base_enlightenment_chance: 0.05,
                enlightenment_multiplier: 2.0,
                comprehension_factors: HashMap::new(),
            },
            spiritual_root_config: SpiritualRootConfig {
                awakening_levels: vec![1, 25, 50, 75, 100],
                evolution_requirements: HashMap::new(),
            },
        }
    }
    
    /// 生成随机天赋
    pub fn generate_random_talent(&self) -> Talent {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        
        let talent_types = vec![
            Talent::ElementalAffinity(ElementalType::Metal),
            Talent::ElementalAffinity(ElementalType::Wood),
            Talent::ElementalAffinity(ElementalType::Water),
            Talent::ElementalAffinity(ElementalType::Fire),
            Talent::ElementalAffinity(ElementalType::Earth),
            Talent::CombatGenius,
            Talent::AlchemyGenius,
            Talent::CraftingGenius,
            Talent::CultivationGenius,
            Talent::ComprehensionGenius,
            Talent::FastLearner,
            Talent::Endurance,
            Talent::Lucky,
            Talent::Insight,
            Talent::Charisma,
            Talent::Regeneration,
        ];
        
        // 根据稀有度权重选择
        let weights: Vec<f32> = talent_types.iter()
            .map(|t| match t.rarity() {
                5 => 0.05,  // 5星：5%
                4 => 0.15,  // 4星：15%
                3 => 0.30,  // 3星：30%
                2 => 0.35,  // 2星：35%
                _ => 0.15,  // 1星：15%
            })
            .collect();
        
        let total_weight: f32 = weights.iter().sum();
        let mut random_value = rng.gen::<f32>() * total_weight;
        
        for (i, &weight) in weights.iter().enumerate() {
            random_value -= weight;
            if random_value <= 0.0 {
                return talent_types[i];
            }
        }
        
        talent_types[0] // 默认返回第一个
    }
    
    /// 生成随机灵根
    pub fn generate_random_spiritual_root(&self) -> SpiritualRoot {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        
        // 根据概率分布生成灵根类型
        let random_value = rng.gen::<f32>();
        let root_type = match random_value {
            v if v < 0.4 => SpiritualRootType::Trash,    // 40%
            v if v < 0.7 => SpiritualRootType::Common,   // 30%
            v if v < 0.85 => SpiritualRootType::Good,    // 15%
            v if v < 0.95 => SpiritualRootType::Excellent, // 10%
            v if v < 0.99 => SpiritualRootType::Genius,  // 4%
            v if v < 0.999 => SpiritualRootType::Legendary, // 0.9%
            _ => SpiritualRootType::Chaos,               // 0.1%
        };
        
        // 生成元素亲和
        let mut elemental_affinities = HashMap::new();
        for element in [ElementalType::Metal, ElementalType::Wood, ElementalType::Water, 
                       ElementalType::Fire, ElementalType::Earth] {
            let base_affinity = match root_type {
                SpiritualRootType::Trash => rng.gen_range(0.1..0.3),
                SpiritualRootType::Common => rng.gen_range(0.3..0.6),
                SpiritualRootType::Good => rng.gen_range(0.5..0.8),
                SpiritualRootType::Excellent => rng.gen_range(0.7..1.0),
                SpiritualRootType::Genius => rng.gen_range(0.8..1.2),
                SpiritualRootType::Legendary => rng.gen_range(1.0..1.5),
                SpiritualRootType::Chaos => rng.gen_range(1.2..2.0),
            };
            elemental_affinities.insert(element, base_affinity);
        }
        
        SpiritualRoot {
            root_type,
            elemental_affinities,
            special_properties: Vec::new(),
        }
    }
    
    /// 处理等级提升时的成长事件
    pub fn process_level_up_growth(
        &self,
        character_growth: &mut CharacterGrowth,
        new_level: Level,
        realm_breakthrough: bool,
    ) -> GrowthResult {
        let mut result = GrowthResult {
            new_talents: Vec::new(),
            attribute_bonus: AttributeBonus::zero(),
            special_rewards: Vec::new(),
            growth_messages: Vec::new(),
        };
        
        // 增加自由属性点
        character_growth.free_attribute_points += self.attribute_allocation_config.free_points_per_level;
        result.growth_messages.push(format!("获得{}点自由属性点", 
            self.attribute_allocation_config.free_points_per_level));
        
        // 检查天赋觉醒
        if self.talent_config.talent_awakening_levels.contains(&new_level) {
            let new_talent = self.generate_random_talent();
            character_growth.talents.push(new_talent);
            result.new_talents.push(new_talent);
            
            let event = GrowthEvent {
                event_type: GrowthEventType::TalentAwakening,
                level: new_level,
                description: format!("觉醒天赋：{}", new_talent.chinese_name()),
                rewards: vec![new_talent.description()],
                timestamp: chrono::Utc::now(),
            };
            character_growth.growth_history.push(event);
            
            result.growth_messages.push(format!("恭喜！觉醒新天赋：{}", new_talent.chinese_name()));
        }
        
        // 境界突破特殊奖励
        if realm_breakthrough {
            character_growth.talent_points += 10;
            result.special_rewards.push("获得10点天赋点".to_string());
            
            // 随机顿悟机会
            if self.check_enlightenment_chance(character_growth.comprehension) {
                let enlightenment_bonus = self.process_enlightenment(character_growth, new_level);
                result.attribute_bonus.hp_bonus += enlightenment_bonus.hp_bonus;
                result.attribute_bonus.mana_bonus += enlightenment_bonus.mana_bonus;
                result.growth_messages.push("突破时顿悟！获得额外属性奖励！".to_string());
            }
        }
        
        result
    }
    
    /// 检查顿悟概率
    fn check_enlightenment_chance(&self, comprehension: u32) -> bool {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        
        let base_chance = self.comprehension_config.base_enlightenment_chance;
        let comprehension_bonus = comprehension as f32 * 0.001; // 每点悟性增加0.1%概率
        let total_chance = base_chance + comprehension_bonus;
        
        rng.gen::<f32>() < total_chance
    }
    
    /// 处理顿悟事件
    fn process_enlightenment(&self, character_growth: &mut CharacterGrowth, level: Level) -> AttributeBonus {
        character_growth.comprehension += 10; // 顿悟增加悟性
        
        let bonus = AttributeBonus {
            hp_bonus: (level * 20) as Health,
            mana_bonus: (level * 15) as Mana,
            attack_bonus: (level * 3) as Attack,
            defense_bonus: (level * 2) as Defense,
            speed_bonus: level as i32,
        };
        
        let event = GrowthEvent {
            event_type: GrowthEventType::Enlightenment,
            level,
            description: "修炼中突然顿悟".to_string(),
            rewards: vec![
                format!("悟性+10"),
                format!("生命值+{}", bonus.hp_bonus),
                format!("法力值+{}", bonus.mana_bonus),
            ],
            timestamp: chrono::Utc::now(),
        };
        character_growth.growth_history.push(event);
        
        bonus
    }
    
    /// 分配属性点
    pub fn allocate_attribute_points(
        &self,
        character_growth: &mut CharacterGrowth,
        hp_points: u32,
        mana_points: u32,
        attack_points: u32,
        defense_points: u32,
    ) -> Result<AttributeBonus, String> {
        let total_points = hp_points + mana_points + attack_points + defense_points;
        
        if total_points > character_growth.free_attribute_points {
            return Err("可用属性点不足".to_string());
        }
        
        character_growth.free_attribute_points -= total_points;
        
        let bonus = AttributeBonus {
            hp_bonus: (hp_points * 10) as Health,
            mana_bonus: (mana_points * 8) as Mana,
            attack_bonus: (attack_points * 2) as Attack,
            defense_bonus: (defense_points * 2) as Defense,
            speed_bonus: 0,
        };
        
        Ok(bonus)
    }
    
    /// 创建初始角色成长数据
    pub fn create_initial_character_growth(&self, character_id: ID) -> CharacterGrowth {
        let initial_talent = self.generate_random_talent();
        let spiritual_root = self.generate_random_spiritual_root();
        
        // 根据灵根类型设置初始悟性和根骨
        let base_comprehension = match spiritual_root.root_type {
            SpiritualRootType::Trash => 10,
            SpiritualRootType::Common => 20,
            SpiritualRootType::Good => 35,
            SpiritualRootType::Excellent => 50,
            SpiritualRootType::Genius => 70,
            SpiritualRootType::Legendary => 100,
            SpiritualRootType::Chaos => 150,
        };
        
        let constitution_base = base_comprehension; // 简化：根骨与悟性相同
        
        CharacterGrowth {
            character_id,
            talents: vec![initial_talent],
            spiritual_root,
            comprehension: base_comprehension,
            constitution_base,
            free_attribute_points: 25, // 初始属性点
            talent_points: 0,
            growth_history: vec![
                GrowthEvent {
                    event_type: GrowthEventType::TalentAwakening,
                    level: 1,
                    description: format!("天生觉醒：{}", initial_talent.chinese_name()),
                    rewards: vec![initial_talent.description()],
                    timestamp: chrono::Utc::now(),
                }
            ],
        }
    }
}

impl Default for GrowthSystem {
    fn default() -> Self {
        Self::new()
    }
}