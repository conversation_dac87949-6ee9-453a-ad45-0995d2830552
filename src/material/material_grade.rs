/// 扩展材料品阶系统
/// 基于修仙体系的详细品阶划分

use serde::{Deserialize, Serialize};
use crate::basic_definition::MaterialGrade;

// ============================================================================
// 详细品阶系统
// ============================================================================

/// 详细材料品阶 - 更精细的品阶划分
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum DetailedMaterialGrade {
    /// 凡品 (1-9品)
    Mortal(MortalGrade),
    /// 灵品 (1-9品)
    Spirit(SpiritGrade),
    /// 仙品 (下中上品)
    Immortal(ImmOrtalGrade),
    /// 神品 (下中上品)
    Divine(DivineGrade),
    /// 圣品 (下中上品)
    Sacred(SacredGrade),
    /// 道品 (下中上品)
    Dao(DaoGrade),
    /// 黄品 (下中上品)
    Yellow(CosmicGrade),
    /// 玄品 (下中上品)
    Mysterious(CosmicGrade),
    /// 地品 (下中上品)
    Earth(CosmicGrade),
    /// 天品 (下中上品)
    Heaven(CosmicGrade),
    /// 宙品 (下中上品)
    Universal(CosmicGrade),
    /// 宇品 (下中上品)
    Cosmic(CosmicGrade),
    /// 洪品 (下中上品)
    Primordial(CosmicGrade),
    /// 荒品 (下中上品)
    Desolate(CosmicGrade),
    /// 后天混沌 (下中上品)
    PostChaos(CosmicGrade),
    /// 先天混沌 (下中上品)
    PreChaos(CosmicGrade),
}

/// 凡品等级 (1-9品，数字越大品质越高)
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum MortalGrade {
    First = 1,
    Second = 2,
    Third = 3,
    Fourth = 4,
    Fifth = 5,
    Sixth = 6,
    Seventh = 7,
    Eighth = 8,
    Ninth = 9,
}

/// 灵品等级 (1-9品，数字越大品质越高)
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum SpiritGrade {
    First = 1,
    Second = 2,
    Third = 3,
    Fourth = 4,
    Fifth = 5,
    Sixth = 6,
    Seventh = 7,
    Eighth = 8,
    Ninth = 9,
}

/// 仙品等级 (下中上品)
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum ImmOrtalGrade {
    Lower,
    Middle,
    Upper,
}

/// 神品等级 (下中上品)
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum DivineGrade {
    Lower,
    Middle,
    Upper,
}

/// 圣品等级 (下中上品)
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum SacredGrade {
    Lower,
    Middle,
    Upper,
}

/// 道品等级 (下中上品)
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum DaoGrade {
    Lower,
    Middle,
    Upper,
}

/// 宇宙级品阶 (下中上品) - 用于天品以上的超越品阶
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum CosmicGrade {
    Lower,
    Middle,
    Upper,
}

impl DetailedMaterialGrade {
    /// 转换为基础材料品阶
    pub fn to_basic_grade(&self) -> MaterialGrade {
        match self {
            DetailedMaterialGrade::Mortal(_) => MaterialGrade::Mortal,
            DetailedMaterialGrade::Spirit(_) => MaterialGrade::Spiritual,
            DetailedMaterialGrade::Immortal(_) => MaterialGrade::Immortal,
            DetailedMaterialGrade::Divine(_) => MaterialGrade::Divine,
            DetailedMaterialGrade::Sacred(_) | DetailedMaterialGrade::Dao(_) => MaterialGrade::Sacred,
            _ => MaterialGrade::Chaos,
        }
    }
    
    /// 获取品阶的数值表示（用于比较和计算）
    pub fn numeric_value(&self) -> u32 {
        match self {
            DetailedMaterialGrade::Mortal(grade) => *grade as u32,
            DetailedMaterialGrade::Spirit(grade) => 100 + (*grade as u32),
            DetailedMaterialGrade::Immortal(grade) => 200 + (*grade as u32),
            DetailedMaterialGrade::Divine(grade) => 300 + (*grade as u32),
            DetailedMaterialGrade::Sacred(grade) => 400 + (*grade as u32),
            DetailedMaterialGrade::Dao(grade) => 500 + (*grade as u32),
            DetailedMaterialGrade::Yellow(grade) => 600 + (*grade as u32),
            DetailedMaterialGrade::Mysterious(grade) => 700 + (*grade as u32),
            DetailedMaterialGrade::Earth(grade) => 800 + (*grade as u32),
            DetailedMaterialGrade::Heaven(grade) => 900 + (*grade as u32),
            DetailedMaterialGrade::Universal(grade) => 1000 + (*grade as u32),
            DetailedMaterialGrade::Cosmic(grade) => 1100 + (*grade as u32),
            DetailedMaterialGrade::Primordial(grade) => 1200 + (*grade as u32),
            DetailedMaterialGrade::Desolate(grade) => 1300 + (*grade as u32),
            DetailedMaterialGrade::PostChaos(grade) => 1400 + (*grade as u32),
            DetailedMaterialGrade::PreChaos(grade) => 1500 + (*grade as u32),
        }
    }
    
    /// 获取品阶的中文名称
    pub fn chinese_name(&self) -> String {
        match self {
            DetailedMaterialGrade::Mortal(grade) => format!("凡品{}品", grade.chinese_number()),
            DetailedMaterialGrade::Spirit(grade) => format!("灵品{}品", grade.chinese_number()),
            DetailedMaterialGrade::Immortal(grade) => format!("仙品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Divine(grade) => format!("神品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Sacred(grade) => format!("圣品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Dao(grade) => format!("道品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Yellow(grade) => format!("黄品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Mysterious(grade) => format!("玄品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Earth(grade) => format!("地品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Heaven(grade) => format!("天品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Universal(grade) => format!("宙品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Cosmic(grade) => format!("宇品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Primordial(grade) => format!("洪品{}", grade.chinese_tier()),
            DetailedMaterialGrade::Desolate(grade) => format!("荒品{}", grade.chinese_tier()),
            DetailedMaterialGrade::PostChaos(grade) => format!("后天混沌{}", grade.chinese_tier()),
            DetailedMaterialGrade::PreChaos(grade) => format!("先天混沌{}", grade.chinese_tier()),
        }
    }
    
    /// 获取品阶的基础价值倍数
    pub fn value_multiplier(&self) -> f64 {
        let base = match self {
            DetailedMaterialGrade::Mortal(_) => 1.0,
            DetailedMaterialGrade::Spirit(_) => 10.0,
            DetailedMaterialGrade::Immortal(_) => 100.0,
            DetailedMaterialGrade::Divine(_) => 1000.0,
            DetailedMaterialGrade::Sacred(_) => 10000.0,
            DetailedMaterialGrade::Dao(_) => 100000.0,
            DetailedMaterialGrade::Yellow(_) => 1000000.0,
            DetailedMaterialGrade::Mysterious(_) => 10000000.0,
            DetailedMaterialGrade::Earth(_) => 100000000.0,
            DetailedMaterialGrade::Heaven(_) => 1000000000.0,
            DetailedMaterialGrade::Universal(_) => 10000000000.0,
            DetailedMaterialGrade::Cosmic(_) => 100000000000.0,
            DetailedMaterialGrade::Primordial(_) => 1000000000000.0,
            DetailedMaterialGrade::Desolate(_) => 10000000000000.0,
            DetailedMaterialGrade::PostChaos(_) => 100000000000000.0,
            DetailedMaterialGrade::PreChaos(_) => 1000000000000000.0,
        };
        
        let sub_multiplier = match self {
            DetailedMaterialGrade::Mortal(grade) => {
                1.0 + (*grade as u8 - 1) as f64 * 0.5
            },
            DetailedMaterialGrade::Spirit(grade) => {
                1.0 + (*grade as u8 - 1) as f64 * 0.5
            },
            DetailedMaterialGrade::Immortal(grade) => {
                match grade {
                    ImmOrtalGrade::Lower => 1.0,
                    ImmOrtalGrade::Middle => 3.0,
                    ImmOrtalGrade::Upper => 8.0,
                }
            },
            DetailedMaterialGrade::Divine(grade) => {
                match grade {
                    DivineGrade::Lower => 1.0,
                    DivineGrade::Middle => 3.0,
                    DivineGrade::Upper => 8.0,
                }
            },
            DetailedMaterialGrade::Sacred(grade) => {
                match grade {
                    SacredGrade::Lower => 1.0,
                    SacredGrade::Middle => 3.0,
                    SacredGrade::Upper => 8.0,
                }
            },
            DetailedMaterialGrade::Dao(grade) => {
                match grade {
                    DaoGrade::Lower => 1.0,
                    DaoGrade::Middle => 3.0,
                    DaoGrade::Upper => 8.0,
                }
            },
            _ => {
                match self.cosmic_tier() {
                    Some(CosmicGrade::Lower) => 1.0,
                    Some(CosmicGrade::Middle) => 3.0,
                    Some(CosmicGrade::Upper) => 8.0,
                    None => 1.0,
                }
            }
        };
        
        base * sub_multiplier
    }
    
    /// 获取宇宙级品阶的具体等级
    fn cosmic_tier(&self) -> Option<CosmicGrade> {
        match self {
            DetailedMaterialGrade::Yellow(tier) | DetailedMaterialGrade::Mysterious(tier) |
            DetailedMaterialGrade::Earth(tier) | DetailedMaterialGrade::Heaven(tier) |
            DetailedMaterialGrade::Universal(tier) | DetailedMaterialGrade::Cosmic(tier) |
            DetailedMaterialGrade::Primordial(tier) | DetailedMaterialGrade::Desolate(tier) |
            DetailedMaterialGrade::PostChaos(tier) | DetailedMaterialGrade::PreChaos(tier) => Some(*tier),
            _ => None,
        }
    }
}

impl MortalGrade {
    fn chinese_number(&self) -> &'static str {
        match self {
            MortalGrade::First => "一",
            MortalGrade::Second => "二",
            MortalGrade::Third => "三",
            MortalGrade::Fourth => "四",
            MortalGrade::Fifth => "五",
            MortalGrade::Sixth => "六",
            MortalGrade::Seventh => "七",
            MortalGrade::Eighth => "八",
            MortalGrade::Ninth => "九",
        }
    }
}

impl SpiritGrade {
    fn chinese_number(&self) -> &'static str {
        match self {
            SpiritGrade::First => "一",
            SpiritGrade::Second => "二",
            SpiritGrade::Third => "三",
            SpiritGrade::Fourth => "四",
            SpiritGrade::Fifth => "五",
            SpiritGrade::Sixth => "六",
            SpiritGrade::Seventh => "七",
            SpiritGrade::Eighth => "八",
            SpiritGrade::Ninth => "九",
        }
    }
}

trait ChineseTier {
    fn chinese_tier(&self) -> &'static str;
}

impl ChineseTier for ImmOrtalGrade {
    fn chinese_tier(&self) -> &'static str {
        match self {
            ImmOrtalGrade::Lower => "下品",
            ImmOrtalGrade::Middle => "中品",
            ImmOrtalGrade::Upper => "上品",
        }
    }
}

impl ChineseTier for DivineGrade {
    fn chinese_tier(&self) -> &'static str {
        match self {
            DivineGrade::Lower => "下品",
            DivineGrade::Middle => "中品",
            DivineGrade::Upper => "上品",
        }
    }
}

impl ChineseTier for SacredGrade {
    fn chinese_tier(&self) -> &'static str {
        match self {
            SacredGrade::Lower => "下品",
            SacredGrade::Middle => "中品",
            SacredGrade::Upper => "上品",
        }
    }
}

impl ChineseTier for DaoGrade {
    fn chinese_tier(&self) -> &'static str {
        match self {
            DaoGrade::Lower => "下品",
            DaoGrade::Middle => "中品",
            DaoGrade::Upper => "上品",
        }
    }
}

impl ChineseTier for CosmicGrade {
    fn chinese_tier(&self) -> &'static str {
        match self {
            CosmicGrade::Lower => "下品",
            CosmicGrade::Middle => "中品",
            CosmicGrade::Upper => "上品",
        }
    }
}

// ============================================================================
// 品质状态修饰
// ============================================================================

/// 材料品质状态 - 影响材料实际效能的修饰词
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum QualityModifier {
    /// 残缺 - 效能降低
    Damaged(u8), // 损坏程度 1-10
    /// 完整 - 标准状态
    Intact,
    /// 精品 - 效能略微提升
    Fine,
    /// 极品 - 效能显著提升
    Excellent,
    /// 绝品 - 效能大幅提升
    Perfect,
    /// 无暇 - 完美无缺的状态
    Flawless,
    /// 变异 - 具有特殊变化的状态
    Mutated(String), // 变异类型描述
}

impl QualityModifier {
    /// 获取品质修饰的效能倍数
    pub fn efficiency_modifier(&self) -> f64 {
        match self {
            QualityModifier::Damaged(level) => 1.0 - (*level as f64 * 0.1),
            QualityModifier::Intact => 1.0,
            QualityModifier::Fine => 1.1,
            QualityModifier::Excellent => 1.25,
            QualityModifier::Perfect => 1.5,
            QualityModifier::Flawless => 2.0,
            QualityModifier::Mutated(_) => 1.3, // 变异通常带来增益，但也可能有特殊效果
        }
    }
    
    /// 获取品质修饰的中文描述
    pub fn chinese_description(&self) -> String {
        match self {
            QualityModifier::Damaged(level) => {
                match level {
                    1..=3 => "轻微受损".to_string(),
                    4..=6 => "明显受损".to_string(),
                    7..=9 => "严重受损".to_string(),
                    10 => "几近损毁".to_string(),
                    _ => "未知损坏程度".to_string(),
                }
            },
            QualityModifier::Intact => "完整".to_string(),
            QualityModifier::Fine => "精品".to_string(),
            QualityModifier::Excellent => "极品".to_string(),
            QualityModifier::Perfect => "绝品".to_string(),
            QualityModifier::Flawless => "无暇".to_string(),
            QualityModifier::Mutated(mutation_type) => format!("变异({})", mutation_type),
        }
    }
}