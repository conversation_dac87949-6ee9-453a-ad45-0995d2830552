//! 通用技能系统模块（支持玩家、怪物、召唤物等，全面扩展预留）
use crate::shared::types::*;
use crate::skill::skill_area::*;
use serde::{Deserialize, Serialize};
use crate::skill::skill_state::SkillState;

/// 技能结构体
/// 技能结构体，用于定义游戏中的各种技能
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Skill {
    /// 技能唯一标识符
    pub id: ID,
    /// 技能名称
    pub name: String,
    /// 技能描述文本
    pub description: String,
    /// 技能类型列表，一个技能可以同时具有多个类型
    pub skill_types: Vec<SkillType>,
    /// 冷却时间（秒）
    pub cooldown: CooldownTime,
    /// 法力/能量消耗
    pub mana_cost: Cost,
    /// 作用距离
    pub range: Distance,
    /// 作用范围，可以是单体、圆形、矩形等
    pub area: Option<SkillArea>,
    /// 释放条件，定义技能释放所需满足的条件
    pub cast_condition: Option<CastCondition>,
    /// 技能优先级，用于多个技能同时触发时的排序
    pub priority: u32,
    /// 技能图标路径
    pub icon: Option<String>,
    pub state: SkillState,
}

impl Skill {
    /// 创建一个新的技能实例
    ///
    /// # 参数
    /// * `id` - 技能唯一标识符
    /// * `name` - 技能名称
    /// * `description` - 技能描述
    /// * `skill_types` - 技能类型列表
    /// * `cooldown` - 冷却时间
    /// * `mana_cost` - 法力消耗
    /// * `range` - 作用距离
    /// * `area` - 作用范围
    /// * `cast_condition` - 释放条件
    /// * `priority` - 技能优先级
    /// * `icon` - 技能图标路径
    ///
    /// # 返回值
    /// 返回一个配置好的技能实例
    pub fn new(
        id: ID,
        name: String,
        description: String,
        skill_types: Vec<SkillType>,
        cooldown: CooldownTime,
        mana_cost: Cost,
        range: Distance,
        area: Option<SkillArea>,
        cast_condition: Option<CastCondition>,
        priority: u32,
        icon: Option<String>,
    ) -> Self {
        Self {
            id,
            name,
            description,
            skill_types,
            cooldown,
            mana_cost,
            range,
            area,
            cast_condition,
            priority,
            icon,
        }
    }
}

/// 技能类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, strum_macros::Display)]
pub enum SkillType {
    #[serde(rename = "主动技能")]
    #[strum(serialize = "主动技能")]
    Active,
    #[serde(rename = "被动技能")]
    #[strum(serialize = "被动技能")]
    Passive,
    #[serde(rename = "触发技能")]
    #[strum(serialize = "触发技能")]
    Triggered,
    #[serde(rename = "光环")]
    #[strum(serialize = "光环")]
    Aura,
    #[serde(rename = "召唤")]
    #[strum(serialize = "召唤")]
    Summon,
    #[serde(rename = "位移")]
    #[strum(serialize = "位移")]
    Move,
    #[serde(rename = "变身")]
    #[strum(serialize = "变身")]
    Transform,
    #[serde(rename = "引导")]
    #[strum(serialize = "引导")]
    Channel,
    #[serde(rename = "开关")]
    #[strum(serialize = "开关")]
    Toggle,
    #[serde(rename = "连击")]
    #[strum(serialize = "连击")]
    Combo,
    #[serde(rename = "反击")]
    #[strum(serialize = "反击")]
    Counter,
    #[serde(rename = "反弹")]
    #[strum(serialize = "反弹")]
    Reflect,
    #[serde(rename = "偷取")]
    #[strum(serialize = "偷取")]
    Steal,
    #[serde(rename = "复制")]
    #[strum(serialize = "复制")]
    Copy,
    #[serde(rename = "交换")]
    #[strum(serialize = "交换")]
    Swap,
    #[serde(rename = "传送")]
    #[strum(serialize = "传送")]
    Teleport,
}

/// 技能释放条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CastCondition {
    pub hp_percent: Option<Percent>,      // 生命值百分比条件
    pub spirit_percent: Option<Percent>,  // 灵气值百分比条件
    pub buff_required: Option<Vec<ID>>,   // 需要特定buff才能释放
    pub buff_forbidden: Option<Vec<ID>>,  // 有特定buff时禁止释放
    pub target_count: Option<u32>,        // 目标数量条件
    pub cooldown_other: Option<Vec<u32>>, // 其他技能冷却条件
}
