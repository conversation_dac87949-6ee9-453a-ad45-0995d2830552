use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::shared::types::Duration;

/// 战斗系统配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BattleConfig {
    /// 伤害系统配置
    pub damage: DamageConfig,
    /// AI系统配置
    pub ai: AIConfig,
    /// 技能系统配置
    pub skills: SkillConfig,
    /// 状态效果配置
    pub status_effects: StatusEffectConfig,
    /// 经验值配置
    pub experience: ExperienceConfig,
}

/// 伤害计算配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DamageConfig {
    /// 基础暴击率 (0.0 - 1.0)
    pub base_critical_rate: f32,
    /// 暴击伤害倍数
    pub critical_multiplier: f32,
    /// 基础格挡率 (0.0 - 1.0)
    pub base_block_rate: f32,
    /// 格挡减伤比例 (0.0 - 1.0)
    pub block_reduction: f32,
    /// 最大暴击率上限
    pub max_critical_rate: f32,
    /// 最大格挡率上限
    pub max_block_rate: f32,
    /// 等级对暴击率的影响系数
    pub level_critical_bonus: f32,
    /// 防御力对格挡率的影响系数
    pub defense_block_bonus: f32,
    /// 真实伤害是否可以被格挡
    pub true_damage_blockable: bool,
}

/// AI行为配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIConfig {
    /// 低血量治疗阈值 (0.0 - 1.0)
    pub heal_threshold: f32,
    /// 法力值优先使用技能的阈值
    pub skill_mana_threshold: i32,
    /// 技能选择随机性 (0.0 - 1.0)
    pub skill_randomness: f32,
    /// 法力不足时回退到普通攻击的概率
    pub fallback_attack_chance: f32,
    /// 等待行动的概率 (0.0 - 1.0)
    pub wait_chance: f32,
    /// 怪物类型法力值倍数
    pub mana_multipliers: HashMap<String, f32>,
}

/// 技能系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillConfig {
    /// 默认技能冷却时间
    pub default_cooldown: f32,
    /// 法力值消耗基础系数
    pub mana_cost_base: i32,
    /// 技能伤害倍数
    pub skill_damage_multiplier: f32,
    /// 治疗技能效果倍数
    pub heal_skill_multiplier: f32,
    /// 最大技能冷却时间
    pub max_cooldown: f32,
    /// 技能ID分类规则
    pub skill_type_rules: SkillTypeRules,
}

/// 技能类型分类规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillTypeRules {
    /// 偶数ID为治疗技能
    pub even_id_heal: bool,
    /// 奇数ID为攻击技能
    pub odd_id_attack: bool,
}

/// 状态效果配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatusEffectConfig {
    /// 默认Buff持续时间（秒）
    pub default_buff_duration: Duration,
    /// 默认Debuff持续时间（秒）
    pub default_debuff_duration: Duration,
    /// 状态效果叠加上限
    pub max_stack_count: u32,
    /// 状态效果是否可以被驱散
    pub dispellable_by_default: bool,
}

/// 经验值配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExperienceConfig {
    /// 击杀经验值基础倍数
    pub kill_exp_multiplier: f32,
    /// 等级差异经验值调整
    pub level_diff_bonus: f32,
    /// 最大经验值奖励倍数
    pub max_exp_multiplier: f32,
    /// 团队经验值分享比例
    pub team_exp_share: f32,
}

impl Default for BattleConfig {
    fn default() -> Self {
        Self {
            damage: DamageConfig::default(),
            ai: AIConfig::default(),
            skills: SkillConfig::default(),
            status_effects: StatusEffectConfig::default(),
            experience: ExperienceConfig::default(),
        }
    }
}

impl Default for DamageConfig {
    fn default() -> Self {
        Self {
            base_critical_rate: 0.05,   // 5% 基础暴击率
            critical_multiplier: 2.0,   // 2倍暴击伤害
            base_block_rate: 0.1,       // 10% 基础格挡率
            block_reduction: 0.5,       // 格挡减伤50%
            max_critical_rate: 0.8,     // 最大80%暴击率
            max_block_rate: 0.5,        // 最大50%格挡率
            level_critical_bonus: 0.001, // 每级0.1%暴击率加成
            defense_block_bonus: 0.002,  // 每点防御0.2%格挡率加成
            true_damage_blockable: false, // 真实伤害不可格挡
        }
    }
}

impl Default for AIConfig {
    fn default() -> Self {
        let mut mana_multipliers = HashMap::new();
        mana_multipliers.insert("Normal".to_string(), 2.0);
        mana_multipliers.insert("Elite".to_string(), 3.0);
        mana_multipliers.insert("Boss".to_string(), 4.0);
        
        Self {
            heal_threshold: 0.3,        // 30%血量以下治疗
            skill_mana_threshold: 15,   // 15法力值以上优先技能
            skill_randomness: 0.7,      // 70%概率使用技能
            fallback_attack_chance: 1.0, // 100%回退到普通攻击
            wait_chance: 0.1,           // 10%概率选择等待
            mana_multipliers,
        }
    }
}

impl Default for SkillConfig {
    fn default() -> Self {
        Self {
            default_cooldown: 2.0,
            mana_cost_base: 10,
            skill_damage_multiplier: 1.5,
            heal_skill_multiplier: 1.0,
            max_cooldown: 10.0,
            skill_type_rules: SkillTypeRules {
                even_id_heal: true,
                odd_id_attack: true,
            },
        }
    }
}

impl Default for StatusEffectConfig {
    fn default() -> Self {
        Self {
            default_buff_duration: 3.0, // 3秒
            default_debuff_duration: 3.0, // 3秒
            max_stack_count: 5,
            dispellable_by_default: true,
        }
    }
}

impl Default for ExperienceConfig {
    fn default() -> Self {
        Self {
            kill_exp_multiplier: 1.0,
            level_diff_bonus: 0.1,
            max_exp_multiplier: 3.0,
            team_exp_share: 0.8,
        }
    }
}

impl BattleConfig {
    /// 从文件加载配置
    pub fn load_from_file(path: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let content = std::fs::read_to_string(path)?;
        let config: BattleConfig = toml::from_str(&content)?;
        Ok(config)
    }
    
    /// 保存配置到文件
    pub fn save_to_file(&self, path: &str) -> Result<(), Box<dyn std::error::Error>> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }
    
    /// 创建默认配置文件
    pub fn create_default_config_file(path: &str) -> Result<(), Box<dyn std::error::Error>> {
        let default_config = BattleConfig::default();
        default_config.save_to_file(path)?;
        Ok(())
    }
    
    /// 验证配置的有效性
    pub fn validate(&self) -> Result<(), String> {
        // 验证伤害配置
        if self.damage.base_critical_rate < 0.0 || self.damage.base_critical_rate > 1.0 {
            return Err("base_critical_rate must be between 0.0 and 1.0".to_string());
        }
        
        if self.damage.base_block_rate < 0.0 || self.damage.base_block_rate > 1.0 {
            return Err("base_block_rate must be between 0.0 and 1.0".to_string());
        }
        
        if self.damage.block_reduction < 0.0 || self.damage.block_reduction > 1.0 {
            return Err("block_reduction must be between 0.0 and 1.0".to_string());
        }
        
        // 验证AI配置
        if self.ai.heal_threshold < 0.0 || self.ai.heal_threshold > 1.0 {
            return Err("heal_threshold must be between 0.0 and 1.0".to_string());
        }
        
        if self.ai.skill_randomness < 0.0 || self.ai.skill_randomness > 1.0 {
            return Err("skill_randomness must be between 0.0 and 1.0".to_string());
        }
        
        // 验证技能配置
        if self.skills.default_cooldown < 0.0 {
            return Err("default_cooldown must be non-negative".to_string());
        }
        
        if self.skills.skill_damage_multiplier < 0.0 {
            return Err("skill_damage_multiplier must be non-negative".to_string());
        }
        
        Ok(())
    }
    
    /// 获取怪物类型的法力值倍数
    pub fn get_mana_multiplier(&self, monster_type: &str) -> f32 {
        self.ai.mana_multipliers
            .get(monster_type)
            .copied()
            .unwrap_or(1.0)
    }
    
    /// 根据技能ID获取技能名称
    pub fn get_skill_name(&self, skill_id: u32) -> String {
        // 根据规则生成名称
        if skill_id % 2 == 0 && self.skills.skill_type_rules.even_id_heal {
            "治疗术".to_string()
        } else if skill_id % 2 == 1 && self.skills.skill_type_rules.odd_id_attack {
            match skill_id % 6 {
                1 => "火球术".to_string(),
                3 => "闪电击".to_string(),
                5 => "冰锥术".to_string(),
                _ => "魔法飞弹".to_string(),
            }
        } else {
            format!("技能{}", skill_id)
        }
    }
    
    /// 判断技能是否为治疗技能
    pub fn is_heal_skill(&self, skill_id: u32) -> bool {
        skill_id % 2 == 0 && self.skills.skill_type_rules.even_id_heal
    }
    
    /// 计算技能法力消耗
    pub fn calculate_skill_mana_cost(&self, level: u32) -> i32 {
        std::cmp::max(self.skills.mana_cost_base, level as i32 / 2)
    }
} 