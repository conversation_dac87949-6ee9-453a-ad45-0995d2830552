# 🗺️ 世界地图模块总体设计方案

**设计版本**: v1.0  
**创建时间**: 2024年12月  
**基于文档**: 初始想法.md  
**架构原则**: 领域驱动设计(DDD) + 修仙风RPG  

---

## 🎯 设计目标

### 核心目标
1. **🌍 完整世界模拟**：支持修仙世界的多层次空间结构
2. **🔗 深度系统集成**：与战斗系统、材料系统无缝集成
3. **🏗️ DDD架构合规**：遵循项目现有的领域驱动设计原则
4. **⚡ 高性能设计**：支持大规模世界地图的高效查询和更新
5. **🔧 配置驱动**：所有参数可通过TOML配置文件调整

### 功能范围
- ✅ 多层次世界空间管理
- ✅ 动态资源点系统  
- ✅ 功能建筑系统
- ✅ 世界事件系统
- ✅ 灵气流动模拟
- ✅ 探索与发现机制

---

## 🏗️ 架构设计

### 总体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    World Map Module                         │
├─────────────────────────────────────────────────────────────┤
│  Core Domain Models                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ WorldMap    │ │ WorldRegion │ │ Position    │            │
│  │ (聚合根)     │ │             │ │ (值对象)     │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  Domain Services                                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │Exploration  │ │ResourceHarv │ │Building     │            │
│  │Service      │ │estService   │ │Management   │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  Infrastructure                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │World Config │ │Event System │ │Spatial      │            │
│  │             │ │Integration  │ │Index        │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
    ┌─────────────────────────────────────────────────────────┐
    │              Integration Layer                          │
    │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
    │  │Battle System│ │Material     │ │Skill System │        │
    │  │Integration  │ │System       │ │Integration  │        │
    │  │             │ │Integration  │ │             │        │
    │  └─────────────┘ └─────────────┘ └─────────────┘        │
    └─────────────────────────────────────────────────────────┘
```

---

## 📊 核心领域模型

### 1. 聚合根：WorldMap

```rust
/// 世界地图聚合根
/// 负责管理整个世界的状态和业务逻辑
pub struct WorldMap {
    // ID和基本信息
    world_id: WorldId,
    world_name: String,
    created_at: DateTime<Utc>,
    
    // 空间结构
    layers: HashMap<LayerId, WorldLayer>,
    regions: HashMap<RegionId, WorldRegion>,
    
    // 动态内容
    resource_nodes: HashMap<NodeId, ResourceNode>,
    buildings: HashMap<BuildingId, FunctionalBuilding>,
    active_events: Vec<WorldEvent>,
    
    // 系统状态
    world_time: WorldTime,
    spiritual_flow: SpiritualFlowMap,
    
    // 配置
    config: Arc<WorldMapConfig>,
}
```

### 2. 值对象：Position (扩展)

```rust
/// 增强的位置值对象
/// 支持多层次世界的完整定位
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct Position {
    // 基础坐标
    pub x: i32,
    pub y: i32,
    pub z: Option<i32>,
    
    // 世界层次
    pub layer: WorldLayer,
    
    // 区域归属
    pub region_id: Option<RegionId>,
}

impl Position {
    pub fn new_2d(x: i32, y: i32, layer: WorldLayer) -> Self { ... }
    pub fn new_3d(x: i32, y: i32, z: i32, layer: WorldLayer) -> Self { ... }
    pub fn distance_to(&self, other: &Position) -> f64 { ... }
    pub fn is_in_region(&self, region: &WorldRegion) -> bool { ... }
    pub fn get_spiritual_density(&self, flow_map: &SpiritualFlowMap) -> f32 { ... }
}
```

### 3. 实体：WorldRegion

```rust
/// 世界区域实体
/// 代表世界中的一个地理区域
pub struct WorldRegion {
    // 标识
    region_id: RegionId,
    region_name: String,
    
    // 地理属性
    boundaries: RegionBoundaries,
    terrain_type: TerrainType,
    climate: ClimateType,
    
    // 游戏属性
    danger_level: DangerLevel,
    spiritual_density: f32,
    resource_richness: ResourceRichness,
    
    // 规则设定
    encounter_rates: EncounterRates,
    resource_spawn_rules: Vec<ResourceSpawnRule>,
    building_restrictions: BuildingRestrictions,
    
    // 状态
    discovered: bool,
    controlled_by: Option<FactionId>,
}
```

### 4. 实体：ResourceNode

```rust
/// 资源节点实体
/// 代表世界中的资源采集点
pub struct ResourceNode {
    // 标识
    node_id: NodeId,
    node_type: ResourceNodeType,
    
    // 位置
    position: Position,
    area_of_effect: f32,
    
    // 资源属性
    resource_type: ResourceType,
    quality_grade: MaterialGrade,
    abundance: ResourceAbundance,
    
    // 生命周期
    spawn_time: DateTime<Utc>,
    last_harvest: Option<DateTime<Utc>>,
    regeneration_cycle: Duration,
    depletion_threshold: f32,
    
    // 采集条件
    access_requirements: Vec<AccessRequirement>,
    harvest_conditions: Vec<HarvestCondition>,
    
    // 状态
    current_yield: f32,
    discovered_by: HashSet<PlayerId>,
    being_harvested: bool,
}
```

### 5. 实体：FunctionalBuilding

```rust
/// 功能建筑实体
/// 代表玩家建造的各种功能性建筑
pub struct FunctionalBuilding {
    // 标识
    building_id: BuildingId,
    building_type: BuildingType,
    building_name: String,
    
    // 位置
    position: Position,
    footprint: BuildingFootprint,
    
    // 建筑属性
    upgrade_level: u8,
    max_level: u8,
    
    // 所有权
    owner_id: PlayerId,
    access_control: AccessControl,
    
    // 功能效果
    effects: Vec<BuildingEffect>,
    passive_bonuses: Vec<PassiveBonus>,
    
    // 维护
    maintenance_cost: Vec<MaterialRequirement>,
    last_maintenance: DateTime<Utc>,
    durability: f32,
    
    // 状态
    is_active: bool,
    construction_progress: f32,
}
```

---

## 🌊 特色系统设计

### 1. 灵气流动系统

```rust
/// 灵气流动地图
/// 模拟修仙世界中灵气的分布和流动
pub struct SpiritualFlowMap {
    // 网格化的灵气密度
    density_grid: Grid2D<f32>,
    
    // 灵气源点
    sources: Vec<SpiritualSource>,
    
    // 流动模式
    flow_patterns: Vec<FlowPattern>,
    
    // 时间因素
    temporal_modifiers: TemporalModifiers,
}

impl SpiritualFlowMap {
    /// 获取指定位置的灵气密度
    pub fn get_density_at(&self, position: Position) -> f32 { ... }
    
    /// 更新灵气流动（每个游戏周期调用）
    pub fn update_flow(&mut self, delta_time: Duration) { ... }
    
    /// 添加灵气扰动（如战斗、炼丹等活动）
    pub fn add_disturbance(&mut self, position: Position, intensity: f32) { ... }
}
```

### 2. 时空系统

```rust
/// 世界时间系统
/// 管理昼夜循环、季节变化等时间因素
pub struct WorldTime {
    // 当前时间
    current_time: GameTime,
    
    // 时间流速
    time_scale: f32,
    
    // 周期性事件
    celestial_events: Vec<CelestialEvent>,
    seasonal_effects: SeasonalEffects,
}

#[derive(Debug, Clone)]
pub struct GameTime {
    pub year: u32,
    pub season: Season,
    pub day: u32,
    pub hour: u8,
    pub minute: u8,
}
```

---

## 🛠️ 领域服务

### 1. 探索服务

```rust
/// 世界探索领域服务
/// 处理玩家的探索行为和发现逻辑
pub struct WorldExplorationService {
    config: Arc<ExplorationConfig>,
}

impl WorldExplorationService {
    /// 探索新区域
    pub fn explore_region(
        &self,
        player: &Player,
        target_position: Position,
        world_map: &mut WorldMap,
    ) -> ExplorationResult { ... }
    
    /// 发现隐藏内容
    pub fn attempt_discovery(
        &self,
        player: &Player,
        search_area: SearchArea,
        world_map: &WorldMap,
    ) -> DiscoveryResult { ... }
    
    /// 计算遭遇概率
    pub fn calculate_encounter_chance(
        &self,
        region: &WorldRegion,
        player_attributes: &PlayerAttributes,
    ) -> f32 { ... }
}
```

### 2. 资源采集服务

```rust
/// 资源采集领域服务
/// 处理资源节点的交互和采集逻辑
pub struct ResourceHarvestingService {
    config: Arc<HarvestingConfig>,
}

impl ResourceHarvestingService {
    /// 尝试采集资源
    pub fn attempt_harvest(
        &self,
        player: &Player,
        node: &mut ResourceNode,
        world_map: &WorldMap,
    ) -> HarvestResult { ... }
    
    /// 检查采集条件
    pub fn check_harvest_conditions(
        &self,
        player: &Player,
        node: &ResourceNode,
        world_time: &WorldTime,
    ) -> Vec<ConditionCheck> { ... }
    
    /// 计算采集收益
    pub fn calculate_yield(
        &self,
        node: &ResourceNode,
        player_skills: &PlayerSkills,
        tools: &HarvestingTools,
    ) -> HarvestYield { ... }
}
```

### 3. 建筑管理服务

```rust
/// 建筑管理领域服务
/// 处理建筑的建造、升级和维护
pub struct BuildingManagementService {
    config: Arc<BuildingConfig>,
}

impl BuildingManagementService {
    /// 建造新建筑
    pub fn construct_building(
        &self,
        player: &Player,
        building_plan: BuildingPlan,
        world_map: &mut WorldMap,
    ) -> ConstructionResult { ... }
    
    /// 升级建筑
    pub fn upgrade_building(
        &self,
        building: &mut FunctionalBuilding,
        upgrade_materials: Vec<Material>,
    ) -> UpgradeResult { ... }
    
    /// 检查风水效果
    pub fn calculate_feng_shui_bonus(
        &self,
        position: Position,
        building_type: BuildingType,
        world_map: &WorldMap,
    ) -> FengShuiBonus { ... }
}
```

---

## ⚙️ 配置系统设计

### TOML配置文件结构

```toml
# world_map_config.toml

[world_settings]
# 基础世界设置
default_spiritual_density = 1.0
time_scale = 1.0
auto_save_interval = 300  # 秒

[exploration]
# 探索相关配置
base_discovery_chance = 0.1
skill_bonus_multiplier = 0.05
fatigue_penalty = 0.02

[resource_nodes]
# 资源节点配置
default_regeneration_hours = 24
quality_upgrade_chance = 0.05
depletion_rate = 0.1

[buildings]
# 建筑配置
max_buildings_per_region = 10
maintenance_interval_hours = 168  # 一周
feng_shui_effect_radius = 50.0

[spiritual_flow]
# 灵气流动配置
flow_update_interval = 60  # 秒
base_flow_speed = 1.0
disturbance_decay_rate = 0.9

[world_events]
# 世界事件配置
event_check_interval = 3600  # 每小时检查一次
rare_event_base_chance = 0.001
```

---

## 🔌 系统集成设计

### 1. 与战斗系统集成

```rust
/// 世界地图战斗集成适配器
pub struct WorldMapBattleAdapter;

impl WorldMapBattleAdapter {
    /// 基于地形修改战斗参数
    pub fn apply_terrain_effects(
        &self,
        battle_config: &mut BattleConfig,
        position: Position,
        world_map: &WorldMap,
    ) { ... }
    
    /// 触发地形相关的战斗事件
    pub fn trigger_terrain_battle_events(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        terrain: TerrainType,
    ) { ... }
}
```

### 2. 与材料系统集成

```rust
/// 世界地图材料系统集成
pub struct WorldMapMaterialAdapter;

impl WorldMapMaterialAdapter {
    /// 基于位置生成材料属性加成
    pub fn apply_location_bonuses(
        &self,
        material: &mut Material,
        position: Position,
        world_map: &WorldMap,
    ) { ... }
    
    /// 根据世界事件生成特殊材料
    pub fn generate_event_materials(
        &self,
        world_event: &WorldEvent,
    ) -> Vec<Material> { ... }
}
```

---

## 📋 分阶段实现计划

### Phase 1: 核心基础设施 (高优先级)
**预计工时**: 2-3周

✅ **任务清单**:
- [ ] 扩展 Position 值对象，支持多层次定位
- [ ] 实现 WorldRegion 实体和基础管理
- [ ] 建立空间索引系统，支持高效查询
- [ ] 创建基础配置系统
- [ ] 实现与现有事件系统的集成

✅ **交付成果**:
- 完整的空间坐标系统
- 区域管理和查询功能
- 基础配置文件支持
- 与项目事件系统的集成

### Phase 2: 资源点系统 (中高优先级)
**预计工时**: 2-3周

✅ **任务清单**:
- [ ] 实现 ResourceNode 实体
- [ ] 开发 ResourceHarvestingService 领域服务
- [ ] 建立资源生成和刷新机制
- [ ] 实现采集条件和收益计算
- [ ] 与材料系统深度集成

✅ **交付成果**:
- 完整的资源点生命周期管理
- 采集系统和条件检查
- 材料系统集成适配器

### Phase 3: 灵气流动系统 (中优先级)
**预计工时**: 1-2周

✅ **任务清单**:
- [ ] 实现 SpiritualFlowMap 核心逻辑
- [ ] 开发灵气密度计算算法
- [ ] 建立时间因素对灵气的影响
- [ ] 实现灵气扰动和恢复机制

✅ **交付成果**:
- 动态灵气流动模拟
- 基于灵气的效果加成系统

### Phase 4: 建筑系统 (中优先级)
**预计工时**: 2-3周

✅ **任务清单**:
- [ ] 实现 FunctionalBuilding 实体
- [ ] 开发 BuildingManagementService 服务
- [ ] 建立风水计算系统
- [ ] 实现建筑效果和维护机制

✅ **交付成果**:
- 完整的建筑生命周期管理
- 风水系统和位置加成
- 建筑效果和维护系统

### Phase 5: 世界事件系统 (低优先级)
**预计工时**: 1-2周

✅ **任务清单**:
- [ ] 实现 WorldEvent 实体
- [ ] 开发事件触发和效果系统
- [ ] 建立时间和条件检查机制
- [ ] 与其他系统的事件集成

✅ **交付成果**:
- 动态世界事件系统
- 条件触发和效果应用
- 跨系统事件集成

### Phase 6: 探索系统 (低优先级)
**预计工时**: 1-2周

✅ **任务清单**:
- [ ] 实现 WorldExplorationService 服务
- [ ] 开发发现和遭遇机制
- [ ] 建立探索技能影响系统
- [ ] 与战斗系统集成

✅ **交付成果**:
- 完整的探索和发现系统
- 技能影响和遭遇机制
- 战斗系统集成

---

## 🎯 成功标准

### 功能性标准
- ✅ 支持多层次世界结构的完整管理
- ✅ 资源点系统的完整生命周期
- ✅ 建筑系统的建造、升级、维护
- ✅ 灵气流动的实时模拟
- ✅ 与现有系统的无缝集成

### 非功能性标准
- ✅ **性能**: 支持 10,000+ 同时存在的世界对象
- ✅ **扩展性**: 模块化设计，易于添加新功能
- ✅ **配置性**: 所有参数可通过配置文件调整
- ✅ **可测试性**: 完整的单元测试和集成测试
- ✅ **文档完整性**: 详细的API文档和使用指南

### 集成标准
- ✅ 与战斗系统的地形效果集成
- ✅ 与材料系统的资源获取集成
- ✅ 与技能系统的探索能力集成
- ✅ 与事件系统的统一日志记录

---

## 📚 技术决策记录

### 坐标系统选择
**决策**: 使用整数坐标系统替代浮点坐标  
**原因**: 整数坐标避免浮点数精度问题，更适合网格化的世界结构  
**权衡**: 牺牲了亚网格精度，但获得了更好的性能和一致性  

### 空间索引选择
**决策**: 使用四叉树（Quadtree）进行空间索引  
**原因**: 适合2D空间查询，支持动态插入和删除  
**权衡**: 比线性搜索复杂，但在大规模数据下性能优势明显  

### 状态持久化策略
**决策**: 使用增量快照和事件溯源混合策略  
**原因**: 既保证了数据一致性，又提供了足够的性能  
**权衡**: 实现复杂度较高，但提供了强大的数据管理能力  

---

## 🔮 未来扩展规划

### 短期扩展 (6个月内)
- 🌐 **多服务器支持**: 支持跨服务器的世界地图同步
- 🎨 **可视化界面**: 开发Web界面进行地图编辑和监控
- 📊 **分析工具**: 建立世界数据分析和平衡调整工具

### 长期扩展 (1年以上)
- 🤖 **AI世界管理**: 使用AI自动生成和管理世界内容
- 🌍 **程序生成**: 实现程序化的世界内容生成
- 🔗 **区块链集成**: 支持NFT形式的土地和建筑所有权

---

**文档状态**: ✅ 设计完成，等待评审  
**下一步**: 开始Phase 1的具体实现工作  
**评审要求**: 需要架构师和产品经理的评审确认  