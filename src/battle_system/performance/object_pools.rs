/// 对象池化系统
/// 
/// 为频繁创建和销毁的战斗对象提供对象池，大幅减少内存分配开销
/// 主要优化目标：
/// - 技能实例
/// - 效果实例  
/// - 战斗事件
/// - 伤害计算结果

use crate::shared::*;
use crate::skill::domain::entities::*;
use crate::battle_system::simplified_battle_traits::*;
use std::collections::{VecDeque, HashMap};
use std::sync::{Arc, Mutex, RwLock};
use std::time::Instant;

// ============================================================================
// 通用对象池trait
// ============================================================================

/// 可池化对象trait
pub trait Poolable {
    /// 重置对象状态，准备复用
    fn reset(&mut self);
    
    /// 检查对象是否可以安全复用
    fn is_reusable(&self) -> bool {
        true
    }
}

/// 对象池trait
pub trait ObjectPool<T: Poolable> {
    /// 从池中获取对象
    fn acquire(&mut self) -> T;
    
    /// 归还对象到池中
    fn release(&mut self, item: T);
    
    /// 获取池中可用对象数量
    fn available_count(&self) -> usize;
    
    /// 获取池的总容量
    fn capacity(&self) -> usize;
    
    /// 清空池
    fn clear(&mut self);
}

// ============================================================================
// 通用对象池实现
// ============================================================================

/// 通用对象池
pub struct GenericObjectPool<T: Poolable> {
    /// 可用对象队列
    available: VecDeque<T>,
    /// 池容量
    capacity: usize,
    /// 创建计数
    created_count: usize,
    /// 复用计数
    reused_count: usize,
    /// 创建对象的函数
    factory: Box<dyn Fn() -> T + Send + Sync>,
}

impl<T: Poolable> GenericObjectPool<T> {
    pub fn with_factory(capacity: usize, factory: Box<dyn Fn() -> T + Send + Sync>) -> Self {
        let mut pool = Self {
            available: VecDeque::with_capacity(capacity),
            capacity,
            created_count: 0,
            reused_count: 0,
            factory,
        };
        
        // 预填充池
        pool.prefill();
        pool
    }
    
    /// 预填充池
    fn prefill(&mut self) {
        for _ in 0..self.capacity {
            let item = (self.factory)();
            self.available.push_back(item);
            self.created_count += 1;
        }
    }
    
    /// 获取统计信息
    pub fn stats(&self) -> PoolStats {
        PoolStats {
            capacity: self.capacity,
            available: self.available.len(),
            created_count: self.created_count,
            reused_count: self.reused_count,
            hit_rate: if self.created_count > 0 {
                self.reused_count as f64 / (self.created_count + self.reused_count) as f64
            } else {
                0.0
            },
        }
    }
}

impl<T: Poolable> ObjectPool<T> for GenericObjectPool<T> {
    fn acquire(&mut self) -> T {
        if let Some(mut item) = self.available.pop_front() {
            item.reset();
            self.reused_count += 1;
            item
        } else {
            // 池已空，创建新对象
            self.created_count += 1;
            (self.factory)()
        }
    }
    
    fn release(&mut self, item: T) {
        if item.is_reusable() && self.available.len() < self.capacity {
            self.available.push_back(item);
        }
        // 如果池已满或对象不可复用，则丢弃对象
    }
    
    fn available_count(&self) -> usize {
        self.available.len()
    }
    
    fn capacity(&self) -> usize {
        self.capacity
    }
    
    fn clear(&mut self) {
        self.available.clear();
    }
}

// ============================================================================
// 池化的技能实例
// ============================================================================

/// 池化的技能实例
/// 
/// 这是一个包装器，它持有一个从池中获取的技能实例。
/// 当这个包装器被销毁（drop）时，它会自动将技能实例返回到池中。
pub struct PooledSkillInstance {
    /// 技能实例
    instance: Option<SkillInstance>,
    /// 池引用
    pool_ref: Option<Arc<Mutex<SkillInstancePool>>>,
    /// 是否已归还
    returned: bool,
}

impl PooledSkillInstance {
    fn new(instance: SkillInstance, pool_ref: Arc<Mutex<SkillInstancePool>>) -> Self {
        Self {
            instance: Some(instance),
            pool_ref: Some(pool_ref),
            returned: false,
        }
    }
    
    /// 获取内部实例的引用
    pub fn as_ref(&self) -> Option<&SkillInstance> {
        self.instance.as_ref()
    }
    
    /// 获取内部实例的可变引用
    pub fn as_mut(&mut self) -> Option<&mut SkillInstance> {
        self.instance.as_mut()
    }
    
    /// 手动归还到池中
    pub fn return_to_pool(mut self) {
        if !self.returned {
            if let (Some(instance), Some(pool_ref)) = (self.instance.take(), &self.pool_ref) {
                if let Ok(mut pool) = pool_ref.lock() {
                    pool.release(instance);
                }
            }
            self.returned = true;
        }
    }
}

impl Drop for PooledSkillInstance {
    fn drop(&mut self) {
        if !self.returned {
            if let (Some(instance), Some(pool_ref)) = (self.instance.take(), &self.pool_ref) {
                if let Ok(mut pool) = pool_ref.lock() {
                    pool.release(instance);
                }
            }
        }
    }
}

impl Poolable for SkillInstance {
    fn reset(&mut self) {
        self.reset_for_pooling();
    }
}

// ============================================================================
// 技能实例池
// ============================================================================

/// 技能实例池
pub struct SkillInstancePool {
    /// 内部池
    pool: GenericObjectPool<SkillInstance>,
    /// 自引用（用于创建PooledSkillInstance）
    self_ref: Option<Arc<Mutex<Self>>>,
}

impl SkillInstancePool {
    pub fn new(capacity: usize) -> Arc<Mutex<Self>> {
        let pool = Arc::new(Mutex::new(Self {
            pool: GenericObjectPool::with_factory(capacity, Box::new(|| {
                SkillInstance::new(
                    SkillId(0), 
                    BattleUnitId::Character(CharacterId(0)),
                    None,
                    1,
                    0.0,
                    crate::skill::domain::value_objects::ResourceCost::new(),
                    crate::skill::domain::value_objects::SkillEffectComposition::new(),
                )
            })),
            self_ref: None,
        }));
        
        // 设置自引用
        {
            let mut pool_guard = pool.lock().unwrap();
            pool_guard.self_ref = Some(Arc::clone(&pool));
        }
        
        pool
    }
    
    /// 获取池化的技能实例
    pub fn acquire_pooled(&mut self) -> PooledSkillInstance {
        let instance = self.pool.acquire();
        let pool_ref = self.self_ref.as_ref().unwrap().clone();
        PooledSkillInstance::new(instance, pool_ref)
    }
    
    /// 创建具体的技能实例
    pub fn create_skill_instance(
        &mut self,
        skill_id: SkillId,
        caster_id: BattleUnitId,
        target_position: Option<Position>,
        level: Level,
        cooldown: f64,
        resource_cost: crate::skill::domain::value_objects::ResourceCost,
        effects: crate::skill::domain::value_objects::SkillEffectComposition,
    ) -> PooledSkillInstance {
        let mut instance = self.acquire_pooled();
        
        // 设置实际参数
        if let Some(ref mut skill_instance) = instance.instance {
            *skill_instance = SkillInstance::new(
                skill_id,
                caster_id,
                target_position,
                level,
                cooldown,
                resource_cost,
                effects,
            );
        }
        
        instance
    }
    
    pub fn stats(&self) -> PoolStats {
        self.pool.stats()
    }
}

impl ObjectPool<SkillInstance> for SkillInstancePool {
    fn acquire(&mut self) -> SkillInstance {
        self.pool.acquire()
    }
    
    fn release(&mut self, item: SkillInstance) {
        self.pool.release(item)
    }
    
    fn available_count(&self) -> usize {
        self.pool.available_count()
    }
    
    fn capacity(&self) -> usize {
        self.pool.capacity()
    }
    
    fn clear(&mut self) {
        self.pool.clear()
    }
}

// ============================================================================
// 效果实例池
// ============================================================================

/// 池化的效果实例
pub struct PooledEffectInstance {
    instance: Option<EffectInstance>,
    pool_ref: Option<Arc<Mutex<EffectInstancePool>>>,
}

impl PooledEffectInstance {
    fn new(instance: EffectInstance, pool_ref: Arc<Mutex<EffectInstancePool>>) -> Self {
        Self {
            instance: Some(instance),
            pool_ref: Some(pool_ref),
        }
    }
    
    pub fn as_ref(&self) -> Option<&EffectInstance> {
        self.instance.as_ref()
    }
    
    pub fn as_mut(&mut self) -> Option<&mut EffectInstance> {
        self.instance.as_mut()
    }
}

impl Drop for PooledEffectInstance {
    fn drop(&mut self) {
        if let (Some(instance), Some(pool_ref)) = (self.instance.take(), &self.pool_ref) {
            if let Ok(mut pool) = pool_ref.lock() {
                pool.release(instance);
            }
        }
    }
}

impl Poolable for EffectInstance {
    fn reset(&mut self) {
        self.reset_for_pooling();
    }
}

/// 效果实例池
pub struct EffectInstancePool {
    pool: GenericObjectPool<EffectInstance>,
    self_ref: Option<Arc<Mutex<Self>>>,
}

impl EffectInstancePool {
    pub fn new(capacity: usize) -> Arc<Mutex<Self>> {
        let pool = Arc::new(Mutex::new(Self {
            pool: GenericObjectPool::with_factory(capacity, Box::new(|| {
                // 为效果实例创建一个有意义的、空的占位符效果
                let placeholder_effect = crate::skill::domain::value_objects::SkillEffect::new(
                    String::from("placeholder"),
                    String::from("占位符"),
                    crate::skill::domain::value_objects::EffectType::Heal, // 选择一个无害的类型
                    0.0,
                );
                EffectInstance::new(
                    String::from("placeholder"),
                    SkillInstanceId(0),
                    BattleUnitId::Character(CharacterId(0)),
                    None,
                    placeholder_effect, 
                    1,
                )
            })),
            self_ref: None,
        }));
        
        // 设置自引用
        {
            let mut pool_guard = pool.lock().unwrap();
            pool_guard.self_ref = Some(Arc::clone(&pool));
        }
        
        pool
    }
    
    pub fn acquire_pooled(&mut self) -> PooledEffectInstance {
        let instance = self.pool.acquire();
        let pool_ref = self.self_ref.as_ref().unwrap().clone();
        PooledEffectInstance::new(instance, pool_ref)
    }
    
    pub fn stats(&self) -> PoolStats {
        self.pool.stats()
    }
}

impl ObjectPool<EffectInstance> for EffectInstancePool {
    fn acquire(&mut self) -> EffectInstance {
        self.pool.acquire()
    }
    
    fn release(&mut self, item: EffectInstance) {
        self.pool.release(item)
    }
    
    fn available_count(&self) -> usize {
        self.pool.available_count()
    }
    
    fn capacity(&self) -> usize {
        self.pool.capacity()
    }
    
    fn clear(&mut self) {
        self.pool.clear()
    }
}

// ============================================================================
// 战斗事件池
// ============================================================================

/// 战斗事件
pub struct BattleEvent {
    pub event_type: BattleEventType,
    pub timestamp: Instant,
    pub source_id: BattleUnitId,
    pub target_id: Option<BattleUnitId>,
    pub data: BattleEventData,
}

impl BattleEvent {
    /// 创建一个用于对象池的有意义的空事件
    pub fn new_empty() -> Self {
        Self {
            event_type: BattleEventType::SkillCast, // 选择一个逻辑上的空或起始状态
            timestamp: Instant::now(),
            source_id: BattleUnitId::Character(CharacterId(0)), // 使用无效ID作为占位符
            target_id: None,
            data: BattleEventData::Empty,
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BattleEventType {
    SkillCast,
    Damage,
    Heal,
    StatusApplied,
    StatusRemoved,
    Death,
    Revival,
}

#[derive(Debug, Clone, PartialEq)]
pub enum BattleEventData {
    SkillCast { skill_id: SkillId },
    Damage { amount: Health, damage_type: String },
    Heal { amount: Health },
    Status { status_name: String },
    Empty,
}

impl Poolable for BattleEvent {
    fn reset(&mut self) {
        self.event_type = BattleEventType::SkillCast;
        self.timestamp = Instant::now();
        self.source_id = BattleUnitId::Character(CharacterId(0));
        self.target_id = None;
        self.data = BattleEventData::Empty;
    }
}

/// 战斗事件池
pub type BattleEventPool = GenericObjectPool<BattleEvent>;

// ============================================================================
// 池统计信息
// ============================================================================

/// 对象池统计信息
#[derive(Debug, Clone)]
pub struct PoolStats {
    /// 池容量
    pub capacity: usize,
    /// 可用对象数
    pub available: usize,
    /// 已创建对象总数
    pub created_count: usize,
    /// 复用次数
    pub reused_count: usize,
    /// 命中率
    pub hit_rate: f64,
}

impl PoolStats {
    /// 池使用率
    pub fn utilization(&self) -> f64 {
        (self.capacity - self.available) as f64 / self.capacity as f64
    }
    
    /// 内存节省估算（假设每个对象1KB）
    pub fn estimated_memory_saved_kb(&self) -> f64 {
        self.reused_count as f64 // 简化估算
    }
}

// ============================================================================
// 池管理器
// ============================================================================

/// 全局池管理器
pub struct PoolManager {
    skill_instance_pool: Arc<Mutex<SkillInstancePool>>,
    effect_instance_pool: Arc<Mutex<EffectInstancePool>>,
    battle_event_pool: Arc<Mutex<BattleEventPool>>,
    enabled: bool,
}

impl PoolManager {
    pub fn new(pool_size: usize) -> Self {
        Self {
            skill_instance_pool: SkillInstancePool::new(pool_size),
            effect_instance_pool: EffectInstancePool::new(pool_size),
            battle_event_pool: Arc::new(Mutex::new(GenericObjectPool::<BattleEvent>::with_factory(pool_size, Box::new(BattleEvent::new_empty)))),
            enabled: true,
        }
    }
    
    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }
    
    /// 获取技能实例池
    pub fn skill_instance_pool(&self) -> Arc<Mutex<SkillInstancePool>> {
        Arc::clone(&self.skill_instance_pool)
    }
    
    /// 获取效果实例池
    pub fn effect_instance_pool(&self) -> Arc<Mutex<EffectInstancePool>> {
        Arc::clone(&self.effect_instance_pool)
    }
    
    /// 获取战斗事件池
    pub fn battle_event_pool(&self) -> Arc<Mutex<BattleEventPool>> {
        Arc::clone(&self.battle_event_pool)
    }
    
    /// 获取所有池的统计信息
    pub fn get_all_stats(&self) -> HashMap<String, PoolStats> {
        let mut stats = HashMap::new();
        
        if let Ok(pool) = self.skill_instance_pool.lock() {
            stats.insert("skill_instances".to_string(), pool.stats());
        }
        
        if let Ok(pool) = self.effect_instance_pool.lock() {
            stats.insert("effect_instances".to_string(), pool.stats());
        }
        
        if let Ok(pool) = self.battle_event_pool.lock() {
            stats.insert("battle_events".to_string(), (*pool).stats());
        }
        
        stats
    }
    
    /// 清空所有池
    pub fn clear_all(&self) {
        if let Ok(mut pool) = self.skill_instance_pool.lock() {
            pool.clear();
        }
        
        if let Ok(mut pool) = self.effect_instance_pool.lock() {
            pool.clear();
        }
        
        if let Ok(mut pool) = self.battle_event_pool.lock() {
            (*pool).clear();
        }
    }
}

// ============================================================================
// 使用示例和测试
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_skill_instance_pool() {
        let pool = SkillInstancePool::new(10);
        
        // 测试获取和归还
        {
            let mut pool_guard = pool.lock().unwrap();
            let instance1 = pool_guard.acquire_pooled();
            let instance2 = pool_guard.acquire_pooled();
            
            assert_eq!(pool_guard.available_count(), 8);
        }
        
        // 实例应该在drop时自动归还
        {
            let pool_guard = pool.lock().unwrap();
            assert_eq!(pool_guard.available_count(), 10);
        }
    }
    
    #[test]
    fn test_pool_stats() {
        let mut pool = GenericObjectPool::<BattleEvent>::with_factory(5, Box::new(BattleEvent::new_empty));
        
        let event1 = pool.acquire();
        let event2 = pool.acquire();
        
        let stats = pool.stats();
        assert_eq!(stats.capacity, 5);
        assert_eq!(stats.available, 3);
        assert_eq!(stats.created_count, 5); // Prefill
        assert_eq!(stats.reused_count, 0); // No reuse yet after prefill
        
        pool.release(event1);
        pool.release(event2);
        
        let stats = pool.stats();
        assert_eq!(stats.available, 5);
    }
}

/// 池事件，用于记录池的操作，如创建、销毁、缓存命中/未命中等
pub struct Event {
    pub event_type: EventType,
    pub timestamp: Instant,
}

/// 事件类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum EventType {
    GameStart,
    GameEnd,
    PlayerSpawn,
    ItemAcquired,
    LevelUp,
}