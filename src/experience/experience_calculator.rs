use crate::shared::types::*;
/// 经验值计算器 - 处理战斗胜利后的经验值奖励
/// 设计基于修仙世界观，考虑等级差异、怪物类型、战斗表现等因素
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// ============================================================================
// 经验值计算核心系统
// ============================================================================

/// 经验值计算器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExperienceCalculator {
    /// 基础经验值配置
    pub base_exp_config: BaseExpConfig,
    /// 等级差异修正配置
    pub level_diff_modifiers: HashMap<i32, f32>,
    /// 怪物类型修正
    pub monster_type_modifiers: HashMap<MonsterType, f32>,
    /// 战斗表现修正
    pub performance_modifiers: PerformanceModifiers,
    /// 团队奖励配置
    pub team_config: TeamExpConfig,
}

/// 基础经验值配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BaseExpConfig {
    /// 基础经验值（按怪物等级）
    pub base_exp_per_level: u32,
    /// 精英怪物经验倍数
    pub elite_multiplier: f32,
    /// Boss 经验倍数
    pub boss_multiplier: f32,
    /// 稀有怪物经验倍数
    pub rare_multiplier: f32,
}

/// 怪物类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MonsterType {
    /// 普通野兽
    CommonBeast,
    /// 灵兽
    SpiritBeast,
    /// 妖兽
    DemonBeast,
    /// 邪修
    EvilCultivator,
    /// 鬼魂
    Spirit,
    /// 傀儡
    Puppet,
    /// 上古凶兽
    AncientBeast,
    /// Boss级
    Boss,
    /// 精英
    Elite,
    /// 稀有
    Rare,
}

impl MonsterType {
    /// 获取怪物类型的中文名称
    pub fn chinese_name(&self) -> &'static str {
        match self {
            MonsterType::CommonBeast => "普通野兽",
            MonsterType::SpiritBeast => "灵兽",
            MonsterType::DemonBeast => "妖兽",
            MonsterType::EvilCultivator => "邪修",
            MonsterType::Spirit => "鬼魂",
            MonsterType::Puppet => "傀儡",
            MonsterType::AncientBeast => "上古凶兽",
            MonsterType::Boss => "Boss",
            MonsterType::Elite => "精英",
            MonsterType::Rare => "稀有",
        }
    }

    /// 获取怪物类型的基础经验倍数
    pub fn base_exp_multiplier(&self) -> f32 {
        match self {
            MonsterType::CommonBeast => 1.0,
            MonsterType::SpiritBeast => 1.2,
            MonsterType::DemonBeast => 1.5,
            MonsterType::EvilCultivator => 1.3,
            MonsterType::Spirit => 1.1,
            MonsterType::Puppet => 0.8,
            MonsterType::AncientBeast => 2.0,
            MonsterType::Boss => 3.0,
            MonsterType::Elite => 1.8,
            MonsterType::Rare => 2.5,
        }
    }
}

/// 战斗表现修正
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceModifiers {
    /// 完美胜利（无伤或低伤）
    pub perfect_victory: f32,
    /// 快速战斗
    pub quick_battle: f32,
    /// 逆风翻盘（血量低于20%时获胜）
    pub comeback_victory: f32,
    /// 连续战斗
    pub combo_bonus: f32,
    /// 技能多样性奖励
    pub skill_variety_bonus: f32,
}

/// 团队经验配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamExpConfig {
    /// 团队基础奖励
    pub team_base_bonus: f32,
    /// 等级均衡奖励
    pub level_balance_bonus: f32,
    /// 最大团队成员数
    pub max_team_size: u32,
    /// 经验分配模式
    pub distribution_mode: ExpDistributionMode,
}

/// 经验分配模式
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExpDistributionMode {
    /// 平均分配
    Equal,
    /// 按贡献分配
    Contribution,
    /// 按等级需求分配（低等级获得更多）
    LevelWeighted,
}

/// 战斗结果数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BattleResult {
    /// 获胜的角色ID
    pub winner_id: ID,
    /// 参与战斗的角色ID列表
    pub participants: Vec<ID>,
    /// 击败的怪物信息
    pub defeated_monsters: Vec<MonsterInfo>,
    /// 战斗持续时间（秒）
    pub battle_duration: f32,
    /// 战斗表现
    pub performance: BattlePerformance,
}

/// 怪物信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonsterInfo {
    /// 怪物ID
    pub id: ID,
    /// 怪物等级
    pub level: Level,
    /// 怪物类型
    pub monster_type: MonsterType,
    /// 击杀者ID
    pub killer_id: ID,
    /// 最后一击伤害
    pub final_damage: i32,
}

/// 战斗表现数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BattlePerformance {
    /// 总伤害输出
    pub total_damage_dealt: i32,
    /// 承受伤害
    pub damage_taken: i32,
    /// 使用的技能数量
    pub skills_used: u32,
    /// 不同技能种类数
    pub skill_variety: u32,
    /// 治疗量
    pub healing_done: i32,
    /// 连胜次数
    pub win_streak: u32,
    /// 是否完美胜利
    pub is_perfect: bool,
    /// 是否快速战斗（<30秒）
    pub is_quick: bool,
    /// 是否逆风翻盘
    pub is_comeback: bool,
}

/// 经验值奖励结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExpReward {
    /// 角色ID
    pub character_id: ID,
    /// 基础经验值
    pub base_exp: Exp,
    /// 等级差异修正
    pub level_diff_bonus: Exp,
    /// 怪物类型奖励
    pub monster_type_bonus: Exp,
    /// 表现奖励
    pub performance_bonus: Exp,
    /// 团队奖励
    pub team_bonus: Exp,
    /// 最终经验值
    pub total_exp: Exp,
    /// 奖励说明
    pub bonus_descriptions: Vec<String>,
}

impl ExperienceCalculator {
    /// 创建默认的经验值计算器
    pub fn new() -> Self {
        Self {
            base_exp_config: BaseExpConfig {
                base_exp_per_level: 100,
                elite_multiplier: 1.8,
                boss_multiplier: 3.0,
                rare_multiplier: 2.5,
            },
            level_diff_modifiers: Self::create_level_diff_modifiers(),
            monster_type_modifiers: Self::create_monster_type_modifiers(),
            performance_modifiers: PerformanceModifiers {
                perfect_victory: 1.5,
                quick_battle: 1.2,
                comeback_victory: 2.0,
                combo_bonus: 1.1,
                skill_variety_bonus: 1.3,
            },
            team_config: TeamExpConfig {
                team_base_bonus: 1.2,
                level_balance_bonus: 1.15,
                max_team_size: 6,
                distribution_mode: ExpDistributionMode::LevelWeighted,
            },
        }
    }

    /// 创建等级差异修正表
    fn create_level_diff_modifiers() -> HashMap<i32, f32> {
        let mut modifiers = HashMap::new();

        // 正数表示怪物等级高于角色等级
        modifiers.insert(10, 3.0); // 高10级: 300%经验
        modifiers.insert(9, 2.8);
        modifiers.insert(8, 2.6);
        modifiers.insert(7, 2.4);
        modifiers.insert(6, 2.2);
        modifiers.insert(5, 2.0); // 高5级: 200%经验
        modifiers.insert(4, 1.8);
        modifiers.insert(3, 1.6);
        modifiers.insert(2, 1.4);
        modifiers.insert(1, 1.2); // 高1级: 120%经验
        modifiers.insert(0, 1.0); // 同级: 100%经验
        modifiers.insert(-1, 0.9); // 低1级: 90%经验
        modifiers.insert(-2, 0.8);
        modifiers.insert(-3, 0.7);
        modifiers.insert(-4, 0.6);
        modifiers.insert(-5, 0.5); // 低5级: 50%经验
        modifiers.insert(-6, 0.4);
        modifiers.insert(-7, 0.3);
        modifiers.insert(-8, 0.2);
        modifiers.insert(-9, 0.1);
        modifiers.insert(-10, 0.05); // 低10级: 5%经验

        modifiers
    }

    /// 创建怪物类型修正表
    fn create_monster_type_modifiers() -> HashMap<MonsterType, f32> {
        let mut modifiers = HashMap::new();

        modifiers.insert(MonsterType::CommonBeast, 1.0);
        modifiers.insert(MonsterType::SpiritBeast, 1.2);
        modifiers.insert(MonsterType::DemonBeast, 1.5);
        modifiers.insert(MonsterType::EvilCultivator, 1.3);
        modifiers.insert(MonsterType::Spirit, 1.1);
        modifiers.insert(MonsterType::Puppet, 0.8);
        modifiers.insert(MonsterType::AncientBeast, 2.0);
        modifiers.insert(MonsterType::Boss, 3.0);
        modifiers.insert(MonsterType::Elite, 1.8);
        modifiers.insert(MonsterType::Rare, 2.5);

        modifiers
    }

    /// 计算战斗经验值奖励
    pub fn calculate_battle_exp(
        &self,
        battle_result: &BattleResult,
        character_levels: &HashMap<ID, Level>,
    ) -> Vec<ExpReward> {
        let mut rewards = Vec::new();

        for &character_id in &battle_result.participants {
            let character_level = character_levels.get(&character_id).copied().unwrap_or(1);
            let reward = self.calculate_character_exp(character_id, character_level, battle_result);
            rewards.push(reward);
        }

        rewards
    }

    /// 计算单个角色的经验值奖励
    fn calculate_character_exp(
        &self,
        character_id: ID,
        character_level: Level,
        battle_result: &BattleResult,
    ) -> ExpReward {
        let mut total_base_exp = 0;
        let mut level_diff_bonus = 0;
        let mut monster_type_bonus = 0;
        let mut bonus_descriptions = Vec::new();

        // 计算击败怪物的基础经验
        for monster in &battle_result.defeated_monsters {
            let base_exp = self.calculate_monster_base_exp(monster);
            total_base_exp += base_exp;

            // 等级差异修正
            let level_diff = monster.level as i32 - character_level as i32;
            let level_modifier = self.get_level_diff_modifier(level_diff);
            let level_bonus = (base_exp as f32 * (level_modifier - 1.0)) as Exp;
            level_diff_bonus += level_bonus;

            if level_diff > 0 {
                bonus_descriptions.push(format!(
                    "挑战高等级{}(+{}级): +{}经验",
                    monster.monster_type.chinese_name(),
                    level_diff,
                    level_bonus
                ));
            } else if level_diff < 0 {
                bonus_descriptions.push(format!(
                    "击败低等级{}(-{}级): {}经验",
                    monster.monster_type.chinese_name(),
                    -level_diff,
                    level_bonus
                ));
            }

            // 怪物类型奖励
            let type_modifier = self
                .monster_type_modifiers
                .get(&monster.monster_type)
                .copied()
                .unwrap_or(1.0);
            let type_bonus = (base_exp as f32 * (type_modifier - 1.0)) as Exp;
            monster_type_bonus += type_bonus;

            if type_bonus > 0 {
                bonus_descriptions.push(format!(
                    "{}: +{}经验",
                    monster.monster_type.chinese_name(),
                    type_bonus
                ));
            }
        }

        // 计算表现奖励
        let performance_bonus = self.calculate_performance_bonus(
            total_base_exp,
            &battle_result.performance,
            &mut bonus_descriptions,
        );

        // 计算团队奖励
        let team_bonus = self.calculate_team_bonus(
            total_base_exp,
            &battle_result.participants,
            &mut bonus_descriptions,
        );

        let total_exp =
            total_base_exp + level_diff_bonus + monster_type_bonus + performance_bonus + team_bonus;

        ExpReward {
            character_id,
            base_exp: total_base_exp,
            level_diff_bonus,
            monster_type_bonus,
            performance_bonus,
            team_bonus,
            total_exp,
            bonus_descriptions,
        }
    }

    /// 计算怪物基础经验值
    fn calculate_monster_base_exp(&self, monster: &MonsterInfo) -> Exp {
        let base = self.base_exp_config.base_exp_per_level * monster.level;

        let multiplier = match monster.monster_type {
            MonsterType::Elite => self.base_exp_config.elite_multiplier,
            MonsterType::Boss => self.base_exp_config.boss_multiplier,
            MonsterType::Rare => self.base_exp_config.rare_multiplier,
            _ => 1.0,
        };

        (base as f32 * multiplier) as Exp
    }

    /// 获取等级差异修正
    fn get_level_diff_modifier(&self, level_diff: i32) -> f32 {
        // 限制等级差异范围
        let clamped_diff = level_diff.clamp(-10, 10);
        self.level_diff_modifiers
            .get(&clamped_diff)
            .copied()
            .unwrap_or(if level_diff > 10 { 3.0 } else { 0.05 })
    }

    /// 计算表现奖励
    fn calculate_performance_bonus(
        &self,
        base_exp: Exp,
        performance: &BattlePerformance,
        descriptions: &mut Vec<String>,
    ) -> Exp {
        let mut total_multiplier = 1.0;

        // 完美胜利
        if performance.is_perfect {
            total_multiplier *= self.performance_modifiers.perfect_victory;
            descriptions.push(format!(
                "完美胜利: +{}%经验",
                (self.performance_modifiers.perfect_victory - 1.0) * 100.0
            ));
        }

        // 快速战斗
        if performance.is_quick {
            total_multiplier *= self.performance_modifiers.quick_battle;
            descriptions.push(format!(
                "闪电战: +{}%经验",
                (self.performance_modifiers.quick_battle - 1.0) * 100.0
            ));
        }

        // 逆风翻盘
        if performance.is_comeback {
            total_multiplier *= self.performance_modifiers.comeback_victory;
            descriptions.push(format!(
                "绝地反击: +{}%经验",
                (self.performance_modifiers.comeback_victory - 1.0) * 100.0
            ));
        }

        // 连胜奖励
        if performance.win_streak > 1 {
            let combo_multiplier = 1.0 + (performance.win_streak as f32 - 1.0) * 0.1;
            total_multiplier *= combo_multiplier;
            descriptions.push(format!(
                "连胜{}场: +{}%经验",
                performance.win_streak,
                (combo_multiplier - 1.0) * 100.0
            ));
        }

        // 技能多样性
        if performance.skill_variety >= 3 {
            total_multiplier *= self.performance_modifiers.skill_variety_bonus;
            descriptions.push(format!(
                "技能多样性({}种): +{}%经验",
                performance.skill_variety,
                (self.performance_modifiers.skill_variety_bonus - 1.0) * 100.0
            ));
        }

        (base_exp as f32 * (total_multiplier - 1.0)) as Exp
    }

    /// 计算团队奖励
    fn calculate_team_bonus(
        &self,
        base_exp: Exp,
        participants: &[ID],
        descriptions: &mut Vec<String>,
    ) -> Exp {
        if participants.len() <= 1 {
            return 0;
        }

        let team_size = participants
            .len()
            .min(self.team_config.max_team_size as usize);
        let team_multiplier = 1.0 + (team_size as f32 - 1.0) * 0.1;

        let bonus = (base_exp as f32 * (team_multiplier - 1.0)) as Exp;

        descriptions.push(format!(
            "团队协作({}人): +{}%经验",
            team_size,
            (team_multiplier - 1.0) * 100.0
        ));

        bonus
    }

    /// 计算探索经验值
    pub fn calculate_exploration_exp(
        &self,
        character_level: Level,
        explored_areas: u32,
        discoveries: u32,
        rare_findings: u32,
    ) -> ExpReward {
        let base_exp = character_level * 50; // 探索基础经验值较低
        let mut bonus_descriptions = Vec::new();

        // 探索区域奖励
        let area_bonus = explored_areas * 20;
        if area_bonus > 0 {
            bonus_descriptions.push(format!("探索{}个区域: +{}经验", explored_areas, area_bonus));
        }

        // 发现奖励
        let discovery_bonus = discoveries * 100;
        if discovery_bonus > 0 {
            bonus_descriptions.push(format!(
                "发现{}个秘密: +{}经验",
                discoveries, discovery_bonus
            ));
        }

        // 稀有发现奖励
        let rare_bonus = rare_findings * 500;
        if rare_bonus > 0 {
            bonus_descriptions.push(format!("稀有发现{}个: +{}经验", rare_findings, rare_bonus));
        }

        let total_exp = base_exp + area_bonus + discovery_bonus + rare_bonus;

        ExpReward {
            character_id: 0, // 需要外部设置
            base_exp,
            level_diff_bonus: 0,
            monster_type_bonus: 0,
            performance_bonus: area_bonus + discovery_bonus,
            team_bonus: rare_bonus,
            total_exp,
            bonus_descriptions,
        }
    }
}

impl Default for ExperienceCalculator {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 便利函数
// ============================================================================

/// 创建简单的战斗结果用于测试
pub fn create_simple_battle_result(
    winner_id: ID,
    monster_level: Level,
    monster_type: MonsterType,
) -> BattleResult {
    BattleResult {
        winner_id,
        participants: vec![winner_id],
        defeated_monsters: vec![MonsterInfo {
            id: 1001,
            level: monster_level,
            monster_type,
            killer_id: winner_id,
            final_damage: 100,
        }],
        battle_duration: 45.0,
        performance: BattlePerformance {
            total_damage_dealt: 500,
            damage_taken: 100,
            skills_used: 3,
            skill_variety: 2,
            healing_done: 0,
            win_streak: 1,
            is_perfect: false,
            is_quick: false,
            is_comeback: false,
        },
    }
}

/// 创建团队战斗结果
pub fn create_team_battle_result(participants: Vec<ID>, boss_level: Level) -> BattleResult {
    let winner_id = participants[0];

    BattleResult {
        winner_id,
        participants,
        defeated_monsters: vec![MonsterInfo {
            id: 2001,
            level: boss_level,
            monster_type: MonsterType::Boss,
            killer_id: winner_id,
            final_damage: 500,
        }],
        battle_duration: 180.0, // 3分钟的Boss战
        performance: BattlePerformance {
            total_damage_dealt: 2000,
            damage_taken: 800,
            skills_used: 15,
            skill_variety: 5,
            healing_done: 200,
            win_streak: 1,
            is_perfect: false,
            is_quick: false,
            is_comeback: true, // 绝地反击
        },
    }
}
