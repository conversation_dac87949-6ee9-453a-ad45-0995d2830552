use crate::shared::GameResult;
use crate::{Position, ResourceCost, SkillId, SkillResult};

/// 技能执行trait
///
/// 管理技能的使用、冷却和施法

/// 技能使用与冷却
pub trait SkillExecution {
    /// 获取技能冷却时间
    fn skill_cooldown(&self, skill_id: &SkillId) -> f64;

    /// 设置技能冷却
    fn set_skill_cooldown(&mut self, skill_id: SkillId, cooldown: f64) -> GameResult<()>;

    /// 更新技能冷却
    fn update_skill_cooldowns(&mut self, delta_time: f32);

    /// 检查技能是否可用
    fn can_use_skill(&self, skill_id: &SkillId) -> bool;

    /// 使用技能
    fn use_skill(
        &mut self,
        skill_id: &SkillId,
        target: Option<Position>,
    ) -> GameResult<SkillResult>;

    /// 打断技能施放
    fn interrupt_skill(&mut self, skill_id: &SkillId) -> GameResult<()>;

    /// 获取当前施放的技能
    fn current_casting_skill(&self) -> Option<SkillId>;

    /// 获取施法时间
    fn casting_time(&self, skill_id: &SkillId) -> f32;

    /// 获取技能消耗
    fn skill_cost(&self, skill_id: &SkillId) -> ResourceCost;
}
