use crate::battle_unit::{EntityType, Faction, Rarity};
/// 战斗实体身份trait
/// 
/// 定义战斗单位的基本身份信息和标识符

use crate::shared::*;

/// 战斗实体标识 - 定义战斗单位的身份和基本属性
pub trait BattleEntityIdentity {
    /// 获取唯一标识符
    fn entity_id(&self) -> BattleUnitId;
    
    /// 获取显示名称
    fn display_name(&self) -> &str;
    
    /// 获取实体类型
    fn entity_type(&self) -> EntityType;
    
    /// 获取阵营
    fn faction(&self) -> Faction;
    
    /// 获取等级
    fn level(&self) -> Level;
    
    /// 获取经验值
    fn experience(&self) -> crate::Exp;
    
    /// 获取稀有度
    fn rarity(&self) -> Rarity;
    
    /// 是否为玩家控制
    fn is_player_controlled(&self) -> bool;
    
    /// 是否为AI控制
    fn is_ai_controlled(&self) -> bool;
}