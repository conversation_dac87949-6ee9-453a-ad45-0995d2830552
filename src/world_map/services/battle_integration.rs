use crate::battle_unit::{BattleConfig, BattleEvent};
/// 世界地图与战斗系统集成服务
/// 处理地形对战斗的影响和世界地图相关的战斗事件
use crate::world_map::domain::{DangerLevel, RegionId, TerrainType};
use crate::{ElementType, Position, WorldLayerType};

/// 世界地图战斗集成适配器
pub struct WorldMapBattleAdapter {
    /// 地形效果配置
    terrain_effects: TerrainEffectConfig,
}

/// 地形效果配置
#[derive(Debug, Clone)]
pub struct TerrainEffectConfig {
    /// 地形对攻击力的影响
    pub attack_modifiers: std::collections::HashMap<TerrainType, f32>,
    /// 地形对防御力的影响
    pub defense_modifiers: std::collections::HashMap<TerrainType, f32>,
    /// 地形对速度的影响
    pub speed_modifiers: std::collections::HashMap<TerrainType, f32>,
    /// 地形对命中率的影响
    pub accuracy_modifiers: std::collections::HashMap<TerrainType, f32>,
    /// 地形对闪避率的影响
    pub evasion_modifiers: std::collections::HashMap<TerrainType, f32>,
}

/// 战斗位置信息
#[derive(Debug, Clone)]
pub struct BattleLocationInfo {
    /// 战斗位置
    pub position: Position,
    /// 地形类型
    pub terrain_type: TerrainType,
    /// 危险等级
    pub danger_level: DangerLevel,
    /// 所属区域
    pub region_id: Option<RegionId>,
    /// 灵气密度
    pub spiritual_density: f32,
    /// 特殊环境效果
    pub environmental_effects: Vec<EnvironmentalEffect>,
}

/// 环境效果
#[derive(Debug, Clone)]
pub enum EnvironmentalEffect {
    /// 高灵气浓度 - 增强法术效果
    HighSpiritualDensity { multiplier: f32 },
    /// 灵气稀薄 - 削弱法术效果
    LowSpiritualDensity { penalty: f32 },
    /// 地脉干扰 - 影响传送和位移技能
    LeyLineInterference,
    /// 古战场残留 - 增强战斗经验获得
    AncientBattlefield { exp_bonus: f32 },
    /// 神圣之地 - 增强治疗效果
    SacredGround { healing_bonus: f32 },
    /// 邪恶之地 - 增强诅咒和负面效果
    CorruptedLand { curse_bonus: f32 },
    /// 元素汇聚 - 增强特定元素技能
    ElementalConvergence { element: ElementType, bonus: f32 },
}

/// 地形战斗事件
#[derive(Debug, Clone)]
pub enum TerrainBattleEvent {
    /// 地形优势触发
    TerrainAdvantage {
        terrain_type: TerrainType,
        advantage_type: String,
        effect_description: String,
    },
    /// 环境危险
    EnvironmentalHazard {
        hazard_type: String,
        damage: f32,
        affected_units: Vec<String>,
    },
    /// 地形技能触发
    TerrainSkillActivation {
        skill_name: String,
        terrain_requirement: TerrainType,
        effect: String,
    },
    /// 位置变化
    PositionChange {
        unit_id: String,
        old_position: Position,
        new_position: Position,
        reason: String,
    },
}

impl WorldMapBattleAdapter {
    /// 创建新的战斗集成适配器
    pub fn new() -> Self {
        Self {
            terrain_effects: TerrainEffectConfig::default(),
        }
    }

    /// 基于地形修改战斗参数
    pub fn apply_terrain_effects(
        &self,
        _battle_config: &mut BattleConfig,
        location_info: &BattleLocationInfo,
    ) {
        // 应用地形对基础属性的影响
        if let Some(attack_modifier) = self
            .terrain_effects
            .attack_modifiers
            .get(&location_info.terrain_type)
        {
            // 这里需要根据实际的BattleConfig结构来修改
            // 由于BattleConfig的具体结构不明确，这里提供示例逻辑
            println!("应用地形攻击力修正: {}", attack_modifier);
        }

        if let Some(defense_modifier) = self
            .terrain_effects
            .defense_modifiers
            .get(&location_info.terrain_type)
        {
            println!("应用地形防御力修正: {}", defense_modifier);
        }

        // 应用世界层级的影响
        let layer = location_info
            .position
            .layer
            .unwrap_or(WorldLayerType::Mortal);
        let layer_modifier = self.get_world_layer_modifier(layer);
        println!("应用世界层级修正: {}", layer_modifier);

        // 应用危险等级的影响
        let danger_modifier = self.get_danger_level_modifier(location_info.danger_level);
        println!("应用危险等级修正: {}", danger_modifier);

        // 应用灵气密度的影响
        let spiritual_modifier =
            self.get_spiritual_density_modifier(location_info.spiritual_density);
        println!("应用灵气密度修正: {}", spiritual_modifier);
    }

    /// 触发地形相关的战斗事件
    pub fn trigger_terrain_battle_events(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        location_info: &BattleLocationInfo,
    ) {
        // 根据地形类型触发特殊事件
        match location_info.terrain_type {
            TerrainType::Mountains => {
                self.trigger_mountain_events(battle_events, location_info);
            }
            TerrainType::Forest => {
                self.trigger_forest_events(battle_events, location_info);
            }
            TerrainType::Desert => {
                self.trigger_desert_events(battle_events, location_info);
            }
            TerrainType::Swamp => {
                self.trigger_swamp_events(battle_events, location_info);
            }
            TerrainType::SpiritualVein => {
                self.trigger_spiritual_land_events(battle_events, location_info);
            }
            _ => {
                // 其他地形的默认处理
                self.trigger_default_terrain_events(battle_events, location_info);
            }
        }

        // 检查是否有古遗迹或特殊历史区域
        if self.has_ancient_ruins(location_info) {
            self.trigger_ancient_ruin_events(battle_events, location_info);
        }

        // 应用环境效果
        for effect in &location_info.environmental_effects {
            self.apply_environmental_effect(battle_events, effect);
        }
    }

    /// 计算地形战斗加成
    pub fn calculate_terrain_combat_bonus(
        &self,
        attacker_position: Position,
        defender_position: Position,
        terrain_type: TerrainType,
    ) -> TerrainCombatBonus {
        let mut bonus = TerrainCombatBonus::default();

        // 计算高度优势
        if let (Some(attacker_z), Some(defender_z)) = (attacker_position.z, defender_position.z) {
            if attacker_z > defender_z {
                bonus.height_advantage = true;
                bonus.attack_bonus += 0.1;
                bonus.accuracy_bonus += 0.05;
            }
        }

        // 计算地形专精加成
        bonus.terrain_specialization = self.get_terrain_specialization_bonus(terrain_type);

        // 计算距离影响
        let distance = attacker_position.distance_to(&defender_position);
        bonus.distance_modifier = self.calculate_distance_modifier(distance as f64, terrain_type);

        bonus
    }

    /// 获取推荐的战斗位置
    pub fn get_recommended_battle_positions(
        &self,
        center: Position,
        radius: u32,
        unit_count: usize,
    ) -> Vec<Position> {
        let mut positions = Vec::new();

        // 获取中心位置周围的所有可能位置
        //TODO: center.get_positions_within_radius(radius);
        let candidate_positions = Vec::new();

        // 根据地形和战术考虑筛选最佳位置
        for position in candidate_positions {
            if self.is_good_battle_position(position) {
                positions.push(position);
                if positions.len() >= unit_count {
                    break;
                }
            }
        }

        positions
    }

    /// 检查位置是否适合战斗
    pub fn is_suitable_for_battle(&self, position: Position) -> bool {
        // 检查基本的战斗适宜性
        self.is_good_battle_position(position)
    }

    /// 获取地形描述
    pub fn get_terrain_description(&self, terrain_type: TerrainType) -> String {
        match terrain_type {
            TerrainType::Plains => "平原 - 开阔的战场，适合大规模战斗".to_string(),
            TerrainType::Forest => "森林 - 密林环境，有利于隐蔽和游击战".to_string(),
            TerrainType::Mountains => "山地 - 崎岖地形，高度优势明显".to_string(),
            TerrainType::Desert => "沙漠 - 炎热干燥，消耗体力较快".to_string(),
            TerrainType::Swamp => "沼泽 - 泥泞难行，影响移动速度".to_string(),
            TerrainType::SpiritualVein => "灵地 - 灵气充沛，增强法术效果".to_string(),
            TerrainType::Water => "水域 - 湖泊河流，游泳或船只通行".to_string(),
            TerrainType::Tundra => "冰原 - 寒冷地区，体力消耗大".to_string(),
            TerrainType::Volcanic => "火山 - 炽热环境，火系技能增强".to_string(),
            TerrainType::Void => "虚空 - 混沌空间，极其危险".to_string(),
        }
    }

    // 私有辅助方法

    fn get_world_layer_modifier(&self, layer: WorldLayerType) -> f32 {
        match layer {
            WorldLayerType::Mortal => 1.0,
            WorldLayerType::Spirit => 1.2,
            WorldLayerType::Immortal => 1.5,
            WorldLayerType::Chaos => 2.0,
            WorldLayerType::SecretRealm(_) => 1.3,
        }
    }

    fn get_danger_level_modifier(&self, danger_level: DangerLevel) -> f32 {
        match danger_level {
            DangerLevel::Safe => 0.8,
            DangerLevel::Low => 1.0,
            DangerLevel::Medium => 1.2,
            DangerLevel::High => 1.5,
            DangerLevel::Extreme => 2.0,
            DangerLevel::Lethal => 3.0,
        }
    }

    fn get_spiritual_density_modifier(&self, density: f32) -> f32 {
        1.0 + (density - 1.0) * 0.2
    }

    fn trigger_mountain_events(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        _location_info: &BattleLocationInfo,
    ) {
        // 山地特有事件：落石、高度优势等
        let event = BattleEvent::BuffApplied {
            target: "地形效果".to_string(),
            buff_name: "山地地形效果：获得高度优势，攻击力提升10%".to_string(),
            duration: 999.0, // 地形效果持续整场战斗
            stack_count: 1,
        };
        battle_events.push(event);
    }

    fn trigger_forest_events(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        _location_info: &BattleLocationInfo,
    ) {
        // 森林特有事件：隐蔽、陷阱等
        let event = BattleEvent::BuffApplied {
            target: "地形效果".to_string(),
            buff_name: "森林地形效果：获得隐蔽加成，闪避率提升15%".to_string(),
            duration: 999.0,
            stack_count: 1,
        };
        battle_events.push(event);
    }

    fn trigger_desert_events(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        _location_info: &BattleLocationInfo,
    ) {
        // 沙漠特有事件：沙尘暴、脱水等
        let event = BattleEvent::BuffApplied {
            target: "地形效果".to_string(),
            buff_name: "沙漠地形效果：炎热环境，体力消耗增加20%".to_string(),
            duration: 999.0,
            stack_count: 1,
        };
        battle_events.push(event);
    }

    fn trigger_swamp_events(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        _location_info: &BattleLocationInfo,
    ) {
        // 沼泽特有事件：泥潭、毒气等
        let event = BattleEvent::BuffApplied {
            target: "地形效果".to_string(),
            buff_name: "沼泽地形效果：泥泞地面，移动速度降低30%".to_string(),
            duration: 999.0,
            stack_count: 1,
        };
        battle_events.push(event);
    }

    fn trigger_spiritual_land_events(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        _location_info: &BattleLocationInfo,
    ) {
        // 灵地特有事件：灵气加成、法术增强等
        let event = BattleEvent::BuffApplied {
            target: "地形效果".to_string(),
            buff_name: "灵地地形效果：灵气充沛，法术威力提升25%".to_string(),
            duration: 999.0,
            stack_count: 1,
        };
        battle_events.push(event);
    }

    fn trigger_ancient_ruin_events(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        location_info: &BattleLocationInfo,
    ) {
        // 古遗迹特有事件：古老魔法、神秘力量等
        use rand::Rng;
        let mut rng = rand::thread_rng();

        // 基础地形效果 - 神秘力量影响
        let base_event = BattleEvent::BuffApplied {
            target: "地形效果".to_string(),
            buff_name: "古遗迹地形效果：神秘力量影响，随机触发特殊效果".to_string(),
            duration: 999.0,
            stack_count: 1,
        };
        battle_events.push(base_event);

        // 根据灵气密度和危险等级决定特殊事件发生概率
        let event_probability = match location_info.danger_level {
            crate::world_map::domain::DangerLevel::Safe => 0.1,
            crate::world_map::domain::DangerLevel::Low => 0.2,
            crate::world_map::domain::DangerLevel::Medium => 0.35,
            crate::world_map::domain::DangerLevel::High => 0.5,
            crate::world_map::domain::DangerLevel::Extreme => 0.7,
            crate::world_map::domain::DangerLevel::Lethal => 0.9,
        };

        let spiritual_multiplier = (location_info.spiritual_density * 0.3).min(0.5);
        let final_probability = (event_probability + spiritual_multiplier).min(0.9);

        // 随机触发特殊事件
        if rng.gen::<f32>() < final_probability {
            let event_type = rng.gen_range(0..6);

            match event_type {
                0 => {
                    // 古老魔法阵激活
                    let magic_circle_event = BattleEvent::BuffApplied {
                        target: "所有参战单位".to_string(),
                        buff_name: "古老魔法阵：全体法术威力提升40%，法力恢复速度翻倍".to_string(),
                        duration: rng.gen_range(3..8) as f32,
                        stack_count: 1,
                    };
                    battle_events.push(magic_circle_event);
                }
                1 => {
                    // 时间扭曲现象
                    let time_distortion_event = BattleEvent::BuffApplied {
                        target: "战场环境".to_string(),
                        buff_name: "时间扭曲：所有技能冷却时间减半，回合结束时有25%概率额外行动"
                            .to_string(),
                        duration: rng.gen_range(2..5) as f32,
                        stack_count: 1,
                    };
                    battle_events.push(time_distortion_event);
                }
                2 => {
                    // 远古守护者苏醒
                    let guardian_event = BattleEvent::BuffApplied {
                        target: "防御方".to_string(),
                        buff_name: "远古守护者：防御力提升60%，免疫首次致命伤害".to_string(),
                        duration: rng.gen_range(4..7) as f32,
                        stack_count: 1,
                    };
                    battle_events.push(guardian_event);
                }
                3 => {
                    // 神秘宝库开启
                    let treasure_event = BattleEvent::BuffApplied {
                        target: "战斗奖励".to_string(),
                        buff_name: "神秘宝库：战斗结束后额外获得珍贵材料和修炼资源".to_string(),
                        duration: 1.0,
                        stack_count: 1,
                    };
                    battle_events.push(treasure_event);
                }
                4 => {
                    // 元素风暴
                    let element_storm = match rng.gen_range(0..4) {
                        0 => "雷电风暴：所有攻击附带雷电伤害，命中率提升30%",
                        1 => "烈焰风暴：所有攻击附带火焰伤害，技能威力提升25%",
                        2 => "寒冰风暴：所有攻击附带冰冻效果，敌方移动速度降低50%",
                        _ => "暗影风暴：所有攻击附带暗影伤害，治疗效果降低70%",
                    };
                    let storm_event = BattleEvent::BuffApplied {
                        target: "战场环境".to_string(),
                        buff_name: format!("元素风暴：{}", element_storm),
                        duration: rng.gen_range(3..6) as f32,
                        stack_count: 1,
                    };
                    battle_events.push(storm_event);
                }
                5 => {
                    // 禁制失效
                    let restriction_event = BattleEvent::BuffApplied {
                        target: "所有参战单位".to_string(),
                        buff_name: "禁制失效：可以使用超阶技能，法力消耗降低50%".to_string(),
                        duration: rng.gen_range(2..4) as f32,
                        stack_count: 1,
                    };
                    battle_events.push(restriction_event);
                }
                _ => {} // 其他事件类型
            }
        }

        // 检查是否有特殊环境效果增强古遗迹事件
        for effect in &location_info.environmental_effects {
            match effect {
                crate::world_map::services::battle_integration::EnvironmentalEffect::AncientBattlefield { exp_bonus } => {
                    let ancient_resonance = BattleEvent::BuffApplied {
                        target: "古战场共鸣".to_string(),
                        buff_name: format!("古战场共鸣：战斗经验获得增加{}%，战意高涨攻击力提升20%", exp_bonus * 100.0),
                        duration: 999.0,
                        stack_count: 1,
                    };
                    battle_events.push(ancient_resonance);
                }
                crate::world_map::services::battle_integration::EnvironmentalEffect::HighSpiritualDensity { multiplier } => {
                    if *multiplier > 2.0 {
                        let spiritual_surge = BattleEvent::BuffApplied {
                            target: "灵气涌动".to_string(),
                            buff_name: "古遗迹灵气涌动：在高灵气密度下，古老符文开始发光，全体修炼者获得顿悟状态".to_string(),
                            duration: rng.gen_range(5..10) as f32,
                            stack_count: 1,
                        };
                        battle_events.push(spiritual_surge);
                    }
                }
                _ => {}
            }
        }
    }

    fn trigger_default_terrain_events(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        location_info: &BattleLocationInfo,
    ) {
        // 默认地形事件
        let event = BattleEvent::BuffApplied {
            target: "地形效果".to_string(),
            buff_name: format!(
                "地形效果：{}",
                self.get_terrain_description(location_info.terrain_type)
            ),
            duration: 999.0,
            stack_count: 1,
        };
        battle_events.push(event);
    }

    fn apply_environmental_effect(
        &self,
        battle_events: &mut Vec<BattleEvent>,
        effect: &EnvironmentalEffect,
    ) {
        match effect {
            EnvironmentalEffect::HighSpiritualDensity { multiplier } => {
                let event = BattleEvent::BuffApplied {
                    target: "环境效果".to_string(),
                    buff_name: format!("高灵气密度：法术效果增强{}%", (multiplier - 1.0) * 100.0),
                    duration: 999.0,
                    stack_count: 1,
                };
                battle_events.push(event);
            }
            EnvironmentalEffect::LowSpiritualDensity { penalty } => {
                let event = BattleEvent::BuffApplied {
                    target: "环境效果".to_string(),
                    buff_name: format!("低灵气密度：法术效果减弱{}%", penalty * 100.0),
                    duration: 999.0,
                    stack_count: 1,
                };
                battle_events.push(event);
            }
            EnvironmentalEffect::AncientBattlefield { exp_bonus } => {
                let event = BattleEvent::BuffApplied {
                    target: "环境效果".to_string(),
                    buff_name: format!("古战场：战斗经验获得增加{}%", exp_bonus * 100.0),
                    duration: 999.0,
                    stack_count: 1,
                };
                battle_events.push(event);
            }
            _ => {
                // 其他环境效果的处理
                let event = BattleEvent::BuffApplied {
                    target: "环境效果".to_string(),
                    buff_name: "特殊环境效果生效".to_string(),
                    duration: 999.0,
                    stack_count: 1,
                };
                battle_events.push(event);
            }
        }
    }

    fn get_terrain_specialization_bonus(&self, _terrain_type: TerrainType) -> f32 {
        // 简化实现，实际应根据单位的地形专精技能计算
        0.0
    }

    fn calculate_distance_modifier(&self, distance: f64, terrain_type: TerrainType) -> f32 {
        let base_modifier = match terrain_type {
            TerrainType::Plains => 1.0,
            TerrainType::Forest => 0.8,    // 森林中远程攻击受阻
            TerrainType::Mountains => 1.2, // 山地有利于远程攻击
            _ => 1.0,
        };

        // 距离越远，修正越小
        base_modifier * (1.0 / (1.0 + distance as f32 * 0.01))
    }

    fn is_good_battle_position(&self, _position: Position) -> bool {
        // 简化实现，实际应检查地形、障碍物、战术价值等
        true
    }

    /// 检查位置是否有古遗迹
    fn has_ancient_ruins(&self, location_info: &BattleLocationInfo) -> bool {
        // 检查环境效果中是否有古战场
        for effect in &location_info.environmental_effects {
            if matches!(effect, EnvironmentalEffect::AncientBattlefield { .. }) {
                return true;
            }
        }

        // 检查灵气密度是否异常高（可能表示古遗迹）
        if location_info.spiritual_density > 3.0 {
            return true;
        }

        // 检查危险等级和地形组合（山地+高危险=可能有遗迹）
        if matches!(
            location_info.terrain_type,
            TerrainType::Mountains | TerrainType::SpiritualVein
        ) && matches!(
            location_info.danger_level,
            DangerLevel::High | DangerLevel::Extreme | DangerLevel::Lethal
        ) {
            return true;
        }

        false
    }
}

/// 地形战斗加成
#[derive(Debug, Clone, Default)]
pub struct TerrainCombatBonus {
    /// 是否有高度优势
    pub height_advantage: bool,
    /// 攻击力加成
    pub attack_bonus: f32,
    /// 防御力加成
    pub defense_bonus: f32,
    /// 命中率加成
    pub accuracy_bonus: f32,
    /// 闪避率加成
    pub evasion_bonus: f32,
    /// 地形专精加成
    pub terrain_specialization: f32,
    /// 距离修正
    pub distance_modifier: f32,
}

impl Default for TerrainEffectConfig {
    fn default() -> Self {
        let mut attack_modifiers = std::collections::HashMap::new();
        attack_modifiers.insert(TerrainType::Mountains, 1.1);
        attack_modifiers.insert(TerrainType::Plains, 1.0);
        attack_modifiers.insert(TerrainType::Forest, 0.9);
        attack_modifiers.insert(TerrainType::Swamp, 0.8);

        let mut defense_modifiers = std::collections::HashMap::new();
        defense_modifiers.insert(TerrainType::Mountains, 1.2);
        defense_modifiers.insert(TerrainType::Forest, 1.1);
        defense_modifiers.insert(TerrainType::Plains, 1.0);
        defense_modifiers.insert(TerrainType::Desert, 0.9);

        let mut speed_modifiers = std::collections::HashMap::new();
        speed_modifiers.insert(TerrainType::Plains, 1.0);
        speed_modifiers.insert(TerrainType::Desert, 0.9);
        speed_modifiers.insert(TerrainType::Forest, 0.8);
        speed_modifiers.insert(TerrainType::Swamp, 0.6);
        speed_modifiers.insert(TerrainType::Mountains, 0.7);

        let mut accuracy_modifiers = std::collections::HashMap::new();
        accuracy_modifiers.insert(TerrainType::Plains, 1.0);
        accuracy_modifiers.insert(TerrainType::Mountains, 1.1);
        accuracy_modifiers.insert(TerrainType::Forest, 0.9);
        accuracy_modifiers.insert(TerrainType::Swamp, 0.8);

        let mut evasion_modifiers = std::collections::HashMap::new();
        evasion_modifiers.insert(TerrainType::Forest, 1.2);
        evasion_modifiers.insert(TerrainType::Mountains, 1.1);
        evasion_modifiers.insert(TerrainType::Plains, 1.0);
        evasion_modifiers.insert(TerrainType::Desert, 0.9);

        Self {
            attack_modifiers,
            defense_modifiers,
            speed_modifiers,
            accuracy_modifiers,
            evasion_modifiers,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_battle_adapter_creation() {
        let _adapter = WorldMapBattleAdapter::new();
        assert!(true); // 如果能创建适配器就算成功
    }

    #[test]
    fn test_terrain_combat_bonus() {
        let adapter = WorldMapBattleAdapter::new();
        let attacker_pos = Position::new_3d(0.0, 0.0, 10.0);
        let defender_pos = Position::new_3d(0.0, 0.0, 5.0);

        let bonus = adapter.calculate_terrain_combat_bonus(
            attacker_pos,
            defender_pos,
            TerrainType::Mountains,
        );

        assert!(bonus.height_advantage);
        assert!(bonus.attack_bonus > 0.0);
    }
}
