//! # 类型系统模块
//!
//! 负责TypedId类型安全系统，替代传统枚举
//!
//! ## 核心组件
//!
//! - `TypedId<T>`: 类型安全的ID包装器
//! - `TypeRegistry`: 类型注册中心，管理所有类型定义
//! - `TypeValidator`: 类型验证器，确保类型的正确性
//! - `TypeConverter`: 类型转换器，处理不同类型间的转换
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::types::{TypedId, TypeRegistry};
//!
//! // 定义材料类型标记
//! struct MaterialType;
//!
//! // 创建类型安全的材料ID
//! let material_id = TypedId::<MaterialType>::new("iron_ore".to_string());
//!
//! // 通过类型注册中心验证
//! let registry = TypeRegistry::new();
//! let is_valid = registry.validate_id(&material_id).await?;
//! ```

// 这些模块将在后续阶段实现
pub mod converter;
pub mod metadata;
pub mod registry;
pub mod traits;
pub mod typed_id;
pub mod validator;

// 重新导出主要类型
pub use converter::{ConversionRule, TypeConverter};
pub use registry::{TypeRegistration, TypeRegistry};
pub use typed_id::{TypeMarker, TypedId};
pub use validator::{TypeValidator, ValidationRule};
// traits 模块暂时保留为空，遵循 YAGNI 原则

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 类型定义元信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeMetadata {
    /// 类型ID
    pub type_id: String,
    /// 类型名称
    pub type_name: String,
    /// 类型描述
    pub description: Option<String>,
    /// 允许的值列表
    pub allowed_values: Vec<String>,
    /// 验证规则
    pub validation_rules: Vec<String>,
    /// 类型标签
    pub tags: HashMap<String, String>,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

impl TypeMetadata {
    pub fn new(type_id: String, type_name: String) -> Self {
        let now = chrono::Utc::now();
        Self {
            type_id,
            type_name,
            description: None,
            allowed_values: Vec::new(),
            validation_rules: Vec::new(),
            tags: HashMap::new(),
            created_at: now,
            updated_at: now,
        }
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn with_allowed_values(mut self, values: Vec<String>) -> Self {
        self.allowed_values = values;
        self
    }

    pub fn with_validation_rule(mut self, rule: String) -> Self {
        self.validation_rules.push(rule);
        self
    }

    pub fn with_tag(mut self, key: String, value: String) -> Self {
        self.tags.insert(key, value);
        self
    }

    pub fn add_allowed_value(&mut self, value: String) {
        if !self.allowed_values.contains(&value) {
            self.allowed_values.push(value);
            self.updated_at = chrono::Utc::now();
        }
    }

    pub fn remove_allowed_value(&mut self, value: &str) {
        if let Some(pos) = self.allowed_values.iter().position(|v| v == value) {
            self.allowed_values.remove(pos);
            self.updated_at = chrono::Utc::now();
        }
    }

    pub fn is_value_allowed(&self, value: &str) -> bool {
        self.allowed_values.is_empty() || self.allowed_values.contains(&value.to_string())
    }
}

/// 类型操作结果
#[derive(Debug, Clone)]
pub enum TypeOperationResult<T> {
    Success(T),
    ValidationError(Vec<String>),
    NotFound(String),
    InvalidOperation(String),
}

impl<T> TypeOperationResult<T> {
    pub fn is_success(&self) -> bool {
        matches!(self, TypeOperationResult::Success(_))
    }

    pub fn is_error(&self) -> bool {
        !self.is_success()
    }

    pub fn unwrap(self) -> T {
        match self {
            TypeOperationResult::Success(value) => value,
            _ => panic!("Called unwrap on an error result"),
        }
    }

    pub fn unwrap_or(self, default: T) -> T {
        match self {
            TypeOperationResult::Success(value) => value,
            _ => default,
        }
    }
}

/// 类型转换规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeConversionRule {
    /// 源类型ID
    pub from_type: String,
    /// 目标类型ID
    pub to_type: String,
    /// 转换函数名称
    pub converter_name: String,
    /// 转换优先级
    pub priority: i32,
    /// 是否双向转换
    pub bidirectional: bool,
    /// 转换参数
    pub parameters: HashMap<String, String>,
}

impl TypeConversionRule {
    pub fn new(from_type: String, to_type: String, converter_name: String) -> Self {
        Self {
            from_type,
            to_type,
            converter_name,
            priority: 0,
            bidirectional: false,
            parameters: HashMap::new(),
        }
    }

    pub fn with_priority(mut self, priority: i32) -> Self {
        self.priority = priority;
        self
    }

    pub fn bidirectional(mut self) -> Self {
        self.bidirectional = true;
        self
    }

    pub fn with_parameter(mut self, key: String, value: String) -> Self {
        self.parameters.insert(key, value);
        self
    }
}

/// 类型查询构建器
pub struct TypeQuery {
    type_name_pattern: Option<String>,
    tags: HashMap<String, String>,
    has_validation_rules: Option<bool>,
    allowed_values_count: Option<(usize, usize)>, // (min, max)
}

impl TypeQuery {
    pub fn new() -> Self {
        Self {
            type_name_pattern: None,
            tags: HashMap::new(),
            has_validation_rules: None,
            allowed_values_count: None,
        }
    }

    pub fn with_name_pattern(mut self, pattern: String) -> Self {
        self.type_name_pattern = Some(pattern);
        self
    }

    pub fn with_tag(mut self, key: String, value: String) -> Self {
        self.tags.insert(key, value);
        self
    }

    pub fn with_validation_rules(mut self, has_rules: bool) -> Self {
        self.has_validation_rules = Some(has_rules);
        self
    }

    pub fn with_allowed_values_count(mut self, min: usize, max: usize) -> Self {
        self.allowed_values_count = Some((min, max));
        self
    }

    pub fn matches(&self, metadata: &TypeMetadata) -> bool {
        // 检查名称模式
        if let Some(pattern) = &self.type_name_pattern {
            if !metadata.type_name.contains(pattern) {
                return false;
            }
        }

        // 检查标签
        for (key, value) in &self.tags {
            if metadata.tags.get(key) != Some(value) {
                return false;
            }
        }

        // 检查验证规则
        if let Some(has_rules) = self.has_validation_rules {
            let actual_has_rules = !metadata.validation_rules.is_empty();
            if has_rules != actual_has_rules {
                return false;
            }
        }

        // 检查允许值数量
        if let Some((min, max)) = self.allowed_values_count {
            let count = metadata.allowed_values.len();
            if count < min || count > max {
                return false;
            }
        }

        true
    }
}

impl Default for TypeQuery {
    fn default() -> Self {
        Self::new()
    }
}

/// 内置类型标记
///
/// 这些标记用于游戏中的主要类型

/// 材料类型标记
pub struct MaterialTypeMarker;

/// 工具类型标记
pub struct ToolTypeMarker;

/// 地形类型标记
pub struct TerrainTypeMarker;

/// 资源节点类型标记
pub struct ResourceNodeTypeMarker;

/// 技能类型标记
pub struct SkillTypeMarker;

/// 状态效果类型标记
pub struct StatusEffectTypeMarker;

/// 类型别名，提供更好的API
pub type MaterialId = TypedId<MaterialTypeMarker>;
pub type ToolId = TypedId<ToolTypeMarker>;
pub type TerrainId = TypedId<TerrainTypeMarker>;
pub type ResourceNodeId = TypedId<ResourceNodeTypeMarker>;
pub type SkillId = TypedId<SkillTypeMarker>;
pub type StatusEffectId = TypedId<StatusEffectTypeMarker>;

/// 类型构建器工具函数
pub mod builders {
    use super::*;

    /// 创建材料ID
    pub fn material_id(id: impl Into<String>) -> MaterialId {
        MaterialId::new(id.into())
    }

    /// 创建工具ID
    pub fn tool_id(id: impl Into<String>) -> ToolId {
        ToolId::new(id.into())
    }

    /// 创建地形ID
    pub fn terrain_id(id: impl Into<String>) -> TerrainId {
        TerrainId::new(id.into())
    }

    /// 创建资源节点ID
    pub fn resource_node_id(id: impl Into<String>) -> ResourceNodeId {
        ResourceNodeId::new(id.into())
    }

    /// 创建技能ID
    pub fn skill_id(id: impl Into<String>) -> SkillId {
        SkillId::new(id.into())
    }

    /// 创建状态效果ID
    pub fn status_effect_id(id: impl Into<String>) -> StatusEffectId {
        StatusEffectId::new(id.into())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config_engine::types::builders::*;

    #[test]
    fn test_type_metadata_creation() {
        let metadata = TypeMetadata::new("Material".to_string(), "材料类型".to_string())
            .with_description("游戏中的材料类型定义".to_string())
            .with_allowed_values(vec![
                "iron_ore".to_string(),
                "gold_ore".to_string(),
                "common_herb".to_string(),
            ])
            .with_validation_rule("id.length > 0".to_string())
            .with_tag("category".to_string(), "resource".to_string());

        assert_eq!(metadata.type_id, "Material");
        assert_eq!(metadata.type_name, "材料类型");
        assert_eq!(metadata.allowed_values.len(), 3);
        assert_eq!(metadata.validation_rules.len(), 1);
        assert_eq!(metadata.tags.get("category"), Some(&"resource".to_string()));
    }

    #[test]
    fn test_type_metadata_value_operations() {
        let mut metadata = TypeMetadata::new("Test".to_string(), "Test Type".to_string())
            .with_allowed_values(vec!["value1".to_string(), "value2".to_string()]);

        assert!(metadata.is_value_allowed("value1"));
        assert!(metadata.is_value_allowed("value2"));
        assert!(!metadata.is_value_allowed("value3"));

        metadata.add_allowed_value("value3".to_string());
        assert!(metadata.is_value_allowed("value3"));

        metadata.remove_allowed_value("value1");
        assert!(!metadata.is_value_allowed("value1"));
    }

    #[test]
    fn test_type_conversion_rule() {
        let rule = TypeConversionRule::new(
            "MaterialType".to_string(),
            "StringType".to_string(),
            "to_string".to_string(),
        )
        .with_priority(100)
        .bidirectional()
        .with_parameter("format".to_string(), "lowercase".to_string());

        assert_eq!(rule.from_type, "MaterialType");
        assert_eq!(rule.to_type, "StringType");
        assert_eq!(rule.priority, 100);
        assert!(rule.bidirectional);
        assert_eq!(
            rule.parameters.get("format"),
            Some(&"lowercase".to_string())
        );
    }

    #[test]
    fn test_type_query() {
        let metadata = TypeMetadata::new("Material".to_string(), "Material Type".to_string())
            .with_tag("category".to_string(), "resource".to_string())
            .with_validation_rule("test".to_string())
            .with_allowed_values(vec!["a".to_string(), "b".to_string()]);

        let query = TypeQuery::new()
            .with_name_pattern("Material".to_string())
            .with_tag("category".to_string(), "resource".to_string())
            .with_validation_rules(true)
            .with_allowed_values_count(1, 5);

        assert!(query.matches(&metadata));

        let query2 = TypeQuery::new().with_tag("category".to_string(), "equipment".to_string());

        assert!(!query2.matches(&metadata));
    }

    #[test]
    fn test_type_builders() {
        let material = material_id("iron_ore");
        let tool = tool_id("pickaxe");
        let terrain = terrain_id("forest");

        assert_eq!(material.as_str(), "iron_ore");
        assert_eq!(tool.as_str(), "pickaxe");
        assert_eq!(terrain.as_str(), "forest");
    }

    #[test]
    fn test_type_operation_result() {
        let success: TypeOperationResult<String> = TypeOperationResult::Success("test".to_string());
        assert!(success.is_success());
        assert!(!success.is_error());
        assert_eq!(success.unwrap(), "test");

        let error: TypeOperationResult<String> =
            TypeOperationResult::ValidationError(vec!["error1".to_string()]);
        assert!(!error.is_success());
        assert!(error.is_error());
        assert_eq!(error.unwrap_or("default".to_string()), "default");
    }
}
