/// 战斗计算器模块
/// 
/// 负责所有战斗相关的计算，包括暴击、闪避、伤害等

use crate::shared::*;
use crate::battle_system::battle_view::BattleView;
use crate::battle_system::simplified_battle_traits::{DamageResult, HealResult};

/// 暴击计算器
pub struct CriticalHitCalculator;

impl CriticalHitCalculator {
    /// 计算暴击概率
    /// 基于攻击者的等级、敏捷度等因素
    pub fn calculate_crit_chance(attacker: &BattleView) -> f32 {
        // 基础暴击率
        let base_crit_rate = 0.05; // 5%
        
        // 等级影响 (每10级增加1%暴击)
        let level_bonus = (attacker.level as f32) * 0.001; // 0.1% per level
        
        // 攻击力影响 (高攻击力有更高暴击机会)
        let attack_bonus = (attacker.attack_power as f32) * 0.0002; // 0.02% per attack point
        
        // Buff影响
        let buff_bonus = attacker.buffs.iter()
            .filter(|buff| buff.skill_type == "crit_bonus")
            .map(|buff| buff.effect_value)
            .sum::<f32>() / 100.0; // 假设buff值是百分比
        
        let total_crit_chance = base_crit_rate + level_bonus + attack_bonus + buff_bonus;
        total_crit_chance.min(0.95) // 最大95%暴击率
    }
    
    /// 计算暴击倍数
    pub fn calculate_crit_multiplier(attacker: &BattleView) -> f32 {
        // 基础暴击倍数
        let base_multiplier = 2.0;
        
        // 等级影响 (每20级增加0.1倍暴击伤害)
        let level_bonus = (attacker.level as f32) * 0.005; // 0.005x per level
        
        // Buff影响
        let buff_bonus = attacker.buffs.iter()
            .filter(|buff| buff.skill_type == "crit_damage")
            .map(|buff| buff.effect_value / 100.0)
            .sum::<f32>();
        
        base_multiplier + level_bonus + buff_bonus
    }
    
    /// 判断是否发生暴击
    pub fn is_critical_hit(attacker: &BattleView) -> bool {
        let crit_chance = Self::calculate_crit_chance(attacker);
        rand::random::<f32>() < crit_chance
    }
}

/// 闪避计算器
pub struct EvasionCalculator;

impl EvasionCalculator {
    /// 计算闪避概率
    /// 基于防御者的敏捷度、等级等因素
    pub fn calculate_evasion_chance(defender: &BattleView, attacker: &BattleView) -> f32 {
        // 基础闪避率
        let base_evasion = 0.02; // 2%
        
        // 防御者等级影响
        let defender_level_bonus = (defender.level as f32) * 0.0008; // 0.08% per level
        
        // 移动速度影响 (高速度有更高闪避)
        let speed_bonus = (defender.movement_speed - 5.0).max(0.0) * 0.005; // 5.0是基础速度
        
        // 等级差异影响 (等级高的更容易闪避低等级攻击)
        let level_diff = (defender.level as i32 - attacker.level as i32) as f32;
        let level_diff_bonus = level_diff * 0.002; // 每级差异0.2%
        
        // Buff影响
        let buff_bonus = defender.buffs.iter()
            .filter(|buff| buff.skill_type == "evasion" || buff.skill_type == "dodge")
            .map(|buff| buff.effect_value / 100.0)
            .sum::<f32>();
        
        let total_evasion = base_evasion + defender_level_bonus + speed_bonus + level_diff_bonus + buff_bonus;
        total_evasion.max(0.0).min(0.5) // 最大50%闪避率
    }
    
    /// 判断是否闪避成功
    pub fn is_evasion_success(defender: &BattleView, attacker: &BattleView) -> bool {
        let evasion_chance = Self::calculate_evasion_chance(defender, attacker);
        rand::random::<f32>() < evasion_chance
    }
}

/// 治疗暴击计算器
pub struct HealCriticalCalculator;

impl HealCriticalCalculator {
    /// 计算治疗暴击概率
    /// 基于施法者的精神力、等级等
    pub fn calculate_heal_crit_chance(healer: &BattleView) -> f32 {
        // 基础治疗暴击率 (比攻击暴击率略高)
        let base_crit_rate = 0.08; // 8%
        
        // 法力值影响 (高法力上限表示高精神力)
        let mana_bonus = (healer.max_mana as f32) * 0.00005; // 每点法力0.005%
        
        // 等级影响
        let level_bonus = (healer.level as f32) * 0.0012; // 0.12% per level
        
        // Buff影响
        let buff_bonus = healer.buffs.iter()
            .filter(|buff| buff.skill_type == "heal_crit")
            .map(|buff| buff.effect_value / 100.0)
            .sum::<f32>();
        
        let total_crit_chance = base_crit_rate + mana_bonus + level_bonus + buff_bonus;
        total_crit_chance.min(0.8) // 最大80%治疗暴击率
    }
    
    /// 计算治疗暴击倍数
    pub fn calculate_heal_crit_multiplier(healer: &BattleView) -> f32 {
        // 基础治疗暴击倍数 (比攻击暴击倍数低)
        let base_multiplier = 1.5;
        
        // 等级影响
        let level_bonus = (healer.level as f32) * 0.003; // 0.003x per level
        
        // Buff影响
        let buff_bonus = healer.buffs.iter()
            .filter(|buff| buff.skill_type == "heal_crit_damage")
            .map(|buff| buff.effect_value / 100.0)
            .sum::<f32>();
        
        base_multiplier + level_bonus + buff_bonus
    }
    
    /// 判断是否发生治疗暴击
    pub fn is_heal_critical(healer: &BattleView) -> bool {
        let crit_chance = Self::calculate_heal_crit_chance(healer);
        rand::random::<f32>() < crit_chance
    }
}

/// 装备和技能增益计算器
pub struct BonusCalculator;

impl BonusCalculator {
    /// 计算攻击范围
    /// 从装备和技能获取额外范围
    pub fn calculate_attack_range(unit: &BattleView) -> f32 {
        // 基础攻击范围
        let base_range = 1.0;
        
        // 等级影响 (每30级增加0.1范围)
        let level_bonus = (unit.level as f32) / 300.0;
        
        // Buff影响 (长武器、技能等)
        let buff_bonus = unit.buffs.iter()
            .filter(|buff| buff.skill_type == "range_bonus" || buff.skill_type == "weapon_reach")
            .map(|buff| buff.effect_value / 10.0) // 假设buff值是以0.1为单位
            .sum::<f32>();
        
        base_range + level_bonus + buff_bonus
    }
    
    /// 计算状态效果列表
    /// 从Buff和其他系统获取当前状态
    pub fn calculate_active_statuses(unit: &BattleView) -> Vec<String> {
        let mut statuses = Vec::new();
        
        // 从Buff获取状态
        for buff in &unit.buffs {
            match buff.skill_type.as_str() {
                "poison" => statuses.push("中毒".to_string()),
                "burn" => statuses.push("燃烧".to_string()),
                "freeze" => statuses.push("冰冻".to_string()),
                "stun" => statuses.push("眩晕".to_string()),
                "shield" => statuses.push("护盾".to_string()),
                "regeneration" => statuses.push("恢复".to_string()),
                "haste" => statuses.push("加速".to_string()),
                "slow" => statuses.push("减速".to_string()),
                _ => {
                    if !buff.buff_name.is_empty() {
                        statuses.push(buff.buff_name.clone());
                    }
                }
            }
        }
        
        // 健康状态
        let health_percent = unit.health_percentage();
        if health_percent < 0.1 {
            statuses.push("濒死".to_string());
        } else if health_percent < 0.3 {
            statuses.push("重伤".to_string());
        } else if health_percent < 0.7 {
            statuses.push("轻伤".to_string());
        }
        
        // 法力状态
        if unit.max_mana > 0 {
            let mana_percent = (unit.current_mana as f32) / (unit.max_mana as f32);
            if mana_percent < 0.1 {
                statuses.push("法力枯竭".to_string());
            } else if mana_percent < 0.3 {
                statuses.push("法力不足".to_string());
            }
        }
        
        // 技能状态
        let skills_on_cooldown = unit.skill_cooldowns.iter()
            .filter(|(_, &cooldown)| cooldown > 0.0)
            .count();
        if skills_on_cooldown == unit.learned_skills.len() && !unit.learned_skills.is_empty() {
            statuses.push("技能冷却".to_string());
        }
        
        statuses
    }
}

/// 综合战斗计算器
pub struct CombatCalculator;

impl CombatCalculator {
    /// 计算完整的伤害结果
    pub fn calculate_damage(attacker: &BattleView, defender: &BattleView, base_damage: u32) -> DamageResult {
        // 检查闪避
        if EvasionCalculator::is_evasion_success(defender, attacker) {
            return DamageResult {
                actual_damage: 0,
                is_critical: false,
                is_miss: true,
                was_fatal: false,
            };
        }
        
        // 检查暴击
        let is_crit = CriticalHitCalculator::is_critical_hit(attacker);
        let crit_multiplier = if is_crit {
            CriticalHitCalculator::calculate_crit_multiplier(attacker)
        } else {
            1.0
        };
        
        // 计算最终伤害
        let damage_after_crit = (base_damage as f32 * crit_multiplier) as u32;
        let final_damage = damage_after_crit.min(defender.current_health as u32);
        let remaining_health = defender.current_health.saturating_sub(final_damage as i32);
        
        DamageResult {
            actual_damage: final_damage as i32,
            is_critical: is_crit,
            is_miss: false,
            was_fatal: remaining_health <= 0,
        }
    }
    
    /// 计算完整的治疗结果
    pub fn calculate_heal(healer: &BattleView, target: &BattleView, base_heal: u32) -> HealResult {
        // 检查治疗暴击
        let is_crit = HealCriticalCalculator::is_heal_critical(healer);
        let crit_multiplier = if is_crit {
            HealCriticalCalculator::calculate_heal_crit_multiplier(healer)
        } else {
            1.0
        };
        
        // 计算最终治疗量
        let heal_after_crit = (base_heal as f32 * crit_multiplier) as u32;
        let final_heal = heal_after_crit.min((target.max_health - target.current_health) as u32);
        
        HealResult {
            actual_heal: final_heal as i32,
            is_critical: is_crit,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::battle_system::battle_view::BattleView;
    
    #[test]
    fn test_critical_hit_calculation() {
        let mut attacker = BattleView::create_test_character(1, "Test", 50);
        attacker.attack_power = 100;
        
        let crit_chance = CriticalHitCalculator::calculate_crit_chance(&attacker);
        assert!(crit_chance > 0.05); // 应该大于基础暴击率
        assert!(crit_chance < 0.95); // 不应该超过最大值
        
        let crit_multiplier = CriticalHitCalculator::calculate_crit_multiplier(&attacker);
        assert!(crit_multiplier >= 2.0); // 至少是基础倍数
    }
    
    #[test]
    fn test_evasion_calculation() {
        let attacker = BattleView::create_test_character(1, "Attacker", 20);
        let mut defender = BattleView::create_test_character(2, "Defender", 30);
        defender.movement_speed = 10.0; // 高速度
        
        let evasion_chance = EvasionCalculator::calculate_evasion_chance(&defender, &attacker);
        assert!(evasion_chance > 0.02); // 应该大于基础闪避率
        assert!(evasion_chance <= 0.5); // 不应该超过最大值
    }
    
    #[test]
    fn test_damage_calculation() {
        let attacker = BattleView::create_test_character(1, "Attacker", 20);
        let defender = BattleView::create_test_character(2, "Defender", 20);
        
        let damage_result = CombatCalculator::calculate_damage(&attacker, &defender, 50);
        
        // 结果应该是合理的
        assert!(damage_result.damage_dealt <= 50 || damage_result.is_critical); // 除非暴击，否则不应超过基础伤害太多
        assert!(damage_result.remaining_health <= defender.current_health);
    }
}