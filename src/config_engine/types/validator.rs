//! # 类型验证器模块
//!
//! 提供复杂的类型验证逻辑，支持自定义验证规则和验证策略
//!
//! ## 核心功能
//!
//! - 多层次验证（语法、语义、业务逻辑）
//! - 自定义验证规则
//! - 异步验证支持
//! - 验证结果缓存
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::types::{TypeValidator, ValidationRule};
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let validator = TypeValidator::new();
//!
//! // 添加验证规则
//! let rule = ValidationRule::new()
//!     .with_name("material_format")
//!     .with_expression("value.matches(/^[a-z_]+$/)")
//!     .with_error_message("Material ID must be lowercase with underscores");
//!
//! validator.add_rule("material", rule).await?;
//!
//! // 验证值
//! let result = validator.validate("material", "iron_ore").await?;
//! assert!(result.is_valid());
//! # Ok(())
//! # }
//! ```

use crate::config_engine::validation::{
    ErrorSeverity, ValidationError, ValidationResult, ValidationSource,
};
use crate::config_engine::{error::ConfigEngineError, Result};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

/// 验证规则定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: Option<String>,
    /// 验证表达式
    pub expression: String,
    /// 规则类型
    pub rule_type: ValidationRuleType,
    /// 错误消息模板
    pub error_message: String,
    /// 警告消息模板
    pub warning_message: Option<String>,
    /// 规则严重性
    pub severity: ErrorSeverity,
    /// 是否启用
    pub enabled: bool,
    /// 执行顺序
    pub order: i32,
    /// 超时时间（毫秒）
    pub timeout_ms: Option<u64>,
    /// 规则参数
    pub parameters: HashMap<String, String>,
    /// 适用的数据类型
    pub target_types: Vec<String>,
    /// 依赖的其他规则
    pub dependencies: Vec<String>,
}

impl ValidationRule {
    /// 创建新的验证规则
    pub fn new() -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            name: String::new(),
            description: None,
            expression: String::new(),
            rule_type: ValidationRuleType::Expression,
            error_message: String::new(),
            warning_message: None,
            severity: ErrorSeverity::Error,
            enabled: true,
            order: 0,
            timeout_ms: Some(5000), // 默认5秒超时
            parameters: HashMap::new(),
            target_types: Vec::new(),
            dependencies: Vec::new(),
        }
    }

    /// 设置规则名称
    pub fn with_name(mut self, name: impl Into<String>) -> Self {
        self.name = name.into();
        self
    }

    /// 设置描述
    pub fn with_description(mut self, description: impl Into<String>) -> Self {
        self.description = Some(description.into());
        self
    }

    /// 设置验证表达式
    pub fn with_expression(mut self, expression: impl Into<String>) -> Self {
        self.expression = expression.into();
        self
    }

    /// 设置规则类型
    pub fn with_rule_type(mut self, rule_type: ValidationRuleType) -> Self {
        self.rule_type = rule_type;
        self
    }

    /// 设置错误消息
    pub fn with_error_message(mut self, message: impl Into<String>) -> Self {
        self.error_message = message.into();
        self
    }

    /// 设置警告消息
    pub fn with_warning_message(mut self, message: impl Into<String>) -> Self {
        self.warning_message = Some(message.into());
        self
    }

    /// 设置严重性
    pub fn with_severity(mut self, severity: ErrorSeverity) -> Self {
        self.severity = severity;
        self
    }

    /// 设置执行顺序
    pub fn with_order(mut self, order: i32) -> Self {
        self.order = order;
        self
    }

    /// 设置超时时间
    pub fn with_timeout(mut self, timeout_ms: u64) -> Self {
        self.timeout_ms = Some(timeout_ms);
        self
    }

    /// 添加参数
    pub fn with_parameter(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.parameters.insert(key.into(), value.into());
        self
    }

    /// 添加目标类型
    pub fn with_target_type(mut self, target_type: impl Into<String>) -> Self {
        self.target_types.push(target_type.into());
        self
    }

    /// 添加依赖规则
    pub fn with_dependency(mut self, dependency: impl Into<String>) -> Self {
        self.dependencies.push(dependency.into());
        self
    }

    /// 禁用规则
    pub fn disabled(mut self) -> Self {
        self.enabled = false;
        self
    }

    /// 检查规则是否适用于指定类型
    pub fn applies_to(&self, type_name: &str) -> bool {
        self.enabled
            && (self.target_types.is_empty() || self.target_types.contains(&type_name.to_string()))
    }

    /// 验证规则本身的有效性
    pub fn validate(&self) -> Result<()> {
        if self.name.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: "Rule name cannot be empty".to_string(),
                details: Some(self.id.clone()),
            });
        }

        if self.expression.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: "Rule expression cannot be empty".to_string(),
                details: Some(self.id.clone()),
            });
        }

        if self.error_message.is_empty() {
            return Err(ConfigEngineError::ValidationError {
                message: "Error message cannot be empty".to_string(),
                details: Some(self.id.clone()),
            });
        }

        // 验证表达式语法（简化实现）
        match self.rule_type {
            ValidationRuleType::Regex => {
                regex::Regex::new(&self.expression).map_err(|e| {
                    ConfigEngineError::ValidationError {
                        message: format!("Invalid regex expression: {}", e),
                        details: Some(self.expression.clone()),
                    }
                })?;
            }
            ValidationRuleType::Expression => {
                // 这里应该验证表达式语法，简化实现
                if !self.expression.contains("value") {
                    return Err(ConfigEngineError::ValidationError {
                        message: "Expression must contain 'value' variable".to_string(),
                        details: Some(self.expression.clone()),
                    });
                }
            }
            _ => {}
        }

        Ok(())
    }
}

impl Default for ValidationRule {
    fn default() -> Self {
        Self::new()
    }
}

/// 验证规则类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ValidationRuleType {
    /// 正则表达式验证
    Regex,
    /// JavaScript表达式验证
    Expression,
    /// 长度验证
    Length,
    /// 范围验证
    Range,
    /// 格式验证
    Format,
    /// 自定义函数验证
    Custom,
    /// 异步验证（调用外部服务）
    Async,
}

/// 验证上下文
#[derive(Debug, Clone)]
pub struct ValidationContext {
    /// 要验证的值
    pub value: String,
    /// 值的类型
    pub value_type: String,
    /// 上下文参数
    pub parameters: HashMap<String, String>,
    /// 验证路径
    pub path: Vec<String>,
    /// 相关数据
    pub related_data: HashMap<String, String>,
}

impl ValidationContext {
    pub fn new(value: String, value_type: String) -> Self {
        Self {
            value,
            value_type,
            parameters: HashMap::new(),
            path: Vec::new(),
            related_data: HashMap::new(),
        }
    }

    pub fn with_parameter(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.parameters.insert(key.into(), value.into());
        self
    }

    pub fn with_path(mut self, path: Vec<String>) -> Self {
        self.path = path;
        self
    }

    pub fn with_related_data(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.related_data.insert(key.into(), value.into());
        self
    }

    pub fn get_parameter(&self, key: &str) -> Option<&String> {
        self.parameters.get(key)
    }

    pub fn get_related_data(&self, key: &str) -> Option<&String> {
        self.related_data.get(key)
    }

    pub fn path_string(&self) -> String {
        self.path.join(".")
    }
}

/// 验证器执行接口
#[async_trait]
pub trait RuleExecutor: Send + Sync {
    /// 执行验证规则
    async fn execute(
        &self,
        rule: &ValidationRule,
        context: &ValidationContext,
    ) -> Result<ValidationResult>;

    /// 检查是否支持指定的规则类型
    fn supports(&self, rule_type: &ValidationRuleType) -> bool;

    /// 获取执行器名称
    fn name(&self) -> &str;
}

/// 正则表达式执行器
pub struct RegexExecutor;

#[async_trait]
impl RuleExecutor for RegexExecutor {
    async fn execute(
        &self,
        rule: &ValidationRule,
        context: &ValidationContext,
    ) -> Result<ValidationResult> {
        let regex = regex::Regex::new(&rule.expression).map_err(|e| {
            ConfigEngineError::ValidationError {
                message: format!("Invalid regex: {}", e),
                details: Some(rule.expression.clone()),
            }
        })?;

        let mut result = ValidationResult::new();

        if !regex.is_match(&context.value) {
            result.add_error(
                ValidationError::new(
                    rule.id.clone(),
                    rule.error_message.clone(),
                    ValidationSource::Type,
                )
                .with_severity(rule.severity.clone()),
            );
        }

        Ok(result)
    }

    fn supports(&self, rule_type: &ValidationRuleType) -> bool {
        matches!(rule_type, ValidationRuleType::Regex)
    }

    fn name(&self) -> &str {
        "RegexExecutor"
    }
}

/// 长度验证执行器
pub struct LengthExecutor;

#[async_trait]
impl RuleExecutor for LengthExecutor {
    async fn execute(
        &self,
        rule: &ValidationRule,
        context: &ValidationContext,
    ) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();
        let value_len = context.value.len();

        // 解析长度约束参数
        let min_length = rule
            .parameters
            .get("min_length")
            .and_then(|s| s.parse::<usize>().ok());
        let max_length = rule
            .parameters
            .get("max_length")
            .and_then(|s| s.parse::<usize>().ok());

        // 检查最小长度
        if let Some(min) = min_length {
            if value_len < min {
                result.add_error(
                    ValidationError::new(
                        format!("{}_min_length", rule.id),
                        format!("Value length {} is less than minimum {}", value_len, min),
                        ValidationSource::Type,
                    )
                    .with_severity(rule.severity.clone()),
                );
            }
        }

        // 检查最大长度
        if let Some(max) = max_length {
            if value_len > max {
                result.add_error(
                    ValidationError::new(
                        format!("{}_max_length", rule.id),
                        format!("Value length {} exceeds maximum {}", value_len, max),
                        ValidationSource::Type,
                    )
                    .with_severity(rule.severity.clone()),
                );
            }
        }

        Ok(result)
    }

    fn supports(&self, rule_type: &ValidationRuleType) -> bool {
        matches!(rule_type, ValidationRuleType::Length)
    }

    fn name(&self) -> &str {
        "LengthExecutor"
    }
}

/// 范围验证执行器
pub struct RangeExecutor;

#[async_trait]
impl RuleExecutor for RangeExecutor {
    async fn execute(
        &self,
        rule: &ValidationRule,
        context: &ValidationContext,
    ) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();

        // 尝试解析为数值
        let value_num =
            context
                .value
                .parse::<f64>()
                .map_err(|_| ConfigEngineError::ValidationError {
                    message: format!("Cannot parse '{}' as number", context.value),
                    details: None,
                })?;

        // 解析范围参数
        let min_value = rule
            .parameters
            .get("min_value")
            .and_then(|s| s.parse::<f64>().ok());
        let max_value = rule
            .parameters
            .get("max_value")
            .and_then(|s| s.parse::<f64>().ok());

        // 检查最小值
        if let Some(min) = min_value {
            if value_num < min {
                result.add_error(
                    ValidationError::new(
                        format!("{}_min_value", rule.id),
                        format!("Value {} is less than minimum {}", value_num, min),
                        ValidationSource::Type,
                    )
                    .with_severity(rule.severity.clone()),
                );
            }
        }

        // 检查最大值
        if let Some(max) = max_value {
            if value_num > max {
                result.add_error(
                    ValidationError::new(
                        format!("{}_max_value", rule.id),
                        format!("Value {} exceeds maximum {}", value_num, max),
                        ValidationSource::Type,
                    )
                    .with_severity(rule.severity.clone()),
                );
            }
        }

        Ok(result)
    }

    fn supports(&self, rule_type: &ValidationRuleType) -> bool {
        matches!(rule_type, ValidationRuleType::Range)
    }

    fn name(&self) -> &str {
        "RangeExecutor"
    }
}

/// 类型验证器
pub struct TypeValidator {
    /// 验证规则集合
    rules: Arc<RwLock<HashMap<String, Vec<ValidationRule>>>>,
    /// 规则执行器
    executors: Arc<RwLock<HashMap<ValidationRuleType, Box<dyn RuleExecutor>>>>,
    /// 验证缓存
    cache: Arc<RwLock<HashMap<String, (ValidationResult, Instant)>>>,
    /// 缓存过期时间
    cache_duration: Duration,
}

impl TypeValidator {
    /// 创建新的类型验证器
    pub fn new() -> Self {
        let mut executors: HashMap<ValidationRuleType, Box<dyn RuleExecutor>> = HashMap::new();
        executors.insert(ValidationRuleType::Regex, Box::new(RegexExecutor));
        executors.insert(ValidationRuleType::Length, Box::new(LengthExecutor));
        executors.insert(ValidationRuleType::Range, Box::new(RangeExecutor));

        Self {
            rules: Arc::new(RwLock::new(HashMap::new())),
            executors: Arc::new(RwLock::new(executors)),
            cache: Arc::new(RwLock::new(HashMap::new())),
            cache_duration: Duration::from_secs(300), // 5分钟缓存
        }
    }

    /// 添加验证规则
    pub async fn add_rule(&self, type_name: impl Into<String>, rule: ValidationRule) -> Result<()> {
        let type_name = type_name.into();

        // 验证规则本身
        rule.validate()?;

        let mut rules = self.rules.write().await;
        rules.entry(type_name).or_insert_with(Vec::new).push(rule);

        // 清除相关缓存
        self.clear_cache_for_type(&type_name).await;

        Ok(())
    }

    /// 移除验证规则
    pub async fn remove_rule(&self, type_name: &str, rule_id: &str) -> Result<bool> {
        let mut rules = self.rules.write().await;

        if let Some(type_rules) = rules.get_mut(type_name) {
            let initial_len = type_rules.len();
            type_rules.retain(|rule| rule.id != rule_id);

            if type_rules.len() < initial_len {
                // 清除相关缓存
                drop(rules);
                self.clear_cache_for_type(type_name).await;
                return Ok(true);
            }
        }

        Ok(false)
    }

    /// 获取类型的所有验证规则
    pub async fn get_rules(&self, type_name: &str) -> Vec<ValidationRule> {
        let rules = self.rules.read().await;
        rules.get(type_name).cloned().unwrap_or_default()
    }

    /// 注册自定义执行器
    pub async fn register_executor(
        &self,
        rule_type: ValidationRuleType,
        executor: Box<dyn RuleExecutor>,
    ) {
        let mut executors = self.executors.write().await;
        executors.insert(rule_type, executor);
    }

    /// 验证值
    pub async fn validate(&self, type_name: &str, value: &str) -> Result<ValidationResult> {
        let context = ValidationContext::new(value.to_string(), type_name.to_string());
        self.validate_with_context(&context).await
    }

    /// 使用上下文验证值
    pub async fn validate_with_context(
        &self,
        context: &ValidationContext,
    ) -> Result<ValidationResult> {
        // 检查缓存
        let cache_key = format!("{}:{}", context.value_type, context.value);
        if let Some(cached_result) = self.get_cached_result(&cache_key).await {
            return Ok(cached_result);
        }

        let mut final_result = ValidationResult::new();

        // 获取适用的规则
        let applicable_rules = self.get_applicable_rules(&context.value_type).await;

        // 按依赖关系和顺序排序规则
        let sorted_rules = self.sort_rules_by_dependencies(applicable_rules).await?;

        // 执行验证规则
        for rule in sorted_rules {
            if !rule.enabled {
                continue;
            }

            let rule_result = self.execute_rule(&rule, context).await?;
            final_result.merge(rule_result);

            // 如果遇到严重错误且设置了快速失败，则停止验证
            if rule.severity == ErrorSeverity::Critical && final_result.has_errors() {
                break;
            }
        }

        // 缓存结果
        self.cache_result(cache_key, final_result.clone()).await;

        Ok(final_result)
    }

    /// 批量验证
    pub async fn validate_batch(
        &self,
        type_name: &str,
        values: &[String],
    ) -> Result<HashMap<String, ValidationResult>> {
        let mut results = HashMap::new();

        // 并行验证（这里简化为顺序执行）
        for value in values {
            let result = self.validate(type_name, value).await?;
            results.insert(value.clone(), result);
        }

        Ok(results)
    }

    /// 验证类型间的关系
    pub async fn validate_relationship(
        &self,
        source_type: &str,
        source_value: &str,
        target_type: &str,
        target_value: &str,
        relationship: &str,
    ) -> Result<ValidationResult> {
        // 这里应该实现复杂的关系验证逻辑
        // 简化实现：只检查基本的存在性
        let mut result = ValidationResult::new();

        let source_result = self.validate(source_type, source_value).await?;
        let target_result = self.validate(target_type, target_value).await?;

        if !source_result.is_valid || !target_result.is_valid {
            result.add_error(ValidationError::new(
                "relationship_validation".to_string(),
                format!(
                    "Invalid relationship '{}' between '{}:{}' and '{}:{}'",
                    relationship, source_type, source_value, target_type, target_value
                ),
                ValidationSource::Type,
            ));
        }

        Ok(result)
    }

    /// 获取验证统计信息
    pub async fn get_statistics(&self) -> ValidationStatistics {
        let rules = self.rules.read().await;
        let cache = self.cache.read().await;

        let total_rules = rules.values().map(|v| v.len()).sum::<usize>();
        let enabled_rules = rules
            .values()
            .flat_map(|v| v.iter())
            .filter(|r| r.enabled)
            .count();

        ValidationStatistics {
            total_types: rules.len(),
            total_rules,
            enabled_rules,
            disabled_rules: total_rules - enabled_rules,
            cached_results: cache.len(),
        }
    }

    /// 清除所有缓存
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
    }

    /// 清除指定类型的缓存
    async fn clear_cache_for_type(&self, type_name: &str) {
        let mut cache = self.cache.write().await;
        cache.retain(|key, _| !key.starts_with(&format!("{}:", type_name)));
    }

    /// 获取缓存的结果
    async fn get_cached_result(&self, cache_key: &str) -> Option<ValidationResult> {
        let cache = self.cache.read().await;

        if let Some((result, timestamp)) = cache.get(cache_key) {
            if timestamp.elapsed() < self.cache_duration {
                return Some(result.clone());
            }
        }

        None
    }

    /// 缓存验证结果
    async fn cache_result(&self, cache_key: String, result: ValidationResult) {
        let mut cache = self.cache.write().await;
        cache.insert(cache_key, (result, Instant::now()));

        // 简单的缓存清理策略
        if cache.len() > 1000 {
            cache.retain(|_, (_, timestamp)| timestamp.elapsed() < self.cache_duration);
        }
    }

    /// 获取适用的验证规则
    async fn get_applicable_rules(&self, type_name: &str) -> Vec<ValidationRule> {
        let rules = self.rules.read().await;

        rules
            .get(type_name)
            .cloned()
            .unwrap_or_default()
            .into_iter()
            .filter(|rule| rule.applies_to(type_name))
            .collect()
    }

    /// 按依赖关系排序规则
    async fn sort_rules_by_dependencies(
        &self,
        mut rules: Vec<ValidationRule>,
    ) -> Result<Vec<ValidationRule>> {
        // 简化的拓扑排序实现
        rules.sort_by(|a, b| {
            // 首先按依赖关系排序，然后按order字段排序
            if a.dependencies.contains(&b.id) {
                std::cmp::Ordering::Greater
            } else if b.dependencies.contains(&a.id) {
                std::cmp::Ordering::Less
            } else {
                a.order.cmp(&b.order)
            }
        });

        Ok(rules)
    }

    /// 执行单个验证规则
    async fn execute_rule(
        &self,
        rule: &ValidationRule,
        context: &ValidationContext,
    ) -> Result<ValidationResult> {
        let executors = self.executors.read().await;

        if let Some(executor) = executors.get(&rule.rule_type) {
            // 设置超时
            let execution_future = executor.execute(rule, context);

            if let Some(timeout_ms) = rule.timeout_ms {
                match tokio::time::timeout(Duration::from_millis(timeout_ms), execution_future)
                    .await
                {
                    Ok(result) => result,
                    Err(_) => {
                        let mut timeout_result = ValidationResult::new();
                        timeout_result.add_error(
                            ValidationError::new(
                                format!("{}_timeout", rule.id),
                                format!(
                                    "Rule '{}' execution timed out after {}ms",
                                    rule.name, timeout_ms
                                ),
                                ValidationSource::Type,
                            )
                            .with_severity(ErrorSeverity::Warning),
                        );
                        Ok(timeout_result)
                    }
                }
            } else {
                execution_future.await
            }
        } else {
            Err(ConfigEngineError::ValidationError {
                message: format!("No executor found for rule type: {:?}", rule.rule_type),
                details: Some(rule.id.clone()),
            })
        }
    }
}

impl Default for TypeValidator {
    fn default() -> Self {
        Self::new()
    }
}

/// 验证统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationStatistics {
    pub total_types: usize,
    pub total_rules: usize,
    pub enabled_rules: usize,
    pub disabled_rules: usize,
    pub cached_results: usize,
}

/// 验证器构建器
pub struct TypeValidatorBuilder {
    rules: Vec<(String, ValidationRule)>,
    executors: Vec<(ValidationRuleType, Box<dyn RuleExecutor>)>,
    cache_duration: Duration,
}

impl TypeValidatorBuilder {
    pub fn new() -> Self {
        Self {
            rules: Vec::new(),
            executors: Vec::new(),
            cache_duration: Duration::from_secs(300),
        }
    }

    pub fn with_rule(mut self, type_name: impl Into<String>, rule: ValidationRule) -> Self {
        self.rules.push((type_name.into(), rule));
        self
    }

    pub fn with_executor(
        mut self,
        rule_type: ValidationRuleType,
        executor: Box<dyn RuleExecutor>,
    ) -> Self {
        self.executors.push((rule_type, executor));
        self
    }

    pub fn with_cache_duration(mut self, duration: Duration) -> Self {
        self.cache_duration = duration;
        self
    }

    pub async fn build(self) -> Result<TypeValidator> {
        let validator = TypeValidator::new();

        // 设置缓存时间
        {
            let mut cache_duration = validator.cache_duration;
            cache_duration = self.cache_duration;
        }

        // 注册执行器
        for (rule_type, executor) in self.executors {
            validator.register_executor(rule_type, executor).await;
        }

        // 添加规则
        for (type_name, rule) in self.rules {
            validator.add_rule(type_name, rule).await?;
        }

        Ok(validator)
    }
}

impl Default for TypeValidatorBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_validation_rule_creation() {
        let rule = ValidationRule::new()
            .with_name("test_rule")
            .with_expression(r"^[a-z_]+$")
            .with_rule_type(ValidationRuleType::Regex)
            .with_error_message("Invalid format")
            .with_target_type("test_type");

        assert_eq!(rule.name, "test_rule");
        assert_eq!(rule.rule_type, ValidationRuleType::Regex);
        assert!(rule.applies_to("test_type"));
        assert!(!rule.applies_to("other_type"));
        assert!(rule.validate().is_ok());
    }

    #[tokio::test]
    async fn test_regex_executor() {
        let executor = RegexExecutor;
        let rule = ValidationRule::new()
            .with_expression(r"^[a-z_]+$")
            .with_rule_type(ValidationRuleType::Regex)
            .with_error_message("Invalid format");

        let context = ValidationContext::new("valid_name".to_string(), "test".to_string());
        let result = executor.execute(&rule, &context).await.unwrap();
        assert!(result.is_valid);

        let context = ValidationContext::new("Invalid-Name".to_string(), "test".to_string());
        let result = executor.execute(&rule, &context).await.unwrap();
        assert!(!result.is_valid);
        assert_eq!(result.errors.len(), 1);
    }

    #[tokio::test]
    async fn test_length_executor() {
        let executor = LengthExecutor;
        let rule = ValidationRule::new()
            .with_rule_type(ValidationRuleType::Length)
            .with_parameter("min_length", "3")
            .with_parameter("max_length", "10")
            .with_error_message("Invalid length");

        // 有效长度
        let context = ValidationContext::new("valid".to_string(), "test".to_string());
        let result = executor.execute(&rule, &context).await.unwrap();
        assert!(result.is_valid);

        // 太短
        let context = ValidationContext::new("ab".to_string(), "test".to_string());
        let result = executor.execute(&rule, &context).await.unwrap();
        assert!(!result.is_valid);

        // 太长
        let context = ValidationContext::new("very_long_name".to_string(), "test".to_string());
        let result = executor.execute(&rule, &context).await.unwrap();
        assert!(!result.is_valid);
    }

    #[tokio::test]
    async fn test_range_executor() {
        let executor = RangeExecutor;
        let rule = ValidationRule::new()
            .with_rule_type(ValidationRuleType::Range)
            .with_parameter("min_value", "0")
            .with_parameter("max_value", "100")
            .with_error_message("Value out of range");

        // 有效范围
        let context = ValidationContext::new("50".to_string(), "number".to_string());
        let result = executor.execute(&rule, &context).await.unwrap();
        assert!(result.is_valid);

        // 超出范围
        let context = ValidationContext::new("150".to_string(), "number".to_string());
        let result = executor.execute(&rule, &context).await.unwrap();
        assert!(!result.is_valid);
    }

    #[tokio::test]
    async fn test_type_validator() {
        let validator = TypeValidator::new();

        let rule = ValidationRule::new()
            .with_name("material_format")
            .with_expression(r"^[a-z_]+$")
            .with_rule_type(ValidationRuleType::Regex)
            .with_error_message("Material ID must be lowercase with underscores")
            .with_target_type("material");

        validator.add_rule("material", rule).await.unwrap();

        // 有效值
        let result = validator.validate("material", "iron_ore").await.unwrap();
        assert!(result.is_valid);

        // 无效值
        let result = validator.validate("material", "Iron-Ore").await.unwrap();
        assert!(!result.is_valid);
        assert_eq!(result.errors.len(), 1);
    }

    #[tokio::test]
    async fn test_validation_with_context() {
        let validator = TypeValidator::new();

        let rule = ValidationRule::new()
            .with_name("context_rule")
            .with_expression(r"^test_")
            .with_rule_type(ValidationRuleType::Regex)
            .with_error_message("Must start with 'test_'");

        validator.add_rule("test_type", rule).await.unwrap();

        let context = ValidationContext::new("test_value".to_string(), "test_type".to_string())
            .with_parameter("extra_param", "extra_value")
            .with_path(vec!["root".to_string(), "item".to_string()]);

        let result = validator.validate_with_context(&context).await.unwrap();
        assert!(result.is_valid);
    }

    #[tokio::test]
    async fn test_batch_validation() {
        let validator = TypeValidator::new();

        let rule = ValidationRule::new()
            .with_expression(r"^[a-z]+$")
            .with_rule_type(ValidationRuleType::Regex)
            .with_error_message("Must be lowercase letters only");

        validator.add_rule("test", rule).await.unwrap();

        let values = vec![
            "valid".to_string(),
            "Invalid".to_string(),
            "another".to_string(),
        ];
        let results = validator.validate_batch("test", &values).await.unwrap();

        assert_eq!(results.len(), 3);
        assert!(results["valid"].is_valid);
        assert!(!results["Invalid"].is_valid);
        assert!(results["another"].is_valid);
    }

    #[tokio::test]
    async fn test_validation_caching() {
        let validator = TypeValidator::new();

        let rule = ValidationRule::new()
            .with_expression(r"^[a-z]+$")
            .with_rule_type(ValidationRuleType::Regex)
            .with_error_message("Invalid format");

        validator.add_rule("test", rule).await.unwrap();

        // 第一次验证
        let start = Instant::now();
        let result1 = validator.validate("test", "value").await.unwrap();
        let first_duration = start.elapsed();

        // 第二次验证（应该从缓存返回）
        let start = Instant::now();
        let result2 = validator.validate("test", "value").await.unwrap();
        let second_duration = start.elapsed();

        assert_eq!(result1.is_valid, result2.is_valid);
        // 第二次应该更快（从缓存）
        // 注意：在测试环境中这个断言可能不稳定
        // assert!(second_duration < first_duration);
    }

    #[tokio::test]
    async fn test_validator_builder() {
        let rule = ValidationRule::new()
            .with_name("test_rule")
            .with_expression(r"^test")
            .with_rule_type(ValidationRuleType::Regex)
            .with_error_message("Must start with 'test'");

        let validator = TypeValidatorBuilder::new()
            .with_rule("test_type", rule)
            .with_cache_duration(Duration::from_secs(60))
            .build()
            .await
            .unwrap();

        let result = validator.validate("test_type", "test_value").await.unwrap();
        assert!(result.is_valid);

        let result = validator
            .validate("test_type", "invalid_value")
            .await
            .unwrap();
        assert!(!result.is_valid);
    }

    #[tokio::test]
    async fn test_validation_statistics() {
        let validator = TypeValidator::new();

        let rule1 = ValidationRule::new()
            .with_name("rule1")
            .with_expression(".*")
            .with_rule_type(ValidationRuleType::Regex)
            .with_error_message("Error");

        let rule2 = ValidationRule::new()
            .with_name("rule2")
            .with_expression(".*")
            .with_rule_type(ValidationRuleType::Regex)
            .with_error_message("Error")
            .disabled();

        validator.add_rule("type1", rule1).await.unwrap();
        validator.add_rule("type1", rule2).await.unwrap();

        let stats = validator.get_statistics().await;
        assert_eq!(stats.total_types, 1);
        assert_eq!(stats.total_rules, 2);
        assert_eq!(stats.enabled_rules, 1);
        assert_eq!(stats.disabled_rules, 1);
    }
}
