use game::character::domain::character_aggregate::{Character, CharacterBuilder};
use game::character::domain::value_objects::BasicAttributes;
use game::shared::{CharacterId, GameResult, Position};
use game::skill::buff::{Buff, BuffType, TriggerEffect};

fn main() -> GameResult<()> {
    println!("🧪 === 状态效果处理器核心功能测试 ===");
    println!();

    // 测试1: Buff查询功能
    test_buff_queries()?;

    println!();

    // 测试2: Buff移除功能
    test_buff_removal()?;

    println!();

    // 测试3: Buff计数功能
    test_buff_counting()?;

    println!("✅ 状态效果处理器核心功能测试完成！");
    Ok(())
}

fn test_buff_queries() -> GameResult<()> {
    println!("🔍 === 测试1: Buff查询功能 ===");

    let mut character = create_test_character()?;

    // 添加一些测试buff
    let heal_buff = create_heal_buff(1, "持续治疗", 3.0);
    let damage_buff = create_damage_buff(2, "持续伤害", 2.0);
    let shield_buff = create_shield_buff(3, "护盾", 5.0);

    character.add_buff(heal_buff)?;
    character.add_buff(damage_buff)?;
    character.add_buff(shield_buff)?;

    println!("📋 Buff查询结果:");

    let buff_manager = character.buff_manager();

    // 测试ID查询
    println!("   按ID查询:");
    for id in [1, 2, 3, 999] {
        let has_buff = buff_manager.get_buffs().iter().any(|b| b.id == id);
        let status = if has_buff {
            "✅ 存在"
        } else {
            "❌ 不存在"
        };
        println!("     Buff {}: {}", id, status);
    }

    // 测试名称查询
    println!("   按名称查询:");
    for name in ["持续治疗", "持续伤害", "护盾", "不存在的buff"] {
        let has_buff = buff_manager.get_buffs().iter().any(|b| b.name == name);
        let status = if has_buff {
            "✅ 存在"
        } else {
            "❌ 不存在"
        };
        println!("     {}: {}", name, status);
    }

    // 测试类型查询
    println!("   按类型查询:");
    let has_buff_type = !buff_manager.get_buffs_by_type("Buff").is_empty();
    let has_debuff_type = !buff_manager.get_buffs_by_type("Debuff").is_empty();
    println!(
        "     Buff类型: {}",
        if has_buff_type {
            "✅ 存在"
        } else {
            "❌ 不存在"
        }
    );
    println!(
        "     Debuff类型: {}",
        if has_debuff_type {
            "✅ 存在"
        } else {
            "❌ 不存在"
        }
    );

    println!("✅ Buff查询功能测试通过");
    Ok(())
}

fn test_buff_removal() -> GameResult<()> {
    println!("🗑️ === 测试2: Buff移除功能 ===");

    let mut character = create_test_character()?;

    // 添加测试buff
    let heal_buff = create_heal_buff(1, "持续治疗", 3.0);
    let damage_buff = create_damage_buff(2, "持续伤害", 2.0);
    let shield_buff = create_shield_buff(3, "护盾", 5.0);
    let poison_debuff = create_poison_debuff(4, "中毒", 4.0);

    character.add_buff(heal_buff)?;
    character.add_buff(damage_buff)?;
    character.add_buff(shield_buff)?;
    character.add_buff(poison_debuff)?;

    println!(
        "📊 移除前Buff数量: {}",
        character.buff_manager().get_buffs().len()
    );

    // 测试按ID移除
    println!("🎯 按ID移除Buff 1 (持续治疗)");
    character.buff_manager_mut().remove_buff("1");
    println!(
        "   移除后Buff数量: {}",
        character.buff_manager().get_buffs().len()
    );

    // 测试按名称移除
    println!("🎯 按名称移除 '护盾'");
    if let Some(buff_to_remove) = character
        .buff_manager()
        .get_buffs()
        .iter()
        .find(|b| b.name == "护盾")
        .cloned()
    {
        character
            .buff_manager_mut()
            .remove_buff(&buff_to_remove.id.to_string());
    }
    println!(
        "   移除后Buff数量: {}",
        character.buff_manager().get_buffs().len()
    );

    // 测试按类型移除
    println!("🎯 按类型移除所有Debuff");
    character.buff_manager_mut().clear_debuffs();
    println!(
        "   移除后Buff数量: {}",
        character.buff_manager().get_buffs().len()
    );

    println!("✅ Buff移除功能测试通过");
    Ok(())
}

fn test_buff_counting() -> GameResult<()> {
    println!("📊 === 测试3: Buff计数功能 ===");

    let mut character = create_test_character()?;

    // 添加混合类型的buff
    let heal_buff1 = create_heal_buff(1, "治疗1", 3.0);
    let heal_buff2 = create_heal_buff(2, "治疗2", 2.0);
    let poison_debuff1 = create_poison_debuff(3, "中毒1", 4.0);
    let poison_debuff2 = create_poison_debuff(4, "中毒2", 3.0);
    let shield_buff = create_shield_buff(5, "护盾", 5.0);

    character.add_buff(heal_buff1)?;
    character.add_buff(heal_buff2)?;
    character.add_buff(poison_debuff1)?;
    character.add_buff(poison_debuff2)?;
    character.add_buff(shield_buff)?;

    let buff_manager = character.buff_manager();

    println!("📈 Buff计数统计:");
    let total = buff_manager.get_buffs().len();
    let buff_count = buff_manager.get_buffs_by_type("Buff").len();
    let debuff_count = buff_manager.get_buffs_by_type("Debuff").len();

    println!("   总Buff数量: {}", total);
    println!("   Buff类型数量: {}", buff_count);
    println!("   Debuff类型数量: {}", debuff_count);

    println!("🔍 计数验证:");
    println!(
        "   总数 = Buff数 + Debuff数: {} = {} + {} = {}",
        total,
        buff_count,
        debuff_count,
        buff_count + debuff_count
    );

    if total == buff_count + debuff_count {
        println!("   ✅ 计数正确");
    } else {
        println!("   ❌ 计数错误");
    }

    println!("✅ Buff计数功能测试通过");
    Ok(())
}

fn create_test_character() -> GameResult<Character> {
    let attributes = BasicAttributes::new(25, 20, 30);

    CharacterBuilder::new()
        .with_id(CharacterId(1))
        .with_name("测试角色".to_string())
        .with_position(Position::new(0.0, 0.0))
        .with_attributes(attributes)
        .build()
}

fn create_heal_buff(id: u32, name: &str, duration: f32) -> Buff {
    let mut buff = Buff::new(id, name.to_string(), duration, BuffType::Buff);
    buff.trigger_effect = Some(TriggerEffect::Heal(10.0));
    buff
}

fn create_damage_buff(id: u32, name: &str, duration: f32) -> Buff {
    let mut buff = Buff::new(id, name.to_string(), duration, BuffType::Buff);
    buff.trigger_effect = Some(TriggerEffect::Damage(5.0));
    buff
}

fn create_shield_buff(id: u32, name: &str, duration: f32) -> Buff {
    let mut buff = Buff::new(id, name.to_string(), duration, BuffType::Buff);
    buff.trigger_effect = Some(TriggerEffect::GainShield(20.0));
    buff
}

fn create_poison_debuff(id: u32, name: &str, duration: f32) -> Buff {
    let mut buff = Buff::new(id, name.to_string(), duration, BuffType::Debuff);
    buff.trigger_effect = Some(TriggerEffect::Poison(3.0, duration));
    buff.max_stack = 3;
    buff
}
