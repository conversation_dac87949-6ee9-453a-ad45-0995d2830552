use crate::shared::types::*;
use chrono::{DateTime, Utc};
/// 成就系统 - 处理战斗成就、探索成就和修炼成就
/// 基于修仙世界观设计的成就体系
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// ============================================================================
// 成就系统核心
// ============================================================================

/// 成就系统管理器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AchievementSystem {
    /// 成就定义
    pub achievements: HashMap<String, Achievement>,
    /// 角色成就进度
    pub character_progress: HashMap<ID, CharacterAchievements>,
    /// 成就分类配置
    pub categories: HashMap<AchievementCategory, CategoryConfig>,
}

/// 成就定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Achievement {
    /// 成就ID
    pub id: String,
    /// 成就名称
    pub name: String,
    /// 成就描述
    pub description: String,
    /// 成就分类
    pub category: AchievementCategory,
    /// 成就类型
    pub achievement_type: AchievementType,
    /// 完成条件
    pub requirements: AchievementRequirement,
    /// 奖励
    pub rewards: AchievementReward,
    /// 稀有度
    pub rarity: AchievementRarity,
    /// 是否隐藏
    pub hidden: bool,
    /// 前置成就
    pub prerequisites: Vec<String>,
}

/// 成就分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AchievementCategory {
    /// 战斗成就
    Combat,
    /// 探索成就
    Exploration,
    /// 修炼成就
    Cultivation,
    /// 炼丹成就
    Alchemy,
    /// 炼器成就
    Crafting,
    /// 社交成就
    Social,
    /// 收集成就
    Collection,
    /// 特殊成就
    Special,
}

impl AchievementCategory {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            AchievementCategory::Combat => "战斗",
            AchievementCategory::Exploration => "探索",
            AchievementCategory::Cultivation => "修炼",
            AchievementCategory::Alchemy => "炼丹",
            AchievementCategory::Crafting => "炼器",
            AchievementCategory::Social => "社交",
            AchievementCategory::Collection => "收集",
            AchievementCategory::Special => "特殊",
        }
    }
}

/// 成就类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AchievementType {
    /// 计数型（达到指定数量）
    Counter,
    /// 里程碑型（达到某个状态）
    Milestone,
    /// 挑战型（完成特定挑战）
    Challenge,
    /// 收集型（收集指定物品）
    Collection,
    /// 连续型（连续完成某事）
    Streak,
}

/// 成就稀有度
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum AchievementRarity {
    /// 普通（1星）
    Common,
    /// 稀有（2星）
    Rare,
    /// 史诗（3星）
    Epic,
    /// 传说（4星）
    Legendary,
    /// 神话（5星）
    Mythic,
}

impl AchievementRarity {
    pub fn chinese_name(&self) -> &'static str {
        match self {
            AchievementRarity::Common => "普通",
            AchievementRarity::Rare => "稀有",
            AchievementRarity::Epic => "史诗",
            AchievementRarity::Legendary => "传说",
            AchievementRarity::Mythic => "神话",
        }
    }

    pub fn star_count(&self) -> u8 {
        match self {
            AchievementRarity::Common => 1,
            AchievementRarity::Rare => 2,
            AchievementRarity::Epic => 3,
            AchievementRarity::Legendary => 4,
            AchievementRarity::Mythic => 5,
        }
    }

    pub fn color_code(&self) -> &'static str {
        match self {
            AchievementRarity::Common => "#FFFFFF",    // 白色
            AchievementRarity::Rare => "#00FF00",      // 绿色
            AchievementRarity::Epic => "#0080FF",      // 蓝色
            AchievementRarity::Legendary => "#8000FF", // 紫色
            AchievementRarity::Mythic => "#FF8000",    // 橙色
        }
    }
}

/// 成就要求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AchievementRequirement {
    /// 击败怪物数量
    DefeatMonsters {
        count: u32,
        monster_type: Option<String>,
        min_level: Option<Level>,
    },
    /// 达到等级
    ReachLevel(Level),
    /// 达到境界
    ReachRealm(String),
    /// 探索区域数量
    ExploreAreas(u32),
    /// 发现秘密数量
    DiscoverSecrets(u32),
    /// 连续胜利
    WinStreak(u32),
    /// 收集材料
    CollectMaterials { material_type: String, count: u32 },
    /// 合成物品
    CraftItems {
        item_type: String,
        count: u32,
        min_quality: Option<String>,
    },
    /// 完成任务
    CompleteQuests(u32),
    /// 组合要求（同时满足多个条件）
    Combined(Vec<AchievementRequirement>),
    /// 自定义条件
    Custom {
        condition_type: String,
        target_value: f64,
        current_value: f64,
    },
}

/// 成就奖励
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AchievementReward {
    /// 经验值奖励
    pub experience: Exp,
    /// 称号
    pub title: Option<String>,
    /// 物品奖励
    pub items: Vec<ItemReward>,
    /// 属性奖励
    pub attributes: AttributeReward,
    /// 特殊奖励
    pub special_rewards: Vec<String>,
}

/// 物品奖励
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ItemReward {
    /// 物品ID
    pub item_id: String,
    /// 数量
    pub quantity: u32,
    /// 品质
    pub quality: Option<String>,
}

/// 属性奖励
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct AttributeReward {
    /// 生命值奖励
    pub hp_bonus: Health,
    /// 法力值奖励
    pub mana_bonus: Mana,
    /// 攻击力奖励
    pub attack_bonus: Attack,
    /// 防御力奖励
    pub defense_bonus: Defense,
    /// 速度奖励
    pub speed_bonus: i32,
    /// 永久属性点
    pub permanent_attribute_points: u32,
}

/// 角色成就数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterAchievements {
    /// 角色ID
    pub character_id: ID,
    /// 已完成的成就
    pub completed_achievements: HashMap<String, CompletedAchievement>,
    /// 进行中的成就进度
    pub progress: HashMap<String, AchievementProgress>,
    /// 成就统计
    pub statistics: AchievementStatistics,
}

/// 已完成的成就
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletedAchievement {
    /// 成就ID
    pub achievement_id: String,
    /// 完成时间
    pub completed_at: DateTime<Utc>,
    /// 完成时等级
    pub level_when_completed: Level,
    /// 是否已领取奖励
    pub rewards_claimed: bool,
}

/// 成就进度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AchievementProgress {
    /// 成就ID
    pub achievement_id: String,
    /// 当前进度值
    pub current_value: f64,
    /// 目标值
    pub target_value: f64,
    /// 进度百分比（0.0-1.0）
    pub progress_percentage: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

/// 成就统计
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct AchievementStatistics {
    /// 总完成成就数
    pub total_completed: u32,
    /// 按分类的完成数
    pub completed_by_category: HashMap<AchievementCategory, u32>,
    /// 按稀有度的完成数
    pub completed_by_rarity: HashMap<AchievementRarity, u32>,
    /// 总成就点数
    pub total_achievement_points: u32,
    /// 获得的称号数量
    pub titles_earned: u32,
}

/// 分类配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CategoryConfig {
    /// 分类名称
    pub name: String,
    /// 分类描述
    pub description: String,
    /// 分类图标
    pub icon: String,
    /// 是否启用
    pub enabled: bool,
}

impl AchievementSystem {
    /// 创建默认成就系统
    pub fn new() -> Self {
        let mut system = Self {
            achievements: HashMap::new(),
            character_progress: HashMap::new(),
            categories: Self::create_default_categories(),
        };

        // 添加默认成就
        system.add_default_achievements();
        system
    }

    /// 创建默认分类配置
    fn create_default_categories() -> HashMap<AchievementCategory, CategoryConfig> {
        let mut categories = HashMap::new();

        categories.insert(
            AchievementCategory::Combat,
            CategoryConfig {
                name: "战斗成就".to_string(),
                description: "在战斗中证明自己的实力".to_string(),
                icon: "⚔️".to_string(),
                enabled: true,
            },
        );

        categories.insert(
            AchievementCategory::Exploration,
            CategoryConfig {
                name: "探索成就".to_string(),
                description: "探索未知的世界和秘密".to_string(),
                icon: "🗺️".to_string(),
                enabled: true,
            },
        );

        categories.insert(
            AchievementCategory::Cultivation,
            CategoryConfig {
                name: "修炼成就".to_string(),
                description: "在修炼之路上的里程碑".to_string(),
                icon: "🧘".to_string(),
                enabled: true,
            },
        );

        categories.insert(
            AchievementCategory::Alchemy,
            CategoryConfig {
                name: "炼丹成就".to_string(),
                description: "在炼丹术上的造诣".to_string(),
                icon: "⚗️".to_string(),
                enabled: true,
            },
        );

        categories.insert(
            AchievementCategory::Crafting,
            CategoryConfig {
                name: "炼器成就".to_string(),
                description: "在炼器术上的成就".to_string(),
                icon: "🔨".to_string(),
                enabled: true,
            },
        );

        categories.insert(
            AchievementCategory::Collection,
            CategoryConfig {
                name: "收集成就".to_string(),
                description: "收集珍稀材料和物品".to_string(),
                icon: "📦".to_string(),
                enabled: true,
            },
        );

        categories
    }

    /// 添加默认成就
    fn add_default_achievements(&mut self) {
        // 战斗成就
        self.add_achievement(Achievement {
            id: "first_blood".to_string(),
            name: "初出茅庐".to_string(),
            description: "击败你的第一个敌人".to_string(),
            category: AchievementCategory::Combat,
            achievement_type: AchievementType::Milestone,
            requirements: AchievementRequirement::DefeatMonsters {
                count: 1,
                monster_type: None,
                min_level: None,
            },
            rewards: AchievementReward {
                experience: 100,
                title: Some("新手战士".to_string()),
                items: Vec::new(),
                attributes: AttributeReward {
                    hp_bonus: 0,
                    mana_bonus: 0,
                    attack_bonus: 5,
                    defense_bonus: 0,
                    speed_bonus: 0,
                    permanent_attribute_points: 0,
                },
                special_rewards: Vec::new(),
            },
            rarity: AchievementRarity::Common,
            hidden: false,
            prerequisites: Vec::new(),
        });

        self.add_achievement(Achievement {
            id: "monster_slayer".to_string(),
            name: "妖兽杀手".to_string(),
            description: "击败100只妖兽".to_string(),
            category: AchievementCategory::Combat,
            achievement_type: AchievementType::Counter,
            requirements: AchievementRequirement::DefeatMonsters {
                count: 100,
                monster_type: Some("妖兽".to_string()),
                min_level: None,
            },
            rewards: AchievementReward {
                experience: 5000,
                title: Some("妖兽克星".to_string()),
                items: vec![ItemReward {
                    item_id: "demon_slayer_sword".to_string(),
                    quantity: 1,
                    quality: Some("精良".to_string()),
                }],
                attributes: AttributeReward {
                    hp_bonus: 100,
                    mana_bonus: 0,
                    attack_bonus: 20,
                    defense_bonus: 0,
                    speed_bonus: 0,
                    permanent_attribute_points: 0,
                },
                special_rewards: vec!["对妖兽伤害提升10%".to_string()],
            },
            rarity: AchievementRarity::Rare,
            hidden: false,
            prerequisites: vec!["first_blood".to_string()],
        });

        // 修炼成就
        self.add_achievement(Achievement {
            id: "qi_condensation".to_string(),
            name: "踏入仙途".to_string(),
            description: "突破至炼气期".to_string(),
            category: AchievementCategory::Cultivation,
            achievement_type: AchievementType::Milestone,
            requirements: AchievementRequirement::ReachRealm("炼气期".to_string()),
            rewards: AchievementReward {
                experience: 2000,
                title: Some("炼气期修士".to_string()),
                items: vec![ItemReward {
                    item_id: "qi_condensation_pill".to_string(),
                    quantity: 5,
                    quality: None,
                }],
                attributes: AttributeReward {
                    hp_bonus: 0,
                    mana_bonus: 200,
                    attack_bonus: 0,
                    defense_bonus: 0,
                    speed_bonus: 0,
                    permanent_attribute_points: 10,
                },
                special_rewards: vec!["法力回复速度提升20%".to_string()],
            },
            rarity: AchievementRarity::Epic,
            hidden: false,
            prerequisites: Vec::new(),
        });

        // 探索成就
        self.add_achievement(Achievement {
            id: "explorer".to_string(),
            name: "天下行者".to_string(),
            description: "探索50个不同的区域".to_string(),
            category: AchievementCategory::Exploration,
            achievement_type: AchievementType::Counter,
            requirements: AchievementRequirement::ExploreAreas(50),
            rewards: AchievementReward {
                experience: 10000,
                title: Some("天下行者".to_string()),
                items: vec![ItemReward {
                    item_id: "explorer_boots".to_string(),
                    quantity: 1,
                    quality: Some("精良".to_string()),
                }],
                attributes: AttributeReward {
                    hp_bonus: 0,
                    mana_bonus: 0,
                    attack_bonus: 0,
                    defense_bonus: 0,
                    speed_bonus: 50,
                    permanent_attribute_points: 0,
                },
                special_rewards: vec!["移动速度提升30%".to_string()],
            },
            rarity: AchievementRarity::Rare,
            hidden: false,
            prerequisites: Vec::new(),
        });

        // 挑战成就
        self.add_achievement(Achievement {
            id: "undefeated".to_string(),
            name: "不败战神".to_string(),
            description: "连续获得50场战斗胜利".to_string(),
            category: AchievementCategory::Combat,
            achievement_type: AchievementType::Streak,
            requirements: AchievementRequirement::WinStreak(50),
            rewards: AchievementReward {
                experience: 25000,
                title: Some("不败战神".to_string()),
                items: vec![ItemReward {
                    item_id: "victory_crown".to_string(),
                    quantity: 1,
                    quality: Some("传说".to_string()),
                }],
                attributes: AttributeReward {
                    hp_bonus: 500,
                    mana_bonus: 0,
                    attack_bonus: 50,
                    defense_bonus: 30,
                    speed_bonus: 0,
                    permanent_attribute_points: 20,
                },
                special_rewards: vec![
                    "战斗开始时获得'战神之怒'状态".to_string(),
                    "所有伤害提升25%".to_string(),
                ],
            },
            rarity: AchievementRarity::Legendary,
            hidden: true,
            prerequisites: vec!["monster_slayer".to_string()],
        });
    }

    /// 添加成就
    pub fn add_achievement(&mut self, achievement: Achievement) {
        self.achievements
            .insert(achievement.id.clone(), achievement);
    }

    /// 为角色初始化成就进度
    pub fn initialize_character(&mut self, character_id: ID) {
        if !self.character_progress.contains_key(&character_id) {
            let mut progress = HashMap::new();

            // 为所有未隐藏的成就初始化进度
            for (achievement_id, achievement) in &self.achievements {
                if !achievement.hidden {
                    progress.insert(
                        achievement_id.clone(),
                        AchievementProgress {
                            achievement_id: achievement_id.clone(),
                            current_value: 0.0,
                            target_value: self.get_target_value(&achievement.requirements),
                            progress_percentage: 0.0,
                            last_updated: Utc::now(),
                        },
                    );
                }
            }

            self.character_progress.insert(
                character_id,
                CharacterAchievements {
                    character_id,
                    completed_achievements: HashMap::new(),
                    progress,
                    statistics: AchievementStatistics::default(),
                },
            );
        }
    }

    /// 获取目标值
    fn get_target_value(&self, requirement: &AchievementRequirement) -> f64 {
        match requirement {
            AchievementRequirement::DefeatMonsters { count, .. } => *count as f64,
            AchievementRequirement::ReachLevel(level) => *level as f64,
            AchievementRequirement::ExploreAreas(count) => *count as f64,
            AchievementRequirement::DiscoverSecrets(count) => *count as f64,
            AchievementRequirement::WinStreak(count) => *count as f64,
            AchievementRequirement::CollectMaterials { count, .. } => *count as f64,
            AchievementRequirement::CraftItems { count, .. } => *count as f64,
            AchievementRequirement::CompleteQuests(count) => *count as f64,
            AchievementRequirement::Custom { target_value, .. } => *target_value,
            AchievementRequirement::Combined(_) => 1.0, // 组合条件目标值为1
            _ => 1.0,
        }
    }

    /// 更新角色成就进度
    pub fn update_progress(
        &mut self,
        character_id: ID,
        event_type: &str,
        value: f64,
        context: &HashMap<String, String>,
    ) -> Vec<String> {
        let mut completed_achievements = Vec::new();
        let mut updates = Vec::new();

        // 首先收集需要更新的成就
        if let Some(character_achievements) = self.character_progress.get(&character_id) {
            for (achievement_id, progress) in &character_achievements.progress {
                if let Some(achievement) = self.achievements.get(achievement_id) {
                    if self.is_relevant_event(&achievement.requirements, event_type, context) {
                        let old_value = progress.current_value;
                        let new_value = self.calculate_new_value(
                            &achievement.requirements,
                            progress.current_value,
                            value,
                            context,
                        );

                        updates.push((
                            achievement_id.clone(),
                            old_value,
                            new_value,
                            progress.target_value,
                        ));
                    }
                }
            }
        }

        // 然后应用更新
        if let Some(character_achievements) = self.character_progress.get_mut(&character_id) {
            let mut to_check = Vec::new();

            for (achievement_id, old_value, new_value, target_value) in updates {
                if let Some(progress) = character_achievements.progress.get_mut(&achievement_id) {
                    progress.current_value = new_value;
                    progress.progress_percentage = (new_value / target_value).min(1.0);
                    progress.last_updated = Utc::now();

                    // 检查是否完成
                    if new_value >= target_value && old_value < target_value {
                        to_check.push(achievement_id);
                    }
                }
            }

            // 处理完成的成就
            for achievement_id in to_check {
                if self.check_prerequisites(&achievement_id, character_id) {
                    self.complete_achievement(character_id, &achievement_id);
                    completed_achievements.push(achievement_id);
                }
            }
        }

        completed_achievements
    }

    /// 检查事件是否与成就相关
    fn is_relevant_event(
        &self,
        requirement: &AchievementRequirement,
        event_type: &str,
        context: &HashMap<String, String>,
    ) -> bool {
        match requirement {
            AchievementRequirement::DefeatMonsters { monster_type, .. } => {
                event_type == "defeat_monster"
                    && (monster_type.is_none()
                        || context
                            .get("monster_type")
                            .map_or(false, |t| Some(t) == monster_type.as_ref()))
            }
            AchievementRequirement::ReachLevel(_) => event_type == "level_up",
            AchievementRequirement::ReachRealm(_) => event_type == "realm_breakthrough",
            AchievementRequirement::ExploreAreas(_) => event_type == "explore_area",
            AchievementRequirement::DiscoverSecrets(_) => event_type == "discover_secret",
            AchievementRequirement::WinStreak(_) => {
                event_type == "battle_victory" || event_type == "battle_defeat"
            }
            AchievementRequirement::CollectMaterials { material_type, .. } => {
                event_type == "collect_material"
                    && context
                        .get("material_type")
                        .map_or(false, |t| t == material_type)
            }
            AchievementRequirement::CraftItems { item_type, .. } => {
                event_type == "craft_item"
                    && context.get("item_type").map_or(false, |t| t == item_type)
            }
            AchievementRequirement::CompleteQuests(_) => event_type == "complete_quest",
            AchievementRequirement::Custom { condition_type, .. } => event_type == condition_type,
            AchievementRequirement::Combined(requirements) => requirements
                .iter()
                .any(|req| self.is_relevant_event(req, event_type, context)),
        }
    }

    /// 计算新的进度值
    fn calculate_new_value(
        &self,
        requirement: &AchievementRequirement,
        current_value: f64,
        event_value: f64,
        context: &HashMap<String, String>,
    ) -> f64 {
        match requirement {
            AchievementRequirement::DefeatMonsters { .. }
            | AchievementRequirement::ExploreAreas(_)
            | AchievementRequirement::DiscoverSecrets(_)
            | AchievementRequirement::CollectMaterials { .. }
            | AchievementRequirement::CraftItems { .. }
            | AchievementRequirement::CompleteQuests(_) => current_value + event_value,
            AchievementRequirement::ReachLevel(_) | AchievementRequirement::Custom { .. } => {
                event_value
            }
            AchievementRequirement::ReachRealm(target_realm) => {
                if context.get("realm").map_or(false, |r| r == target_realm) {
                    1.0
                } else {
                    current_value
                }
            }
            AchievementRequirement::WinStreak(_) => {
                if event_value > 0.0 {
                    // 胜利
                    current_value + 1.0
                } else {
                    // 失败
                    0.0 // 重置连胜
                }
            }
            AchievementRequirement::Combined(_) => {
                // 组合条件需要特殊处理
                current_value
            }
        }
    }

    /// 检查前置条件
    fn check_prerequisites(&self, achievement_id: &str, character_id: ID) -> bool {
        if let Some(achievement) = self.achievements.get(achievement_id) {
            if let Some(character_achievements) = self.character_progress.get(&character_id) {
                return achievement.prerequisites.iter().all(|prereq_id| {
                    character_achievements
                        .completed_achievements
                        .contains_key(prereq_id)
                });
            }
        }
        false
    }

    /// 完成成就
    fn complete_achievement(&mut self, character_id: ID, achievement_id: &str) {
        // 首先获取成就信息
        let achievement_info = if let Some(achievement) = self.achievements.get(achievement_id) {
            Some((
                achievement.category,
                achievement.rarity,
                achievement.rewards.title.is_some(),
            ))
        } else {
            None
        };

        // 收集需要添加的隐藏成就
        let mut hidden_to_add = Vec::new();
        if let Some(character_achievements) = self.character_progress.get(&character_id) {
            for (hidden_achievement_id, hidden_achievement) in &self.achievements {
                if hidden_achievement.hidden
                    && hidden_achievement
                        .prerequisites
                        .contains(&achievement_id.to_string())
                    && !character_achievements
                        .progress
                        .contains_key(hidden_achievement_id)
                    && !character_achievements
                        .completed_achievements
                        .contains_key(hidden_achievement_id)
                {
                    let target_value = self.get_target_value(&hidden_achievement.requirements);
                    hidden_to_add.push((hidden_achievement_id.clone(), target_value));
                }
            }
        }

        // 然后更新角色成就数据
        if let Some(character_achievements) = self.character_progress.get_mut(&character_id) {
            if let Some((category, rarity, has_title)) = achievement_info {
                let completed = CompletedAchievement {
                    achievement_id: achievement_id.to_string(),
                    completed_at: Utc::now(),
                    level_when_completed: 1, // 需要从外部传入当前等级
                    rewards_claimed: false,
                };

                character_achievements
                    .completed_achievements
                    .insert(achievement_id.to_string(), completed);
                character_achievements.progress.remove(achievement_id);

                // 更新统计
                character_achievements.statistics.total_completed += 1;
                *character_achievements
                    .statistics
                    .completed_by_category
                    .entry(category)
                    .or_insert(0) += 1;
                *character_achievements
                    .statistics
                    .completed_by_rarity
                    .entry(rarity)
                    .or_insert(0) += 1;

                if has_title {
                    character_achievements.statistics.titles_earned += 1;
                }

                // 添加隐藏成就到进度跟踪
                for (hidden_achievement_id, target_value) in hidden_to_add {
                    character_achievements.progress.insert(
                        hidden_achievement_id.clone(),
                        AchievementProgress {
                            achievement_id: hidden_achievement_id,
                            current_value: 0.0,
                            target_value,
                            progress_percentage: 0.0,
                            last_updated: Utc::now(),
                        },
                    );
                }
            }
        }
    }

    /// 获取角色成就概览
    pub fn get_character_achievements(&self, character_id: ID) -> Option<&CharacterAchievements> {
        self.character_progress.get(&character_id)
    }

    /// 获取可领取奖励的成就
    pub fn get_claimable_rewards(&self, character_id: ID) -> Vec<String> {
        if let Some(character_achievements) = self.character_progress.get(&character_id) {
            character_achievements
                .completed_achievements
                .iter()
                .filter(|(_, completed)| !completed.rewards_claimed)
                .map(|(achievement_id, _)| achievement_id.clone())
                .collect()
        } else {
            Vec::new()
        }
    }

    /// 领取成就奖励
    pub fn claim_achievement_reward(
        &mut self,
        character_id: ID,
        achievement_id: &str,
    ) -> Option<AchievementReward> {
        if let Some(character_achievements) = self.character_progress.get_mut(&character_id) {
            if let Some(completed) = character_achievements
                .completed_achievements
                .get_mut(achievement_id)
            {
                if !completed.rewards_claimed {
                    completed.rewards_claimed = true;
                    return self
                        .achievements
                        .get(achievement_id)
                        .map(|a| a.rewards.clone());
                }
            }
        }
        None
    }
}

impl Default for AchievementSystem {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 便利函数
// ============================================================================

/// 创建简单的战斗成就进度更新
pub fn update_combat_achievement(
    achievement_system: &mut AchievementSystem,
    character_id: ID,
    monster_type: &str,
    monster_level: Level,
) -> Vec<String> {
    let mut context = HashMap::new();
    context.insert("monster_type".to_string(), monster_type.to_string());
    context.insert("monster_level".to_string(), monster_level.to_string());

    achievement_system.update_progress(character_id, "defeat_monster", 1.0, &context)
}

/// 创建简单的等级成就进度更新
pub fn update_level_achievement(
    achievement_system: &mut AchievementSystem,
    character_id: ID,
    new_level: Level,
) -> Vec<String> {
    let context = HashMap::new();
    achievement_system.update_progress(character_id, "level_up", new_level as f64, &context)
}
