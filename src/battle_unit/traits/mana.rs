/// 法力系统trait
/// 
/// 管理法力值和法力相关操作

use crate::shared::*;

/// 法力系统 - 管理法力值和法力相关操作
pub trait ManaSystem {
    /// 获取当前法力值
    fn current_mana(&self) -> Man<PERSON>;
    
    /// 获取最大法力值
    fn max_mana(&self) -> Mana;
    
    /// 获取基础法力值
    fn base_mana(&self) -> Mana;
    
    /// 获取法力值加成
    fn mana_bonus(&self) -> Mana;
    
    /// 获取法力恢复速度（每秒）
    fn mana_regeneration_rate(&self) -> Mana;
    
    /// 获取法力值百分比
    fn mana_percentage(&self) -> f32;
    
    /// 消耗法力
    fn consume_mana(&mut self, amount: Mana) -> GameResult<()>;
    
    /// 恢复法力
    fn restore_mana(&mut self, amount: Mana) -> GameResult<()>;
    
    /// 设置法力值
    fn set_mana(&mut self, mana: Mana) -> GameResult<()>;
    
    /// 检查是否有足够法力
    fn has_enough_mana(&self, required: Mana) -> bool;
    
    /// 计算法力消耗减免
    fn mana_cost_reduction(&self) -> f32;
}