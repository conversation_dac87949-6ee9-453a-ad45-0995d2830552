/// 统一事件系统
///
/// 此模块增强了现有的事件总线，添加了领域事件支持，
/// 实现了事件溯源和事件处理器模式
use crate::shared::types::*;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// ============================================================================
// 领域事件接口
// ============================================================================

/// 领域事件 trait - 所有领域事件必须实现
/// 注意：移除Clone约束以支持object safety
pub trait DomainEvent: Send + Sync + 'static {
    /// 事件唯一标识
    fn event_id(&self) -> EventId;

    /// 聚合根标识
    fn aggregate_id(&self) -> AggregateId;

    /// 事件发生时间
    fn occurred_at(&self) -> DateTime<Utc>;

    /// 事件版本（用于事件溯源）
    fn version(&self) -> u64;

    /// 事件类型名称
    fn event_type(&self) -> &'static str;

    /// 事件的业务描述
    fn description(&self) -> String;

    /// 克隆事件 - 由于object safety需要，单独提供
    fn clone_event(&self) -> Box<dyn DomainEvent>;
}

/// 事件ID类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct EventId(pub Uuid);

impl EventId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
}

impl Default for EventId {
    fn default() -> Self {
        Self::new()
    }
}

/// 聚合根ID类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AggregateId {
    Character(CharacterId),
    Monster(MonsterId),
    Material(MaterialId),
    Equipment(EquipmentId),
    Battle(BattleId),
    Skill(SkillId),
}

// ============================================================================
// 具体领域事件定义
// ============================================================================

/// 角色相关事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterLeveledUp {
    pub event_id: EventId,
    pub character_id: CharacterId,
    pub old_level: Level,
    pub new_level: Level,
    pub gained_points: u32,
    pub occurred_at: DateTime<Utc>,
    pub version: u64,
}

impl DomainEvent for CharacterLeveledUp {
    fn event_id(&self) -> EventId {
        self.event_id
    }
    fn aggregate_id(&self) -> AggregateId {
        AggregateId::Character(self.character_id)
    }
    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }
    fn version(&self) -> u64 {
        self.version
    }
    fn event_type(&self) -> &'static str {
        "character.leveled_up"
    }
    fn description(&self) -> String {
        format!(
            "角色 {:?} 从等级 {} 升级到 {}",
            self.character_id, self.old_level, self.new_level
        )
    }
    fn clone_event(&self) -> Box<dyn DomainEvent> {
        Box::new(self.clone())
    }
}

/// 角色经验获得事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterExperienceGained {
    pub event_id: EventId,
    pub character_id: CharacterId,
    pub gained_exp: Exp,
    pub total_exp: Exp,
    pub source: ExperienceSource,
    pub occurred_at: DateTime<Utc>,
    pub version: u64,
}

impl DomainEvent for CharacterExperienceGained {
    fn event_id(&self) -> EventId {
        self.event_id
    }
    fn aggregate_id(&self) -> AggregateId {
        AggregateId::Character(self.character_id)
    }
    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }
    fn version(&self) -> u64 {
        self.version
    }
    fn event_type(&self) -> &'static str {
        "character.experience_gained"
    }
    fn description(&self) -> String {
        format!(
            "角色 {:?} 获得 {} 经验值，来源: {:?}",
            self.character_id, self.gained_exp, self.source
        )
    }
    fn clone_event(&self) -> Box<dyn DomainEvent> {
        Box::new(self.clone())
    }
}

/// 经验来源
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExperienceSource {
    Combat { enemy_id: MonsterId },
    Quest { quest_id: String },
    Crafting { item_type: String },
    Discovery { location: Position },
}

/// 战斗相关事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BattleDamageDealt {
    pub event_id: EventId,
    pub battle_id: BattleId,
    pub source: BattleUnitId,
    pub target: BattleUnitId,
    pub damage_amount: Health,
    pub damage_type: DamageType,
    pub skill_id: Option<SkillId>,
    pub occurred_at: DateTime<Utc>,
    pub version: u64,
}

impl DomainEvent for BattleDamageDealt {
    fn event_id(&self) -> EventId {
        self.event_id
    }
    fn aggregate_id(&self) -> AggregateId {
        AggregateId::Battle(self.battle_id)
    }
    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }
    fn version(&self) -> u64 {
        self.version
    }
    fn event_type(&self) -> &'static str {
        "battle.damage_dealt"
    }
    fn description(&self) -> String {
        format!(
            "战斗 {:?} 中 {:?} 对 {:?} 造成 {} 点伤害",
            self.battle_id, self.source, self.target, self.damage_amount
        )
    }
    fn clone_event(&self) -> Box<dyn DomainEvent> {
        Box::new(self.clone())
    }
}

/// 战斗开始事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BattleStarted {
    pub event_id: EventId,
    pub battle_id: BattleId,
    pub participants: Vec<BattleUnitId>,
    pub battle_type: BattleType,
    pub location: Position,
    pub occurred_at: DateTime<Utc>,
    pub version: u64,
}

impl DomainEvent for BattleStarted {
    fn event_id(&self) -> EventId {
        self.event_id
    }
    fn aggregate_id(&self) -> AggregateId {
        AggregateId::Battle(self.battle_id)
    }
    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }
    fn version(&self) -> u64 {
        self.version
    }
    fn event_type(&self) -> &'static str {
        "battle.started"
    }
    fn description(&self) -> String {
        format!(
            "战斗 {:?} 开始，参与者: {} 个",
            self.battle_id,
            self.participants.len()
        )
    }
    fn clone_event(&self) -> Box<dyn DomainEvent> {
        Box::new(self.clone())
    }
}

/// 战斗类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BattleType {
    PvE,      // 玩家vs环境
    PvP,      // 玩家vs玩家
    Boss,     // Boss战
    Training, // 训练战
}

/// 技能相关事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillUsed {
    pub event_id: EventId,
    pub caster: BattleUnitId,
    pub skill_id: SkillId,
    pub targets: Vec<BattleUnitId>,
    pub mana_cost: Mana,
    pub success: bool,
    pub failure_reason: Option<String>,
    pub occurred_at: DateTime<Utc>,
    pub version: u64,
}

impl DomainEvent for SkillUsed {
    fn event_id(&self) -> EventId {
        self.event_id
    }
    fn aggregate_id(&self) -> AggregateId {
        AggregateId::Skill(self.skill_id)
    }
    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }
    fn version(&self) -> u64 {
        self.version
    }
    fn event_type(&self) -> &'static str {
        "skill.used"
    }
    fn description(&self) -> String {
        if self.success {
            format!("{:?} 成功使用技能 {:?}", self.caster, self.skill_id)
        } else {
            format!(
                "{:?} 使用技能 {:?} 失败: {:?}",
                self.caster, self.skill_id, self.failure_reason
            )
        }
    }
    fn clone_event(&self) -> Box<dyn DomainEvent> {
        Box::new(self.clone())
    }
}

/// 材料相关事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaterialCreated {
    pub event_id: EventId,
    pub material_id: MaterialId,
    pub material_name: String,
    pub grade: MaterialGrade,
    pub creator: Option<CharacterId>,
    pub creation_method: CreationMethod,
    pub occurred_at: DateTime<Utc>,
    pub version: u64,
}

impl DomainEvent for MaterialCreated {
    fn event_id(&self) -> EventId {
        self.event_id
    }
    fn aggregate_id(&self) -> AggregateId {
        AggregateId::Material(self.material_id)
    }
    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }
    fn version(&self) -> u64 {
        self.version
    }
    fn event_type(&self) -> &'static str {
        "material.created"
    }
    fn description(&self) -> String {
        format!("材料 {} ({:?}) 被创建", self.material_name, self.grade)
    }
    fn clone_event(&self) -> Box<dyn DomainEvent> {
        Box::new(self.clone())
    }
}

/// 材料创建方式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CreationMethod {
    Crafting { recipe_id: String },
    Drop { monster_id: MonsterId },
    Mining { location: Position },
    Synthesis { base_materials: Vec<MaterialId> },
}

// ============================================================================
// 事件元数据
// ============================================================================

/// 事件元数据，包含事件的上下文信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventMetadata {
    pub event_id: EventId,
    pub event_type: String,
    pub aggregate_id: AggregateId,
    pub aggregate_type: String,
    pub version: u64,
    pub occurred_at: DateTime<Utc>,
    pub causation_id: Option<EventId>, // 引起此事件的事件ID
    pub correlation_id: Option<Uuid>,  // 关联ID，用于追踪业务流程
    pub user_id: Option<CharacterId>,
    pub session_id: Option<String>,
}

/// 事件包装器，包含事件和元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventEnvelope<T: DomainEvent> {
    pub metadata: EventMetadata,
    pub payload: T,
}

// ============================================================================
// 事件存储接口
// ============================================================================

/// 事件存储 trait，支持事件溯源
pub trait EventStore: Send + Sync {
    type Error: std::error::Error + Send + Sync + 'static;

    /// 保存事件
    async fn save_events(
        &self,
        aggregate_id: &AggregateId,
        events: Vec<Box<dyn DomainEvent>>,
        expected_version: Option<u64>,
    ) -> Result<(), Self::Error>;

    /// 加载聚合的所有事件
    async fn load_events(
        &self,
        aggregate_id: &AggregateId,
        from_version: Option<u64>,
    ) -> Result<Vec<Box<dyn DomainEvent>>, Self::Error>;

    /// 加载指定类型的事件
    async fn load_events_by_type(
        &self,
        event_type: &str,
        from: Option<DateTime<Utc>>,
        to: Option<DateTime<Utc>>,
    ) -> Result<Vec<Box<dyn DomainEvent>>, Self::Error>;
}

// ============================================================================
// 事件处理器
// ============================================================================

/// 简化的事件处理器错误类型
#[derive(Debug, thiserror::Error)]
pub enum EventHandlerError {
    #[error("事件处理失败: {message}")]
    ProcessingFailed { message: String },
    #[error("事件处理器内部错误: {source}")]
    InternalError {
        source: Box<dyn std::error::Error + Send + Sync>,
    },
}

/// 事件处理器 trait
#[async_trait::async_trait]
pub trait EventHandler<T>: Send + Sync
where
    T: DomainEvent + Clone,
{
    /// 处理事件
    async fn handle(&self, event: &T) -> Result<(), EventHandlerError>;

    /// 获取处理器名称
    fn name(&self) -> &'static str;

    /// 获取处理器版本
    fn version(&self) -> u32 {
        1
    }
}

/// 简化的事件分发器
pub struct EventDispatcher {
    // 由于trait object的复杂性，暂时简化实现
}

impl EventDispatcher {
    pub fn new() -> Self {
        Self {}
    }
}

impl Default for EventDispatcher {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 事件构建器
// ============================================================================

/// 事件构建器，简化事件创建过程
pub struct EventBuilder;

impl EventBuilder {
    /// 创建角色升级事件
    pub fn character_leveled_up(
        character_id: CharacterId,
        old_level: Level,
        new_level: Level,
        gained_points: u32,
        version: u64,
    ) -> CharacterLeveledUp {
        CharacterLeveledUp {
            event_id: EventId::new(),
            character_id,
            old_level,
            new_level,
            gained_points,
            occurred_at: Utc::now(),
            version,
        }
    }

    /// 创建战斗伤害事件
    pub fn battle_damage_dealt(
        battle_id: BattleId,
        source: BattleUnitId,
        target: BattleUnitId,
        damage_amount: Health,
        damage_type: DamageType,
        skill_id: Option<SkillId>,
        version: u64,
    ) -> BattleDamageDealt {
        BattleDamageDealt {
            event_id: EventId::new(),
            battle_id,
            source,
            target,
            damage_amount,
            damage_type,
            skill_id,
            occurred_at: Utc::now(),
            version,
        }
    }

    /// 创建技能使用事件
    pub fn skill_used(
        caster: BattleUnitId,
        skill_id: SkillId,
        targets: Vec<BattleUnitId>,
        mana_cost: Mana,
        success: bool,
        failure_reason: Option<String>,
        version: u64,
    ) -> SkillUsed {
        SkillUsed {
            event_id: EventId::new(),
            caster,
            skill_id,
            targets,
            mana_cost,
            success,
            failure_reason,
            occurred_at: Utc::now(),
            version,
        }
    }
}
