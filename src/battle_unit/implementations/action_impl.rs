/// 行动能力trait实现
/// 
/// 实现ActionCapacity trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;
use crate::battle_unit::types::*;
use crate::battle_unit::traits::ActionCapacity;
use crate::attribute::{AttributeType, CoreAttribute, DerivedAttribute};

impl ActionCapacity for BattleUnitAdapter {
    fn action_points(&self) -> ActionPoints {
        self.current_action_points
    }
    
    fn max_action_points(&self) -> ActionPoints {
        // 基础行动点数 + 敏捷调整值
        let speed = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Speed)
        );
        let speed_mod = BattleUnitAdapter::calculate_modifier(speed);
        
        (3 + speed_mod).max(1) as ActionPoints // 至少1点行动力
    }
    
    fn initiative(&self) -> Initiative {
        // 计算先攻值：敏捷值 + 木属性调整值 + 随机值
        let speed = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Speed)
        );
        let wood_attr = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Wood)
        );
        let wood_mod = BattleUnitAdapter::calculate_modifier(wood_attr);
        
        // 基础先攻 = 敏捷 + 木属性调整值
        let base_initiative = speed + wood_mod as AttributePoints;
        
        // 装备先攻加成
        let equipment_bonus = self.get_equipment_initiative_bonus();
        
        // Buff先攻加成
        let buff_bonus = self.get_buff_initiative_bonus();
        
        (base_initiative + equipment_bonus + buff_bonus) as Initiative
    }
    
    fn can_act(&self) -> bool {
        self.action_points() > 0 && 
        !self.is_incapacitated() &&
        !self.has_blocking_status()
    }
    
    fn consume_action_points(&mut self, cost: ActionPoints) -> GameResult<()> {
        if self.action_points() >= cost {
            self.current_action_points -= cost;
            Ok(())
        } else {
            Err("行动点数不足".into())
        }
    }
    
    fn restore_action_points(&mut self, amount: ActionPoints) {
        self.current_action_points = (self.current_action_points + amount)
            .min(self.max_action_points());
    }
    
    fn reset_action_points(&mut self) {
        self.current_action_points = self.max_action_points();
    }
    
    fn action_speed(&self) -> ActionSpeed {
        // 计算行动速度（影响行动执行时间）
        let speed = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Speed)
        );
        let wood_attr = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Wood)
        );
        
        // 基础速度 = 敏捷 + 木属性
        let base_speed = speed + wood_attr;
        
        // 装备速度加成
        let equipment_bonus = self.get_equipment_speed_bonus();
        
        // Buff速度加成
        let buff_bonus = self.get_buff_speed_bonus();
        
        // 负重惩罚
        let encumbrance_penalty = self.calculate_encumbrance_penalty();
        
        let total_speed = base_speed + equipment_bonus + buff_bonus - encumbrance_penalty;
        total_speed.max(1) as ActionSpeed // 最小速度为1
    }
    
    fn delay_action(&mut self, delay: GameTime) -> GameResult<()> {
        // 延迟行动（降低先攻值）
        self.next_action_time += delay;
        Ok(())
    }
    
    fn is_ready_to_act(&self, current_time: GameTime) -> bool {
        current_time >= self.next_action_time && self.can_act()
    }
    
    fn calculate_action_cost(&self, action: &ActionType) -> ActionPoints {
        let base_cost = match action {
            ActionType::Move => 1,
            ActionType::Attack => 2,
            ActionType::Skill => 2,
            ActionType::UseItem => 1,
            ActionType::Defend => 1,
            ActionType::Wait => 0,
        };
        
        // 计算行动消耗调整
        let speed_mod = BattleUnitAdapter::calculate_modifier(
            self.calculate_total_attribute(
                AttributeType::Derived(DerivedAttribute::Speed)
            )
        );
        
        // 高敏捷减少行动消耗
        let adjusted_cost = base_cost - (speed_mod / 5).max(0);
        adjusted_cost.max(1) as ActionPoints // 最小消耗1点
    }
}

impl BattleUnitAdapter {
    fn is_incapacitated(&self) -> bool {
        self.current_health() == 0 || 
        self.has_status(StatusType::Stun) ||
        self.has_status(StatusType::Sleep) ||
        self.has_status(StatusType::Paralysis)
    }
    
    fn has_blocking_status(&self) -> bool {
        // 检查阻止行动的状态效果
        for buff in &self.buff_manager.active_buffs {
            if buff.effects.iter().any(|effect| {
                matches!(effect, 
                    BuffEffect::BlockAction |
                    BuffEffect::Stun |
                    BuffEffect::Sleep |
                    BuffEffect::Paralysis
                )
            }) {
                return true;
            }
        }
        false
    }
    
    fn has_status(&self, status: StatusType) -> bool {
        // TODO: 检查是否有特定状态效果
        false
    }
    
    fn get_equipment_initiative_bonus(&self) -> AttributePoints {
        // TODO: 实现装备先攻加成计算
        0
    }
    
    fn get_buff_initiative_bonus(&self) -> AttributePoints {
        // TODO: 实现buff先攻加成计算
        0
    }
    
    fn get_equipment_speed_bonus(&self) -> AttributePoints {
        // TODO: 实现装备速度加成计算
        0
    }
    
    fn get_buff_speed_bonus(&self) -> AttributePoints {
        // TODO: 实现buff速度加成计算
        0
    }
    
    fn calculate_encumbrance_penalty(&self) -> AttributePoints {
        // TODO: 基于负重计算速度惩罚
        0
    }
}