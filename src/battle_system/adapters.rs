use crate::shared::types::*;
use crate::skill::buff::Buff;
use crate::skill::{Skill, SkillType};
use crate::battle_system::battle_event::DamageType;
use crate::battle_system::battle_config::BattleConfig;
use crate::attribute::{AttributeType, CoreAttribute, DerivedAttribute};
use std::collections::HashMap;

/// 增强的技能效果生成器
#[derive(Debug, Clone)]
pub struct IntelligentSkillEffectGenerator {
    /// 战斗配置
    config: BattleConfig,
    /// 效果模板库
    effect_templates: HashMap<String, EffectTemplate>,
    /// 技能名称分析器
    name_analyzer: SkillNameAnalyzer,
}

/// 效果模板
#[derive(Debug, Clone)]
pub struct EffectTemplate {
    pub effect_type: String,
    pub base_value: f64,
    pub scaling_factor: f64,
    pub damage_type: Option<DamageType>,
    pub duration: Option<u32>,
    pub target_type: TargetType,
}

/// 目标类型
#[derive(Debug, Clone, PartialEq)]
pub enum TargetType {
    Self_,
    Enemy,
    Ally,
    All,
    Area,
}

/// 技能名称分析器
#[derive(Debug, Clone)]
pub struct SkillNameAnalyzer {
    /// 攻击关键词
    attack_keywords: Vec<String>,
    /// 治疗关键词
    heal_keywords: Vec<String>,
    /// 元素关键词
    element_keywords: HashMap<String, DamageType>,
    /// 状态效果关键词
    status_keywords: HashMap<String, String>,
}

impl Default for IntelligentSkillEffectGenerator {
    fn default() -> Self {
        let mut effect_templates = HashMap::new();
        
        // 攻击技能模板
        effect_templates.insert("fire_attack".to_string(), EffectTemplate {
            effect_type: "damage".to_string(),
            base_value: 30.0,
            scaling_factor: 2.5,
            damage_type: Some(DamageType::Magical),
            duration: None,
            target_type: TargetType::Enemy,
        });
        
        effect_templates.insert("ice_attack".to_string(), EffectTemplate {
            effect_type: "damage".to_string(),
            base_value: 25.0,
            scaling_factor: 2.0,
            damage_type: Some(DamageType::Magical),
            duration: None,
            target_type: TargetType::Enemy,
        });
        
        effect_templates.insert("lightning_attack".to_string(), EffectTemplate {
            effect_type: "damage".to_string(),
            base_value: 35.0,
            scaling_factor: 2.8,
            damage_type: Some(DamageType::Magical),
            duration: None,
            target_type: TargetType::Enemy,
        });
        
        effect_templates.insert("physical_attack".to_string(), EffectTemplate {
            effect_type: "damage".to_string(),
            base_value: 40.0,
            scaling_factor: 2.2,
            damage_type: Some(DamageType::Physical),
            duration: None,
            target_type: TargetType::Enemy,
        });
        
        // 治疗技能模板
        effect_templates.insert("heal".to_string(), EffectTemplate {
            effect_type: "heal".to_string(),
            base_value: 50.0,
            scaling_factor: 3.0,
            damage_type: None,
            duration: None,
            target_type: TargetType::Self_,
        });
        
        effect_templates.insert("regeneration".to_string(), EffectTemplate {
            effect_type: "heal_over_time".to_string(),
            base_value: 15.0,
            scaling_factor: 1.5,
            damage_type: None,
            duration: Some(5),
            target_type: TargetType::Self_,
        });
        
        // Buff技能模板
        effect_templates.insert("strength_buff".to_string(), EffectTemplate {
            effect_type: "buff".to_string(),
            base_value: 20.0,
            scaling_factor: 1.0,
            damage_type: None,
            duration: Some(4),
            target_type: TargetType::Self_,
        });
        
        // Debuff技能模板
        effect_templates.insert("weakness_debuff".to_string(), EffectTemplate {
            effect_type: "debuff".to_string(),
            base_value: -15.0,
            scaling_factor: 1.0,
            damage_type: None,
            duration: Some(3),
            target_type: TargetType::Enemy,
        });
        
        // 创建名称分析器
        let mut element_keywords = HashMap::new();
        element_keywords.insert("火".to_string(), DamageType::Burn);
        element_keywords.insert("火球".to_string(), DamageType::Burn);
        element_keywords.insert("炎".to_string(), DamageType::Burn);
        element_keywords.insert("冰".to_string(), DamageType::Magical);
        element_keywords.insert("霜".to_string(), DamageType::Magical);
        element_keywords.insert("雷".to_string(), DamageType::Magical);
        element_keywords.insert("电".to_string(), DamageType::Magical);
        element_keywords.insert("闪电".to_string(), DamageType::Magical);
        element_keywords.insert("毒".to_string(), DamageType::Poison);
        element_keywords.insert("血".to_string(), DamageType::Bleed);
        
        let mut status_keywords = HashMap::new();
        status_keywords.insert("盾".to_string(), "shield".to_string());
        status_keywords.insert("护".to_string(), "defense_buff".to_string());
        status_keywords.insert("强化".to_string(), "attack_buff".to_string());
        status_keywords.insert("削弱".to_string(), "weakness_debuff".to_string());
        status_keywords.insert("中毒".to_string(), "poison_debuff".to_string());
        status_keywords.insert("流血".to_string(), "bleed_debuff".to_string());
        
        let name_analyzer = SkillNameAnalyzer {
            attack_keywords: vec![
                "攻击".to_string(), "打击".to_string(), "斩".to_string(), "击".to_string(),
                "刺".to_string(), "砸".to_string(), "爆".to_string(), "破".to_string(),
                "切".to_string(), "劈".to_string(), "球".to_string(), "箭".to_string(),
            ],
            heal_keywords: vec![
                "治疗".to_string(), "恢复".to_string(), "愈合".to_string(), "修复".to_string(),
                "再生".to_string(), "回复".to_string(), "医治".to_string(),
            ],
            element_keywords,
            status_keywords,
        };
        
        Self {
            config: BattleConfig::default(),
            effect_templates,
            name_analyzer,
        }
    }
}

impl IntelligentSkillEffectGenerator {
    pub fn new() -> Self {
        Self::default()
    }
    
    pub fn with_config(mut self, config: BattleConfig) -> Self {
        self.config = config;
        self
    }
    
    /// 智能生成技能效果
    pub fn generate_effects(&self, skill: &Skill) -> Vec<EnhancedSkillEffect> {
        let mut effects = Vec::new();
        
        // 基于技能名称分析
        let name_analysis = self.name_analyzer.analyze_skill_name(&skill.name);
        
        // 基于技能类型分析
        let type_analysis = self.analyze_skill_types(&skill.skill_types);
        
        // 基于技能属性分析
        let attribute_analysis = self.analyze_skill_attributes(skill);
        
        // 合并分析结果生成效果
        effects.extend(self.generate_effects_from_analysis(
            skill,
            &name_analysis,
            &type_analysis,
            &attribute_analysis,
        ));
        
        // 如果没有生成任何效果，使用默认效果
        if effects.is_empty() {
            effects.push(self.generate_default_effect(skill));
        }
        
        effects
    }
    

    
    /// 分析技能类型
    fn analyze_skill_types(&self, skill_types: &[SkillType]) -> SkillTypeAnalysis {
        SkillTypeAnalysis {
            is_active: skill_types.contains(&SkillType::Active),
            is_passive: skill_types.contains(&SkillType::Passive),
            is_toggle: false, // 简化处理
            primary_type: skill_types.first().cloned().unwrap_or(SkillType::Active),
        }
    }
    
    /// 分析技能属性
    fn analyze_skill_attributes(&self, skill: &Skill) -> SkillAttributeAnalysis {
        SkillAttributeAnalysis {
            power_level: self.calculate_power_level(skill),
            mana_efficiency: self.calculate_mana_efficiency(skill),
            cooldown_ratio: self.calculate_cooldown_ratio(skill),
            range_category: self.categorize_range(skill.range),
        }
    }
    
    /// 从分析结果生成效果
    fn generate_effects_from_analysis(
        &self,
        skill: &Skill,
        name_analysis: &SkillNameAnalysis,
        type_analysis: &SkillTypeAnalysis,
        attribute_analysis: &SkillAttributeAnalysis,
    ) -> Vec<EnhancedSkillEffect> {
        let mut effects = Vec::new();
        
        // 基于名称分析生成主要效果
        if let Some(template_key) = self.determine_template_from_analysis(name_analysis, type_analysis) {
            if let Some(template) = self.effect_templates.get(&template_key) {
                let effect = self.create_effect_from_template(skill, template, attribute_analysis);
                effects.push(effect);
            }
        }
        
        // 基于元素类型添加次要效果
        if let Some(element_type) = &name_analysis.element_type {
            let secondary_effect = self.create_elemental_effect(skill, element_type, attribute_analysis);
            effects.push(secondary_effect);
        }
        
        // 基于状态效果添加额外效果
        for status_effect in &name_analysis.status_effects {
            let status_eff = self.create_status_effect(skill, status_effect, attribute_analysis);
            effects.push(status_eff);
        }
        
        effects
    }
    
    /// 确定使用的模板
    fn determine_template_from_analysis(
        &self,
        name_analysis: &SkillNameAnalysis,
        _type_analysis: &SkillTypeAnalysis,
    ) -> Option<String> {
        if name_analysis.is_heal {
            Some("heal".to_string())
        } else if name_analysis.is_attack {
            match &name_analysis.element_type {
                Some(DamageType::Burn) => Some("fire_attack".to_string()),
                Some(DamageType::Magical) if name_analysis.name.contains("冰") => Some("ice_attack".to_string()),
                Some(DamageType::Magical) if name_analysis.name.contains("雷") || name_analysis.name.contains("闪电") => Some("lightning_attack".to_string()),
                _ => Some("physical_attack".to_string()),
            }
        } else {
            None
        }
    }
    
    /// 从模板创建效果
    fn create_effect_from_template(
        &self,
        skill: &Skill,
        template: &EffectTemplate,
        attribute_analysis: &SkillAttributeAnalysis,
    ) -> EnhancedSkillEffect {
        let scaled_value = template.base_value + 
            (skill.mana_cost as f64 * template.scaling_factor) * 
            attribute_analysis.power_level;
        
        EnhancedSkillEffect {
            id: skill.id,
            name: format!("{}_effect", skill.name),
            effect_type: template.effect_type.clone(),
            value: scaled_value,
            damage_type: template.damage_type.clone(),
            duration: template.duration,
            target_type: template.target_type.clone(),
            scaling_factor: template.scaling_factor * attribute_analysis.power_level,
            conditions: Vec::new(),
            metadata: self.generate_effect_metadata(skill, template),
        }
    }
    
    /// 创建元素效果
    fn create_elemental_effect(
        &self,
        skill: &Skill,
        element_type: &DamageType,
        attribute_analysis: &SkillAttributeAnalysis,
    ) -> EnhancedSkillEffect {
        let base_value = skill.mana_cost as f64 * 0.5 * attribute_analysis.power_level;
        
        EnhancedSkillEffect {
            id: skill.id + 1000, // 避免ID冲突
            name: format!("{}_elemental_effect", skill.name),
            effect_type: "elemental_damage".to_string(),
            value: base_value,
            damage_type: Some(element_type.clone()),
            duration: Some(2),
            target_type: TargetType::Enemy,
            scaling_factor: 0.5 * attribute_analysis.power_level,
            conditions: Vec::new(),
            metadata: HashMap::new(),
        }
    }
    
    /// 创建状态效果
    fn create_status_effect(
        &self,
        skill: &Skill,
        status_type: &str,
        attribute_analysis: &SkillAttributeAnalysis,
    ) -> EnhancedSkillEffect {
        let base_value = skill.mana_cost as f64 * 0.3 * attribute_analysis.power_level;
        
        EnhancedSkillEffect {
            id: skill.id + 2000, // 避免ID冲突
            name: format!("{}_status_effect", skill.name),
            effect_type: status_type.to_string(),
            value: base_value,
            damage_type: None,
            duration: Some(3),
            target_type: if status_type.contains("buff") { TargetType::Self_ } else { TargetType::Enemy },
            scaling_factor: 0.3 * attribute_analysis.power_level,
            conditions: Vec::new(),
            metadata: HashMap::new(),
        }
    }
    
    /// 生成默认效果
    fn generate_default_effect(&self, skill: &Skill) -> EnhancedSkillEffect {
        let base_value = skill.mana_cost as f64 * 2.0;
        
        EnhancedSkillEffect {
            id: skill.id,
            name: format!("{}_default", skill.name),
            effect_type: "damage".to_string(),
            value: base_value,
            damage_type: Some(DamageType::Physical),
            duration: None,
            target_type: TargetType::Enemy,
            scaling_factor: 2.0,
            conditions: Vec::new(),
            metadata: HashMap::new(),
        }
    }
    
    /// 计算技能威力等级
    fn calculate_power_level(&self, skill: &Skill) -> f64 {
        let mana_factor = (skill.mana_cost as f64 / 50.0).min(2.0).max(0.5);
        let cooldown_factor = (skill.cooldown as f64 / 5.0).min(2.0).max(0.5);
        let range_factor = (skill.range as f64 / 5.0).min(1.5).max(0.8);
        
        (mana_factor + cooldown_factor + range_factor) / 3.0
    }
    
    /// 计算法力效率
    fn calculate_mana_efficiency(&self, skill: &Skill) -> f64 {
        if skill.mana_cost == 0 {
            1.0
        } else {
            100.0 / skill.mana_cost as f64
        }
    }
    
    /// 计算冷却比率
    fn calculate_cooldown_ratio(&self, skill: &Skill) -> f64 {
        skill.cooldown as f64 / 10.0
    }
    
    /// 分类技能范围
    fn categorize_range(&self, range: f32) -> RangeCategory {
        if range <= 1.0 {
            RangeCategory::Melee
        } else if range <= 3.0 {
            RangeCategory::Short
        } else if range <= 5.0 {
            RangeCategory::Medium
        } else {
            RangeCategory::Long
        }
    }
    
    /// 生成效果元数据
    fn generate_effect_metadata(&self, skill: &Skill, template: &EffectTemplate) -> HashMap<String, String> {
        let mut metadata = HashMap::new();
        metadata.insert("skill_id".to_string(), skill.id.to_string());
        metadata.insert("skill_name".to_string(), skill.name.clone());
        metadata.insert("template_type".to_string(), template.effect_type.clone());
        metadata.insert("generated_by".to_string(), "IntelligentSkillEffectGenerator".to_string());
        metadata
    }
}

/// 技能名称分析结果
#[derive(Debug, Clone)]
pub struct SkillNameAnalysis {
    pub name: String,
    pub is_attack: bool,
    pub is_heal: bool,
    pub is_buff: bool,
    pub is_debuff: bool,
    pub element_type: Option<DamageType>,
    pub status_effects: Vec<String>,
    pub power_words: Vec<String>,
}

/// 技能类型分析结果
#[derive(Debug, Clone)]
pub struct SkillTypeAnalysis {
    pub is_active: bool,
    pub is_passive: bool,
    pub is_toggle: bool,
    pub primary_type: SkillType,
}

/// 技能属性分析结果
#[derive(Debug, Clone)]
pub struct SkillAttributeAnalysis {
    pub power_level: f64,
    pub mana_efficiency: f64,
    pub cooldown_ratio: f64,
    pub range_category: RangeCategory,
}

/// 范围分类
#[derive(Debug, Clone, PartialEq)]
pub enum RangeCategory {
    Melee,    // 近战 (0-1)
    Short,    // 短程 (1-3)
    Medium,   // 中程 (3-5)
    Long,     // 远程 (5+)
}

/// 增强的技能效果
#[derive(Debug, Clone)]
pub struct EnhancedSkillEffect {
    pub id: ID,
    pub name: String,
    pub effect_type: String,
    pub value: f64,
    pub damage_type: Option<DamageType>,
    pub duration: Option<u32>,
    pub target_type: TargetType,
    pub scaling_factor: f64,
    pub conditions: Vec<String>,
    pub metadata: HashMap<String, String>,
}

/// 属性映射结果
#[derive(Debug, Clone)]
pub struct AttributeMappingResult {
    pub effects: Vec<AttributeEffect>,
    pub total_power_bonus: f64,
    pub buff_type: crate::skill::buff::BuffType,
}

/// 属性效果
#[derive(Debug, Clone)]
pub struct AttributeEffect {
    pub effect_name: String,
    pub power_contribution: f64,
    pub element_type: String,
}

impl SkillNameAnalyzer {
    /// 分析技能名称
    pub fn analyze_skill_name(&self, name: &str) -> SkillNameAnalysis {
        let mut analysis = SkillNameAnalysis {
            name: name.to_string(),
            is_attack: false,
            is_heal: false,
            is_buff: false,
            is_debuff: false,
            element_type: None,
            status_effects: Vec::new(),
            power_words: Vec::new(),
        };
        
        // 检查攻击关键词
        for keyword in &self.attack_keywords {
            if name.contains(keyword) {
                analysis.is_attack = true;
                analysis.power_words.push(keyword.clone());
            }
        }
        
        // 检查治疗关键词
        for keyword in &self.heal_keywords {
            if name.contains(keyword) {
                analysis.is_heal = true;
                analysis.power_words.push(keyword.clone());
            }
        }
        
        // 检查元素类型
        for (keyword, damage_type) in &self.element_keywords {
            if name.contains(keyword) {
                analysis.element_type = Some(damage_type.clone());
                break;
            }
        }
        
        // 检查状态效果
        for (keyword, status_type) in &self.status_keywords {
            if name.contains(keyword) {
                analysis.status_effects.push(status_type.clone());
                
                if status_type.contains("buff") {
                    analysis.is_buff = true;
                } else if status_type.contains("debuff") {
                    analysis.is_debuff = true;
                }
            }
        }
        
        // 如果没有找到特定类型，基于通用规则推断
        if !analysis.is_attack && !analysis.is_heal && !analysis.is_buff && !analysis.is_debuff {
            // 默认为攻击技能
            analysis.is_attack = true;
        }
        
        analysis
    }
}

/// 简化的技能效果
#[derive(Debug, Clone)]
pub struct SimpleSkillEffect {
    pub id: ID,
    pub name: String,
    pub effect_type: String,
    pub value: f64,
    pub duration: Option<u32>,
    pub damage_type: Option<String>,
}

/// 简化的触发效果
#[derive(Debug, Clone)]
pub struct SimpleTriggerEffect {
    pub condition: String,
    pub effect: String,
}

/// Buff适配器，提供战斗系统需要的字段访问
pub struct BuffAdapter<'a> {
    pub buff: &'a Buff,
}

impl<'a> BuffAdapter<'a> {
    pub fn new(buff: &'a Buff) -> Self {
        Self { buff }
    }
    
    /// 获取效果类型（从buff_type推导）
    pub fn get_effect_type(&self) -> String {
        match self.buff.buff_type {
            crate::skill::buff::BuffType::Buff => "buff".to_string(),
            crate::skill::buff::BuffType::Debuff => "debuff".to_string(),
        }
    }
    
    /// 获取效果值（从attributes推导）
    pub fn get_value(&self) -> f64 {
        // 如果有属性加成，计算实际的效果值
        if let Some(ref attributes) = self.buff.attributes {
            // 基于Buff类型和属性计算效果值
            let base_value = match self.buff.buff_type {
                crate::skill::buff::BuffType::Buff => 10.0,
                crate::skill::buff::BuffType::Debuff => -8.0,
            };
            
            // 从属性集合中获取额外的属性加成
            let attribute_bonus = self.calculate_attribute_bonus(attributes);
            
            // 基于叠加层数调整效果
            let stack_multiplier = self.buff.stack_count as f64;
            
            // 基于持续时间调整效果（持续时间越长，效果越强）
            let duration_multiplier = (self.buff.duration as f64 / 5.0).max(0.5).min(2.0);
            
            // 综合计算效果值
            let final_value = (base_value + attribute_bonus) * stack_multiplier * duration_multiplier;
            
            // 根据触发效果进一步调整
            if let Some(ref trigger_effect) = self.buff.trigger_effect {
                match trigger_effect {
                    crate::skill::buff::TriggerEffect::Heal(value) => final_value + value,
                    crate::skill::buff::TriggerEffect::Damage(value) => final_value + value,
                    crate::skill::buff::TriggerEffect::GainShield(value) => final_value + (value * 0.5),
                    crate::skill::buff::TriggerEffect::Absorb(value) => final_value + (value * 0.3),
                    _ => final_value,
                }
            } else {
                final_value
            }
        } else {
            // 没有属性加成时，基于Buff类型返回默认值
            match self.buff.buff_type {
                crate::skill::buff::BuffType::Buff => 5.0 * self.buff.stack_count as f64,
                crate::skill::buff::BuffType::Debuff => -3.0 * self.buff.stack_count as f64,
            }
        }
    }
    
    /// 检查是否可以叠加
    pub fn can_stack(&self) -> bool {
        self.buff.max_stack > 1
    }
    
    /// 获取剩余持续时间
    pub fn get_remaining_duration(&self) -> f32 {
        self.buff.duration
    }
    
    /// 获取描述
    pub fn get_description(&self) -> String {
        format!("{} ({})", self.buff.name, self.get_effect_type())
    }
    
    /// 获取最大叠加层数
    pub fn get_max_stack(&self) -> u32 {
        self.buff.max_stack
    }
    
    /// 检查是否是正面效果
    pub fn is_positive(&self) -> bool {
        matches!(self.buff.buff_type, crate::skill::buff::BuffType::Buff)
    }
    
    /// 检查是否是负面效果
    pub fn is_negative(&self) -> bool {
        matches!(self.buff.buff_type, crate::skill::buff::BuffType::Debuff)
    }
    
    /// 获取buff的数值效果（如果有的话）
    pub fn get_numeric_effect(&self) -> Option<f64> {
        if let Some(ref trigger_effect) = self.buff.trigger_effect {
            match trigger_effect {
                crate::skill::buff::TriggerEffect::Heal(value) => Some(*value),
                crate::skill::buff::TriggerEffect::Damage(value) => Some(*value),
                crate::skill::buff::TriggerEffect::GainShield(value) => Some(*value),
                crate::skill::buff::TriggerEffect::Absorb(value) => Some(*value),
                _ => None,
            }
        } else {
            None
        }
    }
    
    /// 从属性集合中计算属性加成
    fn calculate_attribute_bonus(&self, attributes: &crate::attribute::AttributeSet) -> f64 {
        use crate::attribute::{AttributeType, CoreAttribute, DerivedAttribute};
        
        let mut total_bonus = 0.0;
        
        // 遍历所有属性，根据Buff类型计算加成
        for (_attr_type, attribute) in &attributes.attributes {
            let attribute_value = attribute.value;
            
            // 根据属性类型和Buff类型计算加成
            let bonus_multiplier = match (&attribute.attr_type, &self.buff.buff_type) {
                // 基础属性加成
                (AttributeType::Base(CoreAttribute::Metal), crate::skill::buff::BuffType::Buff) => 1.2,
                (AttributeType::Base(CoreAttribute::Wood), crate::skill::buff::BuffType::Buff) => 1.1,
                (AttributeType::Base(CoreAttribute::Water), crate::skill::buff::BuffType::Buff) => 1.0,
                (AttributeType::Base(CoreAttribute::Fire), crate::skill::buff::BuffType::Buff) => 1.3,
                (AttributeType::Base(CoreAttribute::Earth), crate::skill::buff::BuffType::Buff) => 1.05,
                
                // 衍生属性加成
                (AttributeType::Composite(DerivedAttribute::Ice), crate::skill::buff::BuffType::Buff) => 1.15,
                (AttributeType::Composite(DerivedAttribute::Thunder), crate::skill::buff::BuffType::Buff) => 1.4,
                (AttributeType::Composite(DerivedAttribute::Wind), crate::skill::buff::BuffType::Buff) => 1.25,
                
                // Debuff的属性加成通常较小
                _ => 0.3,
            };
            
            total_bonus += attribute_value * bonus_multiplier;
        }
        
        // 限制属性加成的范围，避免过度放大
        total_bonus.min(50.0).max(-25.0)
    }
    
    /// 处理属性映射和转换（增强版）
    pub fn process_attribute_mapping(&self, attributes: &crate::attribute::AttributeSet) -> AttributeMappingResult {
        let mut processed_effects = Vec::new();
        let mut total_power_bonus = 0.0;
        
        // 遍历所有属性
        for (attr_type, attribute) in &attributes.attributes {
            let mapped_effect = self.map_attribute_to_effect(attr_type, attribute);
            
            if let Some(effect) = mapped_effect {
                total_power_bonus += effect.power_contribution;
                processed_effects.push(effect);
            }
        }
        
        AttributeMappingResult {
            effects: processed_effects,
            total_power_bonus,
            buff_type: self.buff.buff_type.clone(),
        }
    }
    
    /// 将属性映射为效果
    fn map_attribute_to_effect(
        &self, 
        attr_type: &AttributeType, 
        attribute: &crate::attribute::Attribute
    ) -> Option<AttributeEffect> {
        match attr_type {
            AttributeType::Base(core_attr) => {
                self.map_core_attribute_effect(core_attr, attribute)
            }
            AttributeType::Composite(derived_attr) => {
                self.map_derived_attribute_effect(derived_attr, attribute)
            }
        }
    }
    
    /// 映射基础属性效果
    fn map_core_attribute_effect(
        &self,
        core_attr: &CoreAttribute,
        attribute: &crate::attribute::Attribute
    ) -> Option<AttributeEffect> {
        let (effect_name, power_multiplier) = match core_attr {
            CoreAttribute::Metal => ("金系加成".to_string(), 1.2),
            CoreAttribute::Wood => ("木系治疗".to_string(), 1.1),
            CoreAttribute::Water => ("水系防御".to_string(), 1.0),
            CoreAttribute::Fire => ("火系攻击".to_string(), 1.3),
            CoreAttribute::Earth => ("土系护盾".to_string(), 1.05),
        };
        
        Some(AttributeEffect {
            effect_name,
            power_contribution: attribute.value * power_multiplier,
            element_type: format!("{:?}", core_attr),
        })
    }
    
    /// 映射衍生属性效果
    fn map_derived_attribute_effect(
        &self,
        derived_attr: &DerivedAttribute,
        attribute: &crate::attribute::Attribute
    ) -> Option<AttributeEffect> {
        let (effect_name, power_multiplier) = match derived_attr {
            DerivedAttribute::Thunder => ("雷电麻痹".to_string(), 1.3),
            DerivedAttribute::Ice => ("冰霜减速".to_string(), 1.1),
            DerivedAttribute::Wind => ("风系加速".to_string(), 1.25),
            DerivedAttribute::Magma => ("岩浆灼烧".to_string(), 1.4),
            DerivedAttribute::Steam => ("蒸汽爆发".to_string(), 1.2),
            DerivedAttribute::Crystal => ("晶化防御".to_string(), 1.5),
            DerivedAttribute::Light => ("圣光净化".to_string(), 1.35),
            DerivedAttribute::Shadow => ("暗影侵蚀".to_string(), 1.35),
        };
        
        Some(AttributeEffect {
            effect_name,
            power_contribution: attribute.value * power_multiplier,
            element_type: format!("{:?}", derived_attr),
        })
    }
}

/// 可变Buff适配器
pub struct BuffAdapterMut<'a> {
    pub buff: &'a mut Buff,
}

impl<'a> BuffAdapterMut<'a> {
    pub fn new(buff: &'a mut Buff) -> Self {
        Self { buff }
    }
    
    /// 设置剩余持续时间
    pub fn set_remaining_duration(&mut self, duration: f32) {
        self.buff.duration = duration;
    }
    
    /// 减少持续时间（秒数）
    pub fn decrease_duration(&mut self, delta_time: f32) {
        if self.buff.duration > 0.0 {
            self.buff.duration = (self.buff.duration - delta_time).max(0.0);
        }
    }
    
    /// 增加叠加层数
    pub fn add_stack(&mut self) {
        if self.buff.stack_count < self.buff.max_stack {
            self.buff.stack_count += 1;
        }
    }
}

/// 技能适配器
pub struct SkillAdapter<'a> {
    pub skill: &'a Skill,
}

impl<'a> SkillAdapter<'a> {
    pub fn new(skill: &'a Skill) -> Self {
        Self { skill }
    }
    
    /// 获取技能效果列表（简化实现）
    pub fn get_effects(&self) -> Vec<SimpleSkillEffect> {
        // 基于技能名称和类型生成效果
        let mut effects = Vec::new();
        
        // 根据技能名称推断效果
        if self.skill.name.contains("攻击") || self.skill.name.contains("斩") || self.skill.name.contains("击") {
            effects.push(SimpleSkillEffect {
                id: self.skill.id,
                name: format!("{}_伤害", self.skill.name),
                effect_type: "damage".to_string(),
                value: self.skill.mana_cost as f64 * 2.0,
                damage_type: Some("physical".to_string()),
                duration: None,
            });
        }
        
        if self.skill.name.contains("治疗") || self.skill.name.contains("恢复") {
            effects.push(SimpleSkillEffect {
                id: self.skill.id,
                name: format!("{}_治疗", self.skill.name),
                effect_type: "heal".to_string(),
                value: self.skill.mana_cost as f64 * 1.5,
                damage_type: None,
                duration: None,
            });
        }
        
        if self.skill.name.contains("火球") || self.skill.name.contains("法术") {
            effects.push(SimpleSkillEffect {
                id: self.skill.id,
                name: format!("{}_魔法伤害", self.skill.name),
                effect_type: "damage".to_string(),
                value: self.skill.mana_cost as f64 * 2.5,
                damage_type: Some("magical".to_string()),
                duration: None,
            });
        }
        
        if self.skill.name.contains("盾") || self.skill.name.contains("护") {
            effects.push(SimpleSkillEffect {
                id: self.skill.id,
                name: format!("{}_护盾", self.skill.name),
                effect_type: "buff".to_string(),
                value: 0.0,
                damage_type: None,
                duration: Some(3),
            });
        }
        
        // 检查技能类型
        for skill_type in &self.skill.skill_types {
            match skill_type {
                crate::skill::SkillType::Active => {
                    if effects.is_empty() {
                        effects.push(SimpleSkillEffect {
                            id: self.skill.id,
                            name: self.skill.name.clone(),
                            effect_type: "damage".to_string(),
                            value: 20.0,
                            duration: None,
                            damage_type: Some("physical".to_string()),
                        });
                    }
                }
                _ => {} // 其他类型暂不处理
            }
        }
        
        // 如果没有任何效果，创建默认效果
        if effects.is_empty() {
            effects.push(SimpleSkillEffect {
                id: self.skill.id,
                name: format!("{}_默认攻击", self.skill.name),
                effect_type: "damage".to_string(),
                value: 15.0,
                duration: None,
                damage_type: Some("physical".to_string()),
            });
        }
        
        effects
    }
    
    /// 获取触发效果列表（简化为空）
    pub fn get_trigger_effects(&self) -> Vec<SimpleTriggerEffect> {
        Vec::new()
    }
    
    /// 检查技能是否有AOE效果
    pub fn has_aoe_effect(&self) -> bool {
        self.skill.area.is_some()
    }
    
    /// 获取技能的AOE范围
    pub fn get_aoe_range(&self) -> Option<f32> {
        self.skill.area.as_ref().map(|_| self.skill.range)
    }
}

/// 创建兼容的Buff（用于技能效果转换）
pub fn create_compatible_buff(
    id: ID,
    name: String,
    buff_type: crate::skill::buff::BuffType,
    duration: f32,
) -> Buff {
    Buff {
        id,
        name,
        duration,
        buff_type,
        attributes: None,
        immunities: Vec::new(),
        vulnerability: Vec::new(),
        trigger_condition: None,
        trigger_effect: None,
        stack_count: 1,
        max_stack: 1,
        is_permanent: false,
    }
}

/// 创建带有治疗效果的Buff
pub fn create_heal_buff(
    id: ID,
    name: String,
    duration: f32,
    heal_amount: f64,
) -> Buff {
    Buff {
        id,
        name,
        duration,
        buff_type: crate::skill::buff::BuffType::Buff,
        attributes: None,
        immunities: Vec::new(),
        vulnerability: Vec::new(),
        trigger_condition: None,
        trigger_effect: Some(crate::skill::buff::TriggerEffect::Heal(heal_amount)),
        stack_count: 1,
        max_stack: 1,
        is_permanent: false,
    }
}

/// 创建带有伤害效果的Debuff
pub fn create_damage_debuff(
    id: ID,
    name: String,
    duration: f32,
    damage_amount: f64,
) -> Buff {
    Buff {
        id,
        name,
        duration,
        buff_type: crate::skill::buff::BuffType::Debuff,
        attributes: None,
        immunities: Vec::new(),
        vulnerability: Vec::new(),
        trigger_condition: None,
        trigger_effect: Some(crate::skill::buff::TriggerEffect::Damage(damage_amount)),
        stack_count: 1,
        max_stack: 3, // 允许叠加
        is_permanent: false,
    }
}

/// 状态效果转换器
pub struct StatusEffectConverter;

impl StatusEffectConverter {
    /// 将字符串状态转换为CharacterState枚举
    pub fn string_to_character_state(status: &str) -> Option<crate::status_panel::status::CharacterState> {
        use crate::status_panel::status::CharacterState;
        
        if status.contains("眩晕") {
            Some(CharacterState::Stunned(3))
        } else if status.contains("沉默") {
            Some(CharacterState::Silenced(2))
        } else if status.contains("减速") {
            Some(CharacterState::Slowed(0.5, 3))
        } else if status.contains("定身") {
            Some(CharacterState::Rooted(2))
        } else if status.contains("无敌") {
            Some(CharacterState::Invincible(1))
        } else if status.contains("隐身") {
            Some(CharacterState::Invisible(3))
        } else if status.contains("狂暴") {
            Some(CharacterState::Berserk(1.5, 5))
        } else if status.contains("虚弱") {
            Some(CharacterState::Weakened(0.7, 3))
        } else {
            None
        }
    }
    
    /// 将CharacterState转换为字符串描述
    pub fn character_state_to_string(state: &crate::status_panel::status::CharacterState) -> String {
        use crate::status_panel::status::CharacterState;
        
        match state {
            CharacterState::Stunned(duration) => format!("眩晕({}回合)", duration),
            CharacterState::Silenced(duration) => format!("沉默({}回合)", duration),
            CharacterState::Slowed(factor, duration) => format!("减速({:.0}%,{}回合)", (1.0 - factor) * 100.0, duration),
            CharacterState::Rooted(duration) => format!("定身({}回合)", duration),
            CharacterState::Invincible(duration) => format!("无敌({}回合)", duration),
            CharacterState::Invisible(duration) => format!("隐身({}回合)", duration),
            CharacterState::Berserk(factor, duration) => format!("狂暴(+{:.0}%,{}回合)", (factor - 1.0) * 100.0, duration),
            CharacterState::Weakened(factor, duration) => format!("虚弱({:.0}%,{}回合)", factor * 100.0, duration),
        }
    }
}

/// 兼容性检查器
pub struct CompatibilityChecker;

impl CompatibilityChecker {
    /// 检查Skill结构是否包含所需字段
    pub fn check_skill_compatibility(skill: &Skill) -> Vec<String> {
        let mut issues = Vec::new();
        
        if skill.name.is_empty() {
            issues.push("技能名称为空".to_string());
        }
        
        if skill.mana_cost == 0 && !skill.name.contains("普通攻击") {
            issues.push("技能法力消耗为0（可能不正确）".to_string());
        }
        
        if skill.cooldown < 0.0 {
            issues.push("技能冷却时间为负数".to_string());
        }
        
        if skill.range <= 0.0 {
            issues.push("技能范围无效".to_string());
        }
        
        issues
    }
    
    /// 检查Buff结构是否包含所需字段
    pub fn check_buff_compatibility(buff: &Buff) -> Vec<String> {
        let mut issues = Vec::new();
        
        if buff.name.is_empty() {
            issues.push("Buff名称为空".to_string());
        }
        
        if buff.duration <= 0.0 && !buff.is_permanent {
            issues.push("非永久Buff持续时间为0或负数".to_string());
        }
        
        if buff.max_stack == 0 {
            issues.push("Buff最大叠加层数为0".to_string());
        }
        
        issues
    }
}
