/// 攻击能力trait实现
/// 
/// 实现AttackCapacity trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;
use crate::battle_unit::types::*;
use crate::battle_unit::traits::AttackCapacity;
use crate::attribute::{AttributeType, CoreAttribute, DerivedAttribute};

impl AttackCapacity for BattleUnitAdapter {
    fn attack_power(&self) -> AttackPower {
        let base_attack = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Attack)
        );
        
        // 基于武器伤害加成
        let weapon_damage = self.get_weapon_damage();
        
        // 基于力量属性(金)的攻击力加成
        let strength_bonus = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Metal)
        ) / 2;
        
        base_attack + weapon_damage + strength_bonus
    }
    
    fn attack_accuracy(&self) -> Accuracy {
        // 基础命中率 = 敏捷 + 木属性
        let agility = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Speed)
        );
        let wood_attr = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Wood)
        );
        
        let base_accuracy = agility + wood_attr;
        
        // 装备命中率加成
        let equipment_bonus = self.get_equipment_accuracy_bonus();
        
        // Buff命中率加成
        let buff_bonus = self.get_buff_accuracy_bonus();
        
        // 状态效果惩罚
        let status_penalty = self.get_accuracy_penalty();
        
        (base_accuracy + equipment_bonus + buff_bonus - status_penalty).max(5) as Accuracy
    }
    
    fn critical_chance(&self) -> CriticalChance {
        // 基础暴击率 = 木属性 / 10
        let wood_attr = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Wood)
        );
        let base_crit = wood_attr as f32 / 10.0;
        
        // 装备暴击率
        let equipment_crit = self.get_equipment_critical_bonus();
        
        // Buff暴击率
        let buff_crit = self.get_buff_critical_bonus();
        
        (base_crit + equipment_crit + buff_crit).min(0.5) // 最大50%暴击率
    }
    
    fn critical_damage_multiplier(&self) -> CriticalMultiplier {
        // 基础暴击伤害倍率1.5 + 金属性加成
        let metal_attr = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Metal)
        );
        let base_multiplier = 1.5;
        let attribute_bonus = metal_attr as f32 / 100.0; // 每100点金属性+1倍率
        
        // 装备暴击伤害加成
        let equipment_bonus = self.get_equipment_critical_damage_bonus();
        
        base_multiplier + attribute_bonus + equipment_bonus
    }
    
    fn attack_range(&self) -> AttackRange {
        // 获取武器攻击范围
        let weapon_range = self.get_weapon_range();
        
        // 基础近战攻击范围
        let base_range = if weapon_range > 1 { weapon_range } else { 1 };
        
        // 技能或buff可能增加攻击范围
        let bonus_range = self.get_attack_range_bonus();
        
        base_range + bonus_range
    }
    
    fn execute_attack(&mut self, target_id: BattleUnitId) -> GameResult<AttackResult> {
        // 检查是否可以攻击
        if !self.can_attack() {
            return Err("无法执行攻击".into());
        }
        
        // 消耗行动点数
        let action_cost = self.calculate_action_cost(&ActionType::Attack);
        self.consume_action_points(action_cost)?;
        
        // 消耗耐力
        let stamina_cost = self.calculate_attack_stamina_cost();
        self.consume_stamina(stamina_cost)?;
        
        // 计算攻击结果
        let attack_result = self.calculate_attack_damage(target_id)?;
        
        // 触发攻击相关效果
        self.trigger_attack_effects(&attack_result)?;
        
        Ok(attack_result)
    }
    
    fn calculate_damage(&self, target: &dyn BattleUnitAdapter) -> DamageResult {
        let base_damage = self.attack_power() as Damage;
        
        // 命中检查
        let accuracy = self.attack_accuracy();
        let target_evasion = target.calculate_evasion();
        let hit_chance = (accuracy as f32 / (accuracy + target_evasion) as f32).min(0.95);
        
        if self.roll_hit_chance() > hit_chance {
            return DamageResult {
                damage: 0,
                damage_type: self.get_primary_damage_type(),
                is_critical: false,
                is_miss: true,
                elemental_effects: vec![],
            };
        }
        
        // 暴击检查
        let is_critical = self.roll_critical_chance() < self.critical_chance();
        let mut final_damage = base_damage;
        
        if is_critical {
            final_damage = (final_damage as f32 * self.critical_damage_multiplier()) as Damage;
        }
        
        // 计算伤害减免
        let damage_type = self.get_primary_damage_type();
        let resistance = target.calculate_damage_reduction(damage_type);
        final_damage = (final_damage as f32 * (1.0 - resistance)) as Damage;
        
        // 获取元素效果
        let elemental_effects = self.get_attack_elemental_effects();
        
        DamageResult {
            damage: final_damage.max(1), // 最小伤害1
            damage_type,
            is_critical,
            is_miss: false,
            elemental_effects,
        }
    }
    
    fn can_attack(&self) -> bool {
        self.can_act() && 
        !self.has_attack_blocking_status() &&
        self.has_enough_stamina(self.calculate_attack_stamina_cost())
    }
    
    fn weapon_proficiency(&self) -> WeaponProficiency {
        // TODO: 基于装备的武器和角色熟练度计算
        WeaponProficiency::Basic
    }
    
    fn dual_wield_penalty(&self) -> f32 {
        if self.is_dual_wielding() {
            0.8 // 双持20%伤害惩罚
        } else {
            1.0
        }
    }
}

impl BattleUnitAdapter {
    fn get_weapon_damage(&self) -> AttackPower {
        // TODO: 从装备系统获取武器伤害
        0
    }
    
    fn get_equipment_accuracy_bonus(&self) -> AttributePoints {
        // TODO: 从装备系统获取命中率加成
        0
    }
    
    fn get_buff_accuracy_bonus(&self) -> AttributePoints {
        // TODO: 从buff系统获取命中率加成
        0
    }
    
    fn get_accuracy_penalty(&self) -> AttributePoints {
        let mut penalty = 0;
        
        // 负面状态惩罚
        if self.has_status(StatusType::Poison) {
            penalty += 5;
        }
        if self.has_status(StatusType::Burn) {
            penalty += 3;
        }
        
        penalty
    }
    
    fn get_equipment_critical_bonus(&self) -> CriticalChance {
        // TODO: 从装备系统获取暴击率加成
        0.0
    }
    
    fn get_buff_critical_bonus(&self) -> CriticalChance {
        // TODO: 从buff系统获取暴击率加成
        0.0
    }
    
    fn get_equipment_critical_damage_bonus(&self) -> CriticalMultiplier {
        // TODO: 从装备系统获取暴击伤害加成
        0.0
    }
    
    fn get_weapon_range(&self) -> AttackRange {
        // TODO: 从装备系统获取武器攻击范围
        1
    }
    
    fn get_attack_range_bonus(&self) -> AttackRange {
        // TODO: 从技能和buff获取攻击范围加成
        0
    }
    
    fn calculate_attack_stamina_cost(&self) -> Stamina {
        let base_cost = 5; // 基础攻击耐力消耗
        
        // 武器重量影响
        let weapon_weight_penalty = self.get_weapon_weight_penalty();
        
        // 体质减少耐力消耗
        let constitution_mod = BattleUnitAdapter::calculate_modifier(
            self.calculate_total_attribute(
                AttributeType::Base(CoreAttribute::Earth)
            )
        );
        
        (base_cost + weapon_weight_penalty - constitution_mod.max(0) as u32).max(1)
    }
    
    fn calculate_attack_damage(&self, target_id: BattleUnitId) -> GameResult<AttackResult> {
        // TODO: 实际计算攻击伤害，需要目标对象
        Ok(AttackResult {
            attacker_id: self.unit_id(),
            target_id,
            damage_dealt: 10, // 临时值
            is_critical: false,
            is_blocked: false,
            effects_applied: vec![],
            action_points_consumed: 2,
            stamina_consumed: 5,
        })
    }
    
    fn trigger_attack_effects(&mut self, result: &AttackResult) -> GameResult<()> {
        // TODO: 触发攻击相关效果（技能触发、buff处理等）
        Ok(())
    }
    
    fn calculate_evasion(&self) -> i32 {
        // 基于敏捷和木属性计算闪避值
        let speed = self.calculate_total_attribute(
            AttributeType::Derived(DerivedAttribute::Speed)
        );
        let wood_attr = self.calculate_total_attribute(
            AttributeType::Base(CoreAttribute::Wood)
        );
        
        (speed + wood_attr) / 2
    }
    
    fn roll_hit_chance(&self) -> f32 {
        // TODO: 实现随机数生成
        0.5
    }
    
    fn roll_critical_chance(&self) -> f32 {
        // TODO: 实现随机数生成
        0.1
    }
    
    fn get_primary_damage_type(&self) -> DamageType {
        // TODO: 基于武器类型确定主要伤害类型
        DamageType::Physical
    }
    
    fn get_attack_elemental_effects(&self) -> Vec<ElementalEffect> {
        // TODO: 基于武器附魔和技能获取元素效果
        vec![]
    }
    
    fn has_attack_blocking_status(&self) -> bool {
        self.has_status(StatusType::Paralysis) ||
        self.has_status(StatusType::Stun) ||
        self.has_status(StatusType::Sleep)
    }
    
    fn is_dual_wielding(&self) -> bool {
        // TODO: 检查是否双持武器
        false
    }
    
    fn get_weapon_weight_penalty(&self) -> u32 {
        // TODO: 基于武器重量计算攻击耐力惩罚
        0
    }
}