use crate::skill::skill::Skill;
use crate::status_panel::StatusPanel;
use std::collections::HashMap;

impl StatusPanel for Skill {
    fn print_status(&self) {
        println!("技能：{}", self.name);
        println!("ID：{}", self.id);
        println!("类型：{}", self.skill_types.iter().map(|t| format!("{}", t)).collect::<Vec<_>>().join(", "));
        println!("冷却时间：{}秒", self.cooldown);
        println!("消耗：{}", self.mana_cost);
        println!("作用距离：{}", self.range);
        println!("作用范围：{}", self.area.as_ref().map(|a| a.to_string()).unwrap_or_else(|| "无".to_string()));
        println!("释放条件：{:?}", self.cast_condition);
        println!("优先级：{}", self.priority);
        println!("描述：{}", self.description);
        if let Some(icon) = &self.icon {
            println!("图标：{}", icon);
        }
    }

    fn get_status_data(&self) -> HashMap<String, serde_json::Value> {
        let mut data = HashMap::new();
        
        data.insert("id".to_string(), serde_json::Value::Number(self.id.into()));
        data.insert("name".to_string(), serde_json::Value::String(self.name.clone()));
        data.insert("skill_types".to_string(), serde_json::json!(self.skill_types));
        data.insert("cooldown".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(self.cooldown).unwrap()));
        data.insert("mana_cost".to_string(), serde_json::Value::Number(self.mana_cost.into()));
        data.insert("range".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(self.range as f64).unwrap()));
        data.insert("area".to_string(), serde_json::json!(self.area));
        data.insert("cast_condition".to_string(), serde_json::json!(self.cast_condition));
        data.insert("priority".to_string(), serde_json::Value::Number(self.priority.into()));
        data.insert("description".to_string(), serde_json::Value::String(self.description.clone()));
        if let Some(icon) = &self.icon {
            data.insert("icon".to_string(), serde_json::Value::String(icon.clone()));
        }

        data
    }
}
