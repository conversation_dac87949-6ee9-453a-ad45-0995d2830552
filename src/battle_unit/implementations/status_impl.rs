/// 状态效果trait实现
/// 
/// 实现StatusEffectManager trait

use super::adapter_struct::BattleUnitAdapter;
use crate::shared::*;
use crate::battle_unit::types::*;
use crate::battle_unit::traits::StatusEffectManager;
use crate::skill::buff::*;

impl StatusEffectManager for BattleUnitAdapter {
    fn active_status_effects(&self) -> Vec<StatusEffectInfo> {
        let mut status_effects = Vec::new();
        
        // 从buff管理器获取活跃状态效果
        for buff in &self.buff_manager.active_buffs {
            if let Some(status_info) = self.buff_to_status_info(buff) {
                status_effects.push(status_info);
            }
        }
        
        status_effects
    }
    
    fn has_status_effect(&self, status: StatusType) -> bool {
        self.active_status_effects()
            .iter()
            .any(|effect| effect.status_type == status)
    }
    
    fn apply_status_effect(&mut self, effect: StatusEffectInfo) -> GameResult<()> {
        // 检查状态抗性
        let resistance = self.status_resistance(effect.status_type);
        if self.roll_resistance_check() < resistance {
            return Ok(()); // 抗性成功，状态无效
        }
        
        // 将状态效果转换为buff
        let buff = self.status_effect_to_buff(effect)?;
        
        // 应用buff到buff管理器
        self.buff_manager.apply_buff(buff)?;
        
        // 触发状态效果应用事件
        self.trigger_status_effect_applied(effect.status_type)?;
        
        Ok(())
    }
    
    fn remove_status_effect(&mut self, status: StatusType) -> GameResult<bool> {
        let mut removed = false;
        
        // 查找并移除对应的buff
        self.buff_manager.active_buffs.retain(|buff| {
            if self.buff_matches_status(buff, status) {
                removed = true;
                false // 移除这个buff
            } else {
                true // 保留这个buff
            }
        });
        
        if removed {
            self.trigger_status_effect_removed(status)?;
        }
        
        Ok(removed)
    }
    
    fn clear_all_status_effects(&mut self) -> GameResult<()> {
        let removed_statuses: Vec<StatusType> = self.active_status_effects()
            .iter()
            .map(|effect| effect.status_type)
            .collect();
        
        // 清除所有状态相关的buff
        self.buff_manager.active_buffs.clear();
        
        // 触发清除事件
        for status in removed_statuses {
            self.trigger_status_effect_removed(status)?;
        }
        
        Ok(())
    }
    
    fn status_effect_duration(&self, status: StatusType) -> Option<Duration> {
        self.active_status_effects()
            .iter()
            .find(|effect| effect.status_type == status)
            .map(|effect| effect.duration)
    }
    
    fn status_effect_intensity(&self, status: StatusType) -> f32 {
        self.active_status_effects()
            .iter()
            .find(|effect| effect.status_type == status)
            .map(|effect| effect.intensity)
            .unwrap_or(0.0)
    }
    
    fn is_incapacitated(&self) -> bool {
        self.has_status_effect(StatusType::Stun) ||
        self.has_status_effect(StatusType::Sleep) ||
        self.has_status_effect(StatusType::Paralysis) ||
        self.current_health() == 0
    }
    
    fn can_be_affected_by(&self, status: StatusType) -> bool {
        // 检查免疫性
        if self.is_immune_to_status(status) {
            return false;
        }
        
        // 检查状态冲突
        if self.has_conflicting_status(status) {
            return false;
        }
        
        // 检查状态上限
        if self.has_reached_status_limit() {
            return false;
        }
        
        true
    }
    
    fn process_status_effects(&mut self, delta_time: Duration) -> GameResult<Vec<StatusEffectEvent>> {
        let mut events = Vec::new();
        
        // 处理每个活跃状态效果
        for effect in self.active_status_effects() {
            let effect_events = self.process_single_status_effect(effect, delta_time)?;
            events.extend(effect_events);
        }
        
        // 更新buff管理器（处理持续时间、周期性效果等）
        self.buff_manager.update(delta_time)?;
        
        Ok(events)
    }
    
    fn apply_temporary_immunity(&mut self, status: StatusType, duration: Duration) -> GameResult<()> {
        // TODO: 实现临时免疫系统
        // 可以通过特殊的buff来实现免疫效果
        Ok(())
    }
    
    fn get_status_effect_modifiers(&self) -> StatusModifiers {
        let mut modifiers = StatusModifiers::default();
        
        // 计算所有状态效果对各种属性的影响
        for effect in self.active_status_effects() {
            self.apply_status_modifiers(&effect, &mut modifiers);
        }
        
        modifiers
    }
}

impl BattleUnitAdapter {
    fn buff_to_status_info(&self, buff: &BuffInstance) -> Option<StatusEffectInfo> {
        // TODO: 将buff转换为状态效果信息
        // 需要实现buff类型到状态类型的映射
        None
    }
    
    fn status_effect_to_buff(&self, effect: StatusEffectInfo) -> GameResult<BuffInstance> {
        // TODO: 将状态效果转换为buff实例
        // 需要创建对应的buff定义
        Err("状态效果转换未实现".into())
    }
    
    fn buff_matches_status(&self, buff: &BuffInstance, status: StatusType) -> bool {
        // TODO: 检查buff是否对应特定状态类型
        false
    }
    
    fn roll_resistance_check(&self) -> f32 {
        // TODO: 实现随机数生成
        0.5
    }
    
    fn trigger_status_effect_applied(&mut self, status: StatusType) -> GameResult<()> {
        // TODO: 触发状态效果应用事件
        Ok(())
    }
    
    fn trigger_status_effect_removed(&mut self, status: StatusType) -> GameResult<()> {
        // TODO: 触发状态效果移除事件
        Ok(())
    }
    
    fn is_immune_to_status(&self, status: StatusType) -> bool {
        // TODO: 检查是否对特定状态免疫
        false
    }
    
    fn has_conflicting_status(&self, status: StatusType) -> bool {
        // 检查状态冲突（如睡眠和兴奋不能同时存在）
        match status {
            StatusType::Sleep => {
                self.has_status_effect(StatusType::Burn) // 燃烧状态阻止睡眠
            },
            StatusType::Freeze => {
                self.has_status_effect(StatusType::Burn) // 燃烧和冰冻互斥
            },
            StatusType::Burn => {
                self.has_status_effect(StatusType::Freeze) // 燃烧和冰冻互斥
            },
            _ => false,
        }
    }
    
    fn has_reached_status_limit(&self) -> bool {
        // 检查状态效果数量上限
        self.active_status_effects().len() >= 10 // 最多10个状态效果
    }
    
    fn process_single_status_effect(
        &mut self, 
        effect: StatusEffectInfo, 
        delta_time: Duration
    ) -> GameResult<Vec<StatusEffectEvent>> {
        let mut events = Vec::new();
        
        match effect.status_type {
            StatusType::Burn => {
                events.extend(self.process_burn_effect(effect, delta_time)?);
            },
            StatusType::Poison => {
                events.extend(self.process_poison_effect(effect, delta_time)?);
            },
            StatusType::Freeze => {
                events.extend(self.process_freeze_effect(effect, delta_time)?);
            },
            StatusType::Paralysis => {
                events.extend(self.process_paralysis_effect(effect, delta_time)?);
            },
            StatusType::Sleep => {
                events.extend(self.process_sleep_effect(effect, delta_time)?);
            },
            StatusType::Stun => {
                events.extend(self.process_stun_effect(effect, delta_time)?);
            },
        }
        
        Ok(events)
    }
    
    fn process_burn_effect(&mut self, effect: StatusEffectInfo, delta_time: Duration) -> GameResult<Vec<StatusEffectEvent>> {
        let mut events = Vec::new();
        
        // 每秒造成火焰伤害
        let damage_per_second = (effect.intensity * 5.0) as Damage;
        let damage = (damage_per_second as f32 * (delta_time as f32 / 1000.0)) as Damage;
        
        if damage > 0 {
            // TODO: 应用火焰伤害
            events.push(StatusEffectEvent {
                status_type: StatusType::Burn,
                event_type: StatusEventType::DamageDealt,
                value: damage as f32,
                target_id: self.unit_id(),
            });
        }
        
        Ok(events)
    }
    
    fn process_poison_effect(&mut self, effect: StatusEffectInfo, delta_time: Duration) -> GameResult<Vec<StatusEffectEvent>> {
        let mut events = Vec::new();
        
        // 每2秒造成毒伤害
        let damage_interval = 2000; // 2秒
        let damage = (effect.intensity * 3.0) as Damage;
        
        // TODO: 实现周期性伤害逻辑
        
        Ok(events)
    }
    
    fn process_freeze_effect(&mut self, effect: StatusEffectInfo, delta_time: Duration) -> GameResult<Vec<StatusEffectEvent>> {
        let events = Vec::new();
        
        // 冰冻状态主要是阻止行动，无周期性效果
        // 但可能有缓慢的生命恢复等
        
        Ok(events)
    }
    
    fn process_paralysis_effect(&mut self, effect: StatusEffectInfo, delta_time: Duration) -> GameResult<Vec<StatusEffectEvent>> {
        let events = Vec::new();
        
        // 麻痹状态主要影响行动能力
        
        Ok(events)
    }
    
    fn process_sleep_effect(&mut self, effect: StatusEffectInfo, delta_time: Duration) -> GameResult<Vec<StatusEffectEvent>> {
        let mut events = Vec::new();
        
        // 睡眠状态可能有生命和法力恢复
        let health_regen = (effect.intensity * 2.0) as Health;
        let mana_regen = (effect.intensity * 3.0) as Mana;
        
        if health_regen > 0 {
            events.push(StatusEffectEvent {
                status_type: StatusType::Sleep,
                event_type: StatusEventType::HealthRestored,
                value: health_regen as f32,
                target_id: self.unit_id(),
            });
        }
        
        Ok(events)
    }
    
    fn process_stun_effect(&mut self, effect: StatusEffectInfo, delta_time: Duration) -> GameResult<Vec<StatusEffectEvent>> {
        let events = Vec::new();
        
        // 眩晕状态主要是阻止行动
        
        Ok(events)
    }
    
    fn apply_status_modifiers(&self, effect: &StatusEffectInfo, modifiers: &mut StatusModifiers) {
        match effect.status_type {
            StatusType::Burn => {
                modifiers.movement_speed_multiplier *= 0.8; // 燃烧减少移动速度
                modifiers.accuracy_penalty += 5; // 燃烧影响命中率
            },
            StatusType::Poison => {
                modifiers.attack_power_multiplier *= 0.9; // 中毒减少攻击力
                modifiers.accuracy_penalty += 3;
            },
            StatusType::Freeze => {
                modifiers.movement_speed_multiplier *= 0.5; // 冰冻大幅减少移动速度
                modifiers.action_speed_multiplier *= 0.7; // 减少行动速度
            },
            StatusType::Paralysis => {
                modifiers.movement_speed_multiplier *= 0.3; // 麻痹严重影响移动
                modifiers.accuracy_penalty += 10;
            },
            StatusType::Sleep => {
                modifiers.movement_speed_multiplier = 0.0; // 睡眠无法移动
                modifiers.action_speed_multiplier = 0.0; // 无法行动
            },
            StatusType::Stun => {
                modifiers.movement_speed_multiplier = 0.0; // 眩晕无法移动
                modifiers.action_speed_multiplier = 0.0; // 无法行动
            },
        }
    }
}