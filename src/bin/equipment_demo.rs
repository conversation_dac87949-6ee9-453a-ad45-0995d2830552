//! 装备系统演示程序
//!
//! 演示装备系统的各种功能：
//! - 装备创建和管理
//! - 装备强化
//! - 套装效果
//! - 装备比较和推荐

use game::equipment::*;

fn main() {
    println!("🎽 装备系统演示程序启动！");
    println!("===========================================");

    // 创建装备服务
    let mut equipment_service = EquipmentService::new();
    let mut equipment_bar = EquipmentBar::new(50);

    // 演示1：创建基础装备
    println!("\n📦 演示1：创建基础装备");
    println!("-------------------------------------------");

    if let Some(sword) = equipment_service.create_equipment_from_template(1001, 1) {
        println!("✅ 创建装备成功：");
        println!("{}", equipment_service.get_equipment_details(&sword));

        // 添加到背包
        equipment_bar.add_to_inventory(sword).unwrap();
        println!("📦 装备已添加到背包");
    }

    if let Some(armor) = equipment_service.create_equipment_from_template(1002, 2) {
        println!("✅ 创建防具成功：");
        println!("{}", equipment_service.get_equipment_details(&armor));

        equipment_bar.add_to_inventory(armor).unwrap();
        println!("📦 防具已添加到背包");
    }

    // 演示2：装备管理
    println!("\n⚔️ 演示2：装备管理");
    println!("-------------------------------------------");

    // 装备武器
    if let Some(sword) = equipment_bar.remove_from_inventory(1) {
        println!("🎯 准备装备：{}", sword.name);
        if let Ok(old_equipment) = equipment_service.equip_to_character(&mut equipment_bar, sword) {
            println!("✅ 装备成功！");
            if let Some(old) = old_equipment {
                println!("📤 替换了旧装备：{}", old.name);
            }
        }
    }

    // 装备防具
    if let Some(armor) = equipment_bar.remove_from_inventory(2) {
        println!("🎯 准备装备：{}", armor.name);
        if let Ok(_) = equipment_service.equip_to_character(&mut equipment_bar, armor) {
            println!("✅ 装备成功！");
        }
    }

    // 显示当前装备状态
    println!("\n📊 当前装备状态：");
    for (eq_type, equipment) in &equipment_bar.equipped {
        println!("  {} - {}", eq_type, equipment.name);
    }

    // 计算总属性
    let total_attrs = equipment_service.get_total_character_attributes(&equipment_bar);
    println!("\n🔢 总属性加成：");
    for (attr_type, attr) in &total_attrs.attributes {
        println!("  {:?}: {:.1}", attr_type, attr.value);
    }

    // 演示3：装备强化
    println!("\n⚡ 演示3：装备强化");
    println!("-------------------------------------------");

    if let Some(weapon) = equipment_bar.equipped.get_mut(&EquipmentType::Weapon) {
        println!("🔨 开始强化武器：{}", weapon.name);
        println!("当前强化等级：+{}", weapon.enhancement.level);

        // 尝试强化几次
        for attempt in 1..=5 {
            let original_level = weapon.enhancement.level;
            let result = equipment_service.enhance_equipment(weapon);

            println!("第{}次强化尝试：{}", attempt, result);

            if weapon.enhancement.level != original_level
                || matches!(result, EnhancementResult::Destroyed)
            {
                break;
            }

            if matches!(result, EnhancementResult::Destroyed) {
                println!("💥 装备被摧毁！演示终止。");
                return;
            }
        }

        println!("最终强化等级：+{}", weapon.enhancement.level);
        println!("装备评分：{:.1}", weapon.get_equipment_score());
    }

    // 演示4：高级装备和套装效果
    println!("\n🌟 演示4：高级装备和套装效果");
    println!("-------------------------------------------");

    // 创建龙鳞套装
    if let Some(dragon_weapon) = equipment_service.create_equipment_from_template(2001, 3) {
        println!("🐉 创建龙鳞剑：");
        println!(
            "{}",
            equipment_service.get_equipment_details(&dragon_weapon)
        );

        // 比较装备
        let current_weapon = equipment_bar.get_equipped(&EquipmentType::Weapon);
        let comparison = equipment_service.compare_equipment(current_weapon, &dragon_weapon);

        println!("📊 装备比较结果：");
        println!("  当前评分：{:.1}", comparison.current_score);
        println!("  新装备评分：{:.1}", comparison.new_score);
        println!("  评分变化：{:+.1}", comparison.score_diff);
        println!(
            "  是否升级：{}",
            if comparison.is_upgrade { "是" } else { "否" }
        );

        println!("\n📈 属性变化：");
        for change in &comparison.attribute_changes {
            println!("  {}", change);
        }

        // 强制装备（忽略等级要求演示）
        if let Ok(_) = equipment_service.equip_to_character(&mut equipment_bar, dragon_weapon) {
            println!("✅ 龙鳞剑装备成功！");
        }
    }

    if let Some(dragon_armor) = equipment_service.create_equipment_from_template(2002, 4) {
        println!("\n🐉 创建龙鳞甲：");
        println!("{}", equipment_service.get_equipment_details(&dragon_armor));

        if let Ok(_) = equipment_service.equip_to_character(&mut equipment_bar, dragon_armor) {
            println!("✅ 龙鳞甲装备成功！");
        }
    }

    // 检查套装效果
    println!("\n🎭 套装效果检查：");
    let active_sets = equipment_service.set_manager.get_active_set_effects();
    if !active_sets.is_empty() {
        for set_effect in active_sets {
            println!("✨ 激活套装：{}", set_effect.set_name);
            println!("  需要件数：{}", set_effect.required_pieces);

            println!("  套装属性加成：");
            for (attr_type, attr) in &set_effect.bonus_attributes.attributes {
                println!("    {:?}: +{:.1}", attr_type, attr.value);
            }

            println!("  特殊效果：");
            for effect in &set_effect.special_effects {
                println!("    - {}", effect);
            }
        }
    } else {
        println!("❌ 未激活任何套装效果");
    }

    // 最终属性统计
    println!("\n🏆 最终装备状态");
    println!("-------------------------------------------");

    let final_attrs = equipment_service.get_total_character_attributes(&equipment_bar);
    println!("总属性加成（装备+套装）：");
    for (attr_type, attr) in &final_attrs.attributes {
        println!("  {:?}: {:.1}", attr_type, attr.value);
    }

    // 装备评分统计
    let mut total_score = 0.0;
    println!("\n装备详情：");
    for (eq_type, equipment) in &equipment_bar.equipped {
        let score = equipment.get_equipment_score();
        total_score += score;
        println!(
            "  {} +{}: {} (评分: {:.1})",
            eq_type, equipment.enhancement.level, equipment.name, score
        );
    }
    println!("总装备评分：{:.1}", total_score);

    // 演示5：装备推荐
    println!("\n🎯 演示5：装备升级推荐");
    println!("-------------------------------------------");

    let recommendations = equipment_service.recommend_equipment_upgrades(&equipment_bar);
    if recommendations.is_empty() {
        println!("✅ 当前装备已是最佳配置，无需升级。");
    } else {
        println!("💡 发现可升级装备：");
        for (eq_type, template_id) in recommendations {
            println!("  {} - 模板ID: {}", eq_type, template_id);
        }
    }

    println!("\n🎉 装备系统演示完成！");
    println!("===========================================");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_equipment_creation() {
        let equipment_service = EquipmentService::new();

        // 测试装备创建
        let equipment = equipment_service.create_equipment_from_template(1001, 1);
        assert!(equipment.is_some());

        let equipment = equipment.unwrap();
        assert_eq!(equipment.name, "烈焰剑");
        assert_eq!(equipment.eq_type, EquipmentType::Weapon);
        assert_eq!(equipment.quality, EquipmentQuality::Common);
    }

    #[test]
    fn test_equipment_enhancement() {
        let equipment_service = EquipmentService::new();
        let mut equipment = equipment_service
            .create_equipment_from_template(1001, 1)
            .unwrap();

        let original_level = equipment.enhancement.level;
        let result = equipment_service.enhance_equipment(&mut equipment);

        // 验证强化结果是有效的
        match result {
            EnhancementResult::Success(level) => {
                assert_eq!(level, original_level + 1);
                assert_eq!(equipment.enhancement.level, level);
            }
            EnhancementResult::Failure => {
                assert_eq!(equipment.enhancement.level, original_level);
            }
            _ => {}
        }
    }

    #[test]
    fn test_equipment_comparison() {
        let equipment_service = EquipmentService::new();

        let basic_equipment = equipment_service
            .create_equipment_from_template(1001, 1)
            .unwrap();
        let advanced_equipment = equipment_service
            .create_equipment_from_template(2001, 2)
            .unwrap();

        let comparison =
            equipment_service.compare_equipment(Some(&basic_equipment), &advanced_equipment);

        assert!(comparison.is_upgrade);
        assert!(comparison.score_diff > 0.0);
        assert!(!comparison.attribute_changes.is_empty());
    }

    #[test]
    fn test_set_effects() {
        let mut equipment_service = EquipmentService::new();
        let mut equipment_bar = EquipmentBar::new(50);

        // 装备龙鳞套装
        let dragon_weapon = equipment_service
            .create_equipment_from_template(2001, 1)
            .unwrap();
        let dragon_armor = equipment_service
            .create_equipment_from_template(2002, 2)
            .unwrap();

        equipment_service
            .equip_to_character(&mut equipment_bar, dragon_weapon)
            .unwrap();
        equipment_service
            .equip_to_character(&mut equipment_bar, dragon_armor)
            .unwrap();

        // 检查套装效果是否激活
        let active_sets = equipment_service.set_manager.get_active_set_effects();
        assert!(!active_sets.is_empty());

        let set_attrs = equipment_service.set_manager.get_total_set_attributes();
        assert!(!set_attrs.attributes.is_empty());
    }
}
