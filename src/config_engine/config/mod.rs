//! # 配置管理模块
//!
//! 负责多源配置的加载、解析、验证和管理
//!
//! ## 核心组件
//!
//! - `ConfigManager`: 统一的配置管理器，协调所有配置源
//! - `ConfigSource`: 配置源抽象接口，支持文件、数据库、HTTP等
//! - `ConfigLoader`: 配置文件解析器，支持JSON、YAML、TOML、JDM格式
//! - `ConfigValidator`: 配置验证器，确保配置的正确性和完整性
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::config::{ConfigManager, FileSystemConfigSource};
//! use game::config_engine::ConfigSourceType;
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let sources = vec![
//!     ConfigSourceType::FileSystem { path: "./configs".to_string() },
//!     ConfigSourceType::Environment { prefix: "GAME".to_string() },
//! ];
//!
//! let manager = ConfigManager::new(sources, true).await?;
//! manager.load_initial_config().await?;
//!
//! let rule_def = manager.get_rule_definition("material_discovery").await?;
//! # Ok(())
//! # }
//! ```

pub mod hot_reload;
pub mod loader;
pub mod manager;
pub mod rollback;
pub mod sources;
pub mod validation;
pub mod version;

// 重新导出主要类型
pub use hot_reload::{HotReloadManager, HotReloadStatus, ReloadHandler};
pub use loader::{ConfigFormat, ConfigLoader, ParsedConfig};
pub use manager::{ConfigChangeEvent, ConfigManager, ConfigManagerStatus, ConfigStats};
pub use rollback::{ConfigRollbackManager, RollbackResult, RollbackStatus, RollbackStrategy};
#[cfg(feature = "reqwest")]
pub use sources::HttpConfigSource;
pub use sources::{
    create_config_source, ConfigSource, EnvironmentConfigSource, FileSystemConfigSource,
};
pub use validation::{ConfigValidator, ValidationResult};
pub use version::{ConfigDiff, ConfigSnapshot, ConfigVersionManager, Version, VersionInfo};

use crate::config_engine::ContextValue;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 规则定义
///
/// 包含规则的所有元信息和执行内容
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleDefinition {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则版本
    pub version: String,
    /// 规则描述
    pub description: Option<String>,
    /// JDM内容（JSON格式的决策模型）
    pub jdm_content: String,
    /// 输入参数Schema
    pub input_schema: Option<serde_json::Value>,
    /// 输出参数Schema
    pub output_schema: Option<serde_json::Value>,
    /// 规则标签
    pub tags: HashMap<String, String>,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,
    /// 是否启用
    pub enabled: bool,
}

impl RuleDefinition {
    pub fn new(id: String, name: String, jdm_content: String) -> Self {
        let now = chrono::Utc::now();
        Self {
            id,
            name,
            version: "1.0.0".to_string(),
            description: None,
            jdm_content,
            input_schema: None,
            output_schema: None,
            tags: HashMap::new(),
            created_at: now,
            updated_at: now,
            enabled: true,
        }
    }

    pub fn with_version(mut self, version: String) -> Self {
        self.version = version;
        self
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn with_input_schema(mut self, schema: serde_json::Value) -> Self {
        self.input_schema = Some(schema);
        self
    }

    pub fn with_output_schema(mut self, schema: serde_json::Value) -> Self {
        self.output_schema = Some(schema);
        self
    }

    pub fn with_tag(mut self, key: String, value: String) -> Self {
        self.tags.insert(key, value);
        self
    }

    pub fn disabled(mut self) -> Self {
        self.enabled = false;
        self
    }
}

/// 类型定义
///
/// 定义自定义数据类型及其验证规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeDefinition {
    /// 类型ID
    pub id: String,
    /// 类型名称
    pub name: String,
    /// 类型描述
    pub description: Option<String>,
    /// 字段定义
    pub fields: HashMap<String, FieldDefinition>,
    /// 验证规则
    pub validation_rules: Vec<String>,
    /// 类型标签
    pub tags: HashMap<String, String>,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

impl TypeDefinition {
    pub fn new(id: String, name: String) -> Self {
        let now = chrono::Utc::now();
        Self {
            id,
            name,
            description: None,
            fields: HashMap::new(),
            validation_rules: Vec::new(),
            tags: HashMap::new(),
            created_at: now,
            updated_at: now,
        }
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn with_field(mut self, name: String, field_def: FieldDefinition) -> Self {
        self.fields.insert(name, field_def);
        self
    }

    pub fn with_validation_rule(mut self, rule: String) -> Self {
        self.validation_rules.push(rule);
        self
    }

    pub fn with_tag(mut self, key: String, value: String) -> Self {
        self.tags.insert(key, value);
        self
    }
}

/// 字段定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FieldDefinition {
    /// 字段类型
    pub field_type: FieldType,
    /// 是否必填
    pub required: bool,
    /// 默认值
    pub default_value: Option<ContextValue>,
    /// 字段描述
    pub description: Option<String>,
    /// 验证规则
    pub validation_rules: Vec<String>,
}

impl FieldDefinition {
    pub fn new(field_type: FieldType, required: bool) -> Self {
        Self {
            field_type,
            required,
            default_value: None,
            description: None,
            validation_rules: Vec::new(),
        }
    }

    pub fn with_default(mut self, value: ContextValue) -> Self {
        self.default_value = Some(value);
        self
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn with_validation_rule(mut self, rule: String) -> Self {
        self.validation_rules.push(rule);
        self
    }
}

/// 字段类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FieldType {
    String,
    Integer,
    Float,
    Boolean,
    Array(Box<FieldType>),
    Object,
    Enum(Vec<String>),
    Reference(String), // 引用其他类型
}

impl FieldType {
    pub fn array_of(element_type: FieldType) -> Self {
        FieldType::Array(Box::new(element_type))
    }

    pub fn enum_of(values: Vec<String>) -> Self {
        FieldType::Enum(values)
    }

    pub fn reference_to(type_name: String) -> Self {
        FieldType::Reference(type_name)
    }
}

/// 合并后的配置
///
/// 包含从所有配置源聚合后的最终配置
#[derive(Debug, Clone)]
pub struct MergedConfig {
    /// 所有规则定义
    pub rules: HashMap<String, RuleDefinition>,
    /// 所有类型定义
    pub types: HashMap<String, TypeDefinition>,
    /// 配置源信息
    pub source_info: Vec<SourceInfo>,
    /// 合并时间
    pub merged_at: chrono::DateTime<chrono::Utc>,
}

impl MergedConfig {
    pub fn new() -> Self {
        Self {
            rules: HashMap::new(),
            types: HashMap::new(),
            source_info: Vec::new(),
            merged_at: chrono::Utc::now(),
        }
    }

    pub fn merge_rules(&mut self, source_name: String, rules: HashMap<String, RuleDefinition>) {
        for (id, rule) in rules {
            self.rules.insert(id.clone(), rule);
            self.source_info.push(SourceInfo {
                source_name: source_name.clone(),
                item_type: "rule".to_string(),
                item_id: id,
                loaded_at: chrono::Utc::now(),
            });
        }
    }

    pub fn merge_types(&mut self, source_name: String, types: HashMap<String, TypeDefinition>) {
        for (id, type_def) in types {
            self.types.insert(id.clone(), type_def);
            self.source_info.push(SourceInfo {
                source_name: source_name.clone(),
                item_type: "type".to_string(),
                item_id: id,
                loaded_at: chrono::Utc::now(),
            });
        }
    }
}

impl Default for MergedConfig {
    fn default() -> Self {
        Self::new()
    }
}

/// 配置源信息
#[derive(Debug, Clone)]
pub struct SourceInfo {
    /// 配置源名称
    pub source_name: String,
    /// 项目类型（rule, type等）
    pub item_type: String,
    /// 项目ID
    pub item_id: String,
    /// 加载时间
    pub loaded_at: chrono::DateTime<chrono::Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rule_definition_creation() {
        let rule = RuleDefinition::new(
            "test_rule".to_string(),
            "Test Rule".to_string(),
            r#"{"contentType": "application/vnd.gorules.decision"}"#.to_string(),
        )
        .with_version("1.1.0".to_string())
        .with_description("A test rule".to_string())
        .with_tag("category".to_string(), "test".to_string());

        assert_eq!(rule.id, "test_rule");
        assert_eq!(rule.version, "1.1.0");
        assert_eq!(rule.description, Some("A test rule".to_string()));
        assert_eq!(rule.tags.get("category"), Some(&"test".to_string()));
        assert!(rule.enabled);
    }

    #[test]
    fn test_type_definition_creation() {
        let type_def = TypeDefinition::new("Material".to_string(), "Material Type".to_string())
            .with_field(
                "id".to_string(),
                FieldDefinition::new(FieldType::String, true)
                    .with_description("Material ID".to_string()),
            )
            .with_field(
                "rarity".to_string(),
                FieldDefinition::new(
                    FieldType::enum_of(vec![
                        "Common".to_string(),
                        "Rare".to_string(),
                        "Epic".to_string(),
                    ]),
                    true,
                ),
            )
            .with_validation_rule("id.length > 0".to_string());

        assert_eq!(type_def.id, "Material");
        assert_eq!(type_def.fields.len(), 2);
        assert_eq!(type_def.validation_rules.len(), 1);
    }

    #[test]
    fn test_field_type_creation() {
        let string_type = FieldType::String;
        let array_type = FieldType::array_of(FieldType::Integer);
        let enum_type = FieldType::enum_of(vec!["A".to_string(), "B".to_string()]);
        let ref_type = FieldType::reference_to("CustomType".to_string());

        assert_eq!(string_type, FieldType::String);
        assert_eq!(array_type, FieldType::Array(Box::new(FieldType::Integer)));
        assert_eq!(
            enum_type,
            FieldType::Enum(vec!["A".to_string(), "B".to_string()])
        );
        assert_eq!(ref_type, FieldType::Reference("CustomType".to_string()));
    }

    #[test]
    fn test_merged_config() {
        let mut config = MergedConfig::new();

        let rule = RuleDefinition::new("test".to_string(), "Test".to_string(), "{}".to_string());

        let mut rules = HashMap::new();
        rules.insert("test".to_string(), rule);

        config.merge_rules("file_source".to_string(), rules);

        assert_eq!(config.rules.len(), 1);
        assert_eq!(config.source_info.len(), 1);
        assert_eq!(config.source_info[0].source_name, "file_source");
    }
}
