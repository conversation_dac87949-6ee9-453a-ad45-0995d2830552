//! # 规则依赖解析模块
//!
//! 提供规则间依赖关系的分析、解析和执行顺序优化功能
//!
//! ## 核心功能
//!
//! - 依赖关系建模和验证
//! - 循环依赖检测和解决
//! - 拓扑排序和执行顺序优化
//! - 依赖树可视化和分析
//! - 动态依赖解析和热更新
//!
//! ## 使用示例
//!
//! ```rust
//! use game::config_engine::rules::dependency::{DependencyResolver, RuleDependency};
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! let mut resolver = DependencyResolver::new();
//!
//! // 定义规则依赖
//! resolver.add_dependency("rule_b", "rule_a").await?;
//! resolver.add_dependency("rule_c", "rule_b").await?;
//!
//! // 解析执行顺序
//! let execution_order = resolver.resolve_execution_order(&["rule_a", "rule_b", "rule_c"]).await?;
//! assert_eq!(execution_order, vec!["rule_a", "rule_b", "rule_c"]);
//! # Ok(())
//! # }
//! ```

use crate::config_engine::{error::ConfigEngineError, Result};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;

/// 规则依赖定义
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct RuleDependency {
    /// 依赖的规则ID
    pub dependency_rule_id: String,
    /// 被依赖的规则ID
    pub dependent_rule_id: String,
    /// 依赖类型
    pub dependency_type: DependencyType,
    /// 是否为硬依赖（必须等待完成）
    pub is_hard_dependency: bool,
    /// 依赖条件（可选）
    pub condition: Option<String>,
    /// 依赖优先级
    pub priority: i32,
    /// 超时时间（毫秒）
    pub timeout_ms: Option<u64>,
    /// 依赖描述
    pub description: Option<String>,
}

impl RuleDependency {
    pub fn new(dependency_rule_id: String, dependent_rule_id: String) -> Self {
        Self {
            dependency_rule_id,
            dependent_rule_id,
            dependency_type: DependencyType::Sequential,
            is_hard_dependency: true,
            condition: None,
            priority: 0,
            timeout_ms: None,
            description: None,
        }
    }

    pub fn with_type(mut self, dependency_type: DependencyType) -> Self {
        self.dependency_type = dependency_type;
        self
    }

    pub fn with_soft_dependency(mut self) -> Self {
        self.is_hard_dependency = false;
        self
    }

    pub fn with_condition(mut self, condition: String) -> Self {
        self.condition = Some(condition);
        self
    }

    pub fn with_priority(mut self, priority: i32) -> Self {
        self.priority = priority;
        self
    }

    pub fn with_timeout(mut self, timeout_ms: u64) -> Self {
        self.timeout_ms = Some(timeout_ms);
        self
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }
}

/// 依赖类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum DependencyType {
    /// 顺序依赖（必须按顺序执行）
    Sequential,
    /// 数据依赖（需要前置规则的输出）
    Data,
    /// 状态依赖（需要前置规则的状态变更）
    State,
    /// 条件依赖（基于条件的依赖）
    Conditional,
    /// 资源依赖（需要前置规则释放资源）
    Resource,
    /// 异步依赖（可以并行但需要结果）
    Async,
}

/// 依赖图节点
#[derive(Debug, Clone)]
pub struct DependencyNode {
    /// 规则ID
    pub rule_id: String,
    /// 直接依赖
    pub dependencies: HashSet<String>,
    /// 被依赖项
    pub dependents: HashSet<String>,
    /// 节点深度
    pub depth: Option<usize>,
    /// 是否已处理
    pub processed: bool,
    /// 节点权重
    pub weight: i32,
}

impl DependencyNode {
    pub fn new(rule_id: String) -> Self {
        Self {
            rule_id,
            dependencies: HashSet::new(),
            dependents: HashSet::new(),
            depth: None,
            processed: false,
            weight: 0,
        }
    }

    pub fn add_dependency(&mut self, dependency_id: String) {
        self.dependencies.insert(dependency_id);
    }

    pub fn add_dependent(&mut self, dependent_id: String) {
        self.dependents.insert(dependent_id);
    }

    pub fn is_leaf(&self) -> bool {
        self.dependents.is_empty()
    }

    pub fn is_root(&self) -> bool {
        self.dependencies.is_empty()
    }

    pub fn calculate_weight(&mut self) {
        self.weight = (self.dependencies.len() as i32) * 2 + (self.dependents.len() as i32);
    }
}

/// 依赖解析器
pub struct DependencyResolver {
    /// 依赖关系存储
    dependencies: Arc<RwLock<HashMap<String, RuleDependency>>>,
    /// 依赖图
    dependency_graph: Arc<RwLock<HashMap<String, DependencyNode>>>,
    /// 解析缓存
    resolution_cache: Arc<RwLock<HashMap<String, Vec<String>>>>,
    /// 循环依赖检测缓存
    cycle_detection_cache: Arc<RwLock<HashMap<String, bool>>>,
    /// 解析器配置
    config: DependencyResolverConfig,
}

impl DependencyResolver {
    /// 创建新的依赖解析器
    pub fn new() -> Self {
        Self::with_config(DependencyResolverConfig::default())
    }

    /// 使用配置创建依赖解析器
    pub fn with_config(config: DependencyResolverConfig) -> Self {
        Self {
            dependencies: Arc::new(RwLock::new(HashMap::new())),
            dependency_graph: Arc::new(RwLock::new(HashMap::new())),
            resolution_cache: Arc::new(RwLock::new(HashMap::new())),
            cycle_detection_cache: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }

    /// 添加依赖关系
    pub async fn add_dependency(
        &self,
        dependent_rule_id: &str,
        dependency_rule_id: &str,
    ) -> Result<()> {
        let dependency = RuleDependency::new(
            dependency_rule_id.to_string(),
            dependent_rule_id.to_string(),
        );
        self.add_dependency_with_details(dependency).await
    }

    /// 添加详细依赖关系
    pub async fn add_dependency_with_details(&self, dependency: RuleDependency) -> Result<()> {
        let dependency_key = format!(
            "{}:{}",
            dependency.dependent_rule_id, dependency.dependency_rule_id
        );

        // 检查是否会形成循环依赖
        if self
            .would_create_cycle(
                &dependency.dependent_rule_id,
                &dependency.dependency_rule_id,
            )
            .await?
        {
            return Err(ConfigEngineError::CircularDependency {
                dependency_chain: format!(
                    "{} -> {}",
                    dependency.dependency_rule_id, dependency.dependent_rule_id
                ),
            });
        }

        // 添加依赖关系
        {
            let mut dependencies = self.dependencies.write().await;
            dependencies.insert(dependency_key, dependency.clone());
        }

        // 更新依赖图
        self.update_dependency_graph(&dependency).await;

        // 清除相关缓存
        self.clear_resolution_cache().await;

        log::debug!(
            "Added dependency: {} depends on {}",
            dependency.dependent_rule_id,
            dependency.dependency_rule_id
        );

        Ok(())
    }

    /// 移除依赖关系
    pub async fn remove_dependency(
        &self,
        dependent_rule_id: &str,
        dependency_rule_id: &str,
    ) -> Result<bool> {
        let dependency_key = format!("{}:{}", dependent_rule_id, dependency_rule_id);

        let removed = {
            let mut dependencies = self.dependencies.write().await;
            dependencies.remove(&dependency_key).is_some()
        };

        if removed {
            // 更新依赖图
            self.remove_from_dependency_graph(dependent_rule_id, dependency_rule_id)
                .await;

            // 清除相关缓存
            self.clear_resolution_cache().await;

            log::debug!(
                "Removed dependency: {} no longer depends on {}",
                dependent_rule_id,
                dependency_rule_id
            );
        }

        Ok(removed)
    }

    /// 获取规则的直接依赖
    pub async fn get_direct_dependencies(&self, rule_id: &str) -> Vec<String> {
        let dependencies = self.dependencies.read().await;
        dependencies
            .values()
            .filter(|dep| dep.dependent_rule_id == rule_id)
            .map(|dep| dep.dependency_rule_id.clone())
            .collect()
    }

    /// 获取规则的传递依赖
    pub async fn get_transitive_dependencies(&self, rule_id: &str) -> Result<HashSet<String>> {
        let mut all_dependencies = HashSet::new();
        let mut to_process = VecDeque::new();
        to_process.push_back(rule_id.to_string());

        while let Some(current_rule) = to_process.pop_front() {
            let direct_deps = self.get_direct_dependencies(&current_rule).await;

            for dep in direct_deps {
                if !all_dependencies.contains(&dep) {
                    all_dependencies.insert(dep.clone());
                    to_process.push_back(dep);
                }
            }
        }

        Ok(all_dependencies)
    }

    /// 解析执行顺序
    pub async fn resolve_execution_order(&self, rule_ids: &[&str]) -> Result<Vec<String>> {
        let cache_key = rule_ids.join(",");

        // 检查缓存
        if self.config.enable_cache {
            let cache = self.resolution_cache.read().await;
            if let Some(cached_order) = cache.get(&cache_key) {
                return Ok(cached_order.clone());
            }
        }

        // 构建子图
        let subgraph = self.build_subgraph(rule_ids).await?;

        // 执行拓扑排序
        let execution_order = self.topological_sort(&subgraph).await?;

        // 缓存结果
        if self.config.enable_cache {
            let mut cache = self.resolution_cache.write().await;
            cache.insert(cache_key, execution_order.clone());
        }

        Ok(execution_order)
    }

    /// 检测循环依赖
    pub async fn detect_cycles(&self) -> Result<Vec<Vec<String>>> {
        let graph = self.dependency_graph.read().await;
        let mut cycles = Vec::new();
        let mut visited = HashSet::new();
        let mut rec_stack = HashSet::new();

        for node_id in graph.keys() {
            if !visited.contains(node_id) {
                let mut current_path = Vec::new();
                if let Some(cycle) = self
                    .dfs_detect_cycle(
                        node_id,
                        &graph,
                        &mut visited,
                        &mut rec_stack,
                        &mut current_path,
                    )
                    .await
                {
                    cycles.push(cycle);
                }
            }
        }

        Ok(cycles)
    }

    /// 优化执行顺序
    pub async fn optimize_execution_order(&self, rule_ids: &[&str]) -> Result<Vec<String>> {
        let mut execution_order = self.resolve_execution_order(rule_ids).await?;

        // 基于权重优化
        let graph = self.dependency_graph.read().await;
        execution_order.sort_by(|a, b| {
            let weight_a = graph.get(a).map(|n| n.weight).unwrap_or(0);
            let weight_b = graph.get(b).map(|n| n.weight).unwrap_or(0);
            weight_a.cmp(&weight_b)
        });

        Ok(execution_order)
    }

    /// 分析依赖复杂度
    pub async fn analyze_dependency_complexity(
        &self,
        rule_ids: &[&str],
    ) -> DependencyComplexityAnalysis {
        let graph = self.dependency_graph.read().await;

        let mut total_dependencies = 0;
        let mut max_depth = 0;
        let mut cyclic_dependencies = 0;
        let mut isolated_rules = 0;

        for rule_id in rule_ids {
            if let Some(node) = graph.get(*rule_id) {
                total_dependencies += node.dependencies.len();
                if let Some(depth) = node.depth {
                    max_depth = max_depth.max(depth);
                }
                if node.is_root() && node.is_leaf() {
                    isolated_rules += 1;
                }
            }
        }

        // 检测循环依赖
        match self.detect_cycles().await {
            Ok(cycles) => cyclic_dependencies = cycles.len(),
            Err(_) => cyclic_dependencies = 0,
        }

        let complexity_score = self.calculate_complexity_score(
            total_dependencies,
            max_depth,
            cyclic_dependencies,
            isolated_rules,
            rule_ids.len(),
        );

        DependencyComplexityAnalysis {
            total_rules: rule_ids.len(),
            total_dependencies,
            max_depth,
            average_dependencies: total_dependencies as f64 / rule_ids.len() as f64,
            cyclic_dependencies,
            isolated_rules,
            complexity_score,
            recommendations: self.generate_optimization_recommendations(
                total_dependencies,
                max_depth,
                cyclic_dependencies,
                isolated_rules,
                rule_ids.len(),
            ),
        }
    }

    /// 生成依赖图可视化
    pub async fn generate_dependency_graph_dot(&self, rule_ids: &[&str]) -> Result<String> {
        let mut dot = String::from("digraph DependencyGraph {\n");
        dot.push_str("  rankdir=TB;\n");
        dot.push_str("  node [shape=box];\n");

        let dependencies = self.dependencies.read().await;

        // 添加节点
        for rule_id in rule_ids {
            dot.push_str(&format!("  \"{}\";\n", rule_id));
        }

        // 添加边
        for dependency in dependencies.values() {
            if rule_ids.contains(&dependency.dependent_rule_id.as_str())
                && rule_ids.contains(&dependency.dependency_rule_id.as_str())
            {
                let edge_style = match dependency.dependency_type {
                    DependencyType::Sequential => "solid",
                    DependencyType::Data => "dashed",
                    DependencyType::Conditional => "dotted",
                    _ => "solid",
                };

                let edge_color = if dependency.is_hard_dependency {
                    "red"
                } else {
                    "blue"
                };

                dot.push_str(&format!(
                    "  \"{}\" -> \"{}\" [style={}, color={}];\n",
                    dependency.dependency_rule_id,
                    dependency.dependent_rule_id,
                    edge_style,
                    edge_color
                ));
            }
        }

        dot.push_str("}\n");
        Ok(dot)
    }

    /// 验证依赖一致性
    pub async fn validate_dependencies(&self) -> Result<DependencyValidationReport> {
        let mut issues = Vec::new();
        let mut warnings = Vec::new();

        let dependencies = self.dependencies.read().await;
        let graph = self.dependency_graph.read().await;

        // 检查孤立的依赖定义
        for dependency in dependencies.values() {
            if !graph.contains_key(&dependency.dependent_rule_id)
                || !graph.contains_key(&dependency.dependency_rule_id)
            {
                issues.push(DependencyIssue {
                    issue_type: DependencyIssueType::OrphanedDependency,
                    rule_id: dependency.dependent_rule_id.clone(),
                    description: format!(
                        "Dependency references non-existent rule: {} -> {}",
                        dependency.dependency_rule_id, dependency.dependent_rule_id
                    ),
                    severity: IssueSeverity::Error,
                });
            }
        }

        // 检查循环依赖
        match self.detect_cycles().await {
            Ok(cycles) => {
                for cycle in cycles {
                    issues.push(DependencyIssue {
                        issue_type: DependencyIssueType::CircularDependency,
                        rule_id: cycle.first().unwrap_or(&String::new()).clone(),
                        description: format!(
                            "Circular dependency detected: {}",
                            cycle.join(" -> ")
                        ),
                        severity: IssueSeverity::Error,
                    });
                }
            }
            Err(e) => {
                issues.push(DependencyIssue {
                    issue_type: DependencyIssueType::ValidationError,
                    rule_id: String::new(),
                    description: format!("Error detecting cycles: {}", e),
                    severity: IssueSeverity::Warning,
                });
            }
        }

        // 检查深度过深的依赖链
        for node in graph.values() {
            if let Some(depth) = node.depth {
                if depth > self.config.max_dependency_depth {
                    warnings.push(DependencyIssue {
                        issue_type: DependencyIssueType::ExcessiveDepth,
                        rule_id: node.rule_id.clone(),
                        description: format!(
                            "Dependency depth {} exceeds maximum {}",
                            depth, self.config.max_dependency_depth
                        ),
                        severity: IssueSeverity::Warning,
                    });
                }
            }
        }

        Ok(DependencyValidationReport {
            is_valid: issues.is_empty(),
            total_dependencies: dependencies.len(),
            total_rules: graph.len(),
            issues,
            warnings,
            validated_at: chrono::Utc::now(),
        })
    }

    /// 清除所有缓存
    pub async fn clear_all_caches(&self) {
        self.clear_resolution_cache().await;
        self.clear_cycle_detection_cache().await;
    }

    /// 获取依赖统计信息
    pub async fn get_dependency_statistics(&self) -> DependencyStatistics {
        let dependencies = self.dependencies.read().await;
        let graph = self.dependency_graph.read().await;

        let mut by_type = HashMap::new();
        for dependency in dependencies.values() {
            *by_type
                .entry(dependency.dependency_type.clone())
                .or_insert(0) += 1;
        }

        let hard_dependencies = dependencies
            .values()
            .filter(|d| d.is_hard_dependency)
            .count();

        let conditional_dependencies = dependencies
            .values()
            .filter(|d| d.condition.is_some())
            .count();

        DependencyStatistics {
            total_dependencies: dependencies.len(),
            total_rules: graph.len(),
            hard_dependencies,
            soft_dependencies: dependencies.len() - hard_dependencies,
            conditional_dependencies,
            dependencies_by_type: by_type,
            cache_hit_rate: self.calculate_cache_hit_rate().await,
        }
    }

    // 私有辅助方法

    async fn would_create_cycle(
        &self,
        dependent_rule_id: &str,
        dependency_rule_id: &str,
    ) -> Result<bool> {
        // 检查是否会创建直接循环
        if dependent_rule_id == dependency_rule_id {
            return Ok(true);
        }

        // 检查是否会创建间接循环
        let transitive_deps = self.get_transitive_dependencies(dependency_rule_id).await?;
        Ok(transitive_deps.contains(dependent_rule_id))
    }

    async fn update_dependency_graph(&self, dependency: &RuleDependency) {
        let mut graph = self.dependency_graph.write().await;

        // 确保节点存在
        let dependent_node = graph
            .entry(dependency.dependent_rule_id.clone())
            .or_insert_with(|| DependencyNode::new(dependency.dependent_rule_id.clone()));
        dependent_node.add_dependency(dependency.dependency_rule_id.clone());

        let dependency_node = graph
            .entry(dependency.dependency_rule_id.clone())
            .or_insert_with(|| DependencyNode::new(dependency.dependency_rule_id.clone()));
        dependency_node.add_dependent(dependency.dependent_rule_id.clone());

        // 重新计算权重和深度
        for node in graph.values_mut() {
            node.calculate_weight();
        }

        // 计算深度需要拓扑排序
        // 这里简化实现，实际应该正确计算
    }

    async fn remove_from_dependency_graph(
        &self,
        dependent_rule_id: &str,
        dependency_rule_id: &str,
    ) {
        let mut graph = self.dependency_graph.write().await;

        if let Some(dependent_node) = graph.get_mut(dependent_rule_id) {
            dependent_node.dependencies.remove(dependency_rule_id);
            dependent_node.calculate_weight();
        }

        if let Some(dependency_node) = graph.get_mut(dependency_rule_id) {
            dependency_node.dependents.remove(dependent_rule_id);
            dependency_node.calculate_weight();
        }
    }

    async fn build_subgraph(&self, rule_ids: &[&str]) -> Result<HashMap<String, DependencyNode>> {
        let graph = self.dependency_graph.read().await;
        let mut subgraph = HashMap::new();

        for rule_id in rule_ids {
            if let Some(node) = graph.get(*rule_id) {
                let mut subnode = node.clone();
                // 只保留子图内的依赖关系
                subnode
                    .dependencies
                    .retain(|dep| rule_ids.contains(&dep.as_str()));
                subnode
                    .dependents
                    .retain(|dep| rule_ids.contains(&dep.as_str()));
                subgraph.insert(rule_id.to_string(), subnode);
            } else {
                // 创建新节点（可能是没有依赖关系的规则）
                subgraph.insert(
                    rule_id.to_string(),
                    DependencyNode::new(rule_id.to_string()),
                );
            }
        }

        Ok(subgraph)
    }

    async fn topological_sort(
        &self,
        graph: &HashMap<String, DependencyNode>,
    ) -> Result<Vec<String>> {
        let mut in_degree = HashMap::new();
        let mut queue = VecDeque::new();
        let mut result = Vec::new();

        // 计算入度
        for (rule_id, node) in graph {
            in_degree.insert(rule_id.clone(), node.dependencies.len());
            if node.dependencies.is_empty() {
                queue.push_back(rule_id.clone());
            }
        }

        // 拓扑排序
        while let Some(current) = queue.pop_front() {
            result.push(current.clone());

            if let Some(node) = graph.get(&current) {
                for dependent in &node.dependents {
                    if let Some(degree) = in_degree.get_mut(dependent) {
                        *degree -= 1;
                        if *degree == 0 {
                            queue.push_back(dependent.clone());
                        }
                    }
                }
            }
        }

        // 检查是否所有节点都被处理
        if result.len() != graph.len() {
            return Err(ConfigEngineError::CircularDependency {
                dependency_chain: "Unresolved circular dependency".to_string(),
            });
        }

        Ok(result)
    }

    async fn dfs_detect_cycle(
        &self,
        node_id: &str,
        graph: &HashMap<String, DependencyNode>,
        visited: &mut HashSet<String>,
        rec_stack: &mut HashSet<String>,
        current_path: &mut Vec<String>,
    ) -> Option<Vec<String>> {
        visited.insert(node_id.to_string());
        rec_stack.insert(node_id.to_string());
        current_path.push(node_id.to_string());

        if let Some(node) = graph.get(node_id) {
            for dependency in &node.dependencies {
                if !visited.contains(dependency) {
                    if let Some(cycle) = Box::pin(self.dfs_detect_cycle(
                        dependency,
                        graph,
                        visited,
                        rec_stack,
                        current_path,
                    ))
                    .await
                    {
                        return Some(cycle);
                    }
                } else if rec_stack.contains(dependency) {
                    // 找到循环
                    let cycle_start = current_path.iter().position(|r| r == dependency)?;
                    let mut cycle = current_path[cycle_start..].to_vec();
                    cycle.push(dependency.clone());
                    return Some(cycle);
                }
            }
        }

        rec_stack.remove(node_id);
        current_path.pop();
        None
    }

    async fn clear_resolution_cache(&self) {
        let mut cache = self.resolution_cache.write().await;
        cache.clear();
    }

    async fn clear_cycle_detection_cache(&self) {
        let mut cache = self.cycle_detection_cache.write().await;
        cache.clear();
    }

    async fn calculate_cache_hit_rate(&self) -> f64 {
        // 简化实现，实际应该维护命中统计
        0.0
    }

    fn calculate_complexity_score(
        &self,
        total_dependencies: usize,
        max_depth: usize,
        cyclic_dependencies: usize,
        isolated_rules: usize,
        total_rules: usize,
    ) -> u32 {
        let mut score = 0u32;

        // 依赖密度影响
        let dependency_density = total_dependencies as f64 / total_rules as f64;
        score += (dependency_density * 20.0) as u32;

        // 深度影响
        score += (max_depth * 10) as u32;

        // 循环依赖严重影响
        score += (cyclic_dependencies * 50) as u32;

        // 孤立规则减少复杂度
        score = score.saturating_sub((isolated_rules * 5) as u32);

        score
    }

    fn generate_optimization_recommendations(
        &self,
        total_dependencies: usize,
        max_depth: usize,
        cyclic_dependencies: usize,
        isolated_rules: usize,
        total_rules: usize,
    ) -> Vec<String> {
        let mut recommendations = Vec::new();

        if cyclic_dependencies > 0 {
            recommendations.push("解决循环依赖以避免死锁和无限递归".to_string());
        }

        if max_depth > 10 {
            recommendations.push("考虑减少依赖链深度以提高执行效率".to_string());
        }

        let dependency_density = total_dependencies as f64 / total_rules as f64;
        if dependency_density > 2.0 {
            recommendations.push("依赖密度过高，考虑重新设计规则架构".to_string());
        }

        if isolated_rules > total_rules / 3 {
            recommendations.push("考虑合并或重新组织孤立的规则".to_string());
        }

        if recommendations.is_empty() {
            recommendations.push("依赖结构合理，无需特别优化".to_string());
        }

        recommendations
    }
}

impl Default for DependencyResolver {
    fn default() -> Self {
        Self::new()
    }
}

/// 依赖解析器配置
#[derive(Debug, Clone)]
pub struct DependencyResolverConfig {
    /// 是否启用缓存
    pub enable_cache: bool,
    /// 缓存大小限制
    pub cache_size: usize,
    /// 最大依赖深度
    pub max_dependency_depth: usize,
    /// 是否自动检测循环依赖
    pub auto_detect_cycles: bool,
    /// 循环依赖检测间隔（秒）
    pub cycle_detection_interval_seconds: u64,
}

impl Default for DependencyResolverConfig {
    fn default() -> Self {
        Self {
            enable_cache: true,
            cache_size: 1000,
            max_dependency_depth: 20,
            auto_detect_cycles: true,
            cycle_detection_interval_seconds: 300, // 5分钟
        }
    }
}

/// 依赖复杂度分析结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyComplexityAnalysis {
    /// 总规则数
    pub total_rules: usize,
    /// 总依赖数
    pub total_dependencies: usize,
    /// 最大深度
    pub max_depth: usize,
    /// 平均依赖数
    pub average_dependencies: f64,
    /// 循环依赖数
    pub cyclic_dependencies: usize,
    /// 孤立规则数
    pub isolated_rules: usize,
    /// 复杂度分数
    pub complexity_score: u32,
    /// 优化建议
    pub recommendations: Vec<String>,
}

/// 依赖验证报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyValidationReport {
    /// 是否有效
    pub is_valid: bool,
    /// 总依赖数
    pub total_dependencies: usize,
    /// 总规则数
    pub total_rules: usize,
    /// 问题列表
    pub issues: Vec<DependencyIssue>,
    /// 警告列表
    pub warnings: Vec<DependencyIssue>,
    /// 验证时间
    pub validated_at: chrono::DateTime<chrono::Utc>,
}

/// 依赖问题
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyIssue {
    /// 问题类型
    pub issue_type: DependencyIssueType,
    /// 相关规则ID
    pub rule_id: String,
    /// 问题描述
    pub description: String,
    /// 严重性
    pub severity: IssueSeverity,
}

/// 依赖问题类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DependencyIssueType {
    /// 循环依赖
    CircularDependency,
    /// 孤立的依赖
    OrphanedDependency,
    /// 深度过深
    ExcessiveDepth,
    /// 验证错误
    ValidationError,
}

/// 问题严重性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IssueSeverity {
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 错误
    Error,
    /// 致命
    Critical,
}

/// 依赖统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyStatistics {
    /// 总依赖数
    pub total_dependencies: usize,
    /// 总规则数
    pub total_rules: usize,
    /// 硬依赖数
    pub hard_dependencies: usize,
    /// 软依赖数
    pub soft_dependencies: usize,
    /// 条件依赖数
    pub conditional_dependencies: usize,
    /// 按类型分组的依赖数
    pub dependencies_by_type: HashMap<DependencyType, usize>,
    /// 缓存命中率
    pub cache_hit_rate: f64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_simple_dependency() {
        let resolver = DependencyResolver::new();

        resolver.add_dependency("rule_b", "rule_a").await.unwrap();

        let order = resolver
            .resolve_execution_order(&["rule_a", "rule_b"])
            .await
            .unwrap();
        assert_eq!(order, vec!["rule_a", "rule_b"]);
    }

    #[tokio::test]
    async fn test_complex_dependency_chain() {
        let resolver = DependencyResolver::new();

        resolver.add_dependency("rule_b", "rule_a").await.unwrap();
        resolver.add_dependency("rule_c", "rule_b").await.unwrap();
        resolver.add_dependency("rule_d", "rule_c").await.unwrap();

        let order = resolver
            .resolve_execution_order(&["rule_a", "rule_b", "rule_c", "rule_d"])
            .await
            .unwrap();
        assert_eq!(order, vec!["rule_a", "rule_b", "rule_c", "rule_d"]);
    }

    #[tokio::test]
    async fn test_circular_dependency_detection() {
        let resolver = DependencyResolver::new();

        resolver.add_dependency("rule_b", "rule_a").await.unwrap();

        let result = resolver.add_dependency("rule_a", "rule_b").await;
        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            ConfigEngineError::CircularDependency { .. }
        ));
    }

    #[tokio::test]
    async fn test_parallel_branches() {
        let resolver = DependencyResolver::new();

        // A -> B, A -> C, B -> D, C -> D
        resolver.add_dependency("rule_b", "rule_a").await.unwrap();
        resolver.add_dependency("rule_c", "rule_a").await.unwrap();
        resolver.add_dependency("rule_d", "rule_b").await.unwrap();
        resolver.add_dependency("rule_d", "rule_c").await.unwrap();

        let order = resolver
            .resolve_execution_order(&["rule_a", "rule_b", "rule_c", "rule_d"])
            .await
            .unwrap();

        // rule_a 应该第一个
        assert_eq!(order[0], "rule_a");
        // rule_d 应该最后一个
        assert_eq!(order[3], "rule_d");
        // rule_b 和 rule_c 应该在 rule_a 之后，rule_d 之前
        assert!(order.iter().position(|r| r == "rule_b").unwrap() > 0);
        assert!(order.iter().position(|r| r == "rule_c").unwrap() > 0);
        assert!(order.iter().position(|r| r == "rule_b").unwrap() < 3);
        assert!(order.iter().position(|r| r == "rule_c").unwrap() < 3);
    }

    #[tokio::test]
    async fn test_dependency_removal() {
        let resolver = DependencyResolver::new();

        resolver.add_dependency("rule_b", "rule_a").await.unwrap();
        resolver.add_dependency("rule_c", "rule_b").await.unwrap();

        let removed = resolver
            .remove_dependency("rule_b", "rule_a")
            .await
            .unwrap();
        assert!(removed);

        let order = resolver
            .resolve_execution_order(&["rule_a", "rule_b", "rule_c"])
            .await
            .unwrap();
        // 移除依赖后，rule_a 和 rule_b 可以是任意顺序
        assert!(order.contains(&"rule_a".to_string()));
        assert!(order.contains(&"rule_b".to_string()));
        assert!(order.contains(&"rule_c".to_string()));
        // rule_c 仍然应该在 rule_b 之后
        assert!(
            order.iter().position(|r| r == "rule_c").unwrap()
                > order.iter().position(|r| r == "rule_b").unwrap()
        );
    }

    #[tokio::test]
    async fn test_transitive_dependencies() {
        let resolver = DependencyResolver::new();

        resolver.add_dependency("rule_b", "rule_a").await.unwrap();
        resolver.add_dependency("rule_c", "rule_b").await.unwrap();
        resolver.add_dependency("rule_d", "rule_c").await.unwrap();

        let transitive = resolver
            .get_transitive_dependencies("rule_d")
            .await
            .unwrap();
        assert!(transitive.contains("rule_a"));
        assert!(transitive.contains("rule_b"));
        assert!(transitive.contains("rule_c"));
        assert_eq!(transitive.len(), 3);
    }

    #[tokio::test]
    async fn test_dependency_validation() {
        let resolver = DependencyResolver::new();

        resolver.add_dependency("rule_b", "rule_a").await.unwrap();
        resolver.add_dependency("rule_c", "rule_b").await.unwrap();

        let report = resolver.validate_dependencies().await.unwrap();
        assert!(report.is_valid);
        assert!(report.issues.is_empty());
    }

    #[tokio::test]
    async fn test_dependency_statistics() {
        let resolver = DependencyResolver::new();

        let dep1 = RuleDependency::new("rule_a".to_string(), "rule_b".to_string())
            .with_type(DependencyType::Sequential);
        let dep2 = RuleDependency::new("rule_b".to_string(), "rule_c".to_string())
            .with_type(DependencyType::Data)
            .with_soft_dependency();

        resolver.add_dependency_with_details(dep1).await.unwrap();
        resolver.add_dependency_with_details(dep2).await.unwrap();

        let stats = resolver.get_dependency_statistics().await;
        assert_eq!(stats.total_dependencies, 2);
        assert_eq!(stats.hard_dependencies, 1);
        assert_eq!(stats.soft_dependencies, 1);
        assert_eq!(
            stats.dependencies_by_type.get(&DependencyType::Sequential),
            Some(&1)
        );
        assert_eq!(
            stats.dependencies_by_type.get(&DependencyType::Data),
            Some(&1)
        );
    }

    #[tokio::test]
    async fn test_complexity_analysis() {
        let resolver = DependencyResolver::new();

        // 创建一个复杂的依赖图
        resolver.add_dependency("rule_b", "rule_a").await.unwrap();
        resolver.add_dependency("rule_c", "rule_a").await.unwrap();
        resolver.add_dependency("rule_d", "rule_b").await.unwrap();
        resolver.add_dependency("rule_d", "rule_c").await.unwrap();
        resolver.add_dependency("rule_e", "rule_d").await.unwrap();

        let analysis = resolver
            .analyze_dependency_complexity(&["rule_a", "rule_b", "rule_c", "rule_d", "rule_e"])
            .await;

        assert_eq!(analysis.total_rules, 5);
        assert_eq!(analysis.total_dependencies, 5);
        assert!(analysis.complexity_score > 0);
        assert!(!analysis.recommendations.is_empty());
    }

    #[test]
    fn test_dependency_builder() {
        let dep = RuleDependency::new("dependent".to_string(), "dependency".to_string())
            .with_type(DependencyType::Data)
            .with_soft_dependency()
            .with_condition("condition".to_string())
            .with_priority(10)
            .with_timeout(5000)
            .with_description("Test dependency".to_string());

        assert_eq!(dep.dependent_rule_id, "dependent");
        assert_eq!(dep.dependency_rule_id, "dependency");
        assert_eq!(dep.dependency_type, DependencyType::Data);
        assert!(!dep.is_hard_dependency);
        assert_eq!(dep.condition, Some("condition".to_string()));
        assert_eq!(dep.priority, 10);
        assert_eq!(dep.timeout_ms, Some(5000));
        assert_eq!(dep.description, Some("Test dependency".to_string()));
    }
}
