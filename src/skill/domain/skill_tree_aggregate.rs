use crate::shared::SkillId;
use std::collections::HashMap;

/// 代表整个技能树
pub struct SkillTree {
    nodes: HashMap<SkillId, SkillNode>,
    root_skills: Vec<SkillId>,
}

/// 技能树中的一个节点
pub struct SkillNode {
    skill_id: SkillId,
    children: Vec<SkillId>,
    unlock_requirements: UnlockRequirements,
}

/// 解锁技能所需的需求
pub struct UnlockRequirements {
    required_level: u32,
    required_skills: Vec<SkillId>,
}

impl SkillTree {
    pub fn new() -> Self {
        Self {
            nodes: HashMap::new(),
            root_skills: Vec::new(),
        }
    }

    pub fn can_unlock_skill(&self, _skill_id: &SkillId) -> bool {
        // 检查是否满足解锁条件
        true
    }
}
