/// 战斗系统类型定义
/// 支持多种不同的战斗模式

/// 战斗系统类型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum BattleSystemType {
    /// 传统回合制：严格按回合顺序执行
    TurnBased,
    /// 实时战斗：基于时间的连续战斗
    RealTime,
    /// 准实时：有冷却但不严格回合
    QuasiRealTime,
}

/// 战斗时间管理器
#[derive(<PERSON>bu<PERSON>, Clone)]
pub struct BattleTimer {
    /// 当前战斗时间（毫秒）
    pub current_time: u64,
    /// 战斗开始时间
    pub start_time: u64,
    /// 时间倍率（1.0 = 正常速度）
    pub time_scale: f32,
    /// 是否暂停
    pub paused: bool,
}

impl BattleTimer {
    pub fn new() -> Self {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        
        Self {
            current_time: now,
            start_time: now,
            time_scale: 1.0,
            paused: false,
        }
    }
    
    /// 更新时间
    pub fn update(&mut self, delta_ms: u64) {
        if !self.paused {
            self.current_time += (delta_ms as f32 * self.time_scale) as u64;
        }
    }
    
    /// 获取战斗持续时间
    pub fn get_battle_duration(&self) -> u64 {
        self.current_time - self.start_time
    }
    
    /// 暂停/恢复
    pub fn toggle_pause(&mut self) {
        self.paused = !self.paused;
    }
    
    /// 设置时间倍率
    pub fn set_time_scale(&mut self, scale: f32) {
        self.time_scale = scale.max(0.1).min(10.0); // 限制在0.1x-10x之间
    }
}

/// 实时战斗的冷却管理
#[derive(Debug, Clone)]
pub struct CooldownManager {
    /// 技能冷却映射：技能ID -> 冷却结束时间
    pub skill_cooldowns: std::collections::HashMap<u32, u64>,
    /// 全局冷却时间
    pub global_cooldown_end: u64,
    /// 普通攻击冷却时间
    pub attack_cooldown_end: u64,
    pub system_type: BattleSystemType,
    /// 基础攻击冷却时间（毫秒）
    pub base_attack_cooldown: u64,
    /// 全局冷却时间（毫秒）
    pub global_cooldown: u64,
    /// 实时战斗的更新频率（毫秒）
    pub real_time_update_interval: u64,
    /// 最大战斗时间（秒）
    pub max_battle_duration: u64,
}

impl CooldownManager {
    pub fn new() -> Self {
        Self {
            skill_cooldowns: std::collections::HashMap::new(),
            global_cooldown_end: 0,
            attack_cooldown_end: 0,
            system_type: BattleSystemType::TurnBased,
            base_attack_cooldown: 1500,    // 1.5秒
            global_cooldown: 500,          // 0.5秒
            real_time_update_interval: 50,   // 50ms
            max_battle_duration: 300,      // 5分钟
        }
    }
    
    /// 检查技能是否可用
    pub fn is_skill_ready(&self, skill_id: u32, current_time: u64) -> bool {
        if current_time < self.global_cooldown_end {
            return false;
        }
        
        if let Some(&cooldown_end) = self.skill_cooldowns.get(&skill_id) {
            current_time >= cooldown_end
        } else {
            true
        }
    }
    
    /// 检查普通攻击是否可用
    pub fn is_attack_ready(&self, current_time: u64) -> bool {
        current_time >= self.attack_cooldown_end && current_time >= self.global_cooldown_end
    }
    
    /// 设置技能冷却
    pub fn set_skill_cooldown(&mut self, skill_id: u32, cooldown_ms: u64, current_time: u64) {
        self.skill_cooldowns.insert(skill_id, current_time + cooldown_ms);
        self.global_cooldown_end = current_time + 500; // 500ms全局冷却
    }
    
    /// 设置攻击冷却
    pub fn set_attack_cooldown(&mut self, cooldown_ms: u64, current_time: u64) {
        self.attack_cooldown_end = current_time + cooldown_ms;
        self.global_cooldown_end = current_time + 200; // 200ms全局冷却
    }
    
    /// 获取技能剩余冷却时间
    pub fn get_skill_remaining_cooldown(&self, skill_id: u32, current_time: u64) -> u64 {
        if let Some(&cooldown_end) = self.skill_cooldowns.get(&skill_id) {
            if current_time < cooldown_end {
                cooldown_end - current_time
            } else {
                0
            }
        } else {
            0
        }
    }
}

/// 战斗系统配置
#[derive(Debug, Clone)]
pub struct BattleSystemConfig {
    /// 战斗系统类型
    pub system_type: BattleSystemType,
    /// 基础攻击冷却时间（毫秒）
    pub base_attack_cooldown: u64,
    /// 全局冷却时间（毫秒）
    pub global_cooldown: u64,
    /// 实时战斗的更新频率（毫秒）
    pub real_time_update_interval: u64,
    /// 最大战斗时间（秒）
    pub max_battle_duration: u64,
}

impl Default for BattleSystemConfig {
    fn default() -> Self {
        Self {
            system_type: BattleSystemType::TurnBased,
            base_attack_cooldown: 1500,    // 1.5秒
            global_cooldown: 500,          // 0.5秒
            real_time_update_interval: 50,   // 50ms
            max_battle_duration: 300,      // 5分钟
        }
    }
} 