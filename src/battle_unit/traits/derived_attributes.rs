/// 派生属性trait
///
/// 从基础属性计算得出的属性，如攻击力、防御力等
use crate::shared::{
    Accuracy, Attack, CriticalDamage, CriticalRate, Defense, Evasion, Range, Speed,
};

/// 派生属性 - 从基础属性计算得出的属性
pub trait DerivedAttributes {
    /// 获取攻击力
    fn attack_power(&self) -> Attack;

    /// 获取物理防御
    fn physical_defense(&self) -> Defense;

    /// 获取魔法防御
    fn magical_defense(&self) -> Defense;

    /// 获取命中率
    fn accuracy(&self) -> Accuracy;

    /// 获取闪避率
    fn evasion(&self) -> Evasion;

    /// 获取暴击率
    fn critical_rate(&self) -> CriticalRate;

    /// 获取暴击伤害倍数
    fn critical_damage(&self) -> CriticalDamage;

    /// 获取移动速度
    fn movement_speed(&self) -> Speed;

    /// 获取攻击速度
    fn attack_speed(&self) -> Speed;

    /// 获取施法速度
    fn casting_speed(&self) -> Speed;

    /// 获取攻击范围
    fn attack_range(&self) -> Range;
}
