/// 世界地图模块的标识符类型定义
/// 提供强类型的ID封装，避免ID混用错误

use serde::{Deserialize, Serialize};

// ============================================================================
// 核心标识符类型
// ============================================================================

/// 世界ID - 用于标识不同的世界实例
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct WorldId(pub u64);

impl WorldId {
    pub fn new(id: u64) -> Self {
        Self(id)
    }
    
    pub fn value(&self) -> u64 {
        self.0
    }
}

/// 层级ID - 用于标识世界的不同层次
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct LayerId(pub u32);

impl LayerId {
    pub fn new(id: u32) -> Self {
        Self(id)
    }
    
    pub fn value(&self) -> u32 {
        self.0
    }
}

/// 区域ID - 用于标识世界中的不同区域
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub struct RegionId(pub u64);

impl RegionId {
    pub fn new(id: u64) -> Self {
        Self(id)
    }
    
    pub fn value(&self) -> u64 {
        self.0
    }
}

/// 节点ID - 用于标识资源节点
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub struct NodeId(pub u64);

impl NodeId {
    pub fn new(id: u64) -> Self {
        Self(id)
    }
    
    pub fn value(&self) -> u64 {
        self.0
    }
}

/// 建筑ID - 用于标识功能建筑
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub struct BuildingId(pub u64);

impl BuildingId {
    pub fn new(id: u64) -> Self {
        Self(id)
    }
    
    pub fn value(&self) -> u64 {
        self.0
    }
}

/// 玩家ID - 用于标识玩家
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct PlayerId(pub u64);

impl PlayerId {
    pub fn new(id: u64) -> Self {
        Self(id)
    }
    
    pub fn value(&self) -> u64 {
        self.0
    }
}

/// 势力ID - 用于标识不同的势力/门派
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct FactionId(pub u32);

impl FactionId {
    pub fn new(id: u32) -> Self {
        Self(id)
    }
    
    pub fn value(&self) -> u32 {
        self.0
    }
}

/// 事件ID - 用于标识世界事件
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct EventId(pub u64);

impl EventId {
    pub fn new(id: u64) -> Self {
        Self(id)
    }
    
    pub fn value(&self) -> u64 {
        self.0
    }
}