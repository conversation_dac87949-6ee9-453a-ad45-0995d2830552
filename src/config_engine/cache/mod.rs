//! # 缓存系统模块
//!
//! 提供高性能的多级缓存系统，支持规则结果缓存、配置缓存和智能失效策略
//! 支持基于Moka的高性能缓存实现

pub mod moka_cache;

// 使用 Moka 缓存作为默认实现，重新导出所有主要类型
pub use moka_cache::{
    CacheEvent, CacheEventListener, 
    CachedConfig, CachedRuleResult, CachedTypeInfo, CachedValidationResult,
    EvictionPolicy, 
    MokaCacheConfig, MokaCacheConfig as CacheConfig,
    MokaCacheConfigBuilder,
    MokaCacheInfo as CacheInfo,
    MokaCacheManager as CacheManager,
    MokaCacheStats as CacheStats,
    CacheHealthStatus,
    CacheType, WarmupRequest, WarmupResult, WarmupStatus,
};

// 为了兼容性，定义 CacheResult 类型别名
#[derive(Debug, Clone)]
pub enum CacheResult<T> {
    /// 缓存命中
    Hit(T),
    /// 缓存未命中
    Miss,
    /// 缓存错误
    Error(String),
}

// 为了兼容性，定义一些基本类型
#[derive(Debug, Clone)]
pub enum CacheValue {
    /// 规则执行结果
    RuleResult(std::collections::HashMap<String, crate::config_engine::rules::ContextValue>),
    /// 配置数据
    Config(serde_json::Value),
    /// 通用字符串
    String(String),
    /// 二进制数据
    Binary(Vec<u8>),
}

#[derive(Debug, Clone)]
pub struct CacheEntry {
    pub key: String,
    pub value: CacheValue,
    pub created_at: std::time::SystemTime,
    pub last_accessed: std::time::SystemTime,
    pub access_count: u64,
    pub ttl: Option<Duration>,
}

// L2缓存提供者接口（用于扩展）
#[async_trait::async_trait]
pub trait L2CacheProvider: Send + Sync {
    async fn get(&self, key: &str) -> Result<Option<Vec<u8>>>;
    async fn set(&self, key: &str, value: Vec<u8>, ttl: Option<Duration>) -> Result<()>;
    async fn delete(&self, key: &str) -> Result<bool>;
    async fn clear(&self) -> Result<()>;
    fn name(&self) -> &str;
}

use crate::config_engine::{Result};
use std::mem::size_of;
use std::time::Duration;

/// 缓存构建器
///
/// 提供便捷的缓存系统配置和创建接口
#[derive(Debug)]
pub struct CacheBuilder {
    config: CacheConfig,
}

impl CacheBuilder {
    /// 创建新的缓存构建器
    pub fn new() -> Self {
        Self {
            config: CacheConfig::default(),
        }
    }

    /// 设置规则缓存容量
    pub fn with_l1_max_size(mut self, max_size: usize) -> Self {
        self.config.rule_cache_capacity = max_size as u64;
        self
    }

    /// 设置规则结果TTL
    pub fn with_rule_result_ttl(mut self, ttl: Duration) -> Self {
        self.config.rule_ttl = ttl;
        self
    }

    /// 设置配置TTL
    pub fn with_config_ttl(mut self, ttl: Duration) -> Self {
        self.config.config_ttl = ttl;
        self
    }

    /// 设置默认TTL
    pub fn with_default_ttl(mut self, ttl: Duration) -> Self {
        self.config.validation_ttl = ttl;
        self
    }

    /// 设置驱逐策略
    pub fn with_eviction_policy(mut self, policy: EvictionPolicy) -> Self {
        self.config.eviction_policy = policy;
        self
    }

    /// 启用基于权重的驱逐
    pub fn with_weight_based_eviction(mut self, enabled: bool) -> Self {
        self.config.enable_weight_based_eviction = enabled;
        self
    }

    /// 设置初始容量
    pub fn with_initial_capacity(mut self, capacity: usize) -> Self {
        self.config.initial_capacity = capacity as u64;
        self
    }

    /// 设置最小健康命中率
    pub fn with_min_healthy_hit_rate(mut self, rate: f64) -> Self {
        self.config.min_healthy_hit_rate = rate;
        self
    }

    /// 构建缓存管理器
    pub async fn build(self) -> Result<CacheManager> {
        CacheManager::new(self.config).await
    }
}

impl Default for CacheBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 缓存预设配置
pub struct CachePresets;

impl CachePresets {
    /// 开发环境配置
    ///
    /// 适用于开发和测试环境，缓存时间较短，便于调试
    pub fn development() -> CacheConfig {
        MokaCacheConfig {
            rule_cache_capacity: 1000,
            config_cache_capacity: 100,
            validation_cache_capacity: 500,
            type_cache_capacity: 50,
            rule_ttl: Duration::from_secs(300), // 5分钟
            config_ttl: Duration::from_secs(180),      // 3分钟
            validation_ttl: Duration::from_secs(180),     // 3分钟
            type_ttl: Duration::from_secs(360),     // 6分钟
            rule_tti: Duration::from_secs(150), // 2.5分钟
            config_tti: Duration::from_secs(90),      // 1.5分钟
            validation_tti: Duration::from_secs(90),     // 1.5分钟
            type_tti: Duration::from_secs(180),     // 3分钟
            eviction_policy: EvictionPolicy::LRU,
            enable_weight_based_eviction: false,
            initial_capacity: 100,
            min_healthy_hit_rate: 0.5,
        }
    }

    /// 生产环境配置
    ///
    /// 适用于生产环境，较长的缓存时间和更大的容量
    pub fn production() -> CacheConfig {
        MokaCacheConfig {
            rule_cache_capacity: 50000,
            config_cache_capacity: 5000,
            validation_cache_capacity: 25000,
            type_cache_capacity: 2500,
            rule_ttl: Duration::from_secs(7200), // 2小时
            config_ttl: Duration::from_secs(3600),      // 1小时
            validation_ttl: Duration::from_secs(3600),     // 1小时
            type_ttl: Duration::from_secs(7200),     // 2小时
            rule_tti: Duration::from_secs(3600), // 1小时
            config_tti: Duration::from_secs(1800),      // 30分钟
            validation_tti: Duration::from_secs(1800),     // 30分钟
            type_tti: Duration::from_secs(3600),     // 1小时
            eviction_policy: EvictionPolicy::WTinyLFU,
            enable_weight_based_eviction: true,
            initial_capacity: 5000,
            min_healthy_hit_rate: 0.8,
        }
    }

    /// 高性能配置
    ///
    /// 针对高并发场景优化，更激进的缓存策略
    pub fn high_performance() -> CacheConfig {
        MokaCacheConfig {
            rule_cache_capacity: 100000,
            config_cache_capacity: 10000,
            validation_cache_capacity: 50000,
            type_cache_capacity: 5000,
            rule_ttl: Duration::from_secs(14400), // 4小时
            config_ttl: Duration::from_secs(7200),       // 2小时
            validation_ttl: Duration::from_secs(7200),      // 2小时
            type_ttl: Duration::from_secs(14400),      // 4小时
            rule_tti: Duration::from_secs(7200), // 2小时
            config_tti: Duration::from_secs(3600),       // 1小时
            validation_tti: Duration::from_secs(3600),      // 1小时
            type_tti: Duration::from_secs(7200),      // 2小时
            eviction_policy: EvictionPolicy::WTinyLFU,        // 使用WTinyLFU策略
            enable_weight_based_eviction: true,
            initial_capacity: 10000,
            min_healthy_hit_rate: 0.9,
        }
    }

    /// 内存优化配置
    ///
    /// 适用于内存受限的环境
    pub fn memory_optimized() -> CacheConfig {
        MokaCacheConfig {
            rule_cache_capacity: 5000,
            config_cache_capacity: 500,
            validation_cache_capacity: 2500,
            type_cache_capacity: 250,
            rule_ttl: Duration::from_secs(1800), // 30分钟
            config_ttl: Duration::from_secs(900),       // 15分钟
            validation_ttl: Duration::from_secs(900),      // 15分钟
            type_ttl: Duration::from_secs(1800),      // 30分钟
            rule_tti: Duration::from_secs(900), // 15分钟
            config_tti: Duration::from_secs(450),       // 7.5分钟
            validation_tti: Duration::from_secs(450),      // 7.5分钟
            type_tti: Duration::from_secs(900),      // 15分钟
            eviction_policy: EvictionPolicy::TinyLFU,
            enable_weight_based_eviction: true,
            initial_capacity: 500,
            min_healthy_hit_rate: 0.6,
        }
    }

    /// 安全配置
    ///
    /// 启用加密和其他安全特性
    pub fn secure() -> CacheConfig {
        MokaCacheConfig {
            rule_cache_capacity: 20000,
            config_cache_capacity: 2000,
            validation_cache_capacity: 10000,
            type_cache_capacity: 1000,
            rule_ttl: Duration::from_secs(1800), // 30分钟
            config_ttl: Duration::from_secs(900),       // 15分钟
            validation_ttl: Duration::from_secs(900),      // 15分钟
            type_ttl: Duration::from_secs(1800),      // 30分钟
            rule_tti: Duration::from_secs(900), // 15分钟
            config_tti: Duration::from_secs(450),       // 7.5分钟
            validation_tti: Duration::from_secs(450),      // 7.5分钟
            type_tti: Duration::from_secs(900),      // 15分钟
            eviction_policy: EvictionPolicy::LRU,       // 使用LRU策略
            enable_weight_based_eviction: false,
            initial_capacity: 2000,
            min_healthy_hit_rate: 0.7,
        }
    }
}

/// 缓存工厂
///
/// 提供便捷的缓存创建方法
pub struct CacheFactory;

impl CacheFactory {
    /// 创建开发环境缓存
    pub async fn create_development_cache() -> Result<CacheManager> {
        CacheManager::new(CachePresets::development()).await
    }

    /// 创建生产环境缓存
    pub async fn create_production_cache() -> Result<CacheManager> {
        CacheManager::new(CachePresets::production()).await
    }

    /// 创建高性能缓存
    pub async fn create_high_performance_cache() -> Result<CacheManager> {
        CacheManager::new(CachePresets::high_performance()).await
    }

    /// 创建内存优化缓存
    pub async fn create_memory_optimized_cache() -> Result<CacheManager> {
        CacheManager::new(CachePresets::memory_optimized()).await
    }

    /// 创建安全缓存
    pub async fn create_secure_cache() -> Result<CacheManager> {
        CacheManager::new(CachePresets::secure()).await
    }

    /// 根据环境变量创建缓存
    pub async fn create_from_env() -> Result<CacheManager> {
        let cache_profile =
            std::env::var("CACHE_PROFILE").unwrap_or_else(|_| "development".to_string());

        let config = match cache_profile.as_str() {
            "production" => CachePresets::production(),
            "high_performance" => CachePresets::high_performance(),
            "memory_optimized" => CachePresets::memory_optimized(),
            "secure" => CachePresets::secure(),
            _ => CachePresets::development(),
        };

        CacheManager::new(config).await
    }
}

/// 缓存工具
///
/// 提供缓存操作的便捷工具函数
pub struct CacheUtils;

impl CacheUtils {
    /// 生成规则缓存键
    pub fn generate_rule_cache_key(rule_id: &str, input_hash: &str) -> String {
        format!("rule:{}:{}", rule_id, input_hash)
    }

    /// 生成配置缓存键
    pub fn generate_config_cache_key(config_type: &str, config_id: &str) -> String {
        format!("config:{}:{}", config_type, config_id)
    }

    /// 生成类型缓存键
    pub fn generate_type_cache_key(type_category: &str, type_id: &str) -> String {
        format!("type:{}:{}", type_category, type_id)
    }

    /// 解析缓存键
    pub fn parse_cache_key(cache_key: &str) -> Option<(String, String, String)> {
        let parts: Vec<&str> = cache_key.split(':').collect();
        if parts.len() == 3 {
            Some((
                parts[0].to_string(),
                parts[1].to_string(),
                parts[2].to_string(),
            ))
        } else {
            None
        }
    }

    /// 计算输入数据哈希
    pub fn calculate_input_hash(
        input_data: &std::collections::HashMap<String, crate::config_engine::rules::ContextValue>,
    ) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();

        // 按键排序以确保一致的哈希
        let mut sorted_keys: Vec<_> = input_data.keys().collect();
        sorted_keys.sort();

        for key in sorted_keys {
            key.hash(&mut hasher);
            if let Some(value) = input_data.get(key) {
                format!("{:?}", value).hash(&mut hasher);
            }
        }

        format!("{:x}", hasher.finish())
    }

    /// 检查缓存键模式
    pub fn matches_pattern(key: &str, pattern: &str) -> bool {
        if pattern.contains('*') {
            let parts: Vec<&str> = pattern.split('*').collect();
            if parts.len() == 2 {
                let prefix = parts[0];
                let suffix = parts[1];
                return key.starts_with(prefix) && key.ends_with(suffix);
            }
        }
        key == pattern
    }

    /// 估算缓存项大小
    pub fn estimate_cache_entry_size(entry: &CacheEntry) -> usize {
        let mut size = size_of::<CacheEntry>();
        size += entry.key.len();

        match &entry.value {
            CacheValue::String(s) => size += s.len(),
            CacheValue::Binary(b) => size += b.len(),
            CacheValue::RuleResult(result) => {
                for (key, value) in result {
                    size += key.len();
                    size += Self::estimate_context_value_size(value);
                }
            }
            CacheValue::Config(config) => {
                size += config.to_string().len();
            }
        }

        size
    }

    /// 估算上下文值大小
    fn estimate_context_value_size(value: &crate::config_engine::rules::ContextValue) -> usize {
        use crate::config_engine::rules::ContextValue;

        match value {
            ContextValue::String(s) => s.len(),
            ContextValue::Int(_) => 8,
            ContextValue::Float(_) => 8,
            ContextValue::Bool(_) => 1,
            ContextValue::Array(arr) => arr
                .iter()
                .map(|v| Self::estimate_context_value_size(v))
                .sum(),
            ContextValue::Object(obj) => obj
                .iter()
                .map(|(k, v)| k.len() + Self::estimate_context_value_size(v))
                .sum(),
            ContextValue::Null => 0,
        }
    }
}

/// 缓存监控器
///
/// 提供缓存性能监控和告警功能
pub struct CacheMonitor {
    manager: std::sync::Arc<CacheManager>,
    alert_thresholds: MonitoringThresholds,
}

impl CacheMonitor {
    /// 创建新的缓存监控器
    pub fn new(manager: std::sync::Arc<CacheManager>) -> Self {
        Self {
            manager,
            alert_thresholds: MonitoringThresholds::default(),
        }
    }

    /// 设置监控阈值
    pub fn with_thresholds(mut self, thresholds: MonitoringThresholds) -> Self {
        self.alert_thresholds = thresholds;
        self
    }

    /// 执行健康检查
    pub async fn health_check(&self) -> CacheMonitoringReport {
        let stats = self.manager.get_stats().await;
        let info = self.manager.get_cache_info().await;
        let health = self.manager.health_check().await;

        let mut alerts = Vec::new();

        // 检查命中率
        if health.hit_rate < self.alert_thresholds.min_hit_rate {
            alerts.push(format!(
                "缓存命中率过低: {:.2}% (阈值: {:.2}%)",
                health.hit_rate * 100.0,
                self.alert_thresholds.min_hit_rate * 100.0
            ));
        }

        // 检查错误率
        if health.error_count > self.alert_thresholds.max_error_count {
            alerts.push(format!(
                "缓存错误次数过多: {} (阈值: {})",
                health.error_count, self.alert_thresholds.max_error_count
            ));
        }

        // 检查内存使用率
        if health.memory_usage_mb > 1000.0 { // 简化检查
            alerts.push(format!(
                "内存使用过高: {:.2} MB",
                health.memory_usage_mb
            ));
        }

        let recommendations = self.generate_recommendations(&health, &info).await;
        CacheMonitoringReport {
            timestamp: std::time::SystemTime::now(),
            stats,
            info,
            health,
            alerts,
            recommendations,
        }
    }

    /// 生成优化建议
    async fn generate_recommendations(
        &self,
        health: &CacheHealthStatus,
        _info: &CacheInfo,
    ) -> Vec<String> {
        let mut recommendations = Vec::new();

        if health.hit_rate < 0.7 {
            recommendations.push("考虑增加缓存TTL以提高命中率".to_string());
        }

        if health.total_entries > 9000 {
            recommendations.push("考虑增加缓存容量".to_string());
        }

        if health.eviction_count > 1000 {
            recommendations.push("考虑调整缓存策略".to_string());
        }

        if health.error_count > 0 {
            recommendations.push("检查缓存系统错误日志".to_string());
        }

        recommendations
    }

    /// 开始持续监控
    pub async fn start_monitoring(&self, interval: Duration) {
        let manager = std::sync::Arc::clone(&self.manager);
        let thresholds = self.alert_thresholds.clone();

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);

            loop {
                interval_timer.tick().await;

                let monitor =
                    CacheMonitor::new(manager.clone()).with_thresholds(thresholds.clone());
                let report = monitor.health_check().await;

                if !report.alerts.is_empty() {
                    log::warn!("缓存监控告警: {:?}", report.alerts);
                }

                if !report.recommendations.is_empty() {
                    log::info!("缓存优化建议: {:?}", report.recommendations);
                }
            }
        });
    }
}

/// 监控阈值
#[derive(Debug, Clone)]
pub struct MonitoringThresholds {
    /// 最小命中率
    pub min_hit_rate: f64,
    /// 最大错误次数
    pub max_error_count: u64,
    /// 最大内存使用率
    pub max_memory_utilization: f64,
}

impl Default for MonitoringThresholds {
    fn default() -> Self {
        Self {
            min_hit_rate: 0.5, // 50%
            max_error_count: 100,
            max_memory_utilization: 0.9, // 90%
        }
    }
}

/// 缓存监控报告
#[derive(Debug, Clone)]
pub struct CacheMonitoringReport {
    /// 报告时间戳
    pub timestamp: std::time::SystemTime,
    /// 缓存统计
    pub stats: CacheStats,
    /// 缓存信息
    pub info: CacheInfo,
    /// 健康状态
    pub health: CacheHealthStatus,
    /// 告警信息
    pub alerts: Vec<String>,
    /// 优化建议
    pub recommendations: Vec<String>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_cache_builder() {
        let builder = CacheBuilder::new()
            .with_l1_max_size(5000)
            .with_rule_result_ttl(Duration::from_secs(1800))
            .with_eviction_policy(EvictionPolicy::LFU)
            .with_weight_based_eviction(true);

        assert_eq!(builder.config.rule_cache_capacity, 5000);
        assert_eq!(builder.config.rule_ttl, Duration::from_secs(1800));
        assert_eq!(builder.config.eviction_policy, EvictionPolicy::LFU);
        assert!(builder.config.enable_weight_based_eviction);
    }

    #[test]
    fn test_cache_presets() {
        let dev_config = CachePresets::development();
        assert_eq!(dev_config.rule_cache_capacity, 1000);
        assert_eq!(dev_config.rule_ttl, Duration::from_secs(300));

        let prod_config = CachePresets::production();
        assert_eq!(prod_config.rule_cache_capacity, 50000);
        assert!(prod_config.enable_weight_based_eviction);

        let hp_config = CachePresets::high_performance();
        assert_eq!(hp_config.eviction_policy, EvictionPolicy::WTinyLFU);
        assert_eq!(hp_config.rule_cache_capacity, 100000);
    }

    #[test]
    fn test_cache_utils_key_generation() {
        let rule_key = CacheUtils::generate_rule_cache_key("material_discovery", "abc123");
        assert_eq!(rule_key, "rule:material_discovery:abc123");

        let config_key = CacheUtils::generate_config_cache_key("rules", "material_discovery");
        assert_eq!(config_key, "config:rules:material_discovery");

        let type_key = CacheUtils::generate_type_cache_key("materials", "common_herb");
        assert_eq!(type_key, "type:materials:common_herb");
    }

    #[test]
    fn test_cache_utils_key_parsing() {
        let key = "rule:material_discovery:abc123";
        let parsed = CacheUtils::parse_cache_key(key);

        assert_eq!(
            parsed,
            Some((
                "rule".to_string(),
                "material_discovery".to_string(),
                "abc123".to_string()
            ))
        );

        let invalid_key = "invalid_key";
        let parsed_invalid = CacheUtils::parse_cache_key(invalid_key);
        assert_eq!(parsed_invalid, None);
    }

    #[test]
    fn test_cache_utils_pattern_matching() {
        assert!(CacheUtils::matches_pattern("rule:test:123", "rule:*"));
        assert!(CacheUtils::matches_pattern("rule:test:123", "rule:test:*"));
        assert!(CacheUtils::matches_pattern("rule:test:123", "*:123"));
        assert!(!CacheUtils::matches_pattern("config:test:123", "rule:*"));
        assert!(CacheUtils::matches_pattern("exact_match", "exact_match"));
    }

    #[test]
    fn test_cache_utils_input_hash() {
        let mut input1 = HashMap::new();
        input1.insert(
            "key1".to_string(),
            crate::config_engine::rules::ContextValue::String("value1".to_string()),
        );
        input1.insert(
            "key2".to_string(),
            crate::config_engine::rules::ContextValue::Int(42),
        );

        let mut input2 = HashMap::new();
        input2.insert(
            "key2".to_string(),
            crate::config_engine::rules::ContextValue::Int(42),
        );
        input2.insert(
            "key1".to_string(),
            crate::config_engine::rules::ContextValue::String("value1".to_string()),
        );

        let hash1 = CacheUtils::calculate_input_hash(&input1);
        let hash2 = CacheUtils::calculate_input_hash(&input2);

        // 相同内容不同顺序应产生相同哈希
        assert_eq!(hash1, hash2);
    }

    #[tokio::test]
    async fn test_cache_factory() {
        let dev_cache = CacheFactory::create_development_cache().await.unwrap();
        let info = dev_cache.get_cache_info().await;
        assert_eq!(info.config.rule_cache_capacity, 1000);

        let prod_cache = CacheFactory::create_production_cache().await.unwrap();
        let prod_info = prod_cache.get_cache_info().await;
        assert_eq!(prod_info.config.rule_cache_capacity, 50000);
    }

    #[tokio::test]
    async fn test_cache_monitor() {
        let cache = CacheFactory::create_development_cache().await.unwrap();
        let monitor = CacheMonitor::new(std::sync::Arc::new(cache));

        let report = monitor.health_check().await;
        assert!(report.alerts.is_empty()); // 新缓存应该没有告警
    }

    #[test]
    fn test_monitoring_thresholds() {
        let thresholds = MonitoringThresholds::default();
        assert_eq!(thresholds.min_hit_rate, 0.5);
        assert_eq!(thresholds.max_error_count, 100);
        assert_eq!(thresholds.max_memory_utilization, 0.9);
    }
}
